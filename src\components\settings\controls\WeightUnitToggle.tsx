'use client'

interface WeightUnitToggleProps {
  value: 'kg' | 'lbs'
  onChange: (value: 'kg' | 'lbs') => void
  disabled?: boolean
}

export default function WeightUnitToggle({
  value,
  onChange,
  disabled = false,
}: WeightUnitToggleProps) {
  return (
    <button
      onClick={() => onChange(value === 'kg' ? 'lbs' : 'kg')}
      disabled={disabled}
      className={`
        relative inline-flex h-8 w-16 items-center rounded-full
        transition-colors duration-200 ease-in-out
        ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        ${value === 'kg' ? 'bg-brand-primary' : 'bg-surface-tertiary'}
      `}
      role="switch"
      aria-checked={value === 'kg'}
      aria-label={`Weight unit: ${value}`}
    >
      <span
        className={`
          inline-block h-6 w-6 transform rounded-full
          bg-white transition-transform duration-200 ease-in-out
          ${value === 'kg' ? 'translate-x-9' : 'translate-x-1'}
        `}
      />
      <span
        className={`
          absolute inset-0 flex items-center justify-center
          text-xs font-medium text-white pointer-events-none
          ${value === 'kg' ? 'pr-6' : 'pl-6'}
        `}
      >
        {value}
      </span>
    </button>
  )
}
