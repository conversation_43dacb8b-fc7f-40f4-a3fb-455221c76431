export const SET_STYLE_DESCRIPTIONS = {
  Normal: {
    name: 'Normal',
    shortDescription: 'Standard straight sets',
    fullDescription:
      'Traditional workout style where you complete all reps and sets with the same weight and rest between sets.',
    benefits: [
      'Simple to track',
      'Good for beginners',
      'Consistent progression',
    ],
  },
  'Rest-Pause': {
    name: 'Rest-Pause',
    shortDescription: 'Brief rest within a set',
    fullDescription:
      'After reaching failure, rest 10-15 seconds and continue for more reps. Increases intensity and volume.',
    benefits: ['Increased intensity', 'Time efficient', 'Breaks plateaus'],
  },
  Drop: {
    name: 'Drop',
    shortDescription: 'Reduce weight, continue reps',
    fullDescription:
      'After reaching failure, immediately reduce weight by 20-30% and continue for more reps without rest.',
    benefits: ['Maximum muscle fatigue', 'Increased volume', 'Great finisher'],
  },
  Pyramid: {
    name: 'Pyramid',
    shortDescription: 'Weight up, reps down',
    fullDescription:
      'Progressively increase weight while decreasing reps with each set. Builds both strength and endurance.',
    benefits: ['Strength building', 'Progressive overload', 'Warm-up built in'],
  },
  'Reverse Pyramid': {
    name: 'Reverse Pyramid',
    shortDescription: 'Weight down, reps up',
    fullDescription:
      'Start with heaviest weight, then decrease weight and increase reps. Maximum strength when fresh.',
    benefits: ['Maximum strength focus', 'High intensity', 'Efficient volume'],
  },
} as const

export type SetStyleKey = keyof typeof SET_STYLE_DESCRIPTIONS
