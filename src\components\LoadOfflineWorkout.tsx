'use client'

import { useState } from 'react'
import { useNetworkStatus } from '@/hooks/useNetworkStatus'
import { useOfflineStore } from '@/stores/offlineStore'
import { WorkoutCacheService } from '@/services/workoutCacheService'
import { Button } from '@/components/ui/Button'

/**
 * LoadOfflineWorkout Component
 *
 * Provides a button to load workout data for offline use.
 * Shows different states based on network status and loading state.
 */
export function LoadOfflineWorkout() {
  const { isOnline } = useNetworkStatus()
  const {
    isOfflineWorkoutLoaded,
    loadedWorkoutId,
    isLoadingOfflineWorkout,
    setLoadingOfflineWorkout,
    setOfflineWorkoutLoaded,
    clearOfflineWorkout,
  } = useOfflineStore()

  const [workoutCacheService] = useState(() => new WorkoutCacheService())

  /**
   * Handle loading workout data for offline use
   */
  const handleLoadOfflineWorkout = async () => {
    if (!isOnline || isLoadingOfflineWorkout) {
      return
    }

    // For now, use a test username - in production this would come from auth
    const username = '<EMAIL>'

    setLoadingOfflineWorkout(true)

    try {
      const result = await workoutCacheService.preloadWorkoutData(username)

      if (result.success && result.workoutDetails) {
        // Mark workout as loaded in store
        setOfflineWorkoutLoaded(result.workoutDetails.Id.toString())
        // Workout loaded successfully: result (removed console.log for production)
      } else {
        // Handle loading failure
        console.error('Failed to load workout:', result.error)
      }
    } catch (error) {
      console.error('Error loading offline workout:', error)
    } finally {
      setLoadingOfflineWorkout(false)
    }
  }

  /**
   * Handle clearing cached workout data
   */
  const handleClearOfflineWorkout = () => {
    clearOfflineWorkout()
    // Offline workout cleared
  }

  /**
   * Get button text based on current state
   */
  const getButtonText = () => {
    if (isLoadingOfflineWorkout) {
      return 'Loading...'
    }

    if (isOfflineWorkoutLoaded) {
      return 'Offline Workout (Loaded)'
    }

    if (!isOnline) {
      return 'Load Offline Workout (offline)'
    }

    return 'Load Offline Workout'
  }

  /**
   * Get ARIA label for accessibility
   */
  const getAriaLabel = () => {
    if (isLoadingOfflineWorkout) {
      return 'Loading workout data for offline use'
    }

    if (isOfflineWorkoutLoaded) {
      return `Offline workout loaded (ID: ${loadedWorkoutId}). Click to manage.`
    }

    if (!isOnline) {
      return 'Load workout for offline use - currently offline'
    }

    return 'Load workout data for offline use'
  }

  /**
   * Determine if button should be disabled
   */
  const isDisabled = !isOnline || isLoadingOfflineWorkout

  return (
    <div className="flex flex-col gap-2">
      <Button
        onClick={handleLoadOfflineWorkout}
        disabled={isDisabled}
        loading={isLoadingOfflineWorkout}
        variant={isOfflineWorkoutLoaded ? 'secondary' : 'primary'}
        size="md"
        fullWidth
        aria-label={getAriaLabel()}
        className={`
          transition-all duration-200
          ${isOfflineWorkoutLoaded ? 'bg-success/10 border-success/20 text-success' : ''}
          ${!isOnline ? 'opacity-60' : ''}
        `}
      >
        {getButtonText()}
      </Button>

      {/* Clear option when workout is loaded */}
      {isOfflineWorkoutLoaded && (
        <button
          onClick={handleClearOfflineWorkout}
          className="text-sm text-text-secondary hover:text-text-primary transition-colors duration-200 underline"
          aria-label="Clear cached offline workout"
        >
          Clear offline workout
        </button>
      )}

      {/* Status information */}
      {isOfflineWorkoutLoaded && loadedWorkoutId && (
        <div className="text-xs text-text-secondary">
          Workout ID: {loadedWorkoutId}
        </div>
      )}

      {/* Offline indicator */}
      {!isOnline && (
        <div className="text-xs text-warning">⚠️ You are currently offline</div>
      )}
    </div>
  )
}
