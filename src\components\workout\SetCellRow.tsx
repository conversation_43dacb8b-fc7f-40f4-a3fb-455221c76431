'use client'

import React from 'react'
import type { SetType } from '@/utils/setTypeUtils'
import { SetTypeBadge } from './SetTypeBadge'

interface SetCellRowProps {
  setNo: number | string
  reps: number
  isFinished: boolean
  isBodyweight: boolean
  setType: SetType
  onRepsChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  onWeightChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  onSetComplete?: () => void
  onSetTypeBadgeClick: () => void
  getFormattedWeight: () => string | number
}

export function SetCellRow({
  setNo,
  reps,
  isFinished,
  isBodyweight,
  setType,
  onRepsChange,
  onWeightChange,
  onSetComplete,
  onSetTypeBadgeClick,
  getFormattedWeight,
}: SetCellRowProps) {
  return (
    <div className="grid grid-cols-[25px_60px_1fr_25px_1fr] gap-0 items-center py-2">
      {/* Check/Delete Icon */}
      <div className="flex items-center justify-center">
        {isFinished ? (
          <svg
            className="w-5 h-5 text-success cursor-pointer"
            data-testid="check-icon"
            fill="currentColor"
            viewBox="0 0 20 20"
            onClick={onSetComplete}
          >
            <path
              fillRule="evenodd"
              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
              clipRule="evenodd"
            />
          </svg>
        ) : (
          <svg
            className="w-5 h-5 text-error cursor-pointer hover:text-error/80"
            data-testid="delete-icon"
            fill="currentColor"
            viewBox="0 0 20 20"
            onClick={onSetComplete}
          >
            <path
              fillRule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        )}
      </div>

      {/* Set Number and Type Badge */}
      <div className="flex flex-col items-center justify-center">
        <span
          className="text-lg font-medium text-text-secondary"
          data-testid="set-cell"
        >
          {setNo}
        </span>
        {setType && (
          <SetTypeBadge
            setType={setType}
            onClick={onSetTypeBadgeClick}
            variant="compact"
          />
        )}
      </div>

      {/* Reps Input */}
      <div className="px-2">
        <input
          type="number"
          value={reps || ''}
          onChange={onRepsChange}
          className="w-full text-center bg-bg-secondary border border-border-primary rounded-theme px-2 py-1 text-lg font-medium disabled:bg-bg-tertiary disabled:text-text-tertiary min-h-[52px]"
          aria-label={`Reps for set ${setNo}`}
          maxLength={4}
        />
      </div>

      {/* Multiplication Symbol */}
      <div className="text-center text-lg text-text-secondary">*</div>

      {/* Weight Input */}
      <div className="px-2">
        <input
          type="number"
          value={getFormattedWeight()}
          onChange={onWeightChange}
          disabled={isBodyweight}
          className="w-full text-center bg-bg-secondary border border-border-primary rounded-theme px-2 py-1 text-lg font-medium disabled:bg-bg-tertiary disabled:text-text-tertiary min-h-[52px]"
          aria-label={`Weight for set ${setNo}`}
        />
      </div>
    </div>
  )
}
