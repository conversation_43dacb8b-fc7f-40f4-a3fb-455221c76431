import { describe, it, expect } from 'vitest'
import { getProgramDescription } from '../programHelpers'

describe('getProgramDescription', () => {
  it('should return empty string for any program name', () => {
    // Test that would fail with current implementation
    expect(getProgramDescription('Full Body Workout')).toBe('')
    expect(getProgramDescription('Beginner Program')).toBe('')
    expect(getProgramDescription('Intermediate Training')).toBe('')
    expect(getProgramDescription('Advanced Routine')).toBe('')
    expect(getProgramDescription('Strength Building')).toBe('')
    expect(getProgramDescription('Hypertrophy Focus')).toBe('')
  })

  it('should return empty string for mixed case program names', () => {
    expect(getProgramDescription('FULL BODY')).toBe('')
    expect(getProgramDescription('full body')).toBe('')
    expect(getProgramDescription('FuLl BoDy')).toBe('')
  })

  it('should return empty string for custom program names', () => {
    expect(getProgramDescription('My Custom Program')).toBe('')
    expect(getProgramDescription('5x5 Stronglifts')).toBe('')
    expect(getProgramDescription('PPL Split')).toBe('')
  })

  it('should return empty string for empty or null inputs', () => {
    expect(getProgramDescription('')).toBe('')
    // @ts-expect-error Testing null input
    expect(getProgramDescription(null)).toBe('')
    // @ts-expect-error Testing undefined input
    expect(getProgramDescription(undefined)).toBe('')
  })
})
