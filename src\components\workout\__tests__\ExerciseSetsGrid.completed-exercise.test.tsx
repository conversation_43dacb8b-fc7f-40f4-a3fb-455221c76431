import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { ExerciseSetsGrid } from '../ExerciseSetsGrid'
import type { ExerciseModel, RecommendationModel } from '@/types'
import type { WorkoutLogSerieModel } from '@/types/api/WorkoutLogSerieModel'

describe('ExerciseSetsGrid - Gold Theme Completion UI', () => {
  const mockExercise: ExerciseModel = {
    Id: 1,
    Label: 'Bench Press',
    Name: 'Bench Press',
    IsBodyweight: false,
    IsSystemExercise: true,
    IsSwapTarget: false,
    IsFinished: true,
    IsUnilateral: false,
    IsTimeBased: false,
    IsEasy: false,
    IsMedium: true,
  }

  const mockRecommendation: RecommendationModel = {
    ExerciseId: 1,
    Series: 3,
    Reps: 10,
    Weight: { Lb: 135, Kg: 61.2 },
    WeightIncrement: 5,
    RestSeconds: 120,
  }

  const completedSets: WorkoutLogSerieModel[] = [
    {
      Id: 1,
      Reps: 10,
      Weight: { Lb: 135, Kg: 61.2 },
      IsFinished: true,
      IsNext: false,
      IsWarmups: false,
      SetNo: '1',
    },
    {
      Id: 2,
      Reps: 10,
      Weight: { Lb: 135, Kg: 61.2 },
      IsFinished: true,
      IsNext: false,
      IsWarmups: false,
      SetNo: '2',
    },
    {
      Id: 3,
      Reps: 10,
      Weight: { Lb: 135, Kg: 61.2 },
      IsFinished: true,
      IsNext: false,
      IsWarmups: false,
      SetNo: '3',
    },
  ]

  it('should show completion message with gold theme colors instead of green', () => {
    render(
      <ExerciseSetsGrid
        exercise={mockExercise}
        recommendation={mockRecommendation}
        sets={completedSets}
        onSetUpdate={vi.fn()}
        onFinishExercise={vi.fn()}
        unit="lbs"
      />
    )

    // Should show the completion message
    expect(screen.getByText('All sets done—congrats!')).toBeInTheDocument()

    // Should have gold/brand primary styling, not green
    const successMessage = screen.getByText('All sets done—congrats!')
    expect(successMessage).toHaveClass('text-brand-primary')
    expect(successMessage).not.toHaveClass('text-success')

    // Background should be brand-primary/10, not success/10
    const messageContainer = successMessage.closest('div')
    expect(messageContainer).toHaveClass('bg-brand-primary/10')

    // Finish button should use gold gradient
    const finishButton = screen.getByText('Finish exercise')
    expect(finishButton).toHaveClass('bg-gradient-metallic-gold')
    expect(finishButton).not.toHaveClass('bg-success')
  })

  it('should not show completion message for partially completed exercises', () => {
    const partiallyCompletedSets: WorkoutLogSerieModel[] = [
      { ...completedSets[0], IsFinished: true },
      { ...completedSets[1], IsFinished: false, IsNext: true },
      { ...completedSets[2], IsFinished: false },
    ]

    render(
      <ExerciseSetsGrid
        exercise={mockExercise}
        recommendation={mockRecommendation}
        sets={partiallyCompletedSets}
        onSetUpdate={vi.fn()}
        unit="lbs"
      />
    )

    // Should not show success message when not all sets are complete
    expect(
      screen.queryByText('All sets done—congrats!')
    ).not.toBeInTheDocument()
  })

  it('should handle exercise with no sets gracefully', () => {
    render(
      <ExerciseSetsGrid
        exercise={mockExercise}
        recommendation={mockRecommendation}
        sets={[]}
        onSetUpdate={vi.fn()}
        unit="lbs"
      />
    )

    expect(screen.getByText('No sets for this exercise')).toBeInTheDocument()
    expect(
      screen.queryByText('All sets done—congrats!')
    ).not.toBeInTheDocument()
  })
})
