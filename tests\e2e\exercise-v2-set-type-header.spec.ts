import { test, expect } from '@playwright/test'
import { mockRecommendationResponse } from './helpers/recommendation-mocks'

test.describe('Exercise V2 Set Type Header Display', () => {
  test.beforeEach(async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 812 })

    // Mock user info API
    await page.route('**/api/userInfo', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Email: '<EMAIL>',
          FirstName: 'Test',
          MassUnit: 'kg',
        }),
      })
    })

    // Mock workout API
    await page.route('**/api/workout/current', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          WorkoutTemplateModel: {
            Id: 1,
            Label: 'Test Workout',
            Exercises: [
              {
                Id: 1,
                Label: 'Bench Press',
                IsFinished: false,
              },
            ],
          },
          Date: new Date().toISOString(),
        }),
      })
    })

    // Navigate to login and set auth
    await page.goto('/login')
    await page.evaluate(() => {
      localStorage.setItem('auth-token', 'mock-token')
      localStorage.setItem('refresh-token', 'mock-refresh')
    })
  })

  test('should display set type tag in header next to set counter in gold', async ({
    page,
  }) => {
    // Mock recommendation with Rest-pause sets
    await page.route('**/GetRecommendation*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          ...mockRecommendationResponse,
          IsNormalSets: false,
          NbPauses: 3, // This makes it a Rest-pause set
        }),
      })
    })

    await page.route('**/api/exercise/*/recommendation', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          ...mockRecommendationResponse,
          IsNormalSets: false,
          NbPauses: 3,
        }),
      })
    })

    // Navigate to exercise page
    await page.goto('/workout')
    await page.getByRole('button', { name: 'Start workout' }).click()

    // Navigate to V2 exercise page
    await page.goto('/workout/exercise-v2/1')

    // Wait for header to render
    await page.waitForSelector('[data-testid="exercise-info-header"]', {
      timeout: 10000,
    })

    // Check that set type tag is displayed
    const setTypeTag = page.locator('text=Rest-pause')
    await expect(setTypeTag).toBeVisible()

    // Check that it's in gold color
    await expect(setTypeTag).toHaveClass(/text-brand-gold/)

    // Check that all elements are on the same row
    const header = page.getByTestId('exercise-info-header')
    const flexContainer = header.locator('.flex.items-center.gap-3')
    await expect(flexContainer).toBeVisible()

    // Verify all three elements are within the flex container
    await expect(flexContainer.locator('text=/Set \\d+ of \\d+/')).toBeVisible()
    await expect(flexContainer.locator('text=Rest-pause')).toBeVisible()
    await expect(
      flexContainer.getByTestId('exercise-progress-bar')
    ).toBeVisible()
  })

  test.describe('different set types', () => {
    test('should display Drop set correctly', async ({ page }) => {
      // Mock recommendation with Drop set
      await page.route('**/GetRecommendation*', async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            ...mockRecommendationResponse,
            IsDropSet: true,
          }),
        })
      })

      await page.route('**/api/exercise/*/recommendation', async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            ...mockRecommendationResponse,
            IsDropSet: true,
          }),
        })
      })

      // Navigate to exercise page
      await page.goto('/workout/exercise-v2/1')

      // Wait for header to render
      await page.waitForSelector('[data-testid="exercise-info-header"]', {
        timeout: 10000,
      })

      // Check that correct set type is displayed
      const setTypeTag = page.locator('text=Drop set')
      await expect(setTypeTag).toBeVisible()
      await expect(setTypeTag).toHaveClass(/text-brand-gold/)
    })

    test('should display Pyramid correctly', async ({ page }) => {
      // Mock recommendation with Pyramid
      await page.route('**/GetRecommendation*', async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            ...mockRecommendationResponse,
            IsPyramid: true,
          }),
        })
      })

      await page.route('**/api/exercise/*/recommendation', async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            ...mockRecommendationResponse,
            IsPyramid: true,
          }),
        })
      })

      // Navigate to exercise page
      await page.goto('/workout/exercise-v2/1')

      // Wait for header to render
      await page.waitForSelector('[data-testid="exercise-info-header"]', {
        timeout: 10000,
      })

      // Check that correct set type is displayed
      const setTypeTag = page.locator('text=Pyramid')
      await expect(setTypeTag).toBeVisible()
      await expect(setTypeTag).toHaveClass(/text-brand-gold/)
    })

    test('should display Back-off correctly', async ({ page }) => {
      // Mock recommendation with Back-off
      await page.route('**/GetRecommendation*', async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            ...mockRecommendationResponse,
            IsBackOffSet: true,
          }),
        })
      })

      await page.route('**/api/exercise/*/recommendation', async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            ...mockRecommendationResponse,
            IsBackOffSet: true,
          }),
        })
      })

      // Navigate to exercise page
      await page.goto('/workout/exercise-v2/1')

      // Wait for header to render
      await page.waitForSelector('[data-testid="exercise-info-header"]', {
        timeout: 10000,
      })

      // Check that correct set type is displayed
      const setTypeTag = page.locator('text=Back-off')
      await expect(setTypeTag).toBeVisible()
      await expect(setTypeTag).toHaveClass(/text-brand-gold/)
    })

    test('should display Reverse pyramid correctly', async ({ page }) => {
      // Mock recommendation with Reverse pyramid
      await page.route('**/GetRecommendation*', async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            ...mockRecommendationResponse,
            IsReversePyramid: true,
          }),
        })
      })

      await page.route('**/api/exercise/*/recommendation', async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            ...mockRecommendationResponse,
            IsReversePyramid: true,
          }),
        })
      })

      // Navigate to exercise page
      await page.goto('/workout/exercise-v2/1')

      // Wait for header to render
      await page.waitForSelector('[data-testid="exercise-info-header"]', {
        timeout: 10000,
      })

      // Check that correct set type is displayed
      const setTypeTag = page.locator('text=Reverse pyramid')
      await expect(setTypeTag).toBeVisible()
      await expect(setTypeTag).toHaveClass(/text-brand-gold/)
    })
  })

  test('should display Normal for normal sets', async ({ page }) => {
    // Mock recommendation with normal sets
    await page.route('**/GetRecommendation*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          ...mockRecommendationResponse,
          IsNormalSets: true,
        }),
      })
    })

    await page.route('**/api/exercise/*/recommendation', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          ...mockRecommendationResponse,
          IsNormalSets: true,
        }),
      })
    })

    // Navigate to exercise page
    await page.goto('/workout/exercise-v2/1')

    // Wait for header to render
    await page.waitForSelector('[data-testid="exercise-info-header"]', {
      timeout: 10000,
    })

    // Check that "Normal" is displayed for normal sets
    const normalTag = page.locator('text=Normal')
    await expect(normalTag).toBeVisible()
    await expect(normalTag).toHaveClass(/text-brand-gold/)
  })

  test('should maintain proper layout on mobile viewport', async ({ page }) => {
    // Mock recommendation with long set type name
    await page.route('**/GetRecommendation*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          ...mockRecommendationResponse,
          IsReversePyramid: true,
        }),
      })
    })

    await page.route('**/api/exercise/*/recommendation', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          ...mockRecommendationResponse,
          IsReversePyramid: true,
        }),
      })
    })

    // Navigate to exercise page
    await page.goto('/workout/exercise-v2/1')

    // Wait for header to render
    await page.waitForSelector('[data-testid="exercise-info-header"]', {
      timeout: 10000,
    })

    // Check that layout doesn't break on mobile
    const header = page.getByTestId('exercise-info-header')
    const flexContainer = header.locator('.flex.items-center.gap-3')

    // Ensure all elements are visible without overflow
    const setCounter = flexContainer.locator('text=/Set \\d+ of \\d+/')
    const setType = flexContainer.locator('text=Reverse pyramid')
    const progressBar = flexContainer.getByTestId('exercise-progress-bar')

    await expect(setCounter).toBeVisible()
    await expect(setType).toBeVisible()
    await expect(progressBar).toBeVisible()
  })
})
