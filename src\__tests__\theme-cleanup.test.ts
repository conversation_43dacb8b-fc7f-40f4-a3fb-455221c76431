import { describe, it, expect } from 'vitest'
import fs from 'fs'
import path from 'path'

describe('Theme Cleanup Tests', () => {
  // Test that ensures no glassmorphism classes remain in globals.css
  it('should not contain glassmorphism CSS utility classes in globals.css', () => {
    const globalsPath = path.join(process.cwd(), 'src/styles/globals.css')
    const content = fs.readFileSync(globalsPath, 'utf-8')

    // Check for glass utility classes
    expect(content).not.toContain('.glass-primary')
    expect(content).not.toContain('.glass-secondary')
    expect(content).not.toContain('.glass-tertiary')
    expect(content).not.toContain('.glass-card')
    expect(content).not.toContain('.glass-button')
    expect(content).not.toContain('.glass-gradient-overlay')
    expect(content).not.toContain('Glassmorphism Utility Classes')
  })

  // Test that backdrop-blur is only used in allowed components
  it('should only use backdrop-blur in exercise V2 modal components', () => {
    const allowedComponents = [
      'src/components/workout-v2/RestTimerDisplay.tsx',
      'src/components/workout-v2/DurationPicker.tsx',
      'src/components/workout-v2/CustomDurationModal.tsx',
      'src/components/workout/SetTypeExplainerModal.tsx',
    ]

    const disallowedComponents = [
      'src/components/navigation/IOSNavigationBar.tsx',
    ]

    // Check that allowed components can have backdrop-blur
    allowedComponents.forEach((componentPath) => {
      const fullPath = path.join(process.cwd(), componentPath)
      if (fs.existsSync(fullPath)) {
        // Just verify file exists, backdrop-blur is allowed here
        expect(fs.existsSync(fullPath)).toBe(true)
      }
    })

    // Check that disallowed components don't have backdrop-blur
    disallowedComponents.forEach((componentPath) => {
      const fullPath = path.join(process.cwd(), componentPath)
      if (fs.existsSync(fullPath)) {
        const content = fs.readFileSync(fullPath, 'utf-8')
        expect(content).not.toContain('backdrop-blur')
      }
    })
  })

  // Test that Button component doesn't have glass variants
  it('should not have glass button variants', () => {
    const buttonPath = path.join(
      process.cwd(),
      'src/design-system/components/Button/Button.tsx'
    )
    if (fs.existsSync(buttonPath)) {
      const content = fs.readFileSync(buttonPath, 'utf-8')
      expect(content).not.toContain('glass-primary')
      expect(content).not.toContain('glass-secondary')
      expect(content).not.toContain('glass-accent')
    }
  })

  // Test that tailwind config has backdrop-blur utilities but not glass components
  it('should have backdrop-blur utilities but not glass components in tailwind config', () => {
    const tailwindPath = path.join(process.cwd(), 'tailwind.config.js')
    const content = fs.readFileSync(tailwindPath, 'utf-8')

    // Should have backdrop-blur utilities
    expect(content).toContain('backdrop-blur')

    // Should not have glass component classes
    expect(content).not.toContain('.glass {')
    expect(content).not.toContain('.glass-dark {')
    expect(content).not.toContain('.glass-light {')
  })

  // Test that only subtle-depth theme exists
  it('should only have subtle-depth theme defined', () => {
    const themeConfigPath = path.join(process.cwd(), 'src/config/theme.ts')
    const content = fs.readFileSync(themeConfigPath, 'utf-8')

    // Should have subtle-depth
    expect(content).toContain('subtle-depth')

    // Should not have other themes
    expect(content).not.toContain('glassmorphism')
    expect(content).not.toContain('flat-bold')
    expect(content).not.toContain('minimal-dark')
    expect(content).not.toContain('neon-glow')
  })

  // Test that ThemeVariant type only includes subtle-depth
  it('should have ThemeVariant type with only subtle-depth', () => {
    const typesPath = path.join(process.cwd(), 'src/design-system/types.ts')
    const content = fs.readFileSync(typesPath, 'utf-8')

    expect(content).toContain("export type ThemeVariant = 'subtle-depth'")
    expect(content).not.toContain('glassmorphism')
    expect(content).not.toContain('flat-bold')
  })
})
