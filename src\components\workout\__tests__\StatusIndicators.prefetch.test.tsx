import { render, screen } from '@testing-library/react'
import { StatusIndicators } from '../WorkoutOverviewStates'

describe('StatusIndicators - Prefetch Status', () => {
  it('should show prefetch loading indicator when prefetching', () => {
    render(
      <StatusIndicators
        isOffline={false}
        isRefreshing={false}
        prefetchStatus={{ 1: 'loading', 2: 'loading' }}
        isPrefetching
      />
    )

    expect(screen.getByText('Loading exercises...')).toBeInTheDocument()
  })

  it('should show prefetch success indicator when exercises are prefetched', () => {
    render(
      <StatusIndicators
        isOffline={false}
        isRefreshing={false}
        prefetchStatus={{ 1: 'success', 2: 'success' }}
        isPrefetching={false}
        prefetchedCount={2}
      />
    )

    expect(screen.getByText('2 exercises ready')).toBeInTheDocument()
  })

  it('should show prefetch error indicator when prefetch fails', () => {
    render(
      <StatusIndicators
        isOffline={false}
        isRefreshing={false}
        prefetchStatus={{ 1: 'error', 2: 'success' }}
        isPrefetching={false}
        prefetchedCount={1}
        errorCount={1}
      />
    )

    expect(screen.getByText('1 exercise ready, 1 failed')).toBeInTheDocument()
  })

  it('should show progress indicator with percentage when available', () => {
    render(
      <StatusIndicators
        isOffline={false}
        isRefreshing={false}
        prefetchStatus={{ 1: 'success', 2: 'loading', 3: 'loading' }}
        isPrefetching
        prefetchProgress={33}
      />
    )

    expect(screen.getByText('Loading exercises... 33%')).toBeInTheDocument()
  })

  it('should prioritize offline status over prefetch status', () => {
    render(
      <StatusIndicators
        isOffline
        isRefreshing={false}
        prefetchStatus={{ 1: 'success' }}
        isPrefetching={false}
        prefetchedCount={1}
      />
    )

    expect(screen.getByText('Offline Mode')).toBeInTheDocument()
    expect(screen.getByText(/1 exercise ready/)).toBeInTheDocument()
  })

  it('should prioritize refresh status over prefetch status', () => {
    render(
      <StatusIndicators
        isOffline={false}
        isRefreshing
        prefetchStatus={{ 1: 'success' }}
        isPrefetching={false}
        prefetchedCount={1}
      />
    )

    expect(screen.getByText('Refreshing workout...')).toBeInTheDocument()
    expect(screen.queryByText('1 exercises ready')).not.toBeInTheDocument()
  })

  it('should handle empty prefetch status gracefully', () => {
    render(
      <StatusIndicators
        isOffline={false}
        isRefreshing={false}
        prefetchStatus={{}}
        isPrefetching={false}
      />
    )

    // Now that StatusIndicators is always visible, check for the container
    expect(
      screen.getByTestId('status-indicators-container')
    ).toBeInTheDocument()
    // But no badges should be shown
    expect(screen.queryByRole('status')).not.toBeInTheDocument()
  })

  it('should show intelligent scheduling indicator', () => {
    render(
      <StatusIndicators
        isOffline={false}
        isRefreshing={false}
        prefetchStatus={{ 1: 'success' }}
        isPrefetching={false}
        prefetchedCount={1}
        isSmartScheduling
      />
    )

    expect(screen.getByText(/1 exercise ready/)).toBeInTheDocument()
    expect(
      screen.getByLabelText('Smart prefetching active')
    ).toBeInTheDocument()
  })

  it('should show retry indicator when prefetch has retries', () => {
    render(
      <StatusIndicators
        isOffline={false}
        isRefreshing={false}
        prefetchStatus={{ 1: 'error' }}
        isPrefetching={false}
        errorCount={1}
        retryCount={2}
      />
    )

    expect(screen.getByText('1 failed (retrying...)')).toBeInTheDocument()
  })

  describe('Always Visible Behavior', () => {
    it('should remain visible even when no status flags are active', () => {
      // Test rationale: StatusIndicators should always be visible as a temporary
      // debugging/monitoring solution, even when there's no active status
      render(
        <StatusIndicators
          isOffline={false}
          isRefreshing={false}
          prefetchStatus={{}}
          isPrefetching={false}
        />
      )

      // The component should render an empty container with test id
      expect(
        screen.getByTestId('status-indicators-container')
      ).toBeInTheDocument()
    })

    it('should show empty container when no badges to display', () => {
      // Test rationale: When no status is active, the component should still
      // occupy space in the layout to prevent layout shifts
      render(
        <StatusIndicators
          isOffline={false}
          isRefreshing={false}
          prefetchStatus={{}}
          isPrefetching={false}
          prefetchedCount={0}
          errorCount={0}
        />
      )

      const container = screen.getByTestId('status-indicators-container')
      expect(container).toBeInTheDocument()
      expect(container).toHaveClass('mb-4', 'text-center', 'space-y-2')
      // Should have no badges inside
      expect(container.children.length).toBe(0)
    })

    it('should transition from empty to showing badges without unmounting', () => {
      // Test rationale: Component should stay mounted during state transitions
      // to avoid flicker and maintain stable layout
      const { rerender } = render(
        <StatusIndicators
          isOffline={false}
          isRefreshing={false}
          prefetchStatus={{}}
          isPrefetching={false}
        />
      )

      const container = screen.getByTestId('status-indicators-container')
      expect(container).toBeInTheDocument()
      expect(container.children.length).toBe(0)

      // Update to show prefetch status
      rerender(
        <StatusIndicators
          isOffline={false}
          isRefreshing={false}
          prefetchStatus={{ 1: 'loading' }}
          isPrefetching
        />
      )

      // Container should still be the same element
      expect(container).toBeInTheDocument()
      expect(screen.getByText('Loading exercises...')).toBeInTheDocument()
    })
  })
})
