import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { LoadOfflineWorkout } from '../LoadOfflineWorkout'
import { useNetworkStatus } from '@/hooks/useNetworkStatus'
import { useOfflineStore } from '@/stores/offlineStore'
import { WorkoutCacheService } from '@/services/workoutCacheService'

// Mock hooks
vi.mock('@/hooks/useNetworkStatus')
vi.mock('@/stores/offlineStore')
vi.mock('@/services/workoutCacheService')

// Mock toast system
vi.mock('@/components/ui/toast', () => ({
  toast: vi.fn(),
}))

describe('LoadOfflineWorkout', () => {
  let mockUseNetworkStatus: any
  let mockUseOfflineStore: any
  let mockWorkoutCacheService: any
  let mockToast: any

  beforeEach(async () => {
    vi.clearAllMocks()

    // Get the mocked toast function
    const { toast } = await import('@/components/ui/toast')
    mockToast = toast

    // Mock network status
    mockUseNetworkStatus = {
      isOnline: true,
      isOffline: false,
    }
    vi.mocked(useNetworkStatus).mockReturnValue(mockUseNetworkStatus)

    // Mock offline store
    mockUseOfflineStore = {
      isOfflineWorkoutLoaded: false,
      loadedWorkoutId: null,
      isLoadingOfflineWorkout: false,
      setLoadingOfflineWorkout: vi.fn(),
      setOfflineWorkoutLoaded: vi.fn(),
      clearOfflineWorkout: vi.fn(),
    }
    vi.mocked(useOfflineStore).mockReturnValue(mockUseOfflineStore)

    // Mock workout cache service
    mockWorkoutCacheService = {
      preloadWorkoutData: vi.fn(),
    }
    vi.mocked(WorkoutCacheService).mockImplementation(
      () => mockWorkoutCacheService
    )
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Visibility', () => {
    it('should show when online and no workout loaded', () => {
      render(<LoadOfflineWorkout />)

      expect(screen.getByText('Load Offline Workout')).toBeInTheDocument()
      expect(screen.getByRole('button')).toBeEnabled()
    })

    it('should show "Offline Workout (Loaded)" when workout is cached', () => {
      mockUseOfflineStore.isOfflineWorkoutLoaded = true
      mockUseOfflineStore.loadedWorkoutId = 'workout-123'
      vi.mocked(useOfflineStore).mockReturnValue(mockUseOfflineStore)

      render(<LoadOfflineWorkout />)

      expect(screen.getByText('Offline Workout (Loaded)')).toBeInTheDocument()
    })

    it('should be disabled when offline', () => {
      mockUseNetworkStatus.isOnline = false
      mockUseNetworkStatus.isOffline = true
      vi.mocked(useNetworkStatus).mockReturnValue(mockUseNetworkStatus)

      render(<LoadOfflineWorkout />)

      expect(screen.getByRole('button')).toBeDisabled()
      expect(screen.getByText(/offline/i)).toBeInTheDocument()
    })

    it('should show loading state when loading workout', () => {
      mockUseOfflineStore.isLoadingOfflineWorkout = true
      vi.mocked(useOfflineStore).mockReturnValue(mockUseOfflineStore)

      render(<LoadOfflineWorkout />)

      expect(screen.getByText('Loading...')).toBeInTheDocument()
      expect(screen.getByRole('button')).toBeDisabled()
    })
  })

  describe('Workout Loading', () => {
    it('should load workout data when clicked', async () => {
      const user = userEvent.setup()
      mockWorkoutCacheService.preloadWorkoutData.mockResolvedValue({
        success: true,
        programInfo: { WorkoutTemplateId: 456 },
        workoutDetails: { Id: 456, Name: 'Test Workout' },
        recommendations: [],
        exerciseCount: 3,
        recommendationCount: 3,
      })

      render(<LoadOfflineWorkout />)

      const button = screen.getByRole('button')
      await user.click(button)

      expect(mockUseOfflineStore.setLoadingOfflineWorkout).toHaveBeenCalledWith(
        true
      )
      expect(mockWorkoutCacheService.preloadWorkoutData).toHaveBeenCalled()

      await waitFor(() => {
        expect(
          mockUseOfflineStore.setOfflineWorkoutLoaded
        ).toHaveBeenCalledWith('456')
        expect(
          mockUseOfflineStore.setLoadingOfflineWorkout
        ).toHaveBeenCalledWith(false)
      })
    })

    it('should show success toast when workout loads successfully', async () => {
      const user = userEvent.setup()
      mockWorkoutCacheService.preloadWorkoutData.mockResolvedValue({
        success: true,
        programInfo: { WorkoutTemplateId: 456 },
        workoutDetails: { Id: 456, Name: 'Test Workout' },
        recommendations: [],
        exerciseCount: 3,
        recommendationCount: 3,
      })

      render(<LoadOfflineWorkout />)

      const button = screen.getByRole('button')
      await user.click(button)

      await waitFor(() => {
        expect(mockToast).toHaveBeenCalledWith(
          expect.objectContaining({
            title: 'Workout Loaded',
            description: expect.stringContaining('cached for offline use'),
            variant: 'default',
          })
        )
      })
    })

    it('should handle loading errors gracefully', async () => {
      const user = userEvent.setup()
      mockWorkoutCacheService.preloadWorkoutData.mockResolvedValue({
        success: false,
        error: 'Network error',
      })

      render(<LoadOfflineWorkout />)

      const button = screen.getByRole('button')
      await user.click(button)

      await waitFor(() => {
        expect(mockToast).toHaveBeenCalledWith(
          expect.objectContaining({
            title: 'Loading Failed',
            description: expect.stringContaining('Failed to load workout'),
            variant: 'error',
          })
        )
        expect(
          mockUseOfflineStore.setLoadingOfflineWorkout
        ).toHaveBeenCalledWith(false)
      })
    })

    it('should handle API exceptions', async () => {
      const user = userEvent.setup()
      mockWorkoutCacheService.preloadWorkoutData.mockRejectedValue(
        new Error('API Error')
      )

      render(<LoadOfflineWorkout />)

      const button = screen.getByRole('button')
      await user.click(button)

      await waitFor(() => {
        expect(mockToast).toHaveBeenCalledWith(
          expect.objectContaining({
            title: 'Loading Failed',
            description: expect.stringContaining('Failed to load workout'),
            variant: 'error',
          })
        )
        expect(
          mockUseOfflineStore.setLoadingOfflineWorkout
        ).toHaveBeenCalledWith(false)
      })
    })
  })

  describe('Clear Cached Workout', () => {
    it('should allow clearing cached workout when loaded', async () => {
      const user = userEvent.setup()
      mockUseOfflineStore.isOfflineWorkoutLoaded = true
      mockUseOfflineStore.loadedWorkoutId = 'workout-123'
      vi.mocked(useOfflineStore).mockReturnValue(mockUseOfflineStore)

      render(<LoadOfflineWorkout />)

      // Should show clear option
      const clearButton = screen.getByText(/clear/i)
      await user.click(clearButton)

      expect(mockUseOfflineStore.clearOfflineWorkout).toHaveBeenCalled()
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      render(<LoadOfflineWorkout />)

      const button = screen.getByRole('button')
      expect(button).toHaveAttribute(
        'aria-label',
        expect.stringContaining('Load workout')
      )
    })

    it('should have proper ARIA labels when disabled', () => {
      mockUseNetworkStatus.isOnline = false
      mockUseNetworkStatus.isOffline = true
      vi.mocked(useNetworkStatus).mockReturnValue(mockUseNetworkStatus)

      render(<LoadOfflineWorkout />)

      const button = screen.getByRole('button')
      expect(button).toHaveAttribute(
        'aria-label',
        expect.stringContaining('offline')
      )
    })

    it('should be keyboard accessible', async () => {
      const user = userEvent.setup()
      mockWorkoutCacheService.preloadWorkoutData.mockResolvedValue({
        success: true,
        programInfo: { WorkoutTemplateId: 456 },
        workoutDetails: { Id: 456, Name: 'Test Workout' },
        recommendations: [],
      })

      render(<LoadOfflineWorkout />)

      const button = screen.getByRole('button')
      button.focus()

      expect(button).toHaveFocus()

      await user.keyboard('{Enter}')

      expect(mockWorkoutCacheService.preloadWorkoutData).toHaveBeenCalled()
    })
  })

  describe('Integration', () => {
    it('should integrate with existing menu structure', () => {
      render(
        <div role="menu">
          <LoadOfflineWorkout />
        </div>
      )

      const menuItem = screen.getByRole('button')
      expect(menuItem.closest('[role="menu"]')).toBeInTheDocument()
    })
  })
})
