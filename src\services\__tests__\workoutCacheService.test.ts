import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { WorkoutCacheService } from '../workoutCacheService'
import { createDefaultCacheManager } from '@/cache'
import type { WorkoutTemplateModel, RecommendationModel } from '@/types'

// Mock the cache manager
vi.mock('@/cache', () => ({
  createDefaultCacheManager: vi.fn(),
}))

// Mock workout API
vi.mock('@/api/workouts', () => ({
  workoutApi: {
    getUserProgramInfo: vi.fn(),
    getWorkoutDetails: vi.fn(),
    getExerciseRecommendation: vi.fn(),
  },
}))

describe('WorkoutCacheService', () => {
  let workoutCacheService: WorkoutCacheService
  let mockCacheManager: any
  let mockWorkoutApi: any

  beforeEach(async () => {
    vi.clearAllMocks()

    // Mock cache manager
    mockCacheManager = {
      set: vi.fn().mockResolvedValue(undefined),
      get: vi.fn().mockResolvedValue(null),
      has: vi.fn().mockResolvedValue(false),
      delete: vi.fn().mockResolvedValue(true),
      clear: vi.fn().mockResolvedValue(undefined),
      getStats: vi.fn().mockReturnValue({
        hits: 0,
        misses: 0,
        sets: 0,
        deletes: 0,
        size: 0,
        memoryUsage: 0,
      }),
    }

    vi.mocked(createDefaultCacheManager).mockReturnValue(mockCacheManager)

    // Get the mocked workout API
    const { workoutApi } = await import('@/api/workouts')
    mockWorkoutApi = workoutApi

    workoutCacheService = new WorkoutCacheService()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Initialization', () => {
    it('should initialize with cache manager', () => {
      expect(createDefaultCacheManager).toHaveBeenCalledTimes(1)
    })
  })

  describe('Program Info Caching', () => {
    const mockProgramInfo = {
      WorkoutTemplateGroupId: 123,
      WorkoutTemplateId: 456,
      NextWorkoutDate: '2025-08-06',
    }

    it('should cache program info with correct TTL', async () => {
      mockWorkoutApi.getUserProgramInfo.mockResolvedValue(mockProgramInfo)

      const result = await workoutCacheService.getCachedProgramInfo()

      expect(mockWorkoutApi.getUserProgramInfo).toHaveBeenCalledTimes(1)
      expect(mockCacheManager.set).toHaveBeenCalledWith(
        'program-info',
        mockProgramInfo,
        {
          namespace: 'offline-program-info',
          ttl: 24 * 60 * 60 * 1000, // 24 hours
        }
      )
      expect(result).toEqual(mockProgramInfo)
    })

    it('should return cached program info if available', async () => {
      mockCacheManager.get.mockResolvedValue(mockProgramInfo)

      const result = await workoutCacheService.getCachedProgramInfo()

      expect(mockWorkoutApi.getUserProgramInfo).not.toHaveBeenCalled()
      expect(mockCacheManager.get).toHaveBeenCalledWith(
        'program-info',
        'offline-program-info'
      )
      expect(result).toEqual(mockProgramInfo)
    })

    it('should force refresh program info when requested', async () => {
      mockCacheManager.get.mockResolvedValue(mockProgramInfo)
      mockWorkoutApi.getUserProgramInfo.mockResolvedValue(mockProgramInfo)

      const result = await workoutCacheService.getCachedProgramInfo(true)

      expect(mockWorkoutApi.getUserProgramInfo).toHaveBeenCalledTimes(1)
      expect(result).toEqual(mockProgramInfo)
    })
  })

  describe('Workout Details Caching', () => {
    const mockWorkoutDetails: WorkoutTemplateModel = {
      Id: 456,
      Name: 'Test Workout',
      Exercises: [
        {
          Id: 1,
          Name: 'Bench Press',
          ExerciseId: 1,
        },
        {
          Id: 2,
          Name: 'Squats',
          ExerciseId: 2,
        },
      ],
    } as WorkoutTemplateModel

    it('should cache workout details with correct TTL', async () => {
      mockWorkoutApi.getWorkoutDetails.mockResolvedValue(mockWorkoutDetails)

      const result = await workoutCacheService.getCachedWorkoutDetails('456')

      expect(mockWorkoutApi.getWorkoutDetails).toHaveBeenCalledWith('456')
      expect(mockCacheManager.set).toHaveBeenCalledWith(
        'workout-456',
        mockWorkoutDetails,
        {
          namespace: 'offline-workouts',
          ttl: 24 * 60 * 60 * 1000, // 24 hours
        }
      )
      expect(result).toEqual(mockWorkoutDetails)
    })

    it('should return cached workout details if available', async () => {
      mockCacheManager.get.mockResolvedValue(mockWorkoutDetails)

      const result = await workoutCacheService.getCachedWorkoutDetails('456')

      expect(mockWorkoutApi.getWorkoutDetails).not.toHaveBeenCalled()
      expect(mockCacheManager.get).toHaveBeenCalledWith(
        'workout-456',
        'offline-workouts'
      )
      expect(result).toEqual(mockWorkoutDetails)
    })
  })

  describe('Exercise Recommendations Caching', () => {
    const mockRecommendation: RecommendationModel = {
      Weight: { Lb: 135, Kg: 61.2 },
      Reps: 8,
      Series: 3,
      RIR: 2,
    } as RecommendationModel

    const mockRequest = {
      ExerciseId: 1,
      WorkoutId: 456,
      Username: '<EMAIL>',
    }

    it('should cache exercise recommendations with correct TTL', async () => {
      mockWorkoutApi.getExerciseRecommendation.mockResolvedValue(
        mockRecommendation
      )

      const result =
        await workoutCacheService.getCachedExerciseRecommendation(mockRequest)

      expect(mockWorkoutApi.getExerciseRecommendation).toHaveBeenCalledWith(
        mockRequest.ExerciseId
      )
      expect(mockCacheManager.set).toHaveBeenCalledWith(
        '<EMAIL>',
        mockRecommendation,
        {
          namespace: 'offline-recommendations',
          ttl: 2 * 60 * 60 * 1000, // 2 hours
        }
      )
      expect(result).toEqual(mockRecommendation)
    })

    it('should return cached recommendations if available', async () => {
      mockCacheManager.get.mockResolvedValue(mockRecommendation)

      const result =
        await workoutCacheService.getCachedExerciseRecommendation(mockRequest)

      expect(mockWorkoutApi.getExerciseRecommendation).not.toHaveBeenCalled()
      expect(mockCacheManager.get).toHaveBeenCalledWith(
        '<EMAIL>',
        'offline-recommendations'
      )
      expect(result).toEqual(mockRecommendation)
    })
  })

  describe('Batch Operations', () => {
    it('should preload complete workout data', async () => {
      const mockProgramInfo = {
        GetUserProgramInfoResponseModel: {
          NextWorkoutTemplate: { Id: 456 },
        },
      }
      const mockWorkoutDetails = {
        Id: 456,
        Exercises: [{ Id: 1 }, { Id: 2 }],
      }
      const mockRecommendation = { Weight: { Lb: 135 }, Reps: 8 }

      mockWorkoutApi.getUserProgramInfo.mockResolvedValue(mockProgramInfo)
      mockWorkoutApi.getWorkoutDetails.mockResolvedValue(mockWorkoutDetails)
      mockWorkoutApi.getExerciseRecommendation.mockResolvedValue(
        mockRecommendation
      )

      const result =
        await workoutCacheService.preloadWorkoutData('<EMAIL>')

      expect(result.success).toBe(true)
      expect(result.programInfo).toEqual(mockProgramInfo)
      expect(result.workoutDetails).toEqual(mockWorkoutDetails)
      expect(result.recommendations).toHaveLength(2)
    })

    it('should handle errors gracefully during preload', async () => {
      mockWorkoutApi.getUserProgramInfo.mockRejectedValue(
        new Error('API Error')
      )

      const result =
        await workoutCacheService.preloadWorkoutData('<EMAIL>')

      expect(result.success).toBe(false)
      expect(result.error).toBe('API Error')
    })
  })

  describe('Cache Management', () => {
    it('should clear all cached data', async () => {
      await workoutCacheService.clearAllCache()

      expect(mockCacheManager.clear).toHaveBeenCalledWith('offline-workouts')
      expect(mockCacheManager.clear).toHaveBeenCalledWith(
        'offline-recommendations'
      )
    })

    it('should get cache statistics', () => {
      const stats = workoutCacheService.getCacheStats()

      expect(mockCacheManager.getStats).toHaveBeenCalled()
      expect(stats).toEqual({
        hits: 0,
        misses: 0,
        sets: 0,
        deletes: 0,
        size: 0,
        memoryUsage: 0,
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle cache errors gracefully', async () => {
      mockCacheManager.get.mockRejectedValue(new Error('cache error'))
      mockWorkoutApi.getUserProgramInfo.mockResolvedValue({
        WorkoutTemplateId: 456,
      })

      // Should fall back to API call
      const result = await workoutCacheService.getCachedProgramInfo()

      expect(mockWorkoutApi.getUserProgramInfo).toHaveBeenCalledTimes(1)
      expect(result).toEqual({ WorkoutTemplateId: 456 })
    })

    it('should handle API errors gracefully', async () => {
      mockCacheManager.get.mockResolvedValue(null)
      mockWorkoutApi.getUserProgramInfo.mockRejectedValue(
        new Error('API error')
      )

      await expect(workoutCacheService.getCachedProgramInfo()).rejects.toThrow(
        'API error'
      )
    })
  })
})
