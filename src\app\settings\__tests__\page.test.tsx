import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import SettingsPage from '../page'

// Mock the AuthGuard component
vi.mock('@/components/AuthGuard', () => ({
  AuthGuard: ({ children }: { children: React.ReactNode }) => {
    // For testing, always consider user authenticated
    return children
  },
}))

// Mock the client component
vi.mock('../SettingsPageClient', () => ({
  default: () => <div data-testid="settings-client">Settings Page Client</div>,
}))

describe('SettingsPage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders the settings page with AuthGuard', async () => {
    // Test that the page component properly wraps content with AuthGuard
    render(await SettingsPage())

    // Should render the client component when authenticated
    expect(screen.getByTestId('settings-client')).toBeInTheDocument()
  })

  it('uses AuthGuard for authentication', async () => {
    // Test that AuthGuard is used (mocked above)
    render(await SettingsPage())

    // Should render the client component through AuthGuard
    expect(screen.getByTestId('settings-client')).toBeInTheDocument()
  })

  it('renders as a server component with proper metadata', async () => {
    // Test that the page exports proper metadata
    const pageModule = await import('../page')

    expect(pageModule.metadata).toBeDefined()
    expect(pageModule.metadata?.title).toContain('Settings')
  })
})
