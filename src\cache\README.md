# Unified Cache Management System

A comprehensive, type-safe cache management system for the Dr. Muscle X PWA application. This system provides a unified interface for caching data across multiple storage mechanisms with features like TTL, namespacing, compression, and automatic cleanup.

## Features

- **Multiple Storage Adapters**: Memory, localStorage, sessionStorage, and IndexedDB support
- **TTL (Time To Live)**: Automatic expiration of cached entries
- **Namespacing**: Isolate cache entries by namespace for better organization
- **Compression**: Automatic compression for large values to save space
- **Statistics**: Track cache hits, misses, and performance metrics
- **Quota Management**: Automatic cleanup when storage quota is exceeded
- **Type Safety**: Full TypeScript support with comprehensive type definitions
- **Batch Operations**: Efficient batch get/set/delete operations where supported
- **Error Handling**: Comprehensive error handling with specific error types

## Quick Start

```typescript
import { createDefaultCacheManager } from '@/cache'

// Create a cache manager with default settings
const cache = createDefaultCacheManager()

// Store data
await cache.set('user:123', { name: '<PERSON>', email: '<EMAIL>' })

// Retrieve data
const user = await cache.get('user:123')

// Store with TTL (expires in 1 hour)
await cache.set('session:abc', sessionData, { ttl: 60 * 60 * 1000 })

// Store in a specific namespace
await cache.set('profile', userData, { namespace: 'user:123' })

// Clear all data in a namespace
await cache.clear('user:123')
```

## Architecture

### Core Components

1. **CacheManager**: High-level interface for cache operations
2. **CacheAdapter**: Low-level storage interface implemented by adapters
3. **Storage Adapters**: Concrete implementations for different storage mechanisms
4. **Types**: Comprehensive type definitions for type safety

### Storage Adapters

- **MemoryAdapter**: In-memory storage with LRU eviction
- **LocalStorageAdapter**: Persistent browser storage with quota handling
- **SessionStorageAdapter**: Session-scoped storage
- **BaseStorageAdapter**: Abstract base class for browser storage adapters

## Usage Examples

### Basic Operations

```typescript
import { createCacheManager } from '@/cache'

const cache = createCacheManager({
  adapter: 'localStorage',
  config: {
    defaultNamespace: 'myapp',
    defaultTTL: 24 * 60 * 60 * 1000, // 24 hours
  },
})

// Store different types of data
await cache.set('string-key', 'Hello World')
await cache.set('object-key', { id: 1, name: 'Item' })
await cache.set('array-key', [1, 2, 3, 4, 5])

// Retrieve data
const stringValue = await cache.get<string>('string-key')
const objectValue = await cache.get<{ id: number; name: string }>('object-key')
```

### Namespacing

```typescript
// Store user-specific data
await cache.set('profile', userProfile, { namespace: 'user:123' })
await cache.set('settings', userSettings, { namespace: 'user:123' })

// Store app-wide data
await cache.set('config', appConfig, { namespace: 'app' })

// Clear only user data
await cache.clear('user:123')

// Get all keys in a namespace
const userKeys = await cache.getAllKeys('user:123')
```

### TTL and Expiration

```typescript
// Store with different TTL values
await cache.set('short-lived', data, { ttl: 5 * 60 * 1000 }) // 5 minutes
await cache.set('medium-lived', data, { ttl: 60 * 60 * 1000 }) // 1 hour
await cache.set('long-lived', data, { ttl: 24 * 60 * 60 * 1000 }) // 24 hours
await cache.set('permanent', data, { ttl: 0 }) // Never expires

// Check metadata
const metadata = await cache.getMetadata('short-lived')
console.log('Expires at:', new Date(metadata.expires))
```

### Batch Operations

```typescript
// Batch set
const entries = new Map([
  ['key1', 'value1'],
  ['key2', 'value2'],
  ['key3', 'value3'],
])
await cache.setMany(entries, { namespace: 'batch-test' })

// Batch get
const keys = ['key1', 'key2', 'key3']
const results = await cache.getMany(keys, 'batch-test')

// Batch delete
await cache.deleteMany(['key1', 'key2'], 'batch-test')
```

### Pattern-based Invalidation

```typescript
// Store related data
await cache.set('user:123:profile', userProfile)
await cache.set('user:123:settings', userSettings)
await cache.set('user:456:profile', otherProfile)

// Invalidate all data for user 123
await cache.invalidate('user:123:', 'default')

// Use regex patterns
await cache.invalidate(/^user:\d+:profile$/, 'default')
```

### Statistics and Monitoring

```typescript
// Get cache statistics
const stats = await cache.getStats()
console.log('Hit ratio:', stats.hitRatio)
console.log('Total entries:', stats.entryCount)
console.log('Cache size:', stats.totalSize)

// Namespace-specific stats
console.log('User namespace hits:', stats.namespaces['user:123']?.hits)
```

## Configuration

### Cache Manager Configuration

```typescript
interface CacheManagerConfig {
  defaultTTL: number // Default TTL in milliseconds
  maxSize: number // Maximum cache size in bytes
  maxEntriesPerNamespace: number // Maximum entries per namespace
  cleanupInterval: number // Cleanup interval in milliseconds
  defaultNamespace: string // Default namespace for operations
  enableCompression: boolean // Enable compression for large values
  compressionThreshold: number // Compression threshold in bytes
  enableStats: boolean // Enable statistics tracking
}
```

### Adapter-Specific Configuration

```typescript
// Memory Adapter
const memoryCache = createCacheManager({
  adapter: 'memory',
  adapterConfig: {
    maxEntries: 1000,
    enableAutoCleanup: true,
    cleanupInterval: 5 * 60 * 1000,
  },
})

// LocalStorage Adapter
const localCache = createCacheManager({
  adapter: 'localStorage',
  adapterConfig: {
    keyPrefix: 'myapp-cache:',
    enableCompression: true,
    compressionThreshold: 1024,
    maxEntries: 1000,
    enableAutoCleanup: true,
  },
})
```

## Factory Functions

The cache system provides several factory functions for common use cases:

```typescript
// Default cache manager (localStorage with fallback to memory)
const defaultCache = createDefaultCacheManager()

// Session-scoped cache
const sessionCache = createSessionCacheManager()

// Memory-only cache
const memoryCache = createMemoryCacheManager()

// Global singleton cache
const globalCache = getGlobalCacheManager()
```

## Error Handling

The cache system provides specific error types for different failure scenarios:

```typescript
import {
  CacheError,
  CacheQuotaExceededError,
  CacheSerializationError,
  CacheKeyError,
} from '@/cache'

try {
  await cache.set('key', value)
} catch (error) {
  if (error instanceof CacheQuotaExceededError) {
    console.log('Storage quota exceeded')
  } else if (error instanceof CacheSerializationError) {
    console.log('Failed to serialize value')
  } else if (error instanceof CacheKeyError) {
    console.log('Invalid cache key')
  }
}
```

## Testing

The cache system includes comprehensive test utilities:

```typescript
import {
  MockCacheAdapter,
  TestDataGenerators,
} from '@/cache/__tests__/test-utils'

// Create mock adapter for testing
const mockAdapter = new MockCacheAdapter()
const testManager = new CacheManager(mockAdapter)

// Generate test data
const testData = TestDataGenerators.createTestData()
const metadata = TestDataGenerators.createMetadata()
```

## Performance Considerations

- **Memory Usage**: The system tracks memory usage and provides cleanup mechanisms
- **Batch Operations**: Use batch operations for better performance when dealing with multiple entries
- **Compression**: Enable compression for large values to save storage space
- **TTL**: Use appropriate TTL values to prevent memory leaks
- **Namespacing**: Use namespaces to organize data and enable efficient cleanup

## Browser Compatibility

- **localStorage**: All modern browsers
- **sessionStorage**: All modern browsers
- **Memory**: Universal support
- **IndexedDB**: Modern browsers (future enhancement)

## Migration Guide

If you're migrating from existing cache solutions:

1. Replace direct localStorage/sessionStorage calls with cache manager operations
2. Update TTL logic to use the built-in TTL system
3. Organize data using namespaces instead of key prefixes
4. Use the global cache manager for application-wide caching

## Contributing

When contributing to the cache system:

1. Add comprehensive tests for new features
2. Update type definitions for new interfaces
3. Follow the existing error handling patterns
4. Add documentation for new configuration options
5. Consider backward compatibility for API changes

## License

This cache system is part of the Dr. Muscle X PWA application.
