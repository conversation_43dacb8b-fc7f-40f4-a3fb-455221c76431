import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import React from 'react'
import {
  useWorkoutRecommendations,
  clearPendingRequests,
} from '../useWorkoutRecommendations'
import { getUserSettings } from '@/services/userSettings'
import { getExerciseRecommendation } from '@/services/api/workout'
import { getCurrentUserEmail } from '@/lib/auth-utils'

// Mock dependencies
vi.mock('@/services/userSettings')
vi.mock('@/services/api/workout')
vi.mock('@/lib/auth-utils')
vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: () => ({
    getCachedExerciseRecommendation: vi.fn(() => null),
    setCachedExerciseRecommendation: vi.fn(),
    loadAllExerciseRecommendations: vi.fn(),
    currentWorkout: {
      Id: 123,
      Exercises: [
        { Id: 1, Label: 'Exercise 1' },
        { Id: 2, Label: 'Exercise 2' },
        { Id: 3, Label: 'Exercise 3' },
      ],
    },
    getSwapForExercise: vi.fn(() => null),
  }),
}))

describe('useWorkoutRecommendations - User Settings Memoization', () => {
  let queryClient: QueryClient

  beforeEach(() => {
    vi.clearAllMocks()
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
      },
    })

    // Mock user email
    vi.mocked(getCurrentUserEmail).mockReturnValue('<EMAIL>')

    // Reset memoization between tests
    clearPendingRequests()
  })

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )

  it('should fetch user settings only once for multiple exercise recommendations', async () => {
    // Mock getUserSettings to track calls
    vi.mocked(getUserSettings).mockResolvedValue({
      isQuickMode: false,
      isStrengthPhase: false,
      isFreePlan: false,
      isFirstWorkoutOfStrengthPhase: false,
      lightSessionDays: null,
      setStyle: 'Normal',
      isPyramid: false,
    })

    // Mock getExerciseRecommendation
    vi.mocked(getExerciseRecommendation).mockImplementation(
      async (exerciseId) =>
        ({
          exerciseId,
          sets: [],
          isSuccess: true,
        }) as any
    )

    const { result } = renderHook(() => useWorkoutRecommendations(), {
      wrapper,
    })

    // Load recommendations for multiple exercises
    const promise1 = result.current.loadRecommendation(1, 'Exercise 1')
    const promise2 = result.current.loadRecommendation(2, 'Exercise 2')
    const promise3 = result.current.loadRecommendation(3, 'Exercise 3')

    await Promise.all([promise1, promise2, promise3])

    // getUserSettings should be called only once
    expect(getUserSettings).toHaveBeenCalledTimes(1)

    // getExerciseRecommendation should be called for each exercise
    expect(getExerciseRecommendation).toHaveBeenCalledTimes(3)
  })

  it('should reuse memoized settings within the same hook instance', async () => {
    vi.mocked(getUserSettings).mockResolvedValue({
      isQuickMode: false,
      isStrengthPhase: false,
      isFreePlan: false,
      isFirstWorkoutOfStrengthPhase: false,
      lightSessionDays: null,
      setStyle: 'Normal',
      isPyramid: false,
    })

    vi.mocked(getExerciseRecommendation).mockResolvedValue({
      exerciseId: 1,
      sets: [],
      isSuccess: true,
    } as any)

    const { result, rerender } = renderHook(() => useWorkoutRecommendations(), {
      wrapper,
    })

    // First load
    await result.current.loadRecommendation(1, 'Exercise 1')

    // Rerender and load another exercise
    rerender()
    await result.current.loadRecommendation(2, 'Exercise 2')

    // Should still only call getUserSettings once
    expect(getUserSettings).toHaveBeenCalledTimes(1)
  })

  it('should handle getUserSettings errors gracefully', async () => {
    // Mock getUserSettings to throw error
    vi.mocked(getUserSettings).mockRejectedValue(
      new Error('Settings fetch failed')
    )

    vi.mocked(getExerciseRecommendation).mockResolvedValue({
      exerciseId: 1,
      sets: [],
      isSuccess: true,
    } as any)

    const { result } = renderHook(() => useWorkoutRecommendations(), {
      wrapper,
    })

    // Should not throw when loading recommendation
    const recommendation = await result.current.loadRecommendation(
      1,
      'Exercise 1'
    )

    // Should have returned a recommendation despite settings error
    expect(recommendation).toBeDefined()

    // Should have attempted to get settings
    expect(getUserSettings).toHaveBeenCalledTimes(1)

    // Should still call getExerciseRecommendation with defaults
    expect(getExerciseRecommendation).toHaveBeenCalled()
  })

  it('should not block recommendation loading on settings fetch', async () => {
    // Make getUserSettings slow
    let settingsResolve: () => void
    const settingsPromise = new Promise<any>((resolve) => {
      settingsResolve = () =>
        resolve({
          isQuickMode: false,
          isStrengthPhase: false,
          isFreePlan: false,
          isFirstWorkoutOfStrengthPhase: false,
          lightSessionDays: null,
          setStyle: 'Normal',
          isPyramid: false,
        })
    })
    vi.mocked(getUserSettings).mockReturnValue(settingsPromise)

    // Make recommendation fast
    vi.mocked(getExerciseRecommendation).mockResolvedValue({
      exerciseId: 1,
      sets: [],
      isSuccess: true,
    } as any)

    const { result } = renderHook(() => useWorkoutRecommendations(), {
      wrapper,
    })

    // Start loading
    const loadPromise = result.current.loadRecommendation(1, 'Exercise 1')

    // Resolve settings after a delay
    setTimeout(() => settingsResolve!(), 10)

    // Should complete loading
    const recommendation = await loadPromise

    expect(recommendation).toBeDefined()

    expect(getUserSettings).toHaveBeenCalledTimes(1)
    expect(getExerciseRecommendation).toHaveBeenCalledTimes(1)
  })

  it('should handle concurrent recommendation requests with single settings fetch', async () => {
    vi.mocked(getUserSettings).mockResolvedValue({
      isQuickMode: false,
      isStrengthPhase: false,
      isFreePlan: false,
      isFirstWorkoutOfStrengthPhase: false,
      lightSessionDays: null,
      setStyle: 'Normal',
      isPyramid: false,
    })

    vi.mocked(getExerciseRecommendation).mockImplementation(
      async (exerciseId) =>
        ({
          exerciseId,
          sets: [],
          isSuccess: true,
        }) as any
    )

    const { result } = renderHook(() => useWorkoutRecommendations(), {
      wrapper,
    })

    // Load multiple recommendations concurrently
    const promises = [
      result.current.loadRecommendation(1, 'Exercise 1'),
      result.current.loadRecommendation(2, 'Exercise 2'),
      result.current.loadRecommendation(3, 'Exercise 3'),
    ]

    await Promise.all(promises)

    // Should only fetch settings once
    expect(getUserSettings).toHaveBeenCalledTimes(1)

    // Should fetch all recommendations
    expect(getExerciseRecommendation).toHaveBeenCalledTimes(3)
  })
})
