import { test, expect, Page } from '@playwright/test'

interface SmoothnessMeasure {
  surfaceThemeRendering: boolean
  transitionAnimations: boolean
  prefetchTiming: number
  progressiveLoading: boolean
  touchGestures: boolean
  serviceWorkerCompatibility: boolean
  authFlowHandling: boolean
  performanceMetrics: {
    firstPaint: number
    domContentLoaded: number
    loadComplete: number
  }
}

// Common test utilities
const measurePerformance = async (
  page: Page
): Promise<SmoothnessMeasure['performanceMetrics']> => {
  return await page.evaluate(() => {
    const perfData = performance.getEntriesByType(
      'navigation'
    )[0] as PerformanceNavigationTiming
    return {
      firstPaint:
        performance.getEntriesByName('first-paint')[0]?.startTime || 0,
      domContentLoaded:
        perfData.domContentLoadedEventEnd - perfData.navigationStart,
      loadComplete: perfData.loadEventEnd - perfData.navigationStart,
    }
  })
}

const checkSurfaceThemes = async (page: Page): Promise<boolean> => {
  return await page.evaluate(() => {
    const elements = document.querySelectorAll('[class*="bg-surface"]')
    if (elements.length === 0) return false

    // Check if CSS variables are properly resolved
    const firstElement = elements[0] as HTMLElement
    const computedStyle = getComputedStyle(firstElement)
    const bgColor = computedStyle.backgroundColor

    // Should not be 'rgba(0, 0, 0, 0)' or empty - indicates CSS variables work
    return (
      bgColor !== 'rgba(0, 0, 0, 0)' &&
      bgColor !== 'transparent' &&
      bgColor !== ''
    )
  })
}

const checkTransitionAnimations = async (page: Page): Promise<boolean> => {
  return await page.evaluate(() => {
    const elements = document.querySelectorAll('.transition-theme-normal')
    if (elements.length === 0) return false

    const element = elements[0] as HTMLElement
    const computedStyle = getComputedStyle(element)

    // Check if transition properties are applied
    return (
      computedStyle.transitionDuration === '300ms' ||
      computedStyle.transitionDuration === '0.3s'
    )
  })
}

const checkServiceWorker = async (page: Page): Promise<boolean> => {
  return await page.evaluate(async () => {
    if (!('serviceWorker' in navigator)) return false

    try {
      const registration = await navigator.serviceWorker.getRegistration()
      return !!registration && registration.active !== null
    } catch {
      return false
    }
  })
}

// Removed unused checkTouchGestures function

// Test configurations for different browsers
const browserConfigs = [
  { name: 'Mobile Safari', device: 'iPhone 13', userAgent: 'iPhone' },
  { name: 'Mobile Chrome', device: 'Pixel 5', userAgent: 'Android' },
]

test.describe('Cross-Browser Login Smoothness Testing', () => {
  // Use forEach instead of for-of to avoid ESLint no-restricted-syntax
  browserConfigs.forEach((config) => {
    test.describe(`${config.name} Browser Tests`, () => {
      test(`Surface Theme Token Rendering - ${config.name}`, async ({
        page,
      }) => {
        const issues: string[] = []

        await page.goto('/login')
        await page.waitForLoadState('networkidle')

        // Check surface theme rendering
        const surfaceThemeWorks = await checkSurfaceThemes(page)
        if (!surfaceThemeWorks) {
          issues.push(`Surface theme tokens not rendering properly`)
        }

        // Check for specific surface classes
        const hasSurfacePrimary =
          (await page.locator('[class*="bg-surface-primary"]').count()) > 0
        const hasSurfaceSecondary =
          (await page.locator('[class*="bg-surface-secondary"]').count()) > 0

        if (!hasSurfacePrimary) {
          issues.push(`bg-surface-primary not found in DOM`)
        }
        if (!hasSurfaceSecondary) {
          issues.push(`bg-surface-secondary not found in DOM`)
        }

        expect(issues).toHaveLength(0)
      })

      test(`Transition Animations - ${config.name}`, async ({ page }) => {
        const issues: string[] = []

        await page.goto('/login')
        await page.waitForLoadState('networkidle')

        // Check transition-theme-normal utility
        const transitionWorks = await checkTransitionAnimations(page)
        if (!transitionWorks) {
          issues.push(`transition-theme-normal not working`)
        }

        // Test animation on interaction
        const button = page.locator('button').first()
        if ((await button.count()) > 0) {
          await button.hover()
          await page.waitForTimeout(100)

          const hasTransition = await button.evaluate((el) => {
            const style = getComputedStyle(el)
            return style.transitionDuration !== '0s'
          })

          if (!hasTransition) {
            issues.push(`Button transitions not working`)
          }
        }

        expect(issues).toHaveLength(0)
      })

      test(`100ms Prefetch Timing - ${config.name}`, async ({ page }) => {
        await page.goto('/login')

        // Login first
        await page.fill('[type="email"]', '<EMAIL>')
        await page.fill('[type="password"]', 'Dr123456')
        await page.click('button[type="submit"]')

        // Wait for redirect to program page
        await page.waitForURL('/program', { timeout: 10000 })
        await page.waitForLoadState('networkidle')

        // Check for prefetch timing in console logs
        const logs: string[] = []
        page.on('console', (msg) => {
          logs.push(msg.text())
        })

        await page.waitForTimeout(500)

        const has100msLog = logs.some((log) => log.includes('100ms'))
        const hasPrefetchLog = logs.some((log) => log.includes('preload'))

        expect(has100msLog || hasPrefetchLog).toBeTruthy()
      })

      test(`Progressive Loading Behavior - ${config.name}`, async ({
        page,
      }) => {
        // Navigate to program page
        await page.goto('/login')
        await page.fill('[type="email"]', '<EMAIL>')
        await page.fill('[type="password"]', 'Dr123456')
        await page.click('button[type="submit"]')

        await page.waitForURL('/program')

        // Check for immediate welcome header
        const welcomeHeader = page.locator('[data-testid="welcome-header"]')
        await expect(welcomeHeader).toBeVisible({ timeout: 2000 })

        // Check for first-time user card if applicable
        const firstTimeCard = page.locator(
          '[data-testid="first-time-user-card"]'
        )
        const cardExists = (await firstTimeCard.count()) > 0

        if (cardExists) {
          await expect(firstTimeCard).toBeVisible({ timeout: 2000 })
        }

        // Check for progressive stat loading
        const statsContainer = page.locator('[data-testid="program-stats"]')
        await expect(statsContainer).toBeVisible({ timeout: 3000 })
      })

      test(`Service Worker Compatibility - ${config.name}`, async ({
        page,
      }) => {
        await page.goto('/')
        await page.waitForLoadState('networkidle')

        const swCompatible = await checkServiceWorker(page)
        expect(swCompatible).toBeTruthy()

        // Check PWA installation capability
        if (config.name === 'Mobile Chrome') {
          const installPrompt = await page.evaluate(() => {
            return 'BeforeInstallPromptEvent' in window
          })
          // Chrome should support install prompts
          expect(installPrompt).toBeTruthy()
        }
      })

      test(`Auth Flow and Popup Handling - ${config.name}`, async ({
        page,
      }) => {
        const issues: string[] = []

        await page.goto('/login')

        // Test Google OAuth button (should not use popups on mobile Safari)
        const googleButton = page.locator('button:has-text("Google")')
        if ((await googleButton.count()) > 0) {
          // On Safari, should use same-window redirect
          if (config.name === 'Mobile Safari') {
            let popupOpened = false

            page.on('popup', () => {
              popupOpened = true
            })

            await googleButton.click()
            await page.waitForTimeout(1000)

            if (popupOpened) {
              issues.push(
                'Safari should not open OAuth popups - use same-window redirect'
              )
            }
          }
        }

        // Test regular login flow
        await page.goto('/login')
        await page.fill('[type="email"]', '<EMAIL>')
        await page.fill('[type="password"]', 'Dr123456')

        const submitButton = page.locator('button[type="submit"]')
        await expect(submitButton).toBeEnabled()

        await submitButton.click()

        // Should redirect without issues
        await page.waitForURL('/program', { timeout: 10000 })

        expect(issues).toHaveLength(0)
      })

      test(`Performance and Memory - ${config.name}`, async ({ page }) => {
        await page.goto('/login')

        const startMemory = await page.evaluate(() => {
          return (performance as any).memory?.usedJSHeapSize || 0
        })

        // Complete login flow
        await page.fill('[type="email"]', '<EMAIL>')
        await page.fill('[type="password"]', 'Dr123456')
        await page.click('button[type="submit"]')

        await page.waitForURL('/program')
        await page.waitForLoadState('networkidle')

        const metrics = await measurePerformance(page)
        const endMemory = await page.evaluate(() => {
          return (performance as any).memory?.usedJSHeapSize || 0
        })

        // Performance assertions
        expect(metrics.firstPaint).toBeLessThan(2000) // < 2s first paint
        expect(metrics.domContentLoaded).toBeLessThan(3000) // < 3s DOM ready

        // Memory shouldn't grow excessively
        const memoryGrowth = endMemory - startMemory
        expect(memoryGrowth).toBeLessThan(10 * 1024 * 1024) // < 10MB growth

        // Performance metrics are captured in test results
        // Removed console.log to avoid ESLint no-console error
      })
    })
  })
})
