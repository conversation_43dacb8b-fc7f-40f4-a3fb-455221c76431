import { test, expect } from '@playwright/test'
import { setupAuth } from './helpers/auth-helper'
import { waitForRecommendations } from './helpers/workout-helper'

test.describe('Weight Input Height Stability', () => {
  test.beforeEach(async ({ context }) => {
    await setupAuth(context)
  })

  test('weight input should maintain fixed height when changing values', async ({
    page,
  }) => {
    // Navigate to workout
    await page.goto('/workout')
    await page.waitForSelector('[data-testid="workout-overview"]', {
      timeout: 10000,
    })

    // Click Try New UI button to go to v2
    const tryNewUIButton = page.getByRole('button', { name: 'Try new UI ▶' })
    await tryNewUIButton.click()

    // Wait for exercise page to load
    await page.waitForURL(/\/workout\/exercise-v2\/\d+/, { timeout: 30000 })
    await waitForRecommendations(page)

    // Wait for current set card to be visible
    await page.waitForSelector('[data-testid="current-set-card"]', {
      state: 'visible',
      timeout: 10000,
    })

    // Get the weight input container
    const weightInputContainer = page.locator('#weight-input').locator('..')

    // Verify container has fixed height class
    await expect(weightInputContainer).toHaveClass(/h-28/)

    // Get initial height
    const initialBox = await weightInputContainer.boundingBox()
    expect(initialBox).not.toBeNull()
    const initialHeight = initialBox!.height

    // Change weight to a longer number
    const weightInput = page.locator('#weight-input')
    await weightInput.click()
    await weightInput.fill('555')

    // Get height after change
    const afterBox = await weightInputContainer.boundingBox()
    expect(afterBox).not.toBeNull()
    const afterHeight = afterBox!.height

    // Heights should be the same
    expect(afterHeight).toBe(initialHeight)

    // Try with decimal value
    await weightInput.click()
    await weightInput.fill('555.55')

    const decimalBox = await weightInputContainer.boundingBox()
    expect(decimalBox).not.toBeNull()
    const decimalHeight = decimalBox!.height

    // Height should still be the same
    expect(decimalHeight).toBe(initialHeight)

    // Take screenshot for visual verification
    await page.screenshot({
      path: 'weight-input-height-test.png',
      clip: (await weightInputContainer.boundingBox()) || undefined,
    })
  })

  test('weight input container should be properly centered with flex', async ({
    page,
  }) => {
    // Navigate to workout
    await page.goto('/workout')
    await page.waitForSelector('[data-testid="workout-overview"]', {
      timeout: 10000,
    })

    // Click Try New UI button
    const tryNewUIButton = page.getByRole('button', { name: 'Try new UI ▶' })
    await tryNewUIButton.click()

    // Wait for exercise page
    await page.waitForURL(/\/workout\/exercise-v2\/\d+/, { timeout: 30000 })
    await waitForRecommendations(page)

    // Wait for current set card
    await page.waitForSelector('[data-testid="current-set-card"]', {
      state: 'visible',
      timeout: 10000,
    })

    // Get the weight input container
    const weightInputContainer = page.locator('#weight-input').locator('..')

    // Verify container has centering classes
    await expect(weightInputContainer).toHaveClass(/flex/)
    await expect(weightInputContainer).toHaveClass(/items-center/)
    await expect(weightInputContainer).toHaveClass(/justify-center/)
  })
})
