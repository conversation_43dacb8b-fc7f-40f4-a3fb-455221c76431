import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import { WelcomeHeader, getMotivationalMessage } from '../WelcomeHeader'
import { useAuthStore } from '@/stores/authStore'
import { useUserStatsStore } from '@/stores/userStatsStore'

// Mock the stores
vi.mock('@/stores/authStore', () => ({
  useAuthStore: Object.assign(
    vi.fn((selector) => {
      const state = {
        user: null,
        getCachedUserInfo: vi.fn(() => null),
      }
      return selector ? selector(state) : state
    }),
    {
      getState: vi.fn(() => ({
        getCachedUserInfo: vi.fn(() => null),
      })),
    }
  ),
}))
vi.mock('@/stores/userStatsStore', () => ({
  useUserStatsStore: Object.assign(
    vi.fn(() => ({ stats: null })),
    {
      getState: vi.fn(() => ({ stats: null })),
    }
  ),
}))

describe('WelcomeHeader', () => {
  const mockGetCachedUserInfo = vi.fn()
  const mockUser = {
    email: '<EMAIL>',
    firstName: 'John',
  }

  beforeEach(() => {
    vi.clearAllMocks()
    // Mock the store hooks separately for each selector
    vi.mocked(useAuthStore).mockImplementation((selector: any) => {
      const state = {
        user: mockUser,
        getCachedUserInfo: mockGetCachedUserInfo,
      }
      return selector ? selector(state) : state
    })

    // Mock getState for direct calls in getMotivationalMessage
    vi.mocked(useAuthStore.getState).mockReturnValue({
      getCachedUserInfo: mockGetCachedUserInfo,
    })

    // Default mock for user stats store
    vi.mocked(useUserStatsStore.getState).mockReturnValue({ stats: null })
  })

  it('displays welcome message with first name when available', () => {
    mockGetCachedUserInfo.mockReturnValue({
      firstName: 'John',
      lastName: 'Doe',
    })

    render(<WelcomeHeader />)

    expect(screen.getByText('Welcome back, John!')).toBeInTheDocument()
    // Check for the dynamic motivational message
    const motivationalMessage = getMotivationalMessage()
    expect(screen.getByText(motivationalMessage)).toBeInTheDocument()
  })

  it('displays welcome message with email when firstName not in cache', () => {
    mockGetCachedUserInfo.mockReturnValue(null)
    // Override to have no firstName in user
    vi.mocked(useAuthStore).mockImplementation((selector: any) => {
      const state = {
        user: { email: '<EMAIL>' }, // No firstName
        getCachedUserInfo: mockGetCachedUserInfo,
      }
      return selector ? selector(state) : state
    })

    render(<WelcomeHeader />)

    expect(screen.getByText('Welcome back!')).toBeInTheDocument()
    const motivationalMessage = getMotivationalMessage()
    expect(screen.getByText(motivationalMessage)).toBeInTheDocument()
  })

  it('displays welcome message with firstName from user object when cache is empty', () => {
    mockGetCachedUserInfo.mockReturnValue(null)
    vi.mocked(useAuthStore).mockImplementation((selector: any) => {
      const state = {
        user: { ...mockUser, firstName: 'Jane' },
        getCachedUserInfo: mockGetCachedUserInfo,
      }
      return selector ? selector(state) : state
    })

    render(<WelcomeHeader />)

    expect(screen.getByText('Welcome back, Jane!')).toBeInTheDocument()
  })

  it('shows simplified motivational message for new users', () => {
    mockGetCachedUserInfo.mockReturnValue({
      firstName: 'John',
    })

    render(<WelcomeHeader />)

    expect(screen.getByText('Welcome back, John!')).toBeInTheDocument()
    // Default message when no stats
    expect(screen.getByText('Ready to start your journey?')).toBeInTheDocument()
  })

  it('prevents XSS attacks in user names', () => {
    const xssName = '<script>alert("XSS")</script>'
    mockGetCachedUserInfo.mockReturnValue({
      firstName: xssName,
    })

    render(<WelcomeHeader />)

    // React automatically escapes text content, so the script tag appears as text
    // The truncation happens at 20 chars so we get '<script>alert("XS...'
    const welcomeText = screen.getByText(/Welcome back,/)
    expect(welcomeText.textContent).toContain('<script>alert("XS...')
    // innerHTML should NOT contain actual script tags - they should be escaped
    expect(welcomeText.innerHTML).not.toContain('<script>')
    expect(welcomeText.innerHTML).toContain('&lt;script&gt;')
  })

  it('truncates very long names', () => {
    const longName = 'A'.repeat(100)
    mockGetCachedUserInfo.mockReturnValue({
      firstName: longName,
    })

    render(<WelcomeHeader />)

    const welcomeText = screen.getByText(/Welcome back,/)
    expect(welcomeText.textContent).toContain('AAA...') // Should truncate
  })

  it('renders without user data gracefully', () => {
    vi.mocked(useAuthStore).mockImplementation((selector: any) => {
      const state = {
        user: null,
        getCachedUserInfo: () => null,
      }
      return selector ? selector(state) : state
    })

    render(<WelcomeHeader />)

    expect(screen.getByText('Welcome back!')).toBeInTheDocument()
    const motivationalMessage = getMotivationalMessage()
    expect(screen.getByText(motivationalMessage)).toBeInTheDocument()
  })

  it('uses proper theme tokens for styling', () => {
    mockGetCachedUserInfo.mockReturnValue({ firstName: 'John' })

    const { container } = render(<WelcomeHeader />)

    const header = container.firstChild as HTMLElement
    expect(header).toHaveClass('mb-6')

    const title = screen.getByText('Welcome back, John!')
    // Check for responsive text sizing classes
    expect(title).toHaveClass(
      'text-2xl',
      'sm:text-3xl',
      'lg:text-4xl',
      'font-bold',
      'text-gradient-gold'
    )

    // Get the actual motivational message
    const motivationalMessage = getMotivationalMessage()
    const subtitle = screen.getByText(motivationalMessage)
    expect(subtitle).toHaveClass('text-text-secondary')
  })

  it('animates in smoothly on mount', () => {
    mockGetCachedUserInfo.mockReturnValue({ firstName: 'John' })

    const { container } = render(<WelcomeHeader />)

    const header = container.firstChild as HTMLElement
    expect(header).toHaveClass('animate-in', 'fade-in', 'zoom-in-95')
  })

  describe('Premium Design Enhancement', () => {
    it('should render with premium gold gradient border at full opacity', () => {
      const { container } = render(<WelcomeHeader />)

      // Test for full gold gradient (not subtle /20 opacity)
      const gradientBorder = container.querySelector(
        '.from-brand-gold-start.to-brand-gold-end'
      )
      expect(gradientBorder).toBeTruthy()
      // Should NOT have opacity modifier
      expect(gradientBorder?.className).not.toContain('/20')
      // Should have proper gradient classes
      expect(gradientBorder?.className).toContain('bg-gradient-to-r')
    })

    it('should apply text-gradient-gold to main heading', () => {
      mockGetCachedUserInfo.mockReturnValue({ firstName: 'John' })
      render(<WelcomeHeader />)

      const heading = screen.getByText('Welcome back, John!')
      expect(heading.className).toContain('text-gradient-gold')
    })

    it('should use zoom-in entrance animation instead of slide-in', () => {
      const { container } = render(<WelcomeHeader />)

      const wrapper = container.firstElementChild
      expect(wrapper).toBeTruthy()
      expect(wrapper?.className).toContain('animate-in')
      expect(wrapper?.className).toContain('zoom-in-95')
      expect(wrapper?.className).toContain('fade-in')
      // Should use zoom-in, not slide-in
      expect(wrapper?.className).not.toContain('slide-in-from-top')
    })

    it('should have premium shadow effects', () => {
      const { container } = render(<WelcomeHeader />)

      const card = container.querySelector('.bg-surface-primary')
      expect(card).toBeTruthy()
      // Should have enhanced shadow
      expect(card?.className).toContain('shadow-theme-lg')
    })

    it('should apply shimmer-hover effect to interactive elements', () => {
      const { container } = render(<WelcomeHeader />)

      // If there's a CTA button or interactive element
      const interactiveElement = container.querySelector('.shimmer-hover')
      // This test will initially fail as we haven't added interactive elements yet
      if (interactiveElement) {
        expect(interactiveElement).toBeTruthy()
      }
    })

    it('should use responsive typography for mobile optimization', () => {
      mockGetCachedUserInfo.mockReturnValue({ firstName: 'John' })
      render(<WelcomeHeader />)

      const heading = screen.getByText('Welcome back, John!')
      // Should use responsive text sizing for mobile-first design
      expect(heading.className).toContain('text-2xl') // Mobile base size
      expect(heading.className).toContain('sm:text-3xl') // Tablet size
      expect(heading.className).toContain('lg:text-4xl') // Desktop size
      expect(heading.className).toContain('font-bold')
    })

    it('should have proper touch targets for mobile (52px minimum)', () => {
      const { container } = render(<WelcomeHeader />)

      // Any interactive elements should meet touch target requirements
      const buttons = container.querySelectorAll('button')
      buttons.forEach((button) => {
        expect(button.className).toContain('min-h-[52px]')
      })
    })
  })

  describe('Mobile Responsiveness', () => {
    it('should handle long names gracefully on mobile viewport', () => {
      const longName = 'Christopher Alexander'
      mockGetCachedUserInfo.mockReturnValue({ firstName: longName })

      render(<WelcomeHeader />)

      const heading = screen.getByText(/Welcome back, Christopher/)
      // Mobile-first responsive text sizing
      expect(heading.className).toContain('text-2xl')
      expect(heading.className).toContain('sm:text-3xl')
      expect(heading.className).toContain('lg:text-4xl')
      // Verify truncation is applied (20 chars max - 3 for ellipsis = 17 chars)
      expect(heading.textContent).toContain('Christopher Alexa...')
    })

    it('should maintain readability with very long streak messages', () => {
      mockGetCachedUserInfo.mockReturnValue({ firstName: 'Carl Juneau' })
      vi.mocked(useUserStatsStore.getState).mockReturnValue({
        stats: { weekStreak: 125, workoutsCompleted: 500, lbsLifted: 50000 },
      })

      render(<WelcomeHeader />)

      const heading = screen.getByText('Welcome back, Carl Juneau!')
      // Responsive text ensures it fits on mobile
      expect(heading.className).toContain('text-2xl')

      // Verify streak message displays correctly
      const streakMessage = screen.getByText(
        "125 week streak—you're unstoppable!"
      )
      expect(streakMessage).toBeInTheDocument()
    })
  })

  describe('Motivational Messages with Stats', () => {
    it('should show week streak message when user has streak', () => {
      vi.mocked(useUserStatsStore.getState).mockReturnValue({
        stats: { weekStreak: 2, workoutsCompleted: 0, lbsLifted: 0 },
      })

      const message = getMotivationalMessage()
      expect(message).toBe('2 weeks strong—keep it up!')
    })

    it('should show workout count message when user has workouts', () => {
      vi.mocked(useUserStatsStore.getState).mockReturnValue({
        stats: { weekStreak: 0, workoutsCompleted: 150, lbsLifted: 0 },
      })

      const message = getMotivationalMessage()
      expect(message).toBe('150 workouts completed')
    })

    it('should show first-time message for new users', () => {
      vi.mocked(useUserStatsStore.getState).mockReturnValue({ stats: null })

      const message = getMotivationalMessage()
      expect(message).toBe('Ready to start your journey?')
    })

    it('should show week streak message for 1 week', () => {
      vi.mocked(useUserStatsStore.getState).mockReturnValue({
        stats: { weekStreak: 1, workoutsCompleted: 10, lbsLifted: 1000 },
      })

      const message = getMotivationalMessage()
      expect(message).toBe('1 week strong—keep it up!')
    })

    it('should show week streak message for 5+ weeks', () => {
      vi.mocked(useUserStatsStore.getState).mockReturnValue({
        stats: { weekStreak: 5, workoutsCompleted: 100, lbsLifted: 5000 },
      })

      const message = getMotivationalMessage()
      expect(message).toBe("5 week streak—you're unstoppable!")
    })
  })
})
