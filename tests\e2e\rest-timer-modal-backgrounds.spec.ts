import { test, expect } from '@playwright/test'
import { mockExerciseData, mockWorkoutData } from './mocks/api-mocks'

test.describe('Rest Timer Modal Backgrounds', () => {
  test.beforeEach(async ({ page }) => {
    // Mock API responses
    await mockWorkoutData(page)
    await mockExerciseData(page)

    // Login
    await page.goto('/login')
    await page.fill('input[name="email"]', '<EMAIL>')
    await page.fill('input[name="password"]', 'password123')
    await page.click('button[type="submit"]')

    // Wait for redirect to program page
    await page.waitForURL('/program')

    // Navigate to workout
    await page.goto('/workout')
    await page.waitForSelector('[data-testid="start-workout-button"]')

    // Start the workout
    await page.click('[data-testid="start-workout-button"]')
    await page.waitForURL('/workout/exercise-v2/**')

    // Navigate to exercise page
    await page.goto('/workout/exercise-v2/1')
    await page.waitForSelector('[data-testid="exercise-page-v2"]')
  })

  test('should have backdrop blur style on duration picker', async ({
    page,
  }) => {
    // Start a set to trigger rest timer
    await page.click('[data-testid="save-set-button"]')

    // Wait for rest timer to appear
    await page.waitForSelector('[data-testid="rest-timer-container"]')

    // Click duration setting button
    await page.click('[data-testid="duration-setting-button"]')

    // Wait for duration picker
    await page.waitForSelector('[data-testid="duration-picker"]')

    // Check backdrop has blur effect
    const backdrop = await page.locator('.backdrop-blur-sm').first()
    await expect(backdrop).toBeVisible()

    // Check picker has backdrop blur style
    const picker = page.locator('[data-testid="duration-picker"]')
    const pickerClasses = await picker.getAttribute('class')
    expect(pickerClasses).toContain('bg-surface-primary/90')
    expect(pickerClasses).toContain('backdrop-blur-md')
    expect(pickerClasses).toContain('border-surface-tertiary')
  })

  test('should have backdrop blur style on custom duration modal', async ({
    page,
  }) => {
    // Start a set to trigger rest timer
    await page.click('[data-testid="save-set-button"]')

    // Wait for rest timer to appear
    await page.waitForSelector('[data-testid="rest-timer-container"]')

    // Click duration setting button
    await page.click('[data-testid="duration-setting-button"]')

    // Wait for duration picker
    await page.waitForSelector('[data-testid="duration-picker"]')

    // Click custom duration option
    await page.click('[data-testid="duration-option-custom"]')

    // Wait for custom modal
    await page.waitForSelector('[data-testid="custom-duration-modal"]')

    // Check backdrop has blur effect
    const backdrop = await page.locator('.backdrop-blur-sm').nth(1)
    await expect(backdrop).toBeVisible()

    // Modal itself doesn't have backdrop-blur
    const modal = page.locator('[data-testid="custom-duration-modal"]')
    const modalClasses = await modal.getAttribute('class')
    expect(modalClasses).not.toContain('backdrop-blur')
  })

  test('should maintain consistent styling between rest timer and modals', async ({
    page,
  }) => {
    // Start a set to trigger rest timer
    await page.click('[data-testid="save-set-button"]')

    // Wait for rest timer to appear
    await page.waitForSelector('[data-testid="rest-timer-container"]')

    // Get rest timer styling
    const restTimer = page.locator('[data-testid="rest-timer-container"]')
    const restTimerClasses = await restTimer.getAttribute('class')

    // Click duration setting button
    await page.click('[data-testid="duration-setting-button"]')

    // Wait for duration picker
    await page.waitForSelector('[data-testid="duration-picker"]')

    // Get picker styling
    const picker = page.locator('[data-testid="duration-picker"]')
    const pickerClasses = await picker.getAttribute('class')

    // Both should have similar styling with blur effects
    expect(restTimerClasses).toContain('bg-surface-primary/90')
    expect(pickerClasses).toContain('bg-surface-primary/90')

    // Both should have backdrop blur
    expect(restTimerClasses).toContain('backdrop-blur-sm')
    expect(pickerClasses).toContain('backdrop-blur-md')
  })
})
