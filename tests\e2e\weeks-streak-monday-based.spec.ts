import { test, expect } from '@playwright/test'
import { login } from './helpers'

// Mobile-first configuration
test.use({
  viewport: { width: 375, height: 667 }, // iPhone SE
  hasTouch: true,
  isMobile: true,
})

test.describe('Weeks Streak Monday-based Calculation', () => {
  test('should display weeks streak using Monday-based calculation', async ({
    page,
  }) => {
    // Mock API response with workout dates that span Sunday-Monday
    await page.route('**/api/WorkoutLog/GetLogAverageWithSetsV2', (route) => {
      const mockData = {
        Result: {
          HistoryExerciseModel: {
            TotalWorkoutCompleted: 10,
            TotalWeight: { Lb: 5000, Kg: 2268 },
            ConsecutiveWeeks: 2, // Expected: 2 weeks with Monday-based calculation
          },
          // Include workout dates spanning Sunday-Monday boundary
          WorkoutLogDates: [
            { Date: '2024-01-07T10:00:00Z' }, // Sunday (week 1)
            { Date: '2024-01-08T10:00:00Z' }, // Monday (week 2)
            { Date: '2024-01-14T10:00:00Z' }, // Sunday (week 2)
            { Date: '2024-01-15T10:00:00Z' }, // Monday (week 3)
          ],
        },
      }
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockData),
      })
    })

    // Mock program data
    await page.route('**/api/userProgram/GetUserProgramInfo', (route) => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          GetUserProgramInfoResponseModel: {
            RecommendedProgram: {
              Id: 4,
              Label: 'Dr. Muscle',
            },
            CurrentWorkout: null,
          },
        }),
      })
    })

    // Mock timezone info
    await page.route(
      '**/api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo',
      (route) => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ Result: { TimeZoneId: 'UTC' } }),
        })
      }
    )

    // Login and navigate to program page
    await login(page)
    await page.goto('/program')

    // Wait for stats to load
    await page.waitForSelector('text=Weeks streak', { timeout: 10000 })

    // Check that weeks streak displays correctly
    const weekStreakElement = await page
      .locator('text=Weeks streak')
      .locator('..')
    await expect(weekStreakElement).toContainText('2')

    // Verify it's not showing 3 or 4 (which would be wrong with Sunday-based calculation)
    await expect(weekStreakElement).not.toContainText('3')
    await expect(weekStreakElement).not.toContainText('4')
  })

  test('should calculate single week correctly for Monday-Sunday workouts', async ({
    page,
  }) => {
    // Mock API response with workouts in the same ISO week
    await page.route('**/api/WorkoutLog/GetLogAverageWithSetsV2', (route) => {
      const mockData = {
        Result: {
          HistoryExerciseModel: {
            TotalWorkoutCompleted: 2,
            TotalWeight: { Lb: 1000, Kg: 454 },
            ConsecutiveWeeks: 1, // Expected: 1 week for workouts in same ISO week
          },
          WorkoutLogDates: [
            { Date: '2024-01-08T10:00:00Z' }, // Monday (week start)
            { Date: '2024-01-14T10:00:00Z' }, // Sunday (week end)
          ],
        },
      }
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockData),
      })
    })

    // Mock program data
    await page.route('**/api/userProgram/GetUserProgramInfo', (route) => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          GetUserProgramInfoResponseModel: {
            RecommendedProgram: {
              Id: 4,
              Label: 'Dr. Muscle',
            },
            CurrentWorkout: null,
          },
        }),
      })
    })

    // Mock timezone info
    await page.route(
      '**/api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo',
      (route) => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ Result: { TimeZoneId: 'UTC' } }),
        })
      }
    )

    await login(page)
    await page.goto('/program')

    // Wait for stats to load
    await page.waitForSelector('text=Weeks streak', { timeout: 10000 })

    // Check that weeks streak displays 1
    const weekStreakElement = await page
      .locator('text=Weeks streak')
      .locator('..')
    await expect(weekStreakElement).toContainText('1')
  })
})
