import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useAuthStore } from '@/stores/authStore'
import { setToken, updateTokenInClient } from '@/utils/tokenManager'

// Mock dependencies
vi.mock('@/utils/tokenManager', () => ({
  setToken: vi.fn(),
  updateTokenInClient: vi.fn(),
  clearToken: vi.fn(),
}))

// Mock fetch for cookie exchange
global.fetch = vi.fn()

describe('authStore - restoreAuthFromCookies', () => {
  beforeEach(() => {
    // Reset store
    useAuthStore.setState({
      user: null,
      token: null,
      refreshToken: null,
      isAuthenticated: false,
      error: null,
      isLoading: false,
      lastActivity: Date.now(),
      hasHydrated: true,
    })

    vi.clearAllMocks()
  })

  it('should restore full auth state from cookies', async () => {
    const mockToken = 'restored-token-123'
    const mockUser = {
      email: '<EMAIL>',
      name: 'Restored User',
    }

    // Mock successful cookie exchange
    vi.mocked(global.fetch).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true }),
    } as Response)

    // Call the restore action
    await useAuthStore.getState().restoreAuthFromCookies(mockToken, mockUser)

    // Verify state is updated
    const state = useAuthStore.getState()
    expect(state.isAuthenticated).toBe(true)
    expect(state.token).toBe(mockToken)
    expect(state.refreshToken).toBe(mockToken) // Dr. Muscle uses same token
    expect(state.user).toEqual(mockUser)
    expect(state.error).toBeNull()

    // Verify token is set in API client
    expect(setToken).toHaveBeenCalledWith(mockToken)
    expect(updateTokenInClient).toHaveBeenCalledWith(mockToken)

    // Should NOT attempt cookie exchange (already have cookies)
    expect(global.fetch).not.toHaveBeenCalled()
  })

  it('should handle restoration with minimal user data', async () => {
    const mockToken = 'token-456'
    const mockUser = {
      email: '<EMAIL>',
      // No name provided
    }

    await useAuthStore.getState().restoreAuthFromCookies(mockToken, mockUser)

    const state = useAuthStore.getState()
    expect(state.isAuthenticated).toBe(true)
    expect(state.token).toBe(mockToken)
    expect(state.user).toEqual(mockUser)
  })

  it('should handle restoration with null user', async () => {
    const mockToken = 'token-789'

    await useAuthStore.getState().restoreAuthFromCookies(mockToken, null)

    const state = useAuthStore.getState()
    expect(state.isAuthenticated).toBe(true)
    expect(state.token).toBe(mockToken)
    expect(state.user).toBeNull() // User might be fetched later
  })

  it('should not restore if token is missing', async () => {
    const mockUser = { email: '<EMAIL>' }

    await useAuthStore.getState().restoreAuthFromCookies(null as any, mockUser)

    const state = useAuthStore.getState()
    expect(state.isAuthenticated).toBe(false)
    expect(state.token).toBeNull()
    expect(state.user).toBeNull()

    // Should not set token in API client
    expect(setToken).not.toHaveBeenCalled()
    expect(updateTokenInClient).not.toHaveBeenCalled()
  })

  it('should update lastActivity on restoration', async () => {
    const beforeTime = Date.now()
    const mockToken = 'token-activity'
    const mockUser = { email: '<EMAIL>' }

    await useAuthStore.getState().restoreAuthFromCookies(mockToken, mockUser)

    const state = useAuthStore.getState()
    expect(state.lastActivity).toBeGreaterThanOrEqual(beforeTime)
    expect(state.lastActivity).toBeLessThanOrEqual(Date.now())
  })

  it('should clear error state on successful restoration', async () => {
    // Set an error state
    useAuthStore.setState({ error: 'Previous error' })

    const mockToken = 'token-clear-error'
    const mockUser = { email: '<EMAIL>' }

    await useAuthStore.getState().restoreAuthFromCookies(mockToken, mockUser)

    const state = useAuthStore.getState()
    expect(state.error).toBeNull()
  })

  it('should be idempotent - multiple calls should have same effect', async () => {
    const mockToken = 'token-idempotent'
    const mockUser = { email: '<EMAIL>' }

    // Call twice
    await useAuthStore.getState().restoreAuthFromCookies(mockToken, mockUser)
    await useAuthStore.getState().restoreAuthFromCookies(mockToken, mockUser)

    const state = useAuthStore.getState()
    expect(state.isAuthenticated).toBe(true)
    expect(state.token).toBe(mockToken)
    expect(state.user).toEqual(mockUser)

    // Token manager should be called twice (idempotent operation)
    expect(setToken).toHaveBeenCalledTimes(2)
    expect(updateTokenInClient).toHaveBeenCalledTimes(2)
  })
})
