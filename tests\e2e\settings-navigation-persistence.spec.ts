import { test, expect } from '@playwright/test'

/**
 * Test for PR #645 issue: Settings reset when navigating back
 *
 * This test reproduces the exact user issue where settings changes
 * are lost when using the back button, despite having localStorage
 * persistence implemented.
 */
test.describe('Settings Navigation Persistence - PR #645', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication to access settings page
    await page.addInitScript(() => {
      localStorage.setItem('auth-token', 'mock-token')
    })
  })

  test('should preserve changes when navigating back quickly (before debounce)', async ({
    page,
  }) => {
    // Given: User is on settings page with default values
    await page.goto('/settings')

    // Wait for page to load completely
    await expect(
      page.locator('[data-testid="card-layout-container"]')
    ).toBeVisible()

    // Get initial quick mode state
    const quickModeToggle = page
      .locator('[role="switch"][aria-label="Quick Mode"]')
      .first()
    const initialState = await quickModeToggle.getAttribute('aria-checked')

    // When: User modifies settings and immediately navigates back (< 3 second debounce)
    await quickModeToggle.click()

    // Verify the change was made
    const changedState = await quickModeToggle.getAttribute('aria-checked')
    expect(changedState).not.toBe(initialState)

    // Navigate back immediately (before 3-second debounce completes)
    const backButton = page.locator('[data-testid="back-button"]')
    await backButton.click()

    // Then: Navigate back to settings - changes should persist from localStorage
    await page.goto('/settings')
    await expect(
      page.locator('[data-testid="card-layout-container"]')
    ).toBeVisible()

    // FAILING ASSERTION: This currently fails because changes are reset
    const finalState = await page
      .locator('[role="switch"][aria-label="Quick Mode"]')
      .first()
      .getAttribute('aria-checked')
    expect(finalState).toBe(changedState) // Should preserve the changed state
    expect(finalState).not.toBe(initialState) // Should not revert to initial state
  })

  test('should preserve multiple changes when navigating back quickly', async ({
    page,
  }) => {
    // Given: User is on settings page
    await page.goto('/settings')
    await expect(
      page.locator('[data-testid="card-layout-container"]')
    ).toBeVisible()

    // When: User makes multiple rapid changes
    // 1. Toggle quick mode
    const quickModeToggle = page
      .locator('[role="switch"][aria-label="Quick Mode"]')
      .first()
    const initialQuickMode = await quickModeToggle.getAttribute('aria-checked')
    await quickModeToggle.click()

    // 2. Change weight unit (if available)
    const kgButton = page.locator('button:has-text("kg")')
    if (await kgButton.isVisible()) {
      await kgButton.click()
    }

    // 3. Change rep range minimum
    const repMinInput = page.locator('input[type="number"]').first()
    await repMinInput.fill('8')

    // Navigate back immediately (all changes should be in localStorage)
    const backButton = page.locator('[data-testid="back-button"]')
    await backButton.click()

    // Then: Return to settings should preserve all changes
    await page.goto('/settings')
    await expect(
      page.locator('[data-testid="card-layout-container"]')
    ).toBeVisible()

    // Verify all changes persisted
    const finalQuickMode = await page
      .locator('[role="switch"][aria-label="Quick Mode"]')
      .first()
      .getAttribute('aria-checked')
    expect(finalQuickMode).not.toBe(initialQuickMode)

    const finalRepMin = await page
      .locator('input[type="number"]')
      .first()
      .inputValue()
    expect(finalRepMin).toBe('8')

    // Check if kg is still selected (if it was available)
    if (await page.locator('button:has-text("kg")').isVisible()) {
      const kgButtonState = await page
        .locator('button:has-text("kg")')
        .getAttribute('class')
      expect(kgButtonState).toContain('bg-') // Should have selected state styling
    }
  })

  test('should handle browser back button during rapid changes', async ({
    page,
  }) => {
    // Given: User is on settings page
    await page.goto('/settings')
    await expect(
      page.locator('[data-testid="card-layout-container"]')
    ).toBeVisible()

    // When: User makes changes and uses browser back
    const quickModeToggle = page
      .locator('[role="switch"][aria-label="Quick Mode"]')
      .first()
    const initialState = await quickModeToggle.getAttribute('aria-checked')
    await quickModeToggle.click()

    // Use browser back button instead of UI back button
    await page.goBack()

    // Navigate to settings again
    await page.goto('/settings')
    await expect(
      page.locator('[data-testid="card-layout-container"]')
    ).toBeVisible()

    // Then: Changes should persist even with browser back
    const finalState = await page
      .locator('[role="switch"][aria-label="Quick Mode"]')
      .first()
      .getAttribute('aria-checked')
    expect(finalState).not.toBe(initialState)
  })

  test('should preserve changes during page reload', async ({ page }) => {
    // Given: User makes changes to settings
    await page.goto('/settings')
    await expect(
      page.locator('[data-testid="card-layout-container"]')
    ).toBeVisible()

    const quickModeToggle = page
      .locator('[role="switch"][aria-label="Quick Mode"]')
      .first()
    const initialState = await quickModeToggle.getAttribute('aria-checked')
    await quickModeToggle.click()

    // When: Page is reloaded (simulates navigation back and forth)
    await page.reload()
    await expect(
      page.locator('[data-testid="card-layout-container"]')
    ).toBeVisible()

    // Then: Changes should survive page reload via localStorage
    const finalState = await page
      .locator('[role="switch"][aria-label="Quick Mode"]')
      .first()
      .getAttribute('aria-checked')
    expect(finalState).not.toBe(initialState)
  })

  test('should handle navigation during auto-save debounce period', async ({
    page,
  }) => {
    // Given: User is on settings page
    await page.goto('/settings')
    await expect(
      page.locator('[data-testid="card-layout-container"]')
    ).toBeVisible()

    // When: User makes changes and waits partial debounce time
    const quickModeToggle = page
      .locator('[role="switch"][aria-label="Quick Mode"]')
      .first()
    const initialState = await quickModeToggle.getAttribute('aria-checked')
    await quickModeToggle.click()

    // Wait 1.5 seconds (less than 3-second debounce)
    await page.waitForTimeout(1500)

    // Navigate back during debounce period
    const backButton = page.locator('[data-testid="back-button"]')
    await backButton.click()

    // Then: Return to settings should preserve changes
    await page.goto('/settings')
    await expect(
      page.locator('[data-testid="card-layout-container"]')
    ).toBeVisible()

    const finalState = await page
      .locator('[role="switch"][aria-label="Quick Mode"]')
      .first()
      .getAttribute('aria-checked')
    expect(finalState).not.toBe(initialState)
  })

  test('should verify localStorage contains pending changes', async ({
    page,
  }) => {
    // Given: User makes changes
    await page.goto('/settings')
    await expect(
      page.locator('[data-testid="card-layout-container"]')
    ).toBeVisible()

    const quickModeToggle = page
      .locator('[role="switch"][aria-label="Quick Mode"]')
      .first()
    await quickModeToggle.click()

    // When: Check localStorage directly
    const localStorageData = await page.evaluate(() => {
      return localStorage.getItem('drMuscle_pendingSettings')
    })

    // Then: localStorage should contain the pending changes
    expect(localStorageData).toBeTruthy()

    if (localStorageData) {
      const parsedData = JSON.parse(localStorageData)
      expect(parsedData).toHaveProperty('quickMode')
      expect(typeof parsedData.quickMode).toBe('boolean')
    }

    // Navigate away and back
    const backButton = page.locator('[data-testid="back-button"]')
    await backButton.click()

    // Verify localStorage persists across navigation
    await page.goto('/settings')
    const persistedData = await page.evaluate(() => {
      return localStorage.getItem('drMuscle_pendingSettings')
    })

    expect(persistedData).toBe(localStorageData) // Should be identical
  })
})

/**
 * Test mobile-specific navigation scenarios
 */
test.describe('Settings Navigation Persistence - Mobile', () => {
  test.beforeEach(async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })

    await page.addInitScript(() => {
      localStorage.setItem('auth-token', 'mock-token')
    })
  })

  test('should preserve changes with mobile back swipe gesture', async ({
    page,
  }) => {
    // Given: User on mobile settings page
    await page.goto('/settings')
    await expect(
      page.locator('[data-testid="card-layout-container"]')
    ).toBeVisible()

    // When: User makes change and uses mobile-style navigation
    const quickModeToggle = page
      .locator('[role="switch"][aria-label="Quick Mode"]')
      .first()
    const initialState = await quickModeToggle.getAttribute('aria-checked')
    await quickModeToggle.click()

    // Simulate mobile back navigation (swipe or system back)
    await page.goBack()

    // Then: Navigate back to settings
    await page.goto('/settings')
    await expect(
      page.locator('[data-testid="card-layout-container"]')
    ).toBeVisible()

    const finalState = await page
      .locator('[role="switch"][aria-label="Quick Mode"]')
      .first()
      .getAttribute('aria-checked')
    expect(finalState).not.toBe(initialState)
  })

  test('should handle virtual keyboard interactions during changes', async ({
    page,
  }) => {
    // Given: User on mobile with numeric input
    await page.goto('/settings')
    await expect(
      page.locator('[data-testid="card-layout-container"]')
    ).toBeVisible()

    // When: User focuses numeric input (triggers virtual keyboard)
    const repMinInput = page.locator('input[type="number"]').first()
    await repMinInput.focus()
    await repMinInput.fill('10')

    // Navigate away immediately (virtual keyboard might still be up)
    const backButton = page.locator('[data-testid="back-button"]')
    await backButton.click()

    // Then: Return to settings should preserve numeric change
    await page.goto('/settings')
    await expect(
      page.locator('[data-testid="card-layout-container"]')
    ).toBeVisible()

    const finalValue = await page
      .locator('input[type="number"]')
      .first()
      .inputValue()
    expect(finalValue).toBe('10')
  })
})
