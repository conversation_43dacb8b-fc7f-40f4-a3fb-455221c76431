/**
 * Local settings interface for the settings page UI.
 * Maps user-friendly setting names to form control values.
 */
export interface LocalSettings {
  /** Quick Mode toggle - enables faster workout progression */
  quickMode: boolean

  /** Weight unit preference - 'lbs' or 'kg' */
  weightUnit: 'lbs' | 'kg'

  /** Set style preference for workout recommendations */
  setStyle: 'Normal' | 'Rest-Pause' | 'Drop' | 'Pyramid' | 'Reverse Pyramid'

  /** Minimum reps in target rep range */
  repsMin: number

  /** Maximum reps in target rep range */
  repsMax: number

  /** Weight increment for progression (in selected unit) */
  weightIncrement: number

  /** Number of warmup sets to perform */
  warmupSets: number
}

/**
 * API payload structure for updating user settings on the server.
 * Maps to Dr. Muscle API endpoint structure.
 */
export interface UserSettingsApiPayload {
  /** Quick Mode setting */
  IsQuickMode: boolean

  /** Mass unit - 'lbs' or 'kg' */
  MassUnit: 'lbs' | 'kg'

  /** Normal sets flag - true for Normal, false for advanced styles */
  IsNormalSet: boolean

  /** Drop set flag - true when set style is Drop */
  IsDropSet?: boolean

  /** Pyramid flag - true when set style is Pyramid */
  IsPyramid?: boolean

  /** Reverse pyramid flag - true when set style is Reverse Pyramid */
  IsReversePyramid?: boolean

  /** Minimum reps in target range */
  RepsMinimum: number

  /** Maximum reps in target range */
  RepsMaximum: number

  /** Weight increment for progression */
  WeightIncrement: number

  /** Number of warmup sets */
  WarmupSets: number
}

/**
 * Response structure from the settings update API.
 */
export interface SettingsUpdateResponse {
  /** Whether the update was successful */
  success: boolean

  /** Response message */
  message?: string

  /** Updated user data (if included in response) */
  user?: unknown

  /** Validation errors (if update failed) */
  errors?: Record<string, string>
}

/**
 * Settings validation error structure.
 */
export interface SettingsValidationError extends Error {
  /** Error type identifier */
  name: 'ValidationError'

  /** General error message */
  message: string

  /** Field-specific validation messages */
  validationErrors: Record<string, string>
}

/**
 * Settings hook return type.
 */
export interface UseSettingsPersistenceReturn {
  /** Current local settings state */
  localSettings: LocalSettings

  /** Whether there are unsaved changes */
  hasChanges: boolean

  /** Whether save operation is in progress */
  isSaving: boolean

  /** Save error message, if any */
  saveError: string | null

  /** Update a single setting */
  updateSetting: <K extends keyof LocalSettings>(
    key: K,
    value: LocalSettings[K]
  ) => void

  /** Save all changes to server */
  saveSettings: () => Promise<void>

  /** Reset changes to original values */
  resetChanges: () => void
}

/**
 * Settings data hook return type.
 */
export interface UseSettingsDataReturn {
  /** Settings derived from userInfo */
  settings: LocalSettings

  /** Whether data is loading */
  loading: boolean

  /** Load error, if any */
  error: string | null
}
