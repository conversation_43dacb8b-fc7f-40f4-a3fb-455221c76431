/**
 * Server-driven endpoint selection for exercise recommendations
 * Implements the comprehensive sets implementation guide
 */

import type { ExerciseModel } from '@/types'
import { logger } from '@/utils/logger'
import { getServerUserInfoCached, clearUserInfoCache } from './userInfoCache'

export interface EndpointSelectionResult {
  endpoint: string
  endpointType: 'Normal' | 'RestPause'
}

/**
 * Clear the user settings cache (useful for testing)
 * Delegates to the shared cache clearing function
 */
export function clearUserSettingsCache(): void {
  clearUserInfoCache()
}

/**
 * Select the appropriate recommendation endpoint based on user preferences and exercise type
 * Implements server-driven endpoint selection per comprehensive sets guide
 */
export async function selectRecommendationEndpoint(
  exercise: ExerciseModel
): Promise<EndpointSelectionResult> {
  try {
    // Get token dynamically to avoid circular dependency
    const { useAuthStore } = await import('@/stores/authStore')
    const { token } = useAuthStore.getState()

    const userSettings = await getServerUserInfoCached(token || undefined)

    if (!userSettings) {
      logger.warn('No user settings available, falling back to Normal endpoint')
      return {
        endpoint:
          '/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew',
        endpointType: 'Normal',
      }
    }

    // Extract user preferences (per comprehensive guide)
    // Do NOT use per-exercise IsDropSet for endpoint selection
    const prefersRestPause = userSettings.IsNormalSet === false
    const pyramidEnabled = userSettings.IsPyramid || false
    const isBodyweight = exercise.IsBodyweight || false

    logger.log('Endpoint selection factors:', {
      prefersRestPause,
      pyramidEnabled,
      isBodyweight,
      exerciseId: exercise.Id,
      exerciseLabel: exercise.Label,
    })

    // Determine base endpoint based on user preference
    const baseEndpoint = prefersRestPause
      ? '/api/Exercise/GetRecommendationRestPauseRIRForExerciseWithoutWarmupsNew'
      : '/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew'

    const baseEndpointType: 'Normal' | 'RestPause' = prefersRestPause
      ? 'RestPause'
      : 'Normal'

    // MAUI special case for bodyweight + pyramid while user prefers Normal
    let finalEndpoint = baseEndpoint
    let finalEndpointType: 'Normal' | 'RestPause' = baseEndpointType

    if (!prefersRestPause && pyramidEnabled && isBodyweight) {
      finalEndpoint =
        '/api/Exercise/GetRecommendationRestPauseRIRForExerciseWithoutWarmupsNew'
      finalEndpointType = 'RestPause'
      logger.log(
        'Applied MAUI special case: bodyweight + pyramid -> RestPause endpoint'
      )
    }

    logger.log('Selected endpoint:', {
      endpoint: finalEndpoint,
      endpointType: finalEndpointType,
      reason:
        finalEndpoint !== baseEndpoint
          ? 'MAUI special case'
          : 'User preference',
    })

    return {
      endpoint: finalEndpoint,
      endpointType: finalEndpointType,
    }
  } catch (error) {
    logger.error('Error in endpoint selection, falling back to Normal:', error)
    return {
      endpoint:
        '/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew',
      endpointType: 'Normal',
    }
  }
}
