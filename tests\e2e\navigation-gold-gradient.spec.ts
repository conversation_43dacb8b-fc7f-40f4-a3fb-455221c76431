import { test, expect } from '@playwright/test'
import { login } from './helpers/auth'

test.describe('Navigation Gold Gradient', () => {
  test.beforeEach(async ({ page }) => {
    await login(page)
  })

  test('should display gold gradient on navigation title', async ({ page }) => {
    await page.goto('/program')

    // Check navigation title has gold gradient
    const title = page.locator('header h1').first()
    await expect(title).toHaveClass(/bg-gradient-to-r/)
    await expect(title).toHaveClass(/from-brand-gold-start/)
    await expect(title).toHaveClass(/to-brand-gold-end/)
    await expect(title).toHaveClass(/bg-clip-text/)
    await expect(title).toHaveClass(/text-transparent/)
  })

  test('should display gold gradient on back arrow', async ({ page }) => {
    await page.goto('/workout')

    // Navigate to a page with back button
    await page.click('text=Start workout')
    await page.waitForURL(/\/workout\/exercise/)

    // Check back arrow has gold gradient SVG
    const backArrow = page.locator('[aria-label="Go back"] svg')
    const gradientDef = backArrow.locator('linearGradient')
    await expect(backArrow).toBeVisible()
    await expect(gradientDef).toBeVisible()
  })

  test('should display gold gradient on kebab menu icon', async ({ page }) => {
    await page.goto('/program')

    // Check kebab menu has gold gradient SVG
    const kebabMenu = page.locator('[aria-label="Open user menu"] svg')
    const gradientDef = kebabMenu.locator('linearGradient')
    await expect(kebabMenu).toBeVisible()
    await expect(gradientDef).toBeVisible()
  })

  test('should display gold gradient on exercise page title', async ({
    page,
  }) => {
    await page.goto('/workout')

    // Start workout and navigate to exercise
    await page.click('text=Start workout')
    await page.waitForURL(/\/workout\/exercise/)

    // Check exercise title has gold gradient
    const exerciseTitle = page.locator('header h1').first()
    await expect(exerciseTitle).toHaveClass(/bg-gradient-to-r/)
    await expect(exerciseTitle).toHaveClass(/from-brand-gold-start/)
    await expect(exerciseTitle).toHaveClass(/to-brand-gold-end/)
  })

  test('should maintain gold gradient on mobile viewport', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    await page.goto('/program')

    // Check all elements still have gold gradient
    const title = page.locator('header h1').first()
    await expect(title).toHaveClass(/bg-gradient-to-r/)

    const kebabMenu = page.locator('[aria-label="Open user menu"] svg')
    await expect(kebabMenu).toBeVisible()
    const menuGradient = kebabMenu.locator('linearGradient')
    await expect(menuGradient).toBeVisible()
  })
})
