'use client'

import { memo } from 'react'

interface WarmupSetsSelectorProps {
  value: number
  onChange: (value: number) => void
  disabled?: boolean
}

function WarmupSetsSelector({
  value,
  onChange,
  disabled = false,
}: WarmupSetsSelectorProps) {
  const options = [0, 1, 2, 3, 4, 5]

  return (
    <div className="space-y-2">
      <div className="grid grid-cols-6 gap-2">
        {options.map((num) => (
          <button
            key={num}
            onClick={() => onChange(num)}
            disabled={disabled}
            className={`
              min-h-[52px] rounded-lg font-medium text-sm
              transition-all duration-200
              ${
                value === num
                  ? 'bg-brand-primary text-white'
                  : 'bg-surface-secondary text-text-primary hover:bg-surface-tertiary'
              }
              ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
            `}
            aria-label={`${num} warmup sets`}
            aria-pressed={value === num}
          >
            {num}
          </button>
        ))}
      </div>
      <div className="text-xs text-text-secondary text-center">
        {value === 0
          ? 'No warmup sets'
          : `${value} warmup ${value === 1 ? 'set' : 'sets'} before work sets`}
      </div>
    </div>
  )
}

export default memo(WarmupSetsSelector)
