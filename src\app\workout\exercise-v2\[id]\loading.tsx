export default function ExerciseV2Loading() {
  // Show skeleton loading for better UX instead of just spinner
  return (
    <div className="min-h-[100dvh] bg-bg-primary flex flex-col">
      {/* Header skeleton */}
      <div className="px-4 py-3 bg-bg-secondary border-b border-brand-primary/10">
        <div className="flex items-center justify-between">
          <div className="h-6 w-24 bg-bg-tertiary rounded animate-pulse" />
          <div className="h-6 w-16 bg-bg-tertiary rounded animate-pulse" />
        </div>
      </div>

      {/* Content area */}
      <div className="flex-1 overflow-y-auto px-4 py-6">
        {/* Exercise info skeleton */}
        <div className="mb-6">
          <div className="h-8 w-48 bg-bg-tertiary rounded animate-pulse mb-2" />
          <div className="h-5 w-32 bg-bg-tertiary rounded animate-pulse" />
        </div>

        {/* Current set card skeleton */}
        <div className="bg-bg-secondary rounded-xl p-6 mb-6">
          <div className="h-6 w-20 bg-bg-tertiary rounded animate-pulse mb-4" />
          <div className="space-y-4">
            {/* Reps row */}
            <div className="flex items-center justify-between">
              <div className="h-5 w-12 bg-bg-tertiary rounded animate-pulse" />
              <div className="flex items-center space-x-3">
                <div className="h-10 w-10 bg-bg-tertiary rounded-lg animate-pulse" />
                <div className="h-10 w-20 bg-bg-tertiary rounded-lg animate-pulse" />
                <div className="h-10 w-10 bg-bg-tertiary rounded-lg animate-pulse" />
              </div>
            </div>
            {/* Weight row */}
            <div className="flex items-center justify-between">
              <div className="h-5 w-16 bg-bg-tertiary rounded animate-pulse" />
              <div className="flex items-center space-x-3">
                <div className="h-10 w-10 bg-bg-tertiary rounded-lg animate-pulse" />
                <div className="h-10 w-20 bg-bg-tertiary rounded-lg animate-pulse" />
                <div className="h-10 w-10 bg-bg-tertiary rounded-lg animate-pulse" />
              </div>
            </div>
          </div>
        </div>

        {/* Today's sets skeleton */}
        <div>
          <div className="h-6 w-32 bg-bg-tertiary rounded animate-pulse mb-3" />
          <div className="space-y-2">
            {[1, 2, 3].map((i) => (
              <div
                key={i}
                className="h-12 bg-bg-tertiary rounded-lg animate-pulse"
              />
            ))}
          </div>
        </div>
      </div>

      {/* Bottom action button skeleton */}
      <div className="fixed bottom-0 left-0 right-0 bg-bg-primary border-t border-brand-primary/10 p-4">
        <div className="max-w-lg mx-auto">
          <div className="h-12 w-full bg-brand-primary/20 rounded-lg animate-pulse" />
        </div>
      </div>
    </div>
  )
}
