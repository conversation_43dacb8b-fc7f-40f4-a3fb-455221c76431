import axios, { AxiosError } from 'axios'
import { retryApiCall, USER_INFO_RETRY_OPTIONS } from '@/utils/apiRetry'
import { userInfoPerformance } from '@/utils/userInfoPerformance'
import { logger } from '@/utils/logger'
import type { UserInfosModel } from '@/types'

/**
 * Separate API client for user info to avoid circular dependencies
 * Similar to tokenRefresh.ts pattern
 */
const userInfoClient = axios.create({
  baseURL:
    process.env.NEXT_PUBLIC_API_URL ||
    (process.env.NODE_ENV === 'development'
      ? ''
      : 'https://drmuscle.azurewebsites.net'),
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
})

/**
 * Get current user information from GetUserInfoPyramid endpoint
 * Standalone function to avoid circular dependencies
 * @returns User profile data with set style preferences
 */
export async function getUserInfoStandalone(
  token?: string
): Promise<UserInfosModel | null> {
  // Start performance timing
  userInfoPerformance.trackUserInfoFetch()

  try {
    // Set token if provided
    const headers = token ? { Authorization: `Bearer ${token}` } : {}

    // Use retry logic with 10 second timeout
    const response = await retryApiCall(
      async () => {
        const res = await userInfoClient.post(
          '/api/Account/GetUserInfoPyramid',
          {},
          {
            headers,
          }
        )

        // Track first byte received
        userInfoPerformance.trackUserInfoFirstByte()

        return res
      },
      USER_INFO_RETRY_OPTIONS,
      10000 // 10 second timeout
    )

    // Track successful API call
    userInfoPerformance.trackUserInfoComplete(false)
    userInfoPerformance.trackCacheOperation('miss') // API call means cache miss

    return response.data
  } catch (error) {
    // Track failed API call
    userInfoPerformance.trackCacheOperation('miss')

    // Log error in development
    if (process.env.NODE_ENV === 'development') {
      logger.error('[User Info API] UserInfo error:', error)
    }

    // Handle 404 and 405 specifically - this endpoint might not exist or require special headers
    if (
      error instanceof Error &&
      'response' in error &&
      ((error as AxiosError).response?.status === 404 ||
        (error as AxiosError).response?.status === 405)
    ) {
      logger.warn(
        'UserInfo endpoint error. This API might not be available or requires different method.'
      )
      // Return minimal user data to prevent breaking the app
      throw new Error('UserInfo endpoint not available')
    }
    throw error
  }
}
