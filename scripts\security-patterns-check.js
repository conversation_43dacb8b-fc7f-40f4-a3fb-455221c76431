#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * Security patterns checker for DrMuscleWebApp
 * Performs basic security pattern detection without external dependencies
 */

const SECURITY_PATTERNS = {
  // High severity - should fail the build
  critical: [
    {
      name: 'Hardcoded API Keys',
      pattern: /(?:api[_-]?key|secret[_-]?key|access[_-]?token)\s*[:=]\s*['"][a-zA-Z0-9]{20,}['"]/gi,
      description: 'Potential hardcoded API keys or secrets found'
    },
    {
      name: 'eval() Usage',
      pattern: /\beval\s*\(/gi,
      description: 'eval() usage detected - potential code injection risk'
    },
    {
      name: 'innerHTML with Variables',
      pattern: /\.innerHTML\s*=\s*[^'"]/gi,
      description: 'innerHTML assignment with variables - potential XSS risk'
    }
  ],
  
  // Medium severity - warnings
  warnings: [
    {
      name: 'console.log in Production',
      pattern: /console\.log\(/gi,
      description: 'console.log statements may leak sensitive information'
    },
    {
      name: 'Hardcoded URLs',
      pattern: /https?:\/\/(?!localhost|127\.0\.0\.1)[a-zA-Z0-9.-]+/gi,
      description: 'Hardcoded URLs found - consider using environment variables'
    },
    {
      name: 'Potential Passwords',
      pattern: /(?:password|pwd)\s*[:=]\s*['"][^'"]{1,}['"]/gi,
      description: 'Potential hardcoded passwords found'
    }
  ]
};

const EXCLUDED_PATTERNS = [
  /node_modules/,
  /\.git/,
  /dist/,
  /build/,
  /coverage/,
  /\.next/,
  /test/,
  /spec/,
  /mock/,
  /\.test\./,
  /\.spec\./
];

function shouldExcludeFile(filePath) {
  return EXCLUDED_PATTERNS.some(pattern => pattern.test(filePath));
}

function findFiles(dir, extensions = ['.js', '.jsx', '.ts', '.tsx']) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !shouldExcludeFile(fullPath)) {
        traverse(fullPath);
      } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
        if (!shouldExcludeFile(fullPath)) {
          files.push(fullPath);
        }
      }
    }
  }
  
  traverse(dir);
  return files;
}

function checkFile(filePath, patterns) {
  const content = fs.readFileSync(filePath, 'utf8');
  const findings = [];
  
  for (const pattern of patterns) {
    const matches = content.match(pattern.pattern);
    if (matches) {
      findings.push({
        file: filePath,
        pattern: pattern.name,
        description: pattern.description,
        matches: matches.length,
        lines: getLineNumbers(content, pattern.pattern)
      });
    }
  }
  
  return findings;
}

function getLineNumbers(content, pattern) {
  const lines = content.split('\n');
  const lineNumbers = [];
  
  lines.forEach((line, index) => {
    if (pattern.test(line)) {
      lineNumbers.push(index + 1);
    }
  });
  
  return lineNumbers;
}

function main() {
  console.log('🔍 Running security patterns check...\n');
  
  const srcDir = path.join(process.cwd(), 'src');
  if (!fs.existsSync(srcDir)) {
    console.log('❌ src directory not found');
    process.exit(1);
  }
  
  const files = findFiles(srcDir);
  console.log(`📁 Scanning ${files.length} files...\n`);
  
  let criticalFindings = [];
  let warningFindings = [];
  
  // Check critical patterns
  for (const file of files) {
    const findings = checkFile(file, SECURITY_PATTERNS.critical);
    criticalFindings.push(...findings);
  }
  
  // Check warning patterns
  for (const file of files) {
    const findings = checkFile(file, SECURITY_PATTERNS.warnings);
    warningFindings.push(...findings);
  }
  
  // Report findings
  if (criticalFindings.length > 0) {
    console.log('🚨 CRITICAL SECURITY ISSUES FOUND:');
    criticalFindings.forEach(finding => {
      console.log(`\n❌ ${finding.pattern}`);
      console.log(`   File: ${finding.file}`);
      console.log(`   Lines: ${finding.lines.join(', ')}`);
      console.log(`   Description: ${finding.description}`);
    });
    console.log('\n');
  }
  
  if (warningFindings.length > 0) {
    console.log('⚠️  SECURITY WARNINGS:');
    warningFindings.forEach(finding => {
      console.log(`\n⚠️  ${finding.pattern}`);
      console.log(`   File: ${finding.file}`);
      console.log(`   Lines: ${finding.lines.join(', ')}`);
      console.log(`   Description: ${finding.description}`);
    });
    console.log('\n');
  }
  
  // Summary
  console.log('📊 SECURITY SCAN SUMMARY:');
  console.log(`   Files scanned: ${files.length}`);
  console.log(`   Critical issues: ${criticalFindings.length}`);
  console.log(`   Warnings: ${warningFindings.length}`);
  
  if (criticalFindings.length === 0 && warningFindings.length === 0) {
    console.log('\n✅ No security issues found!');
  }
  
  // Exit with error if critical issues found
  if (criticalFindings.length > 0) {
    console.log('\n❌ Build failed due to critical security issues');
    process.exit(1);
  }
  
  if (warningFindings.length > 0) {
    console.log('\n⚠️  Build completed with warnings');
  } else {
    console.log('\n✅ Security patterns check passed');
  }
}

if (require.main === module) {
  main();
}

module.exports = { checkFile, findFiles, SECURITY_PATTERNS };
