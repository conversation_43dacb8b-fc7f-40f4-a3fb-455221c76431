import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import SetStyleDropdown from './SetStyleDropdown'
import { SET_STYLE_DESCRIPTIONS } from '@/constants/setStyles'

describe('SetStyleDropdown', () => {
  const mockOnChange = vi.fn()

  beforeEach(() => {
    mockOnChange.mockClear()
  })

  describe('Dynamic Explainer Text Functionality', () => {
    it('should display explainer paragraph for Normal set style', () => {
      render(<SetStyleDropdown value="Normal" onChange={mockOnChange} />)

      // Test will FAIL initially because explainer text is not implemented
      const explainerText = SET_STYLE_DESCRIPTIONS.Normal.fullDescription
      expect(screen.getByText(explainerText)).toBeInTheDocument()
    })

    it('should display explainer paragraph for Rest-Pause set style', () => {
      render(<SetStyleDropdown value="Rest-Pause" onChange={mockOnChange} />)

      // Test will FAIL initially because explainer text is not implemented
      const explainerText = SET_STYLE_DESCRIPTIONS['Rest-Pause'].fullDescription
      expect(screen.getByText(explainerText)).toBeInTheDocument()
    })

    it('should update explainer text when set style changes', () => {
      const { rerender } = render(
        <SetStyleDropdown value="Normal" onChange={mockOnChange} />
      )

      // Should show Normal description
      const normalText = SET_STYLE_DESCRIPTIONS.Normal.fullDescription
      expect(screen.getByText(normalText)).toBeInTheDocument()

      // Re-render with different style
      rerender(<SetStyleDropdown value="Drop" onChange={mockOnChange} />)

      // Should now show Drop description and hide Normal
      const dropText = SET_STYLE_DESCRIPTIONS.Drop.fullDescription
      expect(screen.getByText(dropText)).toBeInTheDocument()
      expect(screen.queryByText(normalText)).not.toBeInTheDocument()
    })

    it('should display benefits list for selected set style', () => {
      render(<SetStyleDropdown value="Pyramid" onChange={mockOnChange} />)

      // Test will FAIL initially because benefits list is not implemented
      const { benefits } = SET_STYLE_DESCRIPTIONS.Pyramid
      benefits.forEach((benefit) => {
        expect(screen.getByText(benefit)).toBeInTheDocument()
      })
    })
  })

  describe('Dropdown Basic Functionality', () => {
    it('should render dropdown with all set style options', () => {
      render(<SetStyleDropdown value="Normal" onChange={mockOnChange} />)

      const dropdown = screen.getByRole('combobox')
      expect(dropdown).toBeInTheDocument()

      // Check all options are present
      Object.entries(SET_STYLE_DESCRIPTIONS).forEach(([, style]) => {
        const expectedText = `${style.name} - ${style.shortDescription}`
        expect(screen.getByText(expectedText)).toBeInTheDocument()
      })
    })

    it('should call onChange when dropdown selection changes', () => {
      render(<SetStyleDropdown value="Normal" onChange={mockOnChange} />)

      const dropdown = screen.getByRole('combobox')
      fireEvent.change(dropdown, { target: { value: 'Rest-Pause' } })

      expect(mockOnChange).toHaveBeenCalledWith('Rest-Pause')
    })

    it('should show currently selected value', () => {
      render(<SetStyleDropdown value="Drop" onChange={mockOnChange} />)

      const dropdown = screen.getByRole('combobox')
      expect(dropdown).toHaveValue('Drop')
    })
  })

  describe('Error Handling', () => {
    it('should handle invalid set style gracefully', () => {
      // @ts-expect-error Testing invalid input
      render(<SetStyleDropdown value="InvalidStyle" onChange={mockOnChange} />)

      // Should not crash and should fallback gracefully
      const dropdown = screen.getByRole('combobox')
      expect(dropdown).toBeInTheDocument()
    })

    it('should handle missing description data gracefully', () => {
      // This tests edge case where setStyles.ts might be missing data
      render(<SetStyleDropdown value="Normal" onChange={mockOnChange} />)

      // Component should render without crashing
      const dropdown = screen.getByRole('combobox')
      expect(dropdown).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels and description association', () => {
      render(<SetStyleDropdown value="Normal" onChange={mockOnChange} />)

      const dropdown = screen.getByRole('combobox')
      expect(dropdown).toHaveAttribute('aria-label', 'Set style selection')

      // Explainer should be aria-describedby
      const explainerContainer = screen
        .getByText(SET_STYLE_DESCRIPTIONS.Normal.fullDescription)
        .closest('div')
      const expectedId = 'set-style-explainer-normal'
      expect(explainerContainer).toHaveAttribute('id', expectedId)
      expect(dropdown).toHaveAttribute('aria-describedby', expectedId)
    })
  })

  describe('Disabled State', () => {
    it('should disable dropdown when disabled prop is true', () => {
      render(
        <SetStyleDropdown value="Normal" onChange={mockOnChange} disabled />
      )

      const dropdown = screen.getByRole('combobox')
      expect(dropdown).toBeDisabled()
    })

    it('should not call onChange when disabled and selection attempted', () => {
      render(
        <SetStyleDropdown value="Normal" onChange={mockOnChange} disabled />
      )

      const dropdown = screen.getByRole('combobox')
      fireEvent.change(dropdown, { target: { value: 'Rest-Pause' } })

      expect(mockOnChange).not.toHaveBeenCalled()
    })
  })
})
