import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useAuthStore } from '@/stores/authStore'
import { performCompleteLogout } from '@/utils/logout'
import { clearToken, updateTokenInClient } from '@/utils/tokenManager'

// Mock the dependencies
vi.mock('@/utils/logout', () => ({
  performCompleteLogout: vi.fn(() => Promise.resolve()),
}))

vi.mock('@/utils/queryClient', () => ({
  queryClient: {
    clear: vi.fn(),
    cancelQueries: vi.fn(() => Promise.resolve()),
  },
}))

vi.mock('@/utils/tokenManager', () => ({
  clearToken: vi.fn(),
  updateTokenInClient: vi.fn(),
  setToken: vi.fn(),
}))

// Mock fetch for cookie exchange
global.fetch = vi.fn()

describe('authStore logout cache clearing', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Reset zustand store
    useAuthStore.setState({
      user: { email: '<EMAIL>', name: 'Test User' },
      token: 'test-token',
      refreshToken: 'test-refresh-token',
      isAuthenticated: true,
      error: null,
      isLoading: false,
      lastActivity: Date.now(),
      cachedUserInfo: {
        data: { id: '123', email: '<EMAIL>' },
        timestamp: Date.now(),
        version: 1,
      },
    })

    // Mock successful cookie clear response
    ;(global.fetch as any).mockResolvedValue({
      ok: true,
    })
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('should call performCompleteLogout when logout is called', async () => {
    // Test rationale: Ensure logout clears all caches to prevent cross-user data conflicts
    const { result } = renderHook(() => useAuthStore())

    expect(result.current.isAuthenticated).toBe(true)
    expect(result.current.user).not.toBeNull()

    // Act: Call logout
    await act(async () => {
      await result.current.logout()
    })

    // Assert: performCompleteLogout should be called
    expect(performCompleteLogout).toHaveBeenCalledTimes(1)

    // Assert: Token clearing should happen
    expect(clearToken).toHaveBeenCalledTimes(1)
    expect(updateTokenInClient).toHaveBeenCalledWith(null)

    // Assert: Cookie exchange DELETE should be called
    expect(global.fetch).toHaveBeenCalledWith('/api/auth/exchange', {
      method: 'DELETE',
      credentials: 'include',
    })

    // Assert: State should be reset
    expect(result.current.isAuthenticated).toBe(false)
    expect(result.current.user).toBeNull()
    expect(result.current.token).toBeNull()
    expect(result.current.refreshToken).toBeNull()
    expect(result.current.cachedUserInfo).toBeNull()
  })

  it('should clear caches even if cookie clear fails', async () => {
    // Test rationale: Cache clearing is critical and should happen regardless of cookie clear success
    const { result } = renderHook(() => useAuthStore())

    // Mock cookie clear failure
    ;(global.fetch as any).mockRejectedValue(new Error('Network error'))

    // Act: Call logout
    await act(async () => {
      await result.current.logout()
    })

    // Assert: performCompleteLogout should still be called despite cookie failure
    expect(performCompleteLogout).toHaveBeenCalledTimes(1)

    // Assert: State should still be reset
    expect(result.current.isAuthenticated).toBe(false)
    expect(result.current.user).toBeNull()
  })

  it('should handle performCompleteLogout failure gracefully', async () => {
    // Test rationale: Logout should complete even if cache clearing fails
    const { result } = renderHook(() => useAuthStore())

    // Mock performCompleteLogout to throw an error
    vi.mocked(performCompleteLogout).mockRejectedValue(
      new Error('Cache clear failed')
    )

    // Act: Call logout (should not throw)
    await act(async () => {
      await expect(result.current.logout()).resolves.not.toThrow()
    })

    // Assert: performCompleteLogout was attempted
    expect(performCompleteLogout).toHaveBeenCalledTimes(1)

    // Assert: State should still be reset despite cache clear failure
    expect(result.current.isAuthenticated).toBe(false)
    expect(result.current.user).toBeNull()
  })

  it('should clear caches during session timeout', async () => {
    // Test rationale: Programmatic logout (e.g., session timeout) should also clear caches
    const { result } = renderHook(() => useAuthStore())

    // Set last activity to trigger timeout
    act(() => {
      useAuthStore.setState({ lastActivity: Date.now() - 25 * 60 * 60 * 1000 }) // 25 hours ago
    })

    // Act: Check session timeout (which calls logout internally)
    await act(async () => {
      result.current.checkSessionTimeout()
    })

    // Assert: performCompleteLogout should be called via logout
    expect(performCompleteLogout).toHaveBeenCalledTimes(1)

    // Assert: User should be logged out
    expect(result.current.isAuthenticated).toBe(false)
  })

  it('should maintain hasHydrated state after logout', async () => {
    // Test rationale: Hydration state should persist across logout to prevent re-hydration issues
    const { result } = renderHook(() => useAuthStore())

    // Set hydrated state
    act(() => {
      result.current.setHasHydrated(true)
    })

    expect(result.current.hasHydrated).toBe(true)

    // Act: Call logout
    await act(async () => {
      await result.current.logout()
    })

    // Assert: hasHydrated should remain true
    expect(result.current.hasHydrated).toBe(true)
  })
})
