import { useState, useEffect, useCallback, useRef } from 'react'
import { logger } from '@/utils/logger'

interface CacheEntry {
  exerciseId: number
  sets: unknown[]
  timestamp: number
  accessCount?: number
  lastAccessed?: number
}

interface MemorySnapshot {
  timestamp: number
  usedMemory: number
  totalMemory: number
  cacheSize: number
}

interface OptimizationRecommendation {
  type:
    | 'reduce_batch_size'
    | 'clear_old_cache'
    | 'reduce_prefetch_frequency'
    | 'emergency_cleanup'
  message: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  action?: () => void
}

interface UsePrefetchMemoryOptimizationProps {
  prefetchedData: Map<number, CacheEntry>
  clearCache: () => void
  maxCacheSize?: number
  maxCacheAge?: number // milliseconds
  memoryThreshold?: number // MB
  autoCleanupInterval?: number // milliseconds
}

interface UsePrefetchMemoryOptimizationReturn {
  cacheSize: number
  memoryUsage: number // MB
  isMemoryPressure: boolean
  shouldReducePrefetching: boolean
  recommendedBatchSize: number
  cacheHitRate: number
  expiredEntries: number
  lruEvictions: number
  cleanupOperations: number
  memoryHistory: MemorySnapshot[]
  cleanExpiredEntries: () => number
  cleanLRUEntries: () => number
  performMemoryCleanup: () => void
  recordCacheHit: () => void
  recordCacheMiss: () => void
  recordMemorySnapshot: () => void
  getOptimizationRecommendations: () => OptimizationRecommendation[]
}

/**
 * Hook that provides memory optimization and cache management for prefetch operations
 * Monitors memory usage, cleans expired entries, and provides recommendations
 */
export function usePrefetchMemoryOptimization({
  prefetchedData,
  clearCache,
  maxCacheSize = 50,
  maxCacheAge = 300000, // 5 minutes
  memoryThreshold = 100, // 100MB
  autoCleanupInterval = 60000, // 1 minute
}: UsePrefetchMemoryOptimizationProps): UsePrefetchMemoryOptimizationReturn {
  const [expiredEntries, setExpiredEntries] = useState(0)
  const [lruEvictions, setLruEvictions] = useState(0)
  const [cleanupOperations, setCleanupOperations] = useState(0)
  const [memoryHistory, setMemoryHistory] = useState<MemorySnapshot[]>([])

  const cacheHitsRef = useRef(0)
  const cacheMissesRef = useRef(0)
  const autoCleanupIntervalRef = useRef<NodeJS.Timeout | undefined>(undefined)

  /**
   * Get current memory usage in MB
   */
  const getMemoryUsage = useCallback((): number => {
    if (typeof performance !== 'undefined') {
      const { memory } = performance as { memory?: { usedJSHeapSize: number } }
      if (memory) {
        return memory.usedJSHeapSize / (1024 * 1024)
      }
    }
    return 0
  }, [])

  /**
   * Calculate current memory usage and pressure status
   */
  const memoryUsage = getMemoryUsage()
  const isMemoryPressure = memoryUsage > memoryThreshold
  const cacheSize = prefetchedData.size

  /**
   * Determine if prefetching should be reduced based on memory conditions
   */
  const shouldReducePrefetching = (() => {
    if (typeof performance === 'undefined') return false

    const { memory } = performance as {
      memory?: { usedJSHeapSize: number; jsHeapSizeLimit: number }
    }
    if (!memory) return false

    const memoryUsage = memory.usedJSHeapSize / (1024 * 1024)
    const memoryLimit = memory.jsHeapSizeLimit / (1024 * 1024)
    const memoryPressureRatio = memoryUsage / memoryLimit

    // Reduce prefetching if we're using more than 80% of available memory
    return memoryPressureRatio > 0.8
  })()

  /**
   * Calculate recommended batch size based on memory conditions
   */
  const recommendedBatchSize = (() => {
    if (shouldReducePrefetching) {
      return Math.max(1, Math.floor(3 * (1 - memoryUsage / memoryThreshold)))
    }
    if (isMemoryPressure) {
      return 2
    }
    return 3
  })()

  /**
   * Calculate cache hit rate
   */
  const cacheHitRate = (() => {
    const totalRequests = cacheHitsRef.current + cacheMissesRef.current
    if (totalRequests === 0) return 0
    return (cacheHitsRef.current / totalRequests) * 100
  })()

  /**
   * Clean expired cache entries
   */
  const cleanExpiredEntries = useCallback((): number => {
    const now = Date.now()
    let removedCount = 0

    const expiredIds: number[] = []
    prefetchedData.forEach((entry, id) => {
      if (now - entry.timestamp > maxCacheAge) {
        expiredIds.push(id)
      }
    })

    expiredIds.forEach((id) => {
      prefetchedData.delete(id)
      removedCount++
    })

    if (removedCount > 0) {
      setExpiredEntries((prev) => prev + removedCount)
      setCleanupOperations((prev) => prev + 1)
      logger.log(
        `[usePrefetchMemoryOptimization] Cleaned ${removedCount} expired entries`
      )
    }

    return removedCount
  }, [prefetchedData, maxCacheAge])

  /**
   * Clean least recently used entries when cache size exceeds limit
   */
  const cleanLRUEntries = useCallback((): number => {
    if (prefetchedData.size <= maxCacheSize) {
      return 0
    }

    const entriesToRemove = prefetchedData.size - maxCacheSize
    const sortedEntries = Array.from(prefetchedData.entries()).sort(
      ([, a], [, b]) => {
        // Sort by last accessed time, then by timestamp
        const aLastAccessed = a.lastAccessed || a.timestamp
        const bLastAccessed = b.lastAccessed || b.timestamp
        return aLastAccessed - bLastAccessed
      }
    )

    let removedCount = 0
    for (let i = 0; i < entriesToRemove && i < sortedEntries.length; i++) {
      const entry = sortedEntries[i]
      if (entry) {
        const [id] = entry
        prefetchedData.delete(id)
        removedCount++
      }
    }

    if (removedCount > 0) {
      setLruEvictions((prev) => prev + removedCount)
      setCleanupOperations((prev) => prev + 1)
      logger.log(
        `[usePrefetchMemoryOptimization] Evicted ${removedCount} LRU entries`
      )
    }

    return removedCount
  }, [prefetchedData, maxCacheSize])

  /**
   * Perform comprehensive memory cleanup
   */
  const performMemoryCleanup = useCallback(() => {
    logger.log('[usePrefetchMemoryOptimization] Performing memory cleanup')

    const expiredCleaned = cleanExpiredEntries()
    const lruCleaned = cleanLRUEntries()

    // Under severe memory pressure, clear entire cache
    if (shouldReducePrefetching && expiredCleaned + lruCleaned < 5) {
      logger.warn(
        '[usePrefetchMemoryOptimization] Severe memory pressure - clearing entire cache'
      )
      clearCache()
      setCleanupOperations((prev) => prev + 1)
    }

    // Force garbage collection if available (development only)
    if (
      process.env.NODE_ENV === 'development' &&
      typeof window !== 'undefined' &&
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (window as any).gc
    ) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      ;(window as any).gc()
    }
  }, [
    cleanExpiredEntries,
    cleanLRUEntries,
    shouldReducePrefetching,
    clearCache,
  ])

  /**
   * Record cache hit for analytics
   */
  const recordCacheHit = useCallback(() => {
    cacheHitsRef.current += 1
  }, [])

  /**
   * Record cache miss for analytics
   */
  const recordCacheMiss = useCallback(() => {
    cacheMissesRef.current += 1
  }, [])

  /**
   * Record current memory snapshot
   */
  const recordMemorySnapshot = useCallback(() => {
    const { memory } = performance as { memory?: { totalJSHeapSize?: number } }
    const snapshot: MemorySnapshot = {
      timestamp: Date.now(),
      usedMemory: memoryUsage,
      totalMemory: (memory?.totalJSHeapSize ?? 0) / (1024 * 1024),
      cacheSize: prefetchedData.size,
    }

    setMemoryHistory((prev) => [...prev.slice(-19), snapshot]) // Keep last 20 snapshots
  }, [memoryUsage, prefetchedData.size])

  /**
   * Get optimization recommendations based on current state
   */
  const getOptimizationRecommendations =
    useCallback((): OptimizationRecommendation[] => {
      const recommendations: OptimizationRecommendation[] = []

      if (shouldReducePrefetching) {
        recommendations.push({
          type: 'emergency_cleanup',
          message:
            'Critical memory usage detected. Consider clearing cache and reducing prefetch operations.',
          severity: 'critical',
          action: performMemoryCleanup,
        })
      } else if (isMemoryPressure) {
        recommendations.push({
          type: 'reduce_batch_size',
          message: `Memory pressure detected (${memoryUsage.toFixed(1)}MB). Reduce prefetch batch size to ${recommendedBatchSize}.`,
          severity: 'high',
        })
      }

      if (expiredEntries > 0) {
        recommendations.push({
          type: 'clear_old_cache',
          message: `${expiredEntries} expired cache entries detected. Clean up to free memory.`,
          severity: 'medium',
          action: cleanExpiredEntries,
        })
      }

      if (cacheSize > maxCacheSize * 0.8) {
        recommendations.push({
          type: 'reduce_prefetch_frequency',
          message: `Cache size approaching limit (${cacheSize}/${maxCacheSize}). Consider reducing prefetch frequency.`,
          severity: 'medium',
        })
      }

      if (
        cacheHitRate < 50 &&
        cacheHitsRef.current + cacheMissesRef.current > 10
      ) {
        recommendations.push({
          type: 'reduce_prefetch_frequency',
          message: `Low cache hit rate (${cacheHitRate.toFixed(1)}%). Prefetch strategy may be inefficient.`,
          severity: 'low',
        })
      }

      return recommendations
    }, [
      shouldReducePrefetching,
      isMemoryPressure,
      memoryUsage,
      recommendedBatchSize,
      expiredEntries,
      cacheSize,
      maxCacheSize,
      cacheHitRate,
      performMemoryCleanup,
      cleanExpiredEntries,
    ])

  /**
   * Automatic cleanup at intervals
   */
  useEffect(() => {
    if (autoCleanupInterval > 0) {
      autoCleanupIntervalRef.current = setInterval(() => {
        const expiredCleaned = cleanExpiredEntries()
        const lruCleaned = cleanLRUEntries()

        if (expiredCleaned > 0 || lruCleaned > 0) {
          logger.log(
            `[usePrefetchMemoryOptimization] Auto-cleanup: ${expiredCleaned} expired, ${lruCleaned} LRU`
          )
        }

        // Record memory snapshot periodically
        recordMemorySnapshot()
      }, autoCleanupInterval)

      return () => {
        if (autoCleanupIntervalRef.current) {
          clearInterval(autoCleanupIntervalRef.current)
        }
      }
    }
    return undefined
  }, [
    autoCleanupInterval,
    cleanExpiredEntries,
    cleanLRUEntries,
    recordMemorySnapshot,
  ])

  /**
   * Monitor memory pressure and react accordingly
   */
  useEffect(() => {
    if (isMemoryPressure) {
      logger.warn(
        `[usePrefetchMemoryOptimization] Memory pressure detected: ${memoryUsage.toFixed(1)}MB`
      )
    }
  }, [isMemoryPressure, memoryUsage])

  return {
    cacheSize,
    memoryUsage,
    isMemoryPressure,
    shouldReducePrefetching,
    recommendedBatchSize,
    cacheHitRate,
    expiredEntries,
    lruEvictions,
    cleanupOperations,
    memoryHistory,
    cleanExpiredEntries,
    cleanLRUEntries,
    performMemoryCleanup,
    recordCacheHit,
    recordCacheMiss,
    recordMemorySnapshot,
    getOptimizationRecommendations,
  }
}
