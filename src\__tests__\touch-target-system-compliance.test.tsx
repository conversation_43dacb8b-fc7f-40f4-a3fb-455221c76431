/**
 * System-Wide Touch Target Compliance Integration Test
 *
 * Purpose: Validate 52px touch target compliance across critical user journeys
 * Context: High-level integration test ensuring no touch target regressions
 * Priority: CRITICAL - Guards against system-wide compliance failures
 */

import React from 'react'
import { render, screen } from '@testing-library/react'
import { vi } from 'vitest'
import {
  validateTouchTargetCompliance,
  TOUCH_TARGET_MINIMUM,
} from '../test-utils/touch-target-utils'

// Import critical components that must be compliant
import { Button } from '../components/ui/Button'
import { FloatingCTAButton } from '../components/ui/FloatingCTAButton'
import { SetCellArrowButtons } from '../components/workout/SetCellArrowButtons'

// Mock functions for component props
const mockHandlers = {
  onClick: vi.fn(),
  onRepsUp: vi.fn(),
  onRepsDown: vi.fn(),
  onWeightUp: vi.fn(),
  onWeightDown: vi.fn(),
}

describe('System-Wide Touch Target Compliance', () => {
  describe('critical interactive components', () => {
    it('should ensure all Button component variants meet 52px requirement', () => {
      // This test WILL FAIL initially due to sm/md Button sizes being < 52px
      const { container } = render(
        <div>
          <Button size="sm" variant="primary">
            Small Primary
          </Button>
          <Button size="md" variant="secondary">
            Medium Secondary
          </Button>
          <Button size="lg" variant="ghost">
            Large Ghost
          </Button>
          <Button variant="danger">Danger Button</Button>
          <Button variant="gold">Gold Button</Button>
        </div>
      )

      const result = validateTouchTargetCompliance(
        container,
        TOUCH_TARGET_MINIMUM
      )

      expect(result.isCompliant).toBe(true)
      expect(result.violations).toHaveLength(0)
      expect(result.complianceRate).toBe(1.0)

      // Log any violations for debugging
      if (!result.isCompliant) {
        result.violations.forEach((violation) => {
          console.error('Button touch target violation:', {
            element: violation.element.textContent,
            dimensions: `${violation.measurement.width}w × ${violation.measurement.height}h`,
            gap: `${violation.expectedMinimum - Math.min(violation.measurement.width, violation.measurement.height)}px below minimum`,
          })
        })
      }
    })

    it('should ensure workout arrow buttons meet 52px requirement', () => {
      // This test WILL FAIL initially due to SetCellArrowButtons using min-h-[44px]
      const { container } = render(
        <SetCellArrowButtons isActive position="above" {...mockHandlers} />
      )

      const result = validateTouchTargetCompliance(
        container,
        TOUCH_TARGET_MINIMUM
      )

      expect(result.isCompliant).toBe(true)
      expect(result.violations).toHaveLength(0)

      // Specific validation for workout-critical elements
      const arrowButtons = screen.getAllByRole('button')
      expect(arrowButtons.length).toBeGreaterThan(0)

      arrowButtons.forEach((button) => {
        const rect = button.getBoundingClientRect()
        expect(Math.min(rect.width, rect.height)).toBeGreaterThanOrEqual(
          TOUCH_TARGET_MINIMUM
        )
      })
    })

    it('should ensure floating CTA button meets 52px requirement', () => {
      const { container } = render(
        <FloatingCTAButton onClick={mockHandlers.onClick} />
      )

      const result = validateTouchTargetCompliance(
        container,
        TOUCH_TARGET_MINIMUM
      )

      expect(result.isCompliant).toBe(true)
      expect(result.violations).toHaveLength(0)

      // Floating CTA is critical for workout flow
      const ctaButton = screen.getByRole('button')
      const rect = ctaButton.getBoundingClientRect()
      expect(rect.height).toBeGreaterThanOrEqual(TOUCH_TARGET_MINIMUM)
    })
  })

  describe('button state variations', () => {
    it('should maintain 52px compliance across all button states', () => {
      const { container } = render(
        <div>
          <Button loading>Loading Button</Button>
          <Button disabled>Disabled Button</Button>
          <Button fullWidth>Full Width Button</Button>
          <Button size="sm" loading disabled>
            Small Loading Disabled
          </Button>
        </div>
      )

      const result = validateTouchTargetCompliance(
        container,
        TOUCH_TARGET_MINIMUM
      )

      expect(result.isCompliant).toBe(true)
      expect(result.violations).toHaveLength(0)

      // All button states should preserve touch targets
      const buttons = screen.getAllByRole('button')
      buttons.forEach((button) => {
        const rect = button.getBoundingClientRect()
        expect(Math.min(rect.width, rect.height)).toBeGreaterThanOrEqual(
          TOUCH_TARGET_MINIMUM
        )
      })
    })
  })

  describe('responsive behavior', () => {
    it('should maintain touch target compliance at mobile viewport sizes', () => {
      // Mock different viewport sizes
      const viewports = [
        { width: 320, height: 568 }, // iPhone SE
        { width: 390, height: 844 }, // iPhone 14
        { width: 412, height: 915 }, // Galaxy S21
      ]

      viewports.forEach((viewport) => {
        // Mock viewport size
        Object.defineProperty(window, 'innerWidth', {
          writable: true,
          configurable: true,
          value: viewport.width,
        })
        Object.defineProperty(window, 'innerHeight', {
          writable: true,
          configurable: true,
          value: viewport.height,
        })

        const { container } = render(
          <div>
            <Button size="sm">Small Mobile</Button>
            <Button size="md">Medium Mobile</Button>
            <SetCellArrowButtons isActive position="above" {...mockHandlers} />
          </div>
        )

        const result = validateTouchTargetCompliance(
          container,
          TOUCH_TARGET_MINIMUM
        )

        expect(result.isCompliant).toBe(true)
        expect(result.violations).toHaveLength(0)
      })
    })
  })

  describe('regression prevention', () => {
    it('should detect any hardcoded 44px touch targets', () => {
      // This test ensures no components accidentally use old 44px standard
      const { container } = render(
        <div>
          <Button size="sm">Test Small</Button>
          <Button size="md">Test Medium</Button>
          <SetCellArrowButtons isActive position="below" {...mockHandlers} />
        </div>
      )

      // Check for any elements that might have 44px dimensions
      const result = validateTouchTargetCompliance(container, 45) // Slightly above 44px

      expect(result.isCompliant).toBe(true)

      // Also validate against full 52px requirement
      const result52 = validateTouchTargetCompliance(
        container,
        TOUCH_TARGET_MINIMUM
      )
      expect(result52.isCompliant).toBe(true)
    })

    it('should maintain compliance with rapid re-renders', () => {
      function TestComponent({ iteration }: { iteration: number }) {
        return (
          <div>
            <Button size="sm">Iteration {iteration}</Button>
            <Button size="md">Medium {iteration}</Button>
          </div>
        )
      }

      const { container, rerender } = render(<TestComponent iteration={1} />)

      // Test compliance across multiple re-renders
      for (let i = 2; i <= 5; i++) {
        rerender(<TestComponent iteration={i} />)

        const result = validateTouchTargetCompliance(
          container,
          TOUCH_TARGET_MINIMUM
        )
        expect(result.isCompliant).toBe(true)
      }
    })
  })

  describe('error reporting and debugging', () => {
    it('should provide detailed violation information when compliance fails', () => {
      // Create a component we know will fail (for testing error reporting)
      function NonCompliantButton() {
        return (
          <button
            style={{
              width: '40px',
              height: '40px',
              minWidth: '40px',
              minHeight: '40px',
            }}
          >
            Too Small
          </button>
        )
      }

      const { container } = render(<NonCompliantButton />)

      const result = validateTouchTargetCompliance(
        container,
        TOUCH_TARGET_MINIMUM
      )

      expect(result.isCompliant).toBe(false)
      expect(result.violations).toHaveLength(1)
      expect(result.complianceRate).toBe(0)

      const violation = result.violations[0]
      expect(violation.expectedMinimum).toBe(TOUCH_TARGET_MINIMUM)
      expect(violation.measurement.meetsMinimum).toBe(false)
      expect(violation.measurement.width).toBe(40)
      expect(violation.measurement.height).toBe(40)
    })
  })

  describe('accessibility integration', () => {
    it('should ensure touch targets work with assistive technologies', () => {
      const { container } = render(
        <div>
          <Button aria-label="Accessible button">Icon Only</Button>
          <Button size="sm" aria-describedby="help-text">
            Help Button
          </Button>
          <p id="help-text">This button provides help</p>
        </div>
      )

      const result = validateTouchTargetCompliance(
        container,
        TOUCH_TARGET_MINIMUM
      )

      expect(result.isCompliant).toBe(true)

      // Verify accessibility attributes are preserved
      const buttons = screen.getAllByRole('button')
      expect(buttons[0]).toHaveAttribute('aria-label')
      expect(buttons[1]).toHaveAttribute('aria-describedby')
    })
  })
})

/**
 * Test Rationale:
 *
 * 1. Integration test WILL FAIL initially because:
 *    - Button sm/md sizes are currently < 52px
 *    - SetCellArrowButtons use min-h-[44px]
 *
 * 2. System-wide validation ensures no component regressions
 *    as individual fixes are implemented
 *
 * 3. Multiple viewport testing validates responsive behavior
 *    doesn't break touch target compliance
 *
 * 4. Error reporting tests ensure debugging capabilities
 *    when violations occur in development
 *
 * 5. Accessibility integration ensures compliance doesn't
 *    break assistive technology compatibility
 *
 * Expected Failures:
 * - Button size variants below 52px
 * - Arrow buttons with 44px minimums
 * - Any hardcoded legacy touch target sizes
 *
 * Expected Coverage: 90%+ of critical touch target scenarios
 * Test Reliability: High - Uses actual DOM measurement
 * Maintenance: Medium - May need updates as components change
 * Business Impact: Critical - Prevents system-wide compliance failures
 */
