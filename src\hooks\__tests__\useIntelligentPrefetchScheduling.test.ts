import { renderHook, act } from '@testing-library/react'
import { vi } from 'vitest'
import { useIntelligentPrefetchScheduling } from '../useIntelligentPrefetchScheduling'

describe('useIntelligentPrefetchScheduling', () => {
  const mockPrefetchExerciseSets = vi.fn()
  const defaultProps = {
    exercises: [
      { Id: 1, Label: 'Exercise 1' },
      { Id: 2, Label: 'Exercise 2' },
      { Id: 3, Label: 'Exercise 3' },
      { Id: 4, Label: 'Exercise 4' },
      { Id: 5, Label: 'Exercise 5' },
    ],
    currentExerciseIndex: 0,
    workoutSession: { startTime: Date.now() - 60000 }, // 1 minute ago
    prefetchedExerciseIds: [],
    previewExerciseSkips: new Set(),
    prefetchExerciseSets: mockPrefetchExerciseSets,
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it('should calculate user pace and schedule prefetch accordingly', () => {
    const { result } = renderHook(() =>
      useIntelligentPrefetchScheduling({
        ...defaultProps,
        currentExerciseIndex: 2, // User is on exercise 3 after 1 minute
      })
    )

    expect(result.current.userPace).toBeGreaterThan(0)
    expect(result.current.schedulingActive).toBe(true)
  })

  it('should prefetch more exercises for fast users', async () => {
    const { result } = renderHook(() =>
      useIntelligentPrefetchScheduling({
        ...defaultProps,
        currentExerciseIndex: 3, // Very fast user - 3 exercises in 1 minute
        workoutSession: { startTime: Date.now() - 60000 },
      })
    )

    await act(async () => {
      // Fast users should get more exercises prefetched
      result.current.schedulePrefetch()
    })

    expect(mockPrefetchExerciseSets).toHaveBeenCalledWith([5])
  })

  it('should prefetch fewer exercises for slow users', async () => {
    const { result } = renderHook(() =>
      useIntelligentPrefetchScheduling({
        ...defaultProps,
        currentExerciseIndex: 1, // Slow user - 1 exercise in 1 minute
        workoutSession: { startTime: Date.now() - 300000 }, // 5 minutes ago
      })
    )

    await act(async () => {
      result.current.schedulePrefetch()
    })

    expect(mockPrefetchExerciseSets).toHaveBeenCalledWith([3, 4])
  })

  it('should adjust timing based on user pace', () => {
    const { result } = renderHook(() =>
      useIntelligentPrefetchScheduling({
        ...defaultProps,
        currentExerciseIndex: 2,
        workoutSession: { startTime: Date.now() - 120000 }, // 2 minutes ago
      })
    )

    // Should calculate when to prefetch next based on pace
    expect(result.current.nextPrefetchTiming).toBeGreaterThan(0)
    expect(result.current.userPace).toBe(1) // 2 exercises in 2 minutes = 1 per minute
  })

  it('should handle workout start correctly', () => {
    const { result } = renderHook(() =>
      useIntelligentPrefetchScheduling({
        ...defaultProps,
        currentExerciseIndex: 0,
        workoutSession: { startTime: Date.now() - 5000 }, // Just started
      })
    )

    expect(result.current.userPace).toBe(0)
    expect(result.current.nextPrefetchTiming).toBeUndefined()
  })

  it('should respect already prefetched exercises', async () => {
    const { result } = renderHook(() =>
      useIntelligentPrefetchScheduling({
        ...defaultProps,
        currentExerciseIndex: 1,
        prefetchedExerciseIds: [2, 3], // Already prefetched
      })
    )

    await act(async () => {
      result.current.schedulePrefetch()
    })

    expect(mockPrefetchExerciseSets).toHaveBeenCalledWith([4, 5])
  })

  it('should respect skipped exercises', async () => {
    const { result } = renderHook(() =>
      useIntelligentPrefetchScheduling({
        ...defaultProps,
        currentExerciseIndex: 1,
        previewExerciseSkips: new Set([3]), // Exercise 3 is skipped
      })
    )

    await act(async () => {
      result.current.schedulePrefetch()
    })

    expect(mockPrefetchExerciseSets).toHaveBeenCalledWith([4, 5])
  })

  it('should provide automatic scheduling with intervals', async () => {
    const { result } = renderHook(() =>
      useIntelligentPrefetchScheduling({
        ...defaultProps,
        currentExerciseIndex: 1,
        workoutSession: { startTime: Date.now() - 60000 },
        autoSchedule: true,
      })
    )

    expect(result.current.schedulingActive).toBe(true)

    // Manually trigger scheduling to verify it works
    await act(async () => {
      await result.current.schedulePrefetch()
    })

    expect(mockPrefetchExerciseSets).toHaveBeenCalled()
  })

  it('should handle edge cases gracefully', () => {
    const { result } = renderHook(() =>
      useIntelligentPrefetchScheduling({
        ...defaultProps,
        exercises: [], // No exercises
        currentExerciseIndex: 0,
        workoutSession: null, // No workout session
      })
    )

    expect(result.current.userPace).toBe(0)
    expect(result.current.schedulingActive).toBe(false)
  })

  it('should calculate optimal prefetch window based on device performance', () => {
    // Mock slow device
    Object.defineProperty(navigator, 'hardwareConcurrency', { value: 2 })
    Object.defineProperty(navigator, 'deviceMemory', { value: 2 })

    const { result } = renderHook(() =>
      useIntelligentPrefetchScheduling({
        ...defaultProps,
        currentExerciseIndex: 1,
      })
    )

    expect(result.current.maxPrefetchCount).toBeLessThanOrEqual(3)
  })
})
