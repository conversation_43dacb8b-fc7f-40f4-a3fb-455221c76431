import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { RestTimer } from '../RestTimer'
import { useWorkoutStore } from '@/stores/workoutStore'

// Mock the workout store
vi.mock('@/stores/workoutStore')
const mockUseWorkoutStore = vi.mocked(useWorkoutStore)

// Mock haptics
vi.mock('@/utils/haptics', () => ({
  vibrate: vi.fn(),
}))

// Mock framer-motion to render immediately without animations
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => children,
}))

// Mock notifications
Object.defineProperty(window, 'Notification', {
  value: vi.fn().mockImplementation(() => ({
    close: vi.fn(),
  })),
  configurable: true,
})

Object.defineProperty(window.Notification, 'permission', {
  value: 'granted',
  configurable: true,
})

describe('RestTimer Two-Row Layout', () => {
  const mockSetRestTimerState = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    mockUseWorkoutStore.mockReturnValue({
      restTimerState: {
        isActive: true,
        duration: 90,
        nextSetInfo: {
          reps: 8,
          weight: 135,
          unit: 'lbs',
        },
      },
      setRestTimerState: mockSetRestTimerState,
    } as any)
  })

  it('displays timer in first row with extra large font', async () => {
    render(<RestTimer />)

    await waitFor(() => {
      expect(screen.getByTestId('rest-timer-container')).toBeInTheDocument()
    })

    // First row should contain timer
    const firstRow = screen.getByTestId('timer-row')
    expect(firstRow).toBeInTheDocument()

    // Timer should be in first row with larger font
    const timeDisplay = screen.getByTestId('time-display')
    expect(firstRow).toContainElement(timeDisplay)
    expect(timeDisplay).toHaveTextContent('1:30')

    // Should have extra large font (7xl or 8xl)
    expect(timeDisplay).toHaveClass('text-7xl')
  })

  it('displays buttons in second row with larger touch targets', async () => {
    render(<RestTimer />)

    await waitFor(() => {
      expect(screen.getByTestId('rest-timer-container')).toBeInTheDocument()
    })

    // Second row should contain buttons
    const buttonsRow = screen.getByTestId('buttons-row')
    expect(buttonsRow).toBeInTheDocument()

    // Buttons should be in second row
    const durationButton = screen.getByTestId('duration-setting-button')
    const hideButton = screen.getByRole('button', { name: /hide/i })

    expect(buttonsRow).toContainElement(durationButton)
    expect(buttonsRow).toContainElement(hideButton)

    // Buttons should have larger size
    expect(durationButton).toHaveClass('text-base', 'px-6', 'py-3')
    expect(hideButton).toHaveClass('text-base', 'px-6', 'py-3')
  })

  it('centers timer in first row', async () => {
    render(<RestTimer />)

    await waitFor(() => {
      expect(screen.getByTestId('rest-timer-container')).toBeInTheDocument()
    })

    const firstRow = screen.getByTestId('timer-row')
    expect(firstRow).toHaveClass('justify-center', 'text-center')
  })

  it('evenly spaces buttons in second row', async () => {
    render(<RestTimer />)

    await waitFor(() => {
      expect(screen.getByTestId('rest-timer-container')).toBeInTheDocument()
    })

    const buttonsRow = screen.getByTestId('buttons-row')
    expect(buttonsRow).toHaveClass('justify-evenly')
  })

  it('shows Rest label below timer', async () => {
    render(<RestTimer />)

    await waitFor(() => {
      expect(screen.getByTestId('rest-timer-container')).toBeInTheDocument()
    })

    // Should show Rest label
    expect(screen.getByText('Rest')).toBeInTheDocument()
  })

  it('maintains button functionality with new layout', async () => {
    const user = userEvent.setup()
    render(<RestTimer />)

    await waitFor(() => {
      expect(screen.getByTestId('rest-timer-container')).toBeInTheDocument()
    })

    // Test duration button click
    const durationButton = screen.getByTestId('duration-setting-button')
    await user.click(durationButton)

    // Should open duration picker
    await waitFor(() => {
      expect(screen.getByText('Rest Duration')).toBeInTheDocument()
    })
  })

  it('maintains progress bar in bottom of container', async () => {
    render(<RestTimer />)

    await waitFor(() => {
      expect(screen.getByTestId('rest-timer-container')).toBeInTheDocument()
    })

    const progressBar = screen.getByTestId('rest-timer-progress')
    expect(progressBar).toBeInTheDocument()

    // Progress bar should still have gold gradient
    expect(progressBar).toHaveClass(
      'bg-gradient-to-r',
      'from-brand-gold-start',
      'to-brand-gold-end'
    )
  })
})
