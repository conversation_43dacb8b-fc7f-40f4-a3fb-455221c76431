import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { FirebaseOAuthHelper } from '../firebaseOAuth-refactored'
import { signInWithPopup, signInWithRedirect } from 'firebase/auth'

vi.mock('firebase/auth', () => ({
  signInWithPopup: vi.fn(),
  signInWithRedirect: vi.fn(),
  getRedirectResult: vi.fn(),
  GoogleAuthProvider: vi.fn(),
  OAuthProvider: vi.fn(),
}))

vi.mock('@/config/firebase', () => ({
  getFirebaseAuth: vi.fn(() => ({ currentUser: null })),
}))

vi.mock('../providers/firebaseProviders', () => ({
  FirebaseProviders: {
    initialize: vi.fn(),
    isReady: vi.fn(() => true),
    getGoogleProvider: vi.fn(() => ({})),
    getAppleProvider: vi.fn(() => ({})),
  },
}))

vi.mock('../handlers/authHandlers', () => ({
  AuthHandlers: {
    handleAuthSuccess: vi.fn(),
    handleAuthError: vi.fn(),
  },
}))

describe('Safari 16.1+ Storage Blocking Fix', () => {
  const originalUserAgent = window.navigator.userAgent

  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    // Restore original user agent
    Object.defineProperty(window.navigator, 'userAgent', {
      value: originalUserAgent,
      writable: true,
    })
  })

  describe('Safari Version Detection', () => {
    it('should detect Safari 16.1 and force popup', async () => {
      // Mock Safari 16.1 user agent
      Object.defineProperty(window.navigator, 'userAgent', {
        value:
          'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Safari/605.1.15',
        writable: true,
      })

      vi.mocked(signInWithPopup).mockResolvedValue({
        user: { uid: 'test-uid' },
      } as any)

      await FirebaseOAuthHelper.signInWithGoogle(
        vi.fn(),
        vi.fn(),
        false // Even with usePopup=false, Safari 16.1+ should force popup
      )

      // Should use popup, not redirect
      expect(signInWithPopup).toHaveBeenCalled()
      expect(signInWithRedirect).not.toHaveBeenCalled()
    })

    it('should detect Safari 17 and force popup', async () => {
      // Mock Safari 17 user agent
      Object.defineProperty(window.navigator, 'userAgent', {
        value:
          'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
        writable: true,
      })

      vi.mocked(signInWithPopup).mockResolvedValue({
        user: { uid: 'test-uid' },
      } as any)

      await FirebaseOAuthHelper.signInWithGoogle(vi.fn(), vi.fn(), false)

      expect(signInWithPopup).toHaveBeenCalled()
      expect(signInWithRedirect).not.toHaveBeenCalled()
    })

    it('should not force popup for Safari 16.0', async () => {
      // Mock Safari 16.0 user agent (before third-party storage blocking)
      Object.defineProperty(window.navigator, 'userAgent', {
        value:
          'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15',
        writable: true,
      })

      await FirebaseOAuthHelper.signInWithGoogle(vi.fn(), vi.fn(), false)

      // Should use redirect for older Safari
      expect(signInWithRedirect).toHaveBeenCalled()
      expect(signInWithPopup).not.toHaveBeenCalled()
    })

    it('should not force popup for Chrome', async () => {
      // Mock Chrome user agent
      Object.defineProperty(window.navigator, 'userAgent', {
        value:
          'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        writable: true,
      })

      vi.mocked(signInWithPopup).mockResolvedValue({
        user: { uid: 'test-uid' },
      } as any)

      await FirebaseOAuthHelper.signInWithGoogle(vi.fn(), vi.fn(), true)

      // Should respect usePopup parameter for non-Safari
      expect(signInWithPopup).toHaveBeenCalled()
      expect(signInWithRedirect).not.toHaveBeenCalled()
    })
  })

  describe('Popup Blocking on Safari 16.1+', () => {
    it('should show Safari-specific error when popup is blocked on Safari 16.1+', async () => {
      // Mock Safari 16.1
      Object.defineProperty(window.navigator, 'userAgent', {
        value:
          'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Safari/605.1.15',
        writable: true,
      })

      // Import AuthHandlers to verify the error is passed correctly
      const { AuthHandlers } = await import('../handlers/authHandlers')

      // Mock popup being blocked
      vi.mocked(signInWithPopup).mockRejectedValue({
        code: 'auth/popup-blocked',
      })

      const onError = vi.fn()
      const onSuccess = vi.fn()

      await FirebaseOAuthHelper.signInWithGoogle(onSuccess, onError)

      // Should not fall back to redirect
      expect(signInWithRedirect).not.toHaveBeenCalled()

      // Should call AuthHandlers.handleAuthError with the Safari-specific error
      expect(AuthHandlers.handleAuthError).toHaveBeenCalledWith(
        expect.objectContaining({
          code: 'auth/popup-required-safari',
          message: expect.stringContaining('Safari requires popups'),
        }),
        'google',
        onError
      )
    })

    it('should fallback to redirect on other browsers when popup is blocked', async () => {
      // Mock Chrome
      Object.defineProperty(window.navigator, 'userAgent', {
        value:
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        writable: true,
      })

      // Mock popup being blocked
      vi.mocked(signInWithPopup).mockRejectedValue({
        code: 'auth/popup-blocked',
      })

      await FirebaseOAuthHelper.signInWithGoogle(vi.fn(), vi.fn())

      // Should fallback to redirect for non-Safari
      expect(signInWithRedirect).toHaveBeenCalled()
    })
  })

  describe('Apple Sign-In', () => {
    it('should also force popup for Apple Sign-In on Safari 16.1+', async () => {
      // Mock Safari 16.1
      Object.defineProperty(window.navigator, 'userAgent', {
        value:
          'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Safari/605.1.15',
        writable: true,
      })

      vi.mocked(signInWithPopup).mockResolvedValue({
        user: { uid: 'test-uid' },
      } as any)

      await FirebaseOAuthHelper.signInWithApple(vi.fn(), vi.fn(), false)

      // Should force popup even with usePopup=false
      expect(signInWithPopup).toHaveBeenCalled()
      expect(signInWithRedirect).not.toHaveBeenCalled()
    })
  })
})
