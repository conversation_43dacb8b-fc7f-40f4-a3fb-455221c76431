'use client'

import { motion } from 'framer-motion'
import { SkipFor<PERSON>, Clock } from 'lucide-react'

interface RestTimerDisplayProps {
  timeRemaining: number
  duration: number
  onDurationClick: () => void
  onSkip: () => void
  formatTime: (seconds: number) => string
}

export function RestTimerDisplay({
  timeRemaining,
  duration,
  onDurationClick,
  onSkip,
  formatTime,
}: RestTimerDisplayProps) {
  const progress = (timeRemaining / duration) * 100

  return (
    <motion.div
      initial={{ y: 100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      exit={{ y: 100, opacity: 0 }}
      data-testid="rest-timer-container"
      className="fixed bottom-0 left-0 right-0 bg-surface-primary/90 backdrop-blur-sm shadow-lg border-t border-surface-tertiary"
    >
      <div className="px-4 py-4">
        <div className="flex flex-col gap-3">
          {/* First row: Large time display only */}
          <div
            data-testid="timer-row"
            className="flex justify-center items-center"
          >
            <div
              data-testid="time-display"
              className="text-7xl font-bold bg-gradient-to-r from-brand-gold-start to-brand-gold-end bg-clip-text text-transparent leading-none"
            >
              {formatTime(timeRemaining)}
            </div>
          </div>

          {/* Second row: Buttons with Rest text in center */}
          <div
            data-testid="buttons-row"
            className="flex justify-center items-center gap-4"
          >
            <button
              onClick={onDurationClick}
              data-testid="duration-setting-button"
              className="flex items-center gap-2 px-8 py-3 bg-gradient-to-r from-brand-gold-start to-brand-gold-end 
                     text-text-inverse rounded-full font-medium text-base
                     hover:opacity-90 transition-all
                     min-h-[52px] touch-manipulation"
            >
              <Clock className="w-5 h-5" />
              <span data-testid="duration-setting">{formatTime(duration)}</span>
            </button>

            <div className="text-xl text-text-secondary font-medium">Rest</div>

            <button
              onClick={onSkip}
              className="flex items-center gap-2 px-8 py-3 bg-gradient-to-r from-brand-gold-start to-brand-gold-end 
                       text-text-inverse rounded-full font-medium text-base
                       hover:opacity-90 transition-all
                       min-h-[52px] touch-manipulation"
            >
              <SkipForward className="w-5 h-5" />
              Hide
            </button>
          </div>

          {/* Progress bar at bottom */}
          <div className="h-2 bg-surface-secondary rounded-full overflow-hidden">
            <motion.div
              data-testid="rest-timer-progress"
              className="h-full bg-gradient-to-r from-brand-gold-start to-brand-gold-end"
              initial={{ width: '100%' }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.3 }}
            />
          </div>
        </div>
      </div>
    </motion.div>
  )
}
