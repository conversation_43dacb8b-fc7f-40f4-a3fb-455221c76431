import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import {
  calculateWeekStreakFromDates,
  calculateStatsFromWorkoutHistory,
  type WorkoutDate,
} from '../calculator'

describe('calculateWeekStreakFromDates', () => {
  beforeEach(() => {
    // Mock current date to ensure consistent test results
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  describe('7-day period calculation', () => {
    it('should count workouts within 7 days as 1 week', () => {
      // Set current date to Jan 15, 2024
      vi.setSystemTime(new Date('2024-01-15T12:00:00Z'))

      // Workouts within 7 days of each other
      const dates = [
        new Date('2024-01-10T10:00:00Z'), // 5 days ago
        new Date('2024-01-12T10:00:00Z'), // 3 days ago
        new Date('2024-01-15T10:00:00Z'), // today
      ]

      const result = calculateWeekStreakFromDates(dates)
      expect(result).toBe(1) // All within one 7-day period
    })

    it('should count consecutive 7-day periods correctly', () => {
      // Set current date to Jan 15, 2024
      vi.setSystemTime(new Date('2024-01-15T12:00:00Z'))

      // Workouts in consecutive 7-day periods
      const dates = [
        new Date('2024-01-01T10:00:00Z'), // 14 days ago
        new Date('2024-01-08T10:00:00Z'), // 7 days ago
        new Date('2024-01-15T10:00:00Z'), // today
      ]

      const result = calculateWeekStreakFromDates(dates)
      expect(result).toBe(3) // Three consecutive 7-day periods
    })

    it('should break streak when more than 7 days between workouts', () => {
      // Set current date to Jan 22, 2024
      vi.setSystemTime(new Date('2024-01-22T12:00:00Z'))

      const dates = [
        new Date('2024-01-01T10:00:00Z'), // 21 days ago
        new Date('2024-01-08T10:00:00Z'), // 14 days ago
        // Gap of 14 days - breaks streak
        new Date('2024-01-22T10:00:00Z'), // today
      ]

      const result = calculateWeekStreakFromDates(dates)
      expect(result).toBe(1) // Only current period counts due to gap
    })

    it('should handle workouts exactly 7 days apart', () => {
      // Set current date to Jan 21, 2024
      vi.setSystemTime(new Date('2024-01-21T12:00:00Z'))

      const dates = [
        new Date('2024-01-07T10:00:00Z'), // 14 days ago
        new Date('2024-01-14T10:00:00Z'), // 7 days ago
        new Date('2024-01-21T10:00:00Z'), // today
      ]

      const result = calculateWeekStreakFromDates(dates)
      expect(result).toBe(3) // Three consecutive weeks
    })

    it('should count multiple workouts in same period as 1 week', () => {
      // Set current date to Jan 7, 2024
      vi.setSystemTime(new Date('2024-01-07T12:00:00Z'))

      const dates = [
        new Date('2024-01-01T10:00:00Z'), // Monday
        new Date('2024-01-03T10:00:00Z'), // Wednesday
        new Date('2024-01-05T10:00:00Z'), // Friday
        new Date('2024-01-07T10:00:00Z'), // Sunday
      ]

      const result = calculateWeekStreakFromDates(dates)
      expect(result).toBe(1) // All in the same 7-day period
    })

    it('should handle Sunday and Monday as potentially different periods', () => {
      // Set current date to Jan 8, 2024
      vi.setSystemTime(new Date('2024-01-08T12:00:00Z'))

      // Workouts 8 days apart
      const dates = [
        new Date('2023-12-31T10:00:00Z'), // 8 days ago (Sunday)
        new Date('2024-01-08T10:00:00Z'), // today (Monday)
      ]

      const result = calculateWeekStreakFromDates(dates)
      expect(result).toBe(2) // Two different 7-day periods
    })

    it('should return 0 if last workout was more than 7 days ago', () => {
      // Set current date to Jan 20, 2024
      vi.setSystemTime(new Date('2024-01-20T12:00:00Z'))

      const dates = [
        new Date('2024-01-01T10:00:00Z'), // 19 days ago
      ]

      const result = calculateWeekStreakFromDates(dates)
      expect(result).toBe(0) // Streak is broken
    })

    it('should handle empty array', () => {
      const result = calculateWeekStreakFromDates([])
      expect(result).toBe(0)
    })

    it('should handle null/undefined', () => {
      const result = calculateWeekStreakFromDates(null as any)
      expect(result).toBe(0)
    })

    it('should handle complex workout pattern', () => {
      // Set current date to Feb 1, 2024
      vi.setSystemTime(new Date('2024-02-01T12:00:00Z'))

      const dates = [
        // Period 4: Jan 26 - Feb 1 (current)
        new Date('2024-01-28T10:00:00Z'),
        new Date('2024-02-01T10:00:00Z'),
        // Period 3: Jan 19-25
        new Date('2024-01-20T10:00:00Z'),
        new Date('2024-01-23T10:00:00Z'),
        // Period 2: Jan 12-18
        new Date('2024-01-15T10:00:00Z'),
        // Period 1: Jan 5-11
        new Date('2024-01-06T10:00:00Z'),
        new Date('2024-01-10T10:00:00Z'),
      ]

      const result = calculateWeekStreakFromDates(dates)
      expect(result).toBe(4) // Four consecutive 7-day periods
    })
  })
})

describe('calculateStatsFromWorkoutHistory', () => {
  beforeEach(() => {
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it('should use 7-day period calculation for weeks count', () => {
    // Set current date to Jan 12, 2024
    vi.setSystemTime(new Date('2024-01-12T12:00:00Z'))

    const workoutDates: WorkoutDate[] = [
      {
        Date: '2024-01-08T10:00:00Z', // 4 days ago
        Sets: [{ Weight: { Lb: 100, Kg: 45 }, Reps: 10, IsWarmups: false }],
      },
      {
        Date: '2024-01-12T10:00:00Z', // today
        Sets: [{ Weight: { Lb: 100, Kg: 45 }, Reps: 10, IsWarmups: false }],
      },
    ]

    const result = calculateStatsFromWorkoutHistory(workoutDates)
    expect(result.weeks).toBe(1) // Both in same 7-day period
    expect(result.workouts).toBe(2)
    expect(result.volume).toBe(2000) // 2 sets × 100 lbs × 10 reps
  })

  it('should calculate consecutive 7-day periods correctly', () => {
    // Set current date to Jan 15, 2024
    vi.setSystemTime(new Date('2024-01-15T12:00:00Z'))

    const workoutDates: WorkoutDate[] = [
      { Date: '2024-01-01T10:00:00Z', Sets: [] }, // 14 days ago
      { Date: '2024-01-08T10:00:00Z', Sets: [] }, // 7 days ago
      { Date: '2024-01-15T10:00:00Z', Sets: [] }, // today
    ]

    const result = calculateStatsFromWorkoutHistory(workoutDates)
    expect(result.weeks).toBe(3) // Three consecutive 7-day periods
  })

  it('should handle broken streak in workout history', () => {
    // Set current date to Jan 22, 2024
    vi.setSystemTime(new Date('2024-01-22T12:00:00Z'))

    const workoutDates: WorkoutDate[] = [
      { Date: '2024-01-01T10:00:00Z', Sets: [] }, // 21 days ago
      // Gap of more than 7 days
      { Date: '2024-01-22T10:00:00Z', Sets: [] }, // today
    ]

    const result = calculateStatsFromWorkoutHistory(workoutDates)
    expect(result.weeks).toBe(1) // Only current period due to gap
  })
})
