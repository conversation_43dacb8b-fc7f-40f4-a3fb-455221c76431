/**
 * E2E tests for background refresh of exercise recommendations
 *
 * Test Rationale:
 * - Verify end-to-end background refresh workflow
 * - Test real browser visibility API integration
 * - Ensure StatusIndicators show correct states during refresh
 * - Test network error recovery scenarios
 * - Verify mobile-specific behavior (Safari, Chrome)
 */

import { test, expect, Page } from '@playwright/test'

test.describe('Background Refresh Exercise Recommendations', () => {
  const TEST_USER = {
    email: '<EMAIL>',
    password: 'Dr123456',
  }

  async function loginAndNavigateToWorkout(page: Page) {
    // Login
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    // Navigate to workout
    await page.click('button:has-text("Continue to workout")')
    await page.waitForURL('/workout')
  }

  async function simulateAppBackground(page: Page) {
    await page.evaluate(() => {
      Object.defineProperty(document, 'visibilityState', {
        configurable: true,
        get: () => 'hidden',
      })
      document.dispatchEvent(new Event('visibilitychange'))
      window.dispatchEvent(new Event('blur'))
    })
  }

  async function simulateAppForeground(page: Page) {
    await page.evaluate(() => {
      Object.defineProperty(document, 'visibilityState', {
        configurable: true,
        get: () => 'visible',
      })
      document.dispatchEvent(new Event('visibilitychange'))
      window.dispatchEvent(new Event('focus'))
    })
  }

  test.beforeEach(async () => {
    // Increase timeout for setup
    test.setTimeout(60000)
  })

  test('should refresh exercise recommendations when returning from background', async ({
    page,
  }) => {
    // Given: User is logged in and has workout loaded
    await loginAndNavigateToWorkout(page)

    // Wait for initial prefetch to complete and status indicators to show
    await page.waitForSelector('[data-testid="status-indicators-container"]', {
      timeout: 15000,
    })

    // Verify initial state shows prefetched exercises
    const initialStatus = page.locator(
      '[data-testid="status-indicators-container"]'
    )
    await expect(initialStatus).toContainText('Prefetched:') // Should show prefetched count

    // When: App goes to background
    await simulateAppBackground(page)
    await page.waitForTimeout(1000) // Allow background handler to run

    // Simulate being in background for 30 seconds (adjust for test speed)
    await page.waitForTimeout(5000)

    // And: App returns to foreground
    await simulateAppForeground(page)

    // Then: Should see refreshing indicator
    const refreshingIndicator = page.locator('text=Refreshing exercises')
    await expect(refreshingIndicator).toBeVisible({ timeout: 5000 })

    // And: Should complete successfully within reasonable time
    const completedIndicator = page.locator('text=exercises ready')
    await expect(completedIndicator).toBeVisible({ timeout: 20000 })

    // Verify no error states
    const errorIndicator = page.locator('text=failed')
    await expect(errorIndicator).not.toBeVisible()
  })

  test('should handle network failures during background refresh gracefully', async ({
    page,
  }) => {
    // Given: User has workout with prefetched exercises
    await loginAndNavigateToWorkout(page)
    await page.waitForSelector('[data-testid="status-indicators-container"]')

    // When: Network fails during background refresh
    await page.route('**/api/exercise/recommendation/**', (route) => {
      route.abort('failed')
    })

    // Simulate background/foreground to trigger refresh
    await simulateAppBackground(page)
    await page.waitForTimeout(1000)
    await simulateAppForeground(page)

    // Then: Should show error state appropriately
    const errorIndicator = page.locator('text=failed')
    await expect(errorIndicator).toBeVisible({ timeout: 10000 })

    // When: Network recovers
    await page.unroute('**/api/exercise/recommendation/**')

    // And: User triggers retry (if retry mechanism is available)
    // This tests the recovery mechanism
    await simulateAppBackground(page)
    await page.waitForTimeout(1000)
    await simulateAppForeground(page)

    // Then: Should eventually recover (depending on retry implementation)
    // This test verifies error handling doesn't break the system
    const statusContainer = page.locator(
      '[data-testid="status-indicators-container"]'
    )
    await expect(statusContainer).toBeVisible({ timeout: 5000 })
  })

  test('should work with rapid background/foreground transitions', async ({
    page,
  }) => {
    // Given: User has workout loaded
    await loginAndNavigateToWorkout(page)
    await page.waitForSelector('[data-testid="status-indicators-container"]')

    // When: Rapid background/foreground transitions occur
    const transitions = []
    for (let i = 0; i < 5; i++) {
      transitions.push(
        simulateAppBackground(page).then(() => page.waitForTimeout(100))
      )
      transitions.push(
        simulateAppForeground(page).then(() => page.waitForTimeout(100))
      )
    }
    await Promise.all(transitions)

    // Then: Should not cause errors or excessive network calls
    // The system should handle this gracefully with debouncing
    const statusContainer = page.locator(
      '[data-testid="status-indicators-container"]'
    )
    await expect(statusContainer).toBeVisible()

    // Should not show multiple simultaneous refreshing states
    const refreshingIndicators = page.locator('text=Refreshing exercises')
    const count = await refreshingIndicators.count()
    expect(count).toBeLessThanOrEqual(1) // At most one refreshing indicator
  })

  test('should maintain workout state during background refresh', async ({
    page,
  }) => {
    // Given: User starts a workout and is on an exercise
    await loginAndNavigateToWorkout(page)
    await page.click('button:has-text("Start Workout")')

    // Wait for exercise page to load
    await page.waitForSelector('[data-testid="save-set-button"]', {
      timeout: 15000,
    })

    // Capture initial workout state
    const initialUrl = page.url()

    // When: App goes to background and returns
    await simulateAppBackground(page)
    await page.waitForTimeout(2000)
    await simulateAppForeground(page)

    // Then: Workout state should be preserved
    expect(page.url()).toBe(initialUrl) // Still on same exercise

    // Wait for any background refresh to complete
    await page.waitForTimeout(3000)

    // Verify exercise page is still functional
    const saveButton = page.locator('[data-testid="save-set-button"]')
    await expect(saveButton).toBeVisible()

    // Weight input should still be accessible (may have been refreshed)
    const weightInput = page.locator('input[placeholder*="weight"]')
    await expect(weightInput).toBeVisible()
  })

  test('should handle auth token refresh during background refresh', async ({
    page,
  }) => {
    // Given: User has workout loaded
    await loginAndNavigateToWorkout(page)
    await page.waitForSelector('[data-testid="status-indicators-container"]')

    // When: Auth token expires during background refresh
    // Simulate 401 response followed by successful token refresh
    let callCount = 0
    await page.route('**/api/exercise/recommendation/**', (route) => {
      callCount++
      if (callCount === 1) {
        // First call fails with 401
        route.fulfill({
          status: 401,
          body: JSON.stringify({ error: 'Unauthorized' }),
        })
      } else {
        // Subsequent calls succeed (after token refresh)
        route.fulfill({
          status: 200,
          body: JSON.stringify({
            Id: 1,
            WeightIncrement: 5,
            Weight: { Kg: 100, Lb: 220 },
            Reps: 8,
          }),
        })
      }
    })

    // Trigger background refresh
    await simulateAppBackground(page)
    await page.waitForTimeout(1000)
    await simulateAppForeground(page)

    // Then: Should handle auth error and retry successfully
    // (The exact behavior depends on auth error handling implementation)
    await page.waitForTimeout(5000)

    // Should not redirect to login page if token refresh works
    expect(page.url()).not.toContain('/login')

    // Should eventually show success state
    const statusContainer = page.locator(
      '[data-testid="status-indicators-container"]'
    )
    await expect(statusContainer).toBeVisible()

    // Clean up route
    await page.unroute('**/api/exercise/recommendation/**')
  })

  test('should display correct progress during multi-exercise refresh', async ({
    page,
  }) => {
    // Given: User has workout with multiple exercises
    await loginAndNavigateToWorkout(page)
    await page.waitForSelector('[data-testid="status-indicators-container"]')

    // When: Background refresh starts
    await simulateAppBackground(page)
    await page.waitForTimeout(1000)
    await simulateAppForeground(page)

    // Then: Should show progress indicators
    const statusContainer = page.locator(
      '[data-testid="status-indicators-container"]'
    )

    // Look for progress indication (percentage or count)
    const progressText = statusContainer.locator('text=/\\d+%|\\d+ exercise/')
    await expect(progressText).toBeVisible({ timeout: 10000 })

    // Wait for completion
    await expect(statusContainer.locator('text=ready')).toBeVisible({
      timeout: 20000,
    })
  })
})

test.describe('Background Refresh - Mobile Specific', () => {
  test('should work on mobile Safari viewport', async ({ page }) => {
    // Set mobile Safari viewport
    await page.setViewportSize({ width: 375, height: 667 })
    await page.setUserAgent(
      'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15'
    )

    const TEST_USER = {
      email: '<EMAIL>',
      password: 'Dr123456',
    }

    // Login and navigate
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    await page.click('button:has-text("Continue to workout")')
    await page.waitForURL('/workout')

    // Test mobile-specific visibility change
    await page.evaluate(() => {
      // Mobile Safari specific visibility change simulation
      Object.defineProperty(document, 'visibilityState', {
        configurable: true,
        get: () => 'hidden',
      })
      document.dispatchEvent(new Event('visibilitychange'))
    })

    await page.waitForTimeout(1000)

    await page.evaluate(() => {
      Object.defineProperty(document, 'visibilityState', {
        configurable: true,
        get: () => 'visible',
      })
      document.dispatchEvent(new Event('visibilitychange'))
    })

    // Should work on mobile viewport
    const statusContainer = page.locator(
      '[data-testid="status-indicators-container"]'
    )
    await expect(statusContainer).toBeVisible({ timeout: 10000 })
  })
})
