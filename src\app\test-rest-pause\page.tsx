'use client'

import { generateAllSets } from '@/utils/generateAllSets'
import type { RecommendationModel } from '@/types'

export default function TestRestPausePage() {
  const mockRecommendation: RecommendationModel = {
    ExerciseId: 123,
    Series: 1,
    Reps: 12,
    Weight: { Lb: 135, Kg: 61.2 },
    NbPauses: 3,
    NbRepsPauses: 8,
    RpRest: 15,
    IsNormalSets: false,
    IsPyramid: false,
    IsReversePyramid: false,
    WarmupsCount: 2,
    WarmUpReps1: 5,
    WarmUpReps2: 3,
    WarmUpWeightSet1: { Lb: 95, Kg: 43.1 },
    WarmUpWeightSet2: { Lb: 115, Kg: 52.2 },
    WarmUpsList: [],
    OneRMProgress: 0,
    RecommendationInKg: 61.2,
    OneRMPercentage: 75,
    IsBodyweight: false,
    IsEasy: false,
    IsMedium: true,
    Increments: { Lb: 2.5, Kg: 1 },
    Max: { Lb: 500, Kg: 227 },
    Min: { Lb: 45, Kg: 20 },
    IsDeload: false,
    IsBackOffSet: false,
    BackOffSetWeight: { Lb: 115, Kg: 52.2 },
    IsMaxChallenge: false,
    IsLightSession: false,
    FirstWorkSetReps: 12,
    FirstWorkSetWeight: { Lb: 135, Kg: 61.2 },
    FirstWorkSet1RM: { Lb: 180, Kg: 81.6 },
    MinReps: 8,
    MaxReps: 12,
    IsDropSet: false,
    IsPlate: false,
    IsTimeBased: false,
    RIR: 2,
  }

  const allSets = generateAllSets({
    recommendation: mockRecommendation,
    completedSets: [],
    currentSetIndex: 0,
    setData: null,
    unit: 'lbs',
    isBodyweight: false, // Default to false for test page
  })

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Rest-Pause Sets Test</h1>

      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-2">Recommendation Data:</h2>
        <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
          {JSON.stringify(
            {
              Series: mockRecommendation.Series,
              Reps: mockRecommendation.Reps,
              NbPauses: mockRecommendation.NbPauses,
              NbRepsPauses: mockRecommendation.NbRepsPauses,
              RpRest: mockRecommendation.RpRest,
              IsNormalSets: mockRecommendation.IsNormalSets,
              IsPyramid: mockRecommendation.IsPyramid,
              IsReversePyramid: mockRecommendation.IsReversePyramid,
            },
            null,
            2
          )}
        </pre>
      </div>

      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-2">
          Generated Sets ({allSets.length} total):
        </h2>
        <div className="space-y-2">
          {allSets.map((set) => {
            const isRestPause = set.SetTitle?.includes('Rest-pause')

            let setClass = 'bg-green-50 border-green-200'
            if (set.IsWarmups) {
              setClass = 'bg-blue-50 border-blue-200'
            } else if (isRestPause) {
              setClass = 'bg-orange-50 border-orange-200'
            }

            let setType = 'Working'
            if (set.IsWarmups) {
              setType = 'Warmup'
            } else if (isRestPause) {
              setType = 'Rest-pause'
            }

            return (
              <div
                key={`set-${set.Id}-${set.SetNo}`}
                className={`p-3 rounded border ${setClass}`}
              >
                <div className="flex justify-between items-center">
                  <span className="font-medium">
                    Set {set.SetNo} {set.SetTitle && `- ${set.SetTitle}`}
                  </span>
                  <span className="text-sm text-gray-600">{setType}</span>
                </div>
                <div className="text-sm mt-1">
                  {set.Reps} reps @ {set.Weight.Lb}lbs ({set.Weight.Kg}kg)
                  {set.restTime && ` • ${set.restTime}s rest`}
                  {(set.NbPause ?? 0) > 0 && ` • NbPause: ${set.NbPause}`}
                </div>
              </div>
            )
          })}
        </div>
      </div>

      <div className="text-sm text-gray-600">
        <p>Expected: 2 warmup + 1 main working + 3 rest-pause = 6 total sets</p>
        <p>Actual: {allSets.length} sets</p>
      </div>
    </div>
  )
}
