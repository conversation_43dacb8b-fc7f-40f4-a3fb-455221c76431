import { describe, it, expect, beforeEach, vi } from 'vitest'
import { performCompleteLogout } from '@/utils/logout'
import { queryClient } from '@/utils/queryClient'

// Mock the cache module
vi.mock('@/cache', () => ({
  clearAllCacheData: vi.fn(() =>
    Promise.resolve({
      localStorage: true,
      sessionStorage: true,
      memory: true,
    })
  ),
}))

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
  writable: true,
})

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn(),
}
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
  writable: true,
})

// Mock caches API
const cachesMock = {
  keys: vi.fn(() => Promise.resolve(['cache1', 'cache2'])),
  delete: vi.fn(() => Promise.resolve(true)),
}
Object.defineProperty(window, 'caches', {
  value: cachesMock,
  writable: true,
  configurable: true,
})

// Mock indexedDB
const indexedDBMock = {
  databases: vi.fn(() =>
    Promise.resolve([
      { name: 'db1', version: 1 },
      { name: 'db2', version: 1 },
    ])
  ),
  deleteDatabase: vi.fn(() => Promise.resolve(undefined)),
}
Object.defineProperty(window, 'indexedDB', {
  value: indexedDBMock,
  writable: true,
})

// Mock console to avoid noise in tests
vi.spyOn(console, 'log').mockImplementation(() => {})
vi.spyOn(console, 'error').mockImplementation(() => {})
vi.spyOn(console, 'warn').mockImplementation(() => {})

// Mock window.location.replace for hard reload
const mockLocationReplace = vi.fn()
Object.defineProperty(window, 'location', {
  value: {
    replace: mockLocationReplace,
    href: 'http://localhost:3000',
    pathname: '/',
  },
  writable: true,
})

describe('performCompleteLogout', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockLocationReplace.mockClear()
    // Mock console methods
    vi.spyOn(console, 'error').mockImplementation(() => {})
    vi.spyOn(console, 'warn').mockImplementation(() => {})
    vi.spyOn(console, 'log').mockImplementation(() => {})
  })

  it('should cancel all React Query queries', async () => {
    const cancelQueriesSpy = vi.spyOn(queryClient, 'cancelQueries')

    await performCompleteLogout()

    expect(cancelQueriesSpy).toHaveBeenCalled()
  })

  it('should clear React Query cache', async () => {
    const clearSpy = vi.spyOn(queryClient, 'clear')
    const removeQueriesSpy = vi.spyOn(queryClient, 'removeQueries')

    await performCompleteLogout()

    expect(clearSpy).toHaveBeenCalled()
    expect(removeQueriesSpy).toHaveBeenCalled()
  })

  it('should remove all localStorage keys including drmuscle-workout-cache', async () => {
    await performCompleteLogout()

    const expectedKeys = [
      'drmuscle-auth',
      'drmuscle-program',
      'drmuscle-cache',
      'drmuscle-failed-requests',
      'drmuscle-offline-queue',
      'drmuscle-workout',
      'drmuscle-workout-cache', // New cache key
      'user-stats-storage',
      'restDuration',
      // User preferences that must be cleared
      'DailyReset',
      'lastWorkoutDate',
      'quickMode',
      'SetStyle',
      'IsPyramid',
      // Additional caches
      'dr-muscle-recommendation-cache',
      'temp_swap_exercise_contexts',
    ]

    expectedKeys.forEach((key) => {
      expect(localStorageMock.removeItem).toHaveBeenCalledWith(key)
    })

    expect(localStorageMock.removeItem).toHaveBeenCalledTimes(
      expectedKeys.length
    )
  })

  it('should clear service worker caches', async () => {
    await performCompleteLogout()

    expect(cachesMock.keys).toHaveBeenCalled()
    expect(cachesMock.delete).toHaveBeenCalledWith('cache1')
    expect(cachesMock.delete).toHaveBeenCalledWith('cache2')
    expect(cachesMock.delete).toHaveBeenCalledTimes(2)
  })

  it('should clear IndexedDB databases', async () => {
    await performCompleteLogout()

    expect(indexedDBMock.databases).toHaveBeenCalled()
    expect(indexedDBMock.deleteDatabase).toHaveBeenCalledWith('db1')
    expect(indexedDBMock.deleteDatabase).toHaveBeenCalledWith('db2')
    expect(indexedDBMock.deleteDatabase).toHaveBeenCalledTimes(2)
  })

  it('should clear sessionStorage', async () => {
    await performCompleteLogout()

    expect(sessionStorageMock.clear).toHaveBeenCalled()
  })

  it('should handle errors gracefully', async () => {
    // Make localStorage throw an error
    localStorageMock.removeItem.mockImplementation((key) => {
      if (key === 'drmuscle-auth') {
        throw new Error('Storage error')
      }
    })

    // Make caches throw an error
    cachesMock.keys.mockRejectedValue(new Error('Cache error'))

    // Make indexedDB throw an error
    indexedDBMock.databases.mockRejectedValue(new Error('IndexedDB error'))

    // Make sessionStorage throw an error
    sessionStorageMock.clear.mockImplementation(() => {
      throw new Error('SessionStorage error')
    })

    // Should not throw
    await expect(performCompleteLogout()).resolves.not.toThrow()

    // Should log errors
    expect(console.error).toHaveBeenCalledWith(
      'Failed to clear drmuscle-auth:',
      expect.any(Error)
    )
    expect(console.error).toHaveBeenCalledWith(
      'Failed to clear service worker caches:',
      expect.any(Error)
    )
    expect(console.warn).toHaveBeenCalledWith(
      'Failed to clear IndexedDB:',
      expect.any(Error)
    )
    expect(console.error).toHaveBeenCalledWith(
      'Failed to clear sessionStorage:',
      expect.any(Error)
    )
  })
})
