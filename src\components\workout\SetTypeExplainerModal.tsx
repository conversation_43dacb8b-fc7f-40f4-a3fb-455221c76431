'use client'

import React, { useEffect } from 'react'
import type { SetType } from '@/utils/setTypeUtils'

interface SetTypeExplainerModalProps {
  setType: SetType | null
  isOpen: boolean
  onClose: () => void
}

const setTypeExplanations: Record<
  SetType,
  { title: string; description: string }
> = {
  Normal: {
    title: 'Normal sets',
    description:
      'Standard straight sets where you perform the same weight and aim for the same rep range across all working sets. The most common training method for building strength and muscle.',
  },
  'Rest-pause': {
    title: 'Rest-pause sets',
    description:
      'After reaching failure, take short rest periods (10-15 seconds) and continue for additional reps. This technique maximizes muscle fatigue and growth stimulus. You may see lower rep counts as muscles tire through pauses.',
  },
  'Back-off': {
    title: 'Back-off sets',
    description:
      'After heavy working sets, perform additional sets with lighter weight. This provides extra training volume while allowing recovery from intense efforts.',
  },
  'Drop set': {
    title: 'Drop sets',
    description:
      'Immediately reduce the weight after reaching failure and continue for more reps. This extends the set beyond normal failure for greater muscle exhaustion and growth.',
  },
  Pyramid: {
    title: 'Pyramid sets',
    description:
      'Weight increases while reps decrease with each set. Start lighter with higher reps, building up to your heaviest weight. Great for warming up and building strength.',
  },
  'Reverse pyramid': {
    title: 'Reverse pyramid sets',
    description:
      "Start with your heaviest weight first (after warming up), then reduce weight and increase reps each set. Ideal for strength gains as you're freshest for the heaviest loads.",
  },
  'Warm-up': {
    title: 'Warm-up sets',
    description:
      'Light sets performed before your working sets to prepare your muscles, joints, and nervous system for heavier loads. Gradually increase weight while keeping reps moderate to prevent fatigue before main sets.',
  },
}

export function SetTypeExplainerModal({
  setType,
  isOpen,
  onClose,
}: SetTypeExplainerModalProps) {
  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      return () => document.removeEventListener('keydown', handleEscape)
    }
    return undefined
  }, [isOpen, onClose])

  // Handle body scroll lock
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
      return () => {
        document.body.style.overflow = 'unset'
      }
    }
    return undefined
  }, [isOpen])

  if (!isOpen || !setType) {
    return null
  }

  const explanation = setTypeExplanations[setType]

  return (
    <>
      {/* Backdrop with blur effect - matching CustomDurationModal */}
      <div
        className="fixed inset-0 bg-black/60 backdrop-blur-sm z-[70] animate-in fade-in duration-200"
        onClick={onClose}
        aria-hidden="true"
      />

      {/* Modal container */}
      <div
        role="dialog"
        aria-modal="true"
        aria-labelledby="set-type-title"
        aria-describedby="set-type-description"
        className="fixed inset-x-4 top-[20vh] max-w-md mx-auto z-[80] animate-in zoom-in-95 slide-in-from-bottom-2 duration-300"
      >
        {/* Gold gradient border wrapper */}
        <div className="p-[2px] bg-gradient-to-r from-brand-gold-start to-brand-gold-end rounded-3xl">
          {/* Dark background content */}
          <div className="p-6 bg-[#0A0B0E] rounded-3xl">
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
              <h2
                id="set-type-title"
                className="text-xl font-semibold text-text-primary"
              >
                {explanation.title}
              </h2>
              <button
                onClick={onClose}
                className="p-2 hover:bg-white/10 rounded-full transition-colors"
                aria-label="Close explanation"
              >
                <svg
                  className="w-5 h-5 text-text-secondary"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            {/* Content */}
            <p
              id="set-type-description"
              className="text-text-secondary leading-relaxed"
            >
              {explanation.description}
            </p>

            {/* Close button - matching CustomDurationModal style */}
            <button
              onClick={onClose}
              className="mt-6 w-full py-3 px-6 rounded-2xl font-medium text-base
                     bg-gradient-to-r from-brand-gold-start to-brand-gold-end
                     text-text-inverse shadow-lg hover:shadow-xl hover:opacity-90
                     transition-all min-h-[48px] touch-manipulation"
            >
              Got it
            </button>
          </div>
        </div>
      </div>
    </>
  )
}
