import { useEffect, useRef } from 'react'

export type VisibilityState = 'visible' | 'hidden'

/**
 * Hook to detect when the app goes to background or foreground
 * Handles both Page Visibility API and window focus/blur events
 * for comprehensive coverage across different browsers and PWAs
 *
 * @param callback - Function called with new visibility state
 */
export function useVisibilityChange(
  callback: (state: VisibilityState) => void
) {
  const lastStateRef = useRef<VisibilityState>(
    typeof document !== 'undefined' && document.visibilityState === 'hidden'
      ? 'hidden'
      : 'visible'
  )

  useEffect(() => {
    // Handle visibility change
    const handleVisibilityChange = () => {
      // Check if visibility API is supported
      if (typeof document.visibilityState === 'undefined') {
        return
      }

      const newState: VisibilityState =
        document.visibilityState === 'hidden' ? 'hidden' : 'visible'

      // Only fire callback if state actually changed
      if (newState !== lastStateRef.current) {
        lastStateRef.current = newState
        callback(newState)
      }
    }

    // Handle window focus (app comes to foreground)
    const handleFocus = () => {
      if (lastStateRef.current !== 'visible') {
        lastStateRef.current = 'visible'
        callback('visible')
      }
    }

    // Handle window blur (app goes to background)
    const handleBlur = () => {
      if (lastStateRef.current !== 'hidden') {
        lastStateRef.current = 'hidden'
        callback('hidden')
      }
    }

    // Add event listeners
    // Only add visibilitychange listener if API is supported
    if (typeof document.visibilityState !== 'undefined') {
      document.addEventListener('visibilitychange', handleVisibilityChange)
    }
    window.addEventListener('focus', handleFocus)
    window.addEventListener('blur', handleBlur)

    // Cleanup
    return () => {
      if (typeof document.visibilityState !== 'undefined') {
        document.removeEventListener('visibilitychange', handleVisibilityChange)
      }
      window.removeEventListener('focus', handleFocus)
      window.removeEventListener('blur', handleBlur)
    }
  }, [callback])
}
