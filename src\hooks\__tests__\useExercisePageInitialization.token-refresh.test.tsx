import { renderHook, waitFor } from '@testing-library/react'
import { vi } from 'vitest'
import { useExercisePageInitialization } from '../useExercisePageInitialization'
import { useWorkout } from '../useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useAuthStore } from '@/stores/authStore'
import { useRouter } from 'next/navigation'

// Mock dependencies
vi.mock('../useWorkout')
vi.mock('@/stores/workoutStore')
vi.mock('@/stores/authStore')
vi.mock('next/navigation')
vi.mock('@/utils/debugLog', () => ({
  debugLog: Object.assign(vi.fn(), {
    log: vi.fn(),
    error: vi.fn(),
  }),
}))
vi.mock('@/utils/RecommendationLoadingCoordinator', () => ({
  RecommendationLoadingCoordinator: {
    getInstance: vi.fn(() => ({
      canStartLoading: vi.fn(() => true),
      startLoading: vi.fn(),
    })),
  },
}))

describe('useExercisePageInitialization - Token Refresh', () => {
  const mockRouter = {
    replace: vi.fn(),
    push: vi.fn(),
  }

  const mockWorkout = {
    todaysWorkout: [
      {
        WorkoutTemplates: [{ id: 1, name: 'Test Workout' }],
      },
    ],
    isLoadingWorkout: false,
    startWorkout: vi.fn().mockResolvedValue({ success: true }),
    exercises: [
      { Id: 123, Label: 'Bench Press' },
      { Id: 456, Label: 'Squat' },
    ],
    workoutSession: { id: 'session-1' },
    loadRecommendation: vi.fn(),
    updateExerciseWorkSets: vi.fn(),
  }

  const mockWorkoutStore = {
    setCurrentExerciseById: vi.fn(),
    loadingStates: new Map(),
    getCachedExerciseRecommendation: vi.fn().mockReturnValue(null),
  }

  beforeEach(() => {
    vi.clearAllMocks()
    ;(useRouter as any).mockReturnValue(mockRouter)
    ;(useWorkout as any).mockReturnValue(mockWorkout)
    ;(useWorkoutStore as any).mockReturnValue(mockWorkoutStore)
  })

  describe('401 Error Handling', () => {
    it('should NOT handle 401 errors directly - let API client handle token refresh', async () => {
      // Given: Valid token that will expire
      ;(useAuthStore as any).mockReturnValue({
        token: 'expired-token',
        isAuthenticated: true,
      })

      // Mock no workout session to trigger startWorkout
      const workoutWithNoSession = { ...mockWorkout, workoutSession: null }
      ;(useWorkout as any).mockReturnValue(workoutWithNoSession)

      // Mock startWorkout to simulate 401 error
      const error401 = {
        response: { status: 401 },
        message: 'Unauthorized',
      }
      workoutWithNoSession.startWorkout.mockRejectedValueOnce(error401)

      // When: Exercise page initializes and gets 401
      const { result } = renderHook(() => useExercisePageInitialization(123))

      // Then: Should propagate the error without redirecting
      await waitFor(() => {
        expect(workoutWithNoSession.startWorkout).toHaveBeenCalled()
        expect(result.current.loadingError).toBeTruthy()
      })

      // And: Should NOT redirect to login (API client will handle it)
      expect(mockRouter.replace).not.toHaveBeenCalledWith('/login')
    })

    it('should allow API client to refresh tokens on 401 without interference', async () => {
      // Given: Token exists
      ;(useAuthStore as any).mockReturnValue({
        token: 'valid-token',
        isAuthenticated: true,
      })

      // Mock loadRecommendation to return 401 initially
      mockWorkout.loadRecommendation.mockRejectedValueOnce({
        response: { status: 401 },
      })

      // When: Exercise page initializes
      const { result } = renderHook(() => useExercisePageInitialization(123))

      // Then: Should complete initialization without redirecting
      await waitFor(() => {
        expect(result.current.isInitializing).toBe(false)
      })

      // And: Should NOT redirect (let API client handle token refresh)
      expect(mockRouter.replace).not.toHaveBeenCalledWith('/login')

      // Note: loadRecommendation errors are handled silently
      expect(mockWorkout.loadRecommendation).toHaveBeenCalled()
    })

    it('should handle other errors normally without redirect', async () => {
      // Given: Valid token but network error during startWorkout
      const workoutNoSession = { ...mockWorkout, workoutSession: null }
      ;(useWorkout as any).mockReturnValue(workoutNoSession)

      // Mock network error during workout start
      workoutNoSession.startWorkout.mockRejectedValueOnce(
        new Error('Network error')
      )

      // When: Exercise page initializes
      const { result } = renderHook(() => useExercisePageInitialization(123))

      // Then: Should set error but NOT redirect
      await waitFor(() => {
        expect(result.current.loadingError).toBeTruthy()
        expect(result.current.loadingError?.message).toContain('Network')
      })

      expect(mockRouter.replace).not.toHaveBeenCalled()
    })

    it('should not check auth status before initialization', async () => {
      // Given: No auth token (simulating expired session)
      ;(useAuthStore as any).mockReturnValue({
        token: null,
        isAuthenticated: false,
      })

      // When: Exercise page initializes
      const { result } = renderHook(() => useExercisePageInitialization(123))

      // Then: Should attempt initialization without pre-checking auth
      await waitFor(() => {
        expect(result.current.isInitializing).toBe(false)
      })

      // And: Should NOT redirect based on auth state alone
      expect(mockRouter.replace).not.toHaveBeenCalledWith('/login')
    })

    it('should handle retry without auth checks', async () => {
      // Given: Initial load with 401 error
      ;(useAuthStore as any).mockReturnValue({
        token: 'expired-token',
        isAuthenticated: true,
      })

      const workoutNoSession = { ...mockWorkout, workoutSession: null }
      ;(useWorkout as any).mockReturnValue(workoutNoSession)

      // First call fails with 401
      workoutNoSession.startWorkout.mockRejectedValueOnce({
        response: { status: 401 },
      })

      const { result } = renderHook(() => useExercisePageInitialization(123))

      await waitFor(() => {
        expect(result.current.loadingError).toBeTruthy()
      })

      // Clear mocks for retry
      vi.clearAllMocks()

      // Second call succeeds (after token refresh by API client)
      workoutNoSession.startWorkout.mockResolvedValueOnce({ success: true })

      // When: Retry is attempted
      await result.current.retryInitialization()

      // Then: Should succeed without redirect
      await waitFor(() => {
        expect(result.current.loadingError).toBeFalsy()
        expect(result.current.isInitializing).toBe(false)
      })

      expect(mockRouter.replace).not.toHaveBeenCalledWith('/login')
    })
  })

  describe('Indefinite Session Support', () => {
    it('should work seamlessly after extended background periods', async () => {
      // Given: User returns after 2+ hours in background
      ;(useAuthStore as any).mockReturnValue({
        token: 'old-but-valid-token',
        isAuthenticated: true,
      })

      // API might return 401 initially but token refresh should handle it
      mockWorkout.loadRecommendation
        .mockRejectedValueOnce({ response: { status: 401 } })
        .mockResolvedValueOnce({ success: true })

      // When: Exercise page initializes
      const { result } = renderHook(() => useExercisePageInitialization(123))

      // Then: Should complete initialization without redirect
      await waitFor(() => {
        expect(result.current.isInitializing).toBe(false)
      })

      // And: User should remain on the page (no forced logout)
      expect(mockRouter.replace).not.toHaveBeenCalledWith('/login')

      // loadRecommendation should be called despite 401
      expect(mockWorkout.loadRecommendation).toHaveBeenCalled()
    })
  })
})
