import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import SettingsPageSkeleton from '../SettingsPageSkeleton'

describe('SettingsPageSkeleton', () => {
  it('renders loading skeleton with proper structure', () => {
    const { container } = render(<SettingsPageSkeleton />)

    // Should have skeleton elements
    const skeletons = container.querySelectorAll('.animate-pulse')
    expect(skeletons.length).toBeGreaterThan(0)
  })

  it('has proper ARIA attributes for accessibility', () => {
    render(<SettingsPageSkeleton />)

    // Should have proper ARIA attributes for loading state
    const skeleton = screen.getByRole('status')
    expect(skeleton).toHaveAttribute('aria-busy', 'true')
    expect(skeleton).toHaveAttribute('aria-label', 'Loading settings')
  })

  it('follows existing skeleton patterns from WorkoutCardSkeleton', () => {
    const { container } = render(<SettingsPageSkeleton />)

    // Should use consistent skeleton styling
    expect(container.querySelector('.bg-surface-secondary')).toBeInTheDocument()
    expect(container.querySelector('.animate-pulse')).toBeInTheDocument()
  })

  it('displays skeleton for header area', () => {
    const { container } = render(<SettingsPageSkeleton />)

    // Should have header skeleton
    const headerSkeleton = container.querySelector(
      '[data-testid="header-skeleton"]'
    )
    expect(headerSkeleton).toBeInTheDocument()
  })

  it('displays skeletons for settings sections', () => {
    const { container } = render(<SettingsPageSkeleton />)

    // Should have multiple section skeletons
    const sectionSkeletons = container.querySelectorAll(
      '[data-testid="section-skeleton"]'
    )
    expect(sectionSkeletons.length).toBeGreaterThanOrEqual(3)
  })

  it('uses mobile-optimized spacing', () => {
    const { container } = render(<SettingsPageSkeleton />)

    // Check for mobile-first spacing classes
    expect(container.querySelector('.px-4')).toBeInTheDocument()
    expect(container.querySelector('.space-y-4')).toBeInTheDocument()
  })

  it('maintains consistent height to prevent layout shift', () => {
    const { container } = render(<SettingsPageSkeleton />)

    // Should have min-height to prevent layout shift
    const mainContainer = container.querySelector('.min-h-screen')
    expect(mainContainer).toBeInTheDocument()
  })
})
