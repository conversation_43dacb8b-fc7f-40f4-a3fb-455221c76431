'use client'

import { render, screen } from '@testing-library/react'
import { ExerciseCompleteViewV2 } from '../ExerciseCompleteViewV2'
import { vi } from 'vitest'
import type { ExerciseModel, RecommendationModel } from '@/types'

// Mock all dependencies
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => (
      <button {...props}>{children}</button>
    ),
    p: ({ children, ...props }: any) => <p {...props}>{children}</p>,
  },
}))

vi.mock('@/utils/haptics', () => ({
  vibrate: vi.fn(),
}))

vi.mock('@/hooks/useSuccessAnimation', () => ({
  useSuccessAnimation: () => ({
    state: 'complete',
    isAnimating: false,
    prefersReducedMotion: false,
    start: vi.fn(),
  }),
}))

vi.mock('@/types/animations', () => ({}))

vi.mock('@/components/workout/SuccessIcon', () => ({
  SuccessIcon: () => <div data-testid="success-icon">Success Icon</div>,
}))

describe('ExerciseCompleteViewV2 - Simple Tests', () => {
  const mockExercise: ExerciseModel = {
    Id: 123,
    Label: 'Bench Press',
    IsBodyweight: false,
  } as ExerciseModel

  const mockOnContinue = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should display percentage when OneRMProgress is positive', () => {
    const mockRecommendation: RecommendationModel = {
      ExerciseId: 123,
      Series: 3,
      Reps: 10,
      Weight: { Kg: 60, Lb: 132 },
      OneRMProgress: 5.2,
      WarmupsCount: 0,
      RpRest: 120,
      NbPauses: 0,
      NbRepsPauses: 0,
    } as RecommendationModel

    render(
      <ExerciseCompleteViewV2
        exercise={mockExercise}
        isLastExercise={false}
        onContinue={mockOnContinue}
        recommendation={mockRecommendation}
      />
    )

    expect(screen.getByText('+5.2% stronger!')).toBeInTheDocument()
  })

  it('should display SuccessIcon component', () => {
    render(
      <ExerciseCompleteViewV2
        exercise={mockExercise}
        isLastExercise={false}
        onContinue={mockOnContinue}
      />
    )

    expect(screen.getByTestId('success-icon')).toBeInTheDocument()
  })

  it('should not display percentage when recommendation is null', () => {
    render(
      <ExerciseCompleteViewV2
        exercise={mockExercise}
        isLastExercise={false}
        onContinue={mockOnContinue}
        recommendation={null}
      />
    )

    expect(screen.queryByText(/stronger/)).not.toBeInTheDocument()
  })
})
