/**
 * Singleton coordinator for managing recommendation loading states
 * Prevents duplicate API calls and provides centralized loading state management
 */
export class RecommendationLoadingCoordinator {
  private static instance: RecommendationLoadingCoordinator | null = null

  private loadingStates: Map<number, boolean> = new Map()

  private loadTimestamps: Map<number, number> = new Map()

  private completedLoads: Set<number> = new Set()

  private stats = {
    totalRequests: 0,
    successfulLoads: 0,
    failedLoads: 0,
  }

  private timeouts: Map<number, NodeJS.Timeout> = new Map()

  private timeoutDurations: Map<number, number> = new Map()

  private timeoutStartTimes: Map<number, number> = new Map()

  private pausedTimeouts: Map<number, number> = new Map()

  private exercisesToRetry: Set<number> = new Set()

  private recoveryCallback:
    | ((data: {
        clearedExerciseIds: number[]
        backgroundDuration: number
      }) => void)
    | null = null

  private visibilityHandler: (() => void) | null = null

  private lastVisibilityState: DocumentVisibilityState = 'visible'

  public backgroundTimestamp: number | null = null

  private constructor() {
    // Private constructor for singleton pattern
    this.setupVisibilityListener()
  }

  /**
   * Setup visibility change listener to handle background/foreground transitions
   */
  private setupVisibilityListener(): void {
    if (typeof document === 'undefined') {
      return // Skip in non-browser environments (tests, SSR)
    }

    this.visibilityHandler = () => {
      const currentState = document.visibilityState

      if (currentState === 'hidden' && this.lastVisibilityState === 'visible') {
        // App going to background
        this.handleAppBackground()
      } else if (
        currentState === 'visible' &&
        this.lastVisibilityState === 'hidden'
      ) {
        // App returning from background
        this.handleAppForeground()
      }

      this.lastVisibilityState = currentState
    }

    document.addEventListener('visibilitychange', this.visibilityHandler)
  }

  /**
   * Handle app going to background
   */
  private handleAppBackground(): void {
    this.backgroundTimestamp = Date.now()

    // Save remaining timeout durations before clearing
    this.timeouts.forEach((timeout, exerciseId) => {
      const startTime = this.timeoutStartTimes.get(exerciseId)
      const duration = this.timeoutDurations.get(exerciseId)
      if (startTime && duration) {
        const elapsed = Date.now() - startTime
        const remaining = Math.max(0, duration - elapsed)
        this.pausedTimeouts.set(exerciseId, remaining)
      }
      clearTimeout(timeout)
    })
    this.timeouts.clear()

    // Clear all loading states since we can't track them in background
    const loadingIds = this.getLoadingExerciseIds()
    loadingIds.forEach((id) => {
      this.loadingStates.delete(id)
      this.loadTimestamps.delete(id)
    })
  }

  /**
   * Handle app returning from background
   */
  private handleAppForeground(): void {
    const backgroundDuration = this.backgroundTimestamp
      ? Date.now() - this.backgroundTimestamp
      : 0
    this.backgroundTimestamp = null

    // Clear any remaining stuck states
    if (this.loadingStates.size > 0) {
      const stuckIds = this.getLoadingExerciseIds()
      this.loadingStates.clear()
      this.loadTimestamps.clear()

      // Emit recovery event for external handlers
      if (stuckIds.length > 0 && this.recoveryCallback) {
        this.recoveryCallback({
          clearedExerciseIds: stuckIds,
          backgroundDuration,
        })
      }
    }

    // Resume any paused timeouts
    this.pausedTimeouts.forEach((remainingTime, exerciseId) => {
      // Only resume if we have a recovery callback registered
      if (this.recoveryCallback && remainingTime > 0) {
        // The recovery callback should handle re-initiating the load
        // We just track that this exercise needs to be retried
        this.exercisesToRetry.add(exerciseId)
      }
    })
    this.pausedTimeouts.clear()
  }

  public static getInstance(): RecommendationLoadingCoordinator {
    if (!RecommendationLoadingCoordinator.instance) {
      RecommendationLoadingCoordinator.instance =
        new RecommendationLoadingCoordinator()
    }
    return RecommendationLoadingCoordinator.instance
  }

  /**
   * Check if an exercise can start loading
   */
  public canStartLoading(exerciseId: number): boolean {
    return !this.loadingStates.get(exerciseId)
  }

  /**
   * Start loading for an exercise
   */
  public startLoading(
    exerciseId: number,
    options?: { timeout?: number }
  ): void {
    if (this.loadingStates.get(exerciseId)) {
      return // Already loading
    }

    this.loadingStates.set(exerciseId, true)
    this.loadTimestamps.set(exerciseId, Date.now())
    this.stats.totalRequests++

    // Set timeout if specified
    if (options?.timeout) {
      const timeoutId = setTimeout(() => {
        this.failLoading(exerciseId)
      }, options.timeout)
      this.timeouts.set(exerciseId, timeoutId)
      this.timeoutDurations.set(exerciseId, options.timeout)
      this.timeoutStartTimes.set(exerciseId, Date.now())
    }
  }

  /**
   * Mark exercise loading as complete
   */
  public completeLoading(exerciseId: number): void {
    if (!this.loadingStates.get(exerciseId)) {
      return // Not loading
    }

    this.loadingStates.delete(exerciseId)
    this.loadTimestamps.delete(exerciseId)
    this.completedLoads.add(exerciseId)
    this.stats.successfulLoads++

    // Clear timeout if exists
    const timeoutId = this.timeouts.get(exerciseId)
    if (timeoutId) {
      clearTimeout(timeoutId)
      this.timeouts.delete(exerciseId)
      this.timeoutDurations.delete(exerciseId)
      this.timeoutStartTimes.delete(exerciseId)
    }
  }

  /**
   * Mark exercise loading as failed
   */
  public failLoading(exerciseId: number): void {
    if (!this.loadingStates.get(exerciseId)) {
      return // Not loading
    }

    this.loadingStates.delete(exerciseId)
    this.loadTimestamps.delete(exerciseId)
    this.stats.failedLoads++

    // Clear timeout if exists
    const timeoutId = this.timeouts.get(exerciseId)
    if (timeoutId) {
      clearTimeout(timeoutId)
      this.timeouts.delete(exerciseId)
      this.timeoutDurations.delete(exerciseId)
      this.timeoutStartTimes.delete(exerciseId)
    }
  }

  /**
   * Check if an exercise is currently loading
   */
  public isLoading(exerciseId: number): boolean {
    return this.loadingStates.get(exerciseId) || false
  }

  /**
   * Check if any of the given exercises are loading
   */
  public isAnyLoading(exerciseIds: number[]): boolean {
    return exerciseIds.some((id) => this.isLoading(id))
  }

  /**
   * Check if all of the given exercises are loading
   */
  public areAllLoading(exerciseIds: number[]): boolean {
    return (
      exerciseIds.length > 0 && exerciseIds.every((id) => this.isLoading(id))
    )
  }

  /**
   * Start loading for multiple exercises
   */
  public startBatchLoading(exerciseIds: number[]): void {
    exerciseIds.forEach((id) => this.startLoading(id))
  }

  /**
   * Get loading statistics
   */
  public getStats(): {
    totalRequests: number
    successfulLoads: number
    failedLoads: number
    currentlyLoading: number
  } {
    return {
      ...this.stats,
      currentlyLoading: this.loadingStates.size,
    }
  }

  /**
   * Reset all loading states (useful for testing)
   */
  public reset(): void {
    // Clear all timeouts
    this.timeouts.forEach((timeout) => clearTimeout(timeout))

    this.loadingStates.clear()
    this.loadTimestamps.clear()
    this.completedLoads.clear()
    this.timeouts.clear()
    this.timeoutDurations.clear()
    this.timeoutStartTimes.clear()
    this.pausedTimeouts.clear()
    this.exercisesToRetry.clear()
    this.stats = {
      totalRequests: 0,
      successfulLoads: 0,
      failedLoads: 0,
    }
    this.backgroundTimestamp = null
  }

  /**
   * Cleanup event listeners (useful for testing)
   */
  public cleanup(): void {
    if (this.visibilityHandler && typeof document !== 'undefined') {
      document.removeEventListener('visibilitychange', this.visibilityHandler)
      this.visibilityHandler = null
    }
  }

  /**
   * Get all currently loading exercise IDs
   */
  public getLoadingExerciseIds(): number[] {
    return Array.from(this.loadingStates.keys())
  }

  /**
   * Check how long an exercise has been loading
   */
  public getLoadingDuration(exerciseId: number): number | null {
    const timestamp = this.loadTimestamps.get(exerciseId)
    if (!timestamp) {
      return null
    }
    return Date.now() - timestamp
  }

  /**
   * Check if a specific exercise has completed loading
   */
  public isCompleted(exerciseId: number): boolean {
    return this.completedLoads.has(exerciseId)
  }

  /**
   * Get remaining timeout duration for an exercise
   */
  public getRemainingTimeout(exerciseId: number): number | null {
    const startTime = this.timeoutStartTimes.get(exerciseId)
    const duration = this.timeoutDurations.get(exerciseId)

    if (!startTime || !duration) {
      return null
    }

    const elapsed = Date.now() - startTime
    return Math.max(0, duration - elapsed)
  }

  /**
   * Resume loading with a specific timeout (used after returning from background)
   */
  public resumeLoading(
    exerciseId: number,
    options?: { timeout?: number }
  ): void {
    // Only resume if not already loading
    if (this.loadingStates.get(exerciseId)) {
      return
    }

    // Start loading with the provided timeout (usually the remaining time)
    this.startLoading(exerciseId, options)
  }

  /**
   * Register a callback to be called when recovery from background is needed
   */
  public onRecovery(
    callback: (data: {
      clearedExerciseIds: number[]
      backgroundDuration: number
    }) => void
  ): void {
    this.recoveryCallback = callback
  }

  /**
   * Get exercises that need to be retried after returning from background
   */
  public getExercisesToRetry(): number[] {
    const exercises = Array.from(this.exercisesToRetry)
    this.exercisesToRetry.clear()
    return exercises
  }
}

// Development-time debugging helper
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  ;(
    window as unknown as {
      RecommendationLoadingCoordinator: typeof RecommendationLoadingCoordinator
    }
  ).RecommendationLoadingCoordinator = RecommendationLoadingCoordinator
}
