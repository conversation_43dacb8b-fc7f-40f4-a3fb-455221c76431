import { useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useRestTimer } from '@/components/workout-v2/RestTimer'
import { useAuthStore } from '@/stores/authStore'
import { debugLog } from '@/utils/debugLog'
import type { ExerciseModel, WorkoutLogSerieModel } from '@/types'
import {
  type ExtendedWorkoutLogSerieModel,
  getNextSetInfo,
  getRestDuration,
  findNextExercise,
  logSaveAttempt,
  logMissingData,
} from './useExerciseV2Actions.helpers'

interface UseExerciseV2ActionsProps {
  currentExercise: ExerciseModel | null
  workoutSession: unknown
  setData: { reps: number; weight: number; duration: number }
  currentSetIndex: number
  allSets: ExtendedWorkoutLogSerieModel[]
  isWarmup: boolean
  isLastSet: boolean
  isFirstWorkSet: boolean
  currentSet: WorkoutLogSerieModel | null
  setSaveError: (error: string) => void
  onExerciseComplete?: () => void
  handleSaveSet?: () => void
  isLastExercise?: boolean
  exercises?: ExerciseModel[]
}

export function useExerciseV2Actions({
  currentExercise,
  workoutSession,
  setData,
  currentSetIndex,
  allSets,
  isWarmup,
  isLastSet,
  isFirstWorkSet,
  currentSet,
  setSaveError,
  onExerciseComplete,
  handleSaveSet,
  isLastExercise = false,
  exercises = [],
}: UseExerciseV2ActionsProps) {
  const router = useRouter()
  const { saveSet } = useWorkout()
  const { nextSet } = useWorkoutStore()
  const { startRestTimer } = useRestTimer()
  const { getCachedUserInfo } = useAuthStore()
  const userInfo = getCachedUserInfo()
  const unit: 'kg' | 'lbs' = userInfo?.MassUnit === 'kg' ? 'kg' : 'lbs'

  const handleCompleteSet = useCallback(async () => {
    logSaveAttempt(currentExercise, workoutSession, setData)

    if (!currentExercise || !workoutSession) {
      logMissingData(currentExercise)
      return
    }

    debugLog('[ExerciseV2] Completing set')

    try {
      // For first work set, use handleSaveSet to trigger RIR picker
      if (isFirstWorkSet && !currentExercise.IsTimeBased && handleSaveSet) {
        // Call the original handleSaveSet which will trigger RIR picker
        await handleSaveSet()
        return // Exit early, handleSaveSet will handle the rest
      }

      // For other sets, save directly
      await saveSet({
        exerciseId: currentExercise.Id,
        reps: currentExercise.IsTimeBased ? undefined : setData.reps,
        weight: setData.weight,
        isWarmup,
        setNumber: currentSetIndex + 1,
        duration: currentExercise.IsTimeBased ? setData.duration : undefined,
        RIR: undefined,
      })

      // Check if this is the last set
      const currentSetIdx = allSets.findIndex((s) => s.IsNext)
      const hasMoreSets = currentSetIdx < allSets.length - 1

      if (isLastSet) {
        // Stay in V2 flow - handle last set differently
        if (isLastExercise) {
          // Show exercise complete view for last exercise
          if (onExerciseComplete) {
            onExerciseComplete()
          }
        } else {
          // Navigate to next exercise in V2 flow
          const nextExercise = findNextExercise(exercises, currentExercise.Id)
          if (nextExercise) {
            router.push(`/workout/exercise-v2/${nextExercise.Id}`)
          }
        }
      } else if (hasMoreSets) {
        // Move to next set without navigation
        nextSet()

        // Find the next set info
        const nextSetInfo = getNextSetInfo(allSets, currentSetIdx, unit)

        // Calculate rest duration based on set type
        const restDuration = getRestDuration(currentSet?.IsWarmups || false)
        startRestTimer(restDuration, nextSetInfo)
      }
    } catch (error) {
      console.error('Failed to save set:', error)
      setSaveError(
        error instanceof Error ? error.message : 'Failed to save set'
      )
    }
  }, [
    currentExercise,
    workoutSession,
    setData,
    isWarmup,
    currentSetIndex,
    isFirstWorkSet,
    allSets,
    isLastSet,
    currentSet,
    saveSet,
    nextSet,
    startRestTimer,
    setSaveError,
    onExerciseComplete,
    unit,
    handleSaveSet,
    isLastExercise,
    exercises,
    router,
  ])

  const handleSkipSet = useCallback(async () => {
    if (!currentExercise || !workoutSession) return

    debugLog('[ExerciseV2] Skipping set')

    try {
      // Save the set with 0 reps to mark as skipped
      await saveSet({
        exerciseId: currentExercise.Id,
        reps: 0,
        weight: 0,
        isWarmup,
        setNumber: currentSetIndex + 1,
        duration: 0,
        RIR: undefined,
      })

      // Check if this is the last set
      const currentSetIdx = allSets.findIndex((s) => s.IsNext)
      const hasMoreSets = currentSetIdx < allSets.length - 1

      if (isLastSet) {
        // Always show exercise complete view when finishing an exercise
        if (onExerciseComplete) {
          onExerciseComplete()
        }
      } else if (hasMoreSets) {
        // Move to next set without navigation
        nextSet()

        // Find the next set info
        const nextSetInfo = getNextSetInfo(allSets, currentSetIdx, unit)

        // Get rest duration from localStorage or use defaults
        let baseDuration = 90 // Default for work sets
        if (typeof window !== 'undefined') {
          const savedDuration = localStorage.getItem('restDuration')
          if (savedDuration) {
            const parsed = parseInt(savedDuration, 10)
            if (!Number.isNaN(parsed) && parsed > 0) {
              // Clamp to valid range (5-600 seconds)
              baseDuration = Math.max(5, Math.min(600, parsed))
            }
          }
        }

        // Calculate rest duration based on set type (shorter for skipped sets)
        const restDuration = currentSet?.IsWarmups
          ? Math.round(baseDuration / 6) // 1/6 for skipped warmups
          : Math.round(baseDuration / 4) // 1/4 for skipped work sets
        startRestTimer(restDuration, nextSetInfo)
      }
    } catch (error) {
      console.error('Failed to skip set:', error)
      setSaveError(
        error instanceof Error ? error.message : 'Failed to skip set'
      )
    }
  }, [
    currentExercise,
    workoutSession,
    isWarmup,
    currentSetIndex,
    allSets,
    isLastSet,
    currentSet,
    saveSet,
    nextSet,
    startRestTimer,
    setSaveError,
    onExerciseComplete,
    unit,
  ])

  return {
    handleCompleteSet,
    handleSkipSet,
  }
}
