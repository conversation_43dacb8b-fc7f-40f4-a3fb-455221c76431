import { ExerciseCard } from '@/components/workout/ExerciseCard'
import { ExerciseItemSkeleton } from '@/components/ui/Skeletons'
import type { ExerciseWorkSetsModel, WorkoutTemplateModel } from '@/types'

interface ExerciseListProps {
  displayExercises: ExerciseWorkSetsModel[]
  handleExerciseClick: (exerciseId: number) => void
  handleRetryExercise: (exerciseId: number) => void
  handleSkipExercise: (exerciseId: number) => void
  workout: WorkoutTemplateModel | undefined
  isLoadingWorkout: boolean
  expectedExerciseCount: number
}

export function ExerciseList({
  displayExercises,
  handleExerciseClick,
  handleRetryExercise,
  handleSkipExercise,
  workout,
  isLoadingWorkout,
  expectedExerciseCount,
}: ExerciseListProps) {
  return (
    <div className="mb-8 space-y-3">
      {/* Show loaded exercises using ExerciseCard */}
      {displayExercises?.map((exercise) => (
        <ExerciseCard
          key={exercise.Id}
          exercise={exercise}
          onExerciseClick={handleExerciseClick}
          onRetry={handleRetryExercise}
          onSkipExercise={handleSkipExercise}
          workout={workout}
        />
      ))}

      {/* Show skeleton loaders for remaining exercises if still loading */}
      {isLoadingWorkout &&
      expectedExerciseCount &&
      displayExercises &&
      expectedExerciseCount > displayExercises.length
        ? Array.from({
            length: expectedExerciseCount - displayExercises.length,
          }).map((_, index) => (
            <ExerciseItemSkeleton
              key={`skeleton-${displayExercises.length + index}`}
            />
          ))
        : null}
    </div>
  )
}
