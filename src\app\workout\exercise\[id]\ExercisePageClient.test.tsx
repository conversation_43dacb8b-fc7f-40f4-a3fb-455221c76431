import { render, screen, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { ExercisePageClient } from './ExercisePageClient'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useSetScreenLogic } from '@/hooks/useSetScreenLogic'
import { useExercisePageInitialization } from '@/hooks/useExercisePageInitialization'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import React from 'react'

// Mock Next.js hooks
vi.mock('next/navigation', () => ({
  useSearchParams: () => ({
    get: vi.fn().mockReturnValue('Bench Press'),
  }),
}))

// Mock the hooks and components
vi.mock('@/hooks/useWorkout')
vi.mock('@/stores/workoutStore')
vi.mock('@/hooks/useSetScreenLogic')
vi.mock('@/hooks/useExercisePageInitialization')
vi.mock('@/components/workout/SetScreenWithGrid', () => ({
  SetScreenWithGrid: ({ exercise }: any) => (
    <div data-testid="set-screen">{exercise?.Name}</div>
  ),
}))
vi.mock('@/components/workout/SetScreenLoadingState', () => ({
  SetScreenLoadingState: ({ exerciseName, isRecommendationLoading }: any) => (
    <div data-testid="loading-state">
      {isRecommendationLoading && (
        <div data-testid="transition-screen">Transition</div>
      )}
      Loading {exerciseName}
    </div>
  ),
}))
vi.mock('@/components/workout/ExerciseTransitionScreen', () => ({
  ExerciseTransitionScreen: ({ exerciseName, onComplete }: any) => {
    React.useEffect(() => {
      // Simulate transition completion
      setTimeout(() => onComplete?.(), 100)
    }, [onComplete])
    return (
      <div data-testid="exercise-transition">
        Transitioning to {exerciseName}
      </div>
    )
  },
}))

const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

describe('ExercisePageClient - Transition Screen Logic', () => {
  const mockExerciseId = 123
  const mockExerciseName = 'Bench Press'
  const mockRecommendation = { Reps: 10, Weight: { Kg: 50, Lb: 110 } }
  const mockExercise = { Id: mockExerciseId, Name: mockExerciseName }

  beforeEach(() => {
    vi.clearAllMocks()

    // Default mocks
    vi.mocked(useWorkout).mockReturnValue({
      isLoadingWorkout: false,
      workoutError: null,
      exercises: [mockExercise],
      workoutSession: { id: 1 },
    } as any)

    vi.mocked(useExercisePageInitialization).mockReturnValue({
      isInitializing: false,
      loadingError: null,
      retryInitialization: vi.fn(),
    } as any)
  })

  it('should NOT show transition screen when exercise is prefetched with success status', async () => {
    // Given: Exercise is prefetched with success status
    const mockPrefetchStatus = { [mockExerciseId]: 'success' as const }
    const mockIsExercisePrefetched = vi.fn().mockReturnValue(true)

    vi.mocked(useWorkoutStore).mockReturnValue({
      loadingStates: new Map(),
      prefetchStatus: mockPrefetchStatus,
      isExercisePrefetched: mockIsExercisePrefetched,
    } as any)

    vi.mocked(useSetScreenLogic).mockReturnValue({
      recommendation: mockRecommendation, // Already has recommendation
      exercise: mockExercise,
      isLoadingRecommendation: false,
      currentExerciseIndex: 0,
      currentSetIndex: 0,
      totalWorkSets: 3,
      isLastSet: false,
      setData: { reps: '10', weight: '50' },
      updateSetData: vi.fn(),
      handleSaveSet: vi.fn(),
      navigateToNextExercise: vi.fn(),
      navigateToNextSet: vi.fn(),
      navigateToPreviousSet: vi.fn(),
      completeExercise: vi.fn(),
      allSets: [],
    } as any)

    // When: Navigating to exercise page
    render(
      <QueryClientProvider client={createTestQueryClient()}>
        <ExercisePageClient exerciseId={mockExerciseId} />
      </QueryClientProvider>
    )

    // Then: No transition screen shown, goes directly to set screen
    await waitFor(() => {
      expect(
        screen.queryByTestId('exercise-transition')
      ).not.toBeInTheDocument()
      expect(screen.getByTestId('set-screen')).toBeInTheDocument()
    })
  })

  it('should show transition screen when exercise is NOT prefetched', async () => {
    // Given: Exercise is not prefetched
    const mockPrefetchStatus = {}
    const mockIsExercisePrefetched = vi.fn().mockReturnValue(false)

    vi.mocked(useWorkoutStore).mockReturnValue({
      loadingStates: new Map(),
      prefetchStatus: mockPrefetchStatus,
      isExercisePrefetched: mockIsExercisePrefetched,
    } as any)

    vi.mocked(useSetScreenLogic).mockReturnValue({
      recommendation: null, // No recommendation yet
      exercise: mockExercise,
      isLoadingRecommendation: true,
      currentExerciseIndex: 0,
      currentSetIndex: 0,
      totalWorkSets: 3,
      isLastSet: false,
      setData: { reps: '', weight: '' },
      updateSetData: vi.fn(),
      handleSaveSet: vi.fn(),
      navigateToNextExercise: vi.fn(),
      navigateToNextSet: vi.fn(),
      navigateToPreviousSet: vi.fn(),
      completeExercise: vi.fn(),
      allSets: [],
    } as any)

    // When: Navigating to exercise page
    render(
      <QueryClientProvider client={createTestQueryClient()}>
        <ExercisePageClient exerciseId={mockExerciseId} />
      </QueryClientProvider>
    )

    // Then: Transition screen shown
    await waitFor(() => {
      expect(screen.getByTestId('exercise-transition')).toBeInTheDocument()
    })
  })

  it('should NOT show transition when recommendation already loaded (existing behavior)', async () => {
    // Given: Recommendation already loaded
    const mockIsExercisePrefetched = vi.fn().mockReturnValue(false)

    vi.mocked(useWorkoutStore).mockReturnValue({
      loadingStates: new Map(),
      prefetchStatus: {},
      isExercisePrefetched: mockIsExercisePrefetched,
    } as any)

    vi.mocked(useSetScreenLogic).mockReturnValue({
      recommendation: mockRecommendation, // Already has recommendation
      exercise: mockExercise,
      isLoadingRecommendation: false,
      currentExerciseIndex: 0,
      currentSetIndex: 0,
      totalWorkSets: 3,
      isLastSet: false,
      setData: { reps: '10', weight: '50' },
      updateSetData: vi.fn(),
      handleSaveSet: vi.fn(),
      navigateToNextExercise: vi.fn(),
      navigateToNextSet: vi.fn(),
      navigateToPreviousSet: vi.fn(),
      completeExercise: vi.fn(),
      allSets: [],
    } as any)

    // When: Navigating to exercise page
    render(
      <QueryClientProvider client={createTestQueryClient()}>
        <ExercisePageClient exerciseId={mockExerciseId} />
      </QueryClientProvider>
    )

    // Then: No transition shown
    await waitFor(() => {
      expect(
        screen.queryByTestId('exercise-transition')
      ).not.toBeInTheDocument()
      expect(screen.getByTestId('set-screen')).toBeInTheDocument()
    })
  })
})
