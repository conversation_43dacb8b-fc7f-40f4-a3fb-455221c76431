name: Streamlined CI Pipeline

on:
  push:
    branches: [main, develop, staging]
  pull_request:
    branches: [main, develop, staging]
  workflow_dispatch:
    inputs:
      test_tier:
        description: 'Force specific test tier'
        required: false
        default: 'auto'
        type: choice
        options:
        - auto
        - development
        - integration
        - production
      run_full_suite:
        description: 'Run full comprehensive test suite regardless of branch'
        required: false
        default: false
        type: boolean

# Cancel outdated runs when new commits are pushed
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-${{ github.event_name == 'workflow_dispatch' && inputs.run_full_suite == 'true' && 'full' || 'normal' }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' && github.ref != 'refs/heads/develop' && github.ref != 'refs/heads/staging' && github.event_name != 'workflow_dispatch' }}

env:
  NODE_VERSION: '20.x'
  NODE_OPTIONS: '--max_old_space_size=6144 --max-semi-space-size=512 --expose-gc'
  WEBKIT_DISABLE_COMPOSITING: '1'
  WEBKIT_FORCE_COMPOSITING_MODE: '0'
  PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: '0'
  UV_THREADPOOL_SIZE: '128'
  USE_API_MOCK: '1'
  CI_FULL_SUITE: '1'

jobs:
  # Validation: Linting, type checking, and build (macOS for stability)
  validation:
    name: Validation & Build
    runs-on: [self-hosted, macos]
    timeout-minutes: 30

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Validate critical tests
        run: node scripts/validate-critical-tests.js

      - name: Run validation checks in parallel
        run: |
          echo "Running validation checks..."
          npm run typecheck &
          npm run lint &
          npm run prettier:check &
          npm run check:file-sizes &
          wait
          echo "✅ All validation checks completed"

      - name: Build application
        run: |
          echo "Building application..."
          npm run build
          echo "✅ Build completed successfully"

      - name: Verify build output
        run: |
          if [ ! -d ".next" ]; then
            echo "::error::.next directory not found after build"
            exit 1
          fi
          echo "✅ Build output verified ($(du -sh .next/))"

      - name: Bundle analysis
        run: |
          echo "Analyzing bundle size..."
          npm run analyze > bundle-analysis.txt 2>&1 || true
          if grep -q "First Load JS" bundle-analysis.txt; then
            BUNDLE_SIZE=$(grep "First Load JS" bundle-analysis.txt | grep -oE '[0-9]+(\.[0-9]+)? kB' | head -1 | grep -oE '[0-9]+(\.[0-9]+)?')
            if [ -n "$BUNDLE_SIZE" ]; then
              echo "Bundle size: ${BUNDLE_SIZE}KB"
              if (( $(echo "$BUNDLE_SIZE > 150" | bc -l) )); then
                echo "Bundle size exceeds 150KB limit!"
              fi
            fi
          fi

      - name: Upload build artifact
        uses: actions/upload-artifact@v4
        with:
          name: build-output
          path: .next/
          retention-days: 3
          compression-level: 6

  # Security review for PRs to main using free tools
  security-review:
    name: Security Review
    runs-on: ubuntu-latest
    timeout-minutes: 10
    if: github.event_name == 'pull_request' && github.base_ref == 'main'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run comprehensive security review
        run: |
          echo "🔍 Running security review for PR to main..."

          # Run dependency audit
          echo "## Dependency Security Audit"
          npm audit --audit-level=moderate || {
            echo "::warning::Dependency vulnerabilities found - check npm audit output"
          }

          # Run security linting
          echo "## Security Linting"
          npm run lint:security || {
            echo "::error::Security linting issues found"
            exit 1
          }

          # Check for common security patterns
          echo "## Pattern-based Security Checks"

          # Check for hardcoded secrets
          if grep -r -i "password\|secret\|key\|token" --include="*.ts" --include="*.tsx" --include="*.js" --include="*.jsx" src/ | grep -v "test" | grep -v "mock" | grep -E "(=|:)\s*['\"][^'\"]{8,}['\"]"; then
            echo "::warning::Potential hardcoded secrets found - please review"
          fi

          # Check for console.log in production code
          if find src/ -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" | grep -v test | xargs grep -l "console\.log"; then
            echo "::warning::console.log statements found in production code"
          fi

          # Check for eval usage
          if find src/ -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" | xargs grep -l "eval("; then
            echo "::error::eval() usage found - potential security risk"
            exit 1
          fi

          echo "✅ Security review completed"

      - name: Generate security report
        if: always()
        run: |
          echo "# Security Review Report" > security-review-report.md
          echo "Generated on: $(date)" >> security-review-report.md
          echo "" >> security-review-report.md

          echo "## Dependency Audit Results" >> security-review-report.md
          npm audit --json > audit-results.json 2>/dev/null || true
          if [ -s audit-results.json ]; then
            echo "Vulnerabilities found - see audit-results.json for details" >> security-review-report.md
          else
            echo "No dependency vulnerabilities found" >> security-review-report.md
          fi

          echo "" >> security-review-report.md
          echo "## Security Linting Results" >> security-review-report.md
          npm run lint:security > lint-results.txt 2>&1 || true
          if [ -s lint-results.txt ]; then
            echo "Security linting issues found - see lint-results.txt" >> security-review-report.md
          else
            echo "No security linting issues found" >> security-review-report.md
          fi

      - name: Upload security report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: security-review-report
          path: |
            security-review-report.md
            audit-results.json
            lint-results.txt
          retention-days: 30

  # Unit tests with tiered coverage based on branch importance
  test-unit:
    name: Unit Tests
    runs-on: ubicloud-standard-2
    timeout-minutes: 180  # Extended to 180min to avoid cancellations on full suites

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run tiered unit tests
        run: |
          echo "Running tiered unit tests based on branch importance..."
          export NODE_OPTIONS="--max_old_space_size=4096 --max-semi-space-size=256"
          export NODE_ENV=test
          export NEXT_PUBLIC_DISABLE_OAUTH=true

          # Determine test tier based on branch, event, and manual inputs
          echo "🔍 DEBUG: Branch: ${{ github.ref }}"
          echo "🔍 DEBUG: Base ref: ${{ github.base_ref }}"
          echo "🔍 DEBUG: Event: ${{ github.event_name }}"
          echo "🔍 DEBUG: Head branch: ${{ github.head_ref }}"
          echo "🔍 DEBUG: Manual tier: ${{ github.event.inputs.test_tier }}"
          echo "🔍 DEBUG: Force full suite: ${{ github.event.inputs.run_full_suite }}"

          # Check for manual overrides first
          if [[ "${{ github.event.inputs.run_full_suite }}" == "true" ]]; then
            echo "🏆 MANUAL OVERRIDE: Running full comprehensive test suite..."
            echo "Target: 200+ tests for maximum confidence"
            npx vitest run --coverage --config vitest.config.parallel.mjs --reporter=verbose
            TEST_TIER="production-manual"
          elif [[ "${{ github.event.inputs.test_tier }}" == "production" ]]; then
            echo "🏆 MANUAL PRODUCTION TIER: Running full comprehensive test suite..."
            echo "Target: 200+ tests for production confidence"
            npx vitest run --coverage --config vitest.config.parallel.mjs --reporter=verbose
            TEST_TIER="production-manual"
          elif [[ "${{ github.event.inputs.test_tier }}" == "integration" ]]; then
            echo "🎯 MANUAL INTEGRATION TIER: Running enhanced test suite..."
            echo "Target: 100+ tests for integration confidence"
            npx vitest run --coverage --config vitest.config.parallel.mjs --exclude="**/performance/**" --exclude="**/stress/**" --reporter=verbose
            TEST_TIER="integration-manual"
          elif [[ "${{ github.event.inputs.test_tier }}" == "development" ]]; then
            echo "⚡ MANUAL DEVELOPMENT TIER: Running minimal critical tests..."
            echo "Target: 24 tests for fast feedback"
            export VITEST_MINIMAL_SUITE=true
            npx vitest run --coverage --config vitest.config.parallel.mjs src/__tests__/font-loading.test.ts src/__tests__/login-integration.test.tsx src/test-utils/__tests__/ --reporter=verbose
            TEST_TIER="development-manual"
          elif [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            echo "🏆 PRODUCTION TIER: Running FULL comprehensive test suite..."
            echo "Target: ALL tests for maximum production confidence"
            echo "⚠️  WARNING: This will run the complete test suite (787 tests) and may take 60-90 minutes"
            echo "🔍 DEBUG: Job timeout is 120 minutes, starting production tests now..."
            echo "📊 INFO: Running full test suite for production deployment confidence"

            # Run production tests - FULL comprehensive test suite
            echo "Starting vitest at $(date)"
            # Run ALL tests with comprehensive coverage - no exclusions for production
            # Provide periodic output to avoid any CI idle timeouts
            while true; do echo "[keepalive] $(date) running vitest"; sleep 60; done &
            KEEPALIVE_PID=$!
            npx vitest run --coverage --config vitest.config.parallel.mjs --reporter=verbose; VITEST_EXIT_CODE=$?; kill $KEEPALIVE_PID || true
            VITEST_EXIT_CODE=$?
            echo "Vitest completed at $(date) with exit code: $VITEST_EXIT_CODE"

            if [ $VITEST_EXIT_CODE -ne 0 ]; then
              echo "❌ Production tests failed with exit code: $VITEST_EXIT_CODE"
              exit $VITEST_EXIT_CODE
            fi

            TEST_TIER="production"
          elif [[ "${{ github.event_name }}" == "pull_request" && "${{ github.base_ref }}" == "main" ]]; then
            echo "🎯 INTEGRATION TIER: Running enhanced test suite for PR to main..."
            echo "Target: 100+ tests for integration confidence"
            # Run comprehensive tests excluding only the most expensive ones
            npx vitest run --coverage --config vitest.config.parallel.mjs --exclude="**/performance/**" --exclude="**/stress/**" --reporter=verbose
            TEST_TIER="integration"
          else
            echo "⚡ DEVELOPMENT TIER: Running minimal critical tests..."
            echo "Target: 24 tests for fast feedback"
            echo "🔍 Using minimal test suite for daily development"
            export VITEST_MINIMAL_SUITE=true
            npx vitest run --coverage --config vitest.config.parallel.mjs src/__tests__/font-loading.test.ts src/__tests__/login-integration.test.tsx src/test-utils/__tests__/ --reporter=verbose
            TEST_TIER="development"
          fi

          # Store test tier for summary
          echo "TEST_TIER=$TEST_TIER" >> $GITHUB_ENV
          echo "✅ Unit tests completed for $TEST_TIER tier"

      - name: Upload coverage
        uses: actions/upload-artifact@v4
        if: always() && hashFiles('coverage/**') != ''
        with:
          name: coverage-report
          path: coverage/
          retention-days: 3

  # E2E Tests - Tiered based on branch importance
  e2e-tests:
    name: E2E Tests
    runs-on: ubicloud-standard-2
    timeout-minutes: 180  # Extended to 180min to avoid cancellations on full suites
    needs: [validation]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/staging' || github.base_ref == 'main' || contains(github.event.pull_request.labels.*.name, 'full-test')

    env:
      USE_API_MOCK: '1'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Download build artifact
        uses: actions/download-artifact@v4
        continue-on-error: true
        with:
          name: build-output
          path: .next/

      - name: Verify build exists
        run: |
          if [ ! -d ".next" ]; then
            echo "Build artifact not found, running build..."
            npm run build
          fi
          echo "✅ Build ready for E2E tests"

      - name: Setup Playwright
        run: |
          echo "Setting up Playwright..."
          sudo apt-get update -qq
          npx playwright install --with-deps chromium
          sudo lsof -ti:3000 | xargs sudo kill -9 || true
          echo "✅ Playwright setup completed"

      - name: Start development server
        run: |
          echo "Starting development server..."
          npm run start &
          SERVER_PID=$!
          echo "SERVER_PID=$SERVER_PID" >> $GITHUB_ENV

          for i in {1..30}; do
            if curl -f http://localhost:3000 >/dev/null 2>&1; then
              echo "✅ Server ready on port 3000"
              break
            fi
            echo "Waiting for server... ($i/30)"
            sleep 2
          done

      - name: Run tiered E2E tests
        run: |
          # Determine E2E test tier based on branch, event, and manual inputs
          echo "🔍 E2E Branch: ${{ github.ref }}"
          echo "🔍 E2E Base ref: ${{ github.base_ref }}"
          echo "🔍 E2E Event: ${{ github.event_name }}"
          echo "🔍 E2E Manual tier: ${{ github.event.inputs.test_tier }}"
          echo "🔍 E2E Force full suite: ${{ github.event.inputs.run_full_suite }}"

          # Check for manual overrides first
          if [[ "${{ github.event.inputs.run_full_suite }}" == "true" ]]; then
            echo "🏆 MANUAL OVERRIDE: Running full comprehensive E2E suite..."
            echo "Target: 200+ tests across all browsers and scenarios"
            unset CI_FULL_SUITE  # Use full Playwright config
            npx playwright test --config=playwright.config.ts
            E2E_TIER="production-manual"
          elif [[ "${{ github.event.inputs.test_tier }}" == "production" ]]; then
            echo "🏆 MANUAL PRODUCTION TIER: Running full comprehensive E2E suite..."
            echo "Target: 200+ tests across all browsers and scenarios"
            unset CI_FULL_SUITE  # Use full Playwright config
            npx playwright test --config=playwright.config.ts
            E2E_TIER="production-manual"
          elif [[ "${{ github.event.inputs.test_tier }}" == "integration" ]]; then
            echo "🎯 MANUAL INTEGRATION TIER: Running enhanced E2E suite..."
            echo "Target: 50+ tests covering key user journeys"
            export CI_ENHANCED_SUITE=1
            npx playwright test --config=playwright.config.ts
            E2E_TIER="integration-manual"
          elif [[ "${{ github.event.inputs.test_tier }}" == "development" ]]; then
            echo "⚡ MANUAL DEVELOPMENT TIER: Running minimal smoke tests..."
            echo "Target: 7 tests for critical path validation"
            export CI_FULL_SUITE=1
            npx playwright test --config=playwright.config.ts
            E2E_TIER="development-manual"
          elif [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            echo "🏆 PRODUCTION TIER: Running FULL comprehensive E2E suite..."
            echo "Target: ALL E2E tests across all browsers and scenarios"
            echo "⚠️  WARNING: This will run the complete E2E suite and may take 60-90 minutes"
            echo "🔍 DEBUG: Job timeout is 120 minutes, starting production E2E tests now..."

            # FULL comprehensive E2E suite - no limitations for production
            # Use full suite for maximum production confidence
            unset CI_ENHANCED_SUITE
            unset CI_FULL_SUITE

            echo "Starting Playwright at $(date)"
            # Provide periodic output to avoid any CI idle timeouts
            while true; do echo "[keepalive] $(date) running playwright"; sleep 60; done &
            KEEPALIVE_E2E_PID=$!
            npx playwright test --config=playwright.config.ts
            PLAYWRIGHT_EXIT_CODE=$?
            kill $KEEPALIVE_E2E_PID || true
            echo "Playwright completed at $(date) with exit code: $PLAYWRIGHT_EXIT_CODE"

            if [ $PLAYWRIGHT_EXIT_CODE -ne 0 ]; then
              echo "❌ Production E2E tests failed with exit code: $PLAYWRIGHT_EXIT_CODE"
              exit $PLAYWRIGHT_EXIT_CODE
            fi

            E2E_TIER="production"
          elif [[ "${{ github.event_name }}" == "pull_request" && "${{ github.base_ref }}" == "main" ]]; then
            echo "🎯 INTEGRATION TIER: Running enhanced E2E suite for PR to main..."
            echo "Target: 50+ tests covering key user journeys"
            # Enhanced test suite - key paths, mobile + desktop
            export CI_ENHANCED_SUITE=1
            npx playwright test --config=playwright.config.ts
            E2E_TIER="integration"
          else
            echo "⚡ DEVELOPMENT TIER: Running minimal smoke tests..."
            echo "Target: 7 tests for critical path validation"
            echo "🔍 Using minimal smoke tests for daily development"
            # Minimal smoke tests (current optimized approach)
            export CI_FULL_SUITE=1
            npx playwright test --config=playwright.config.ts
            E2E_TIER="development"
          fi

          # Store E2E tier for summary
          echo "E2E_TIER=$E2E_TIER" >> $GITHUB_ENV
          echo "✅ E2E tests completed for $E2E_TIER tier"
        env:
          CI: true
          USE_API_MOCK: 1
          PLAYWRIGHT_RETRIES: 1

      - name: Stop development server
        if: always()
        run: |
          echo "Stopping development server..."
          if [ -n "$SERVER_PID" ]; then
            kill $SERVER_PID || true
          fi

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: playwright-results
          path: playwright-report/
          retention-days: 3

  # Quality gates and comprehensive summary (macOS for final reporting)
  summary:
    name: CI Summary & Quality Gates
    runs-on: [self-hosted, macos]
    timeout-minutes: 5
    needs: [validation, security-review, test-unit, e2e-tests]
    if: always()

    steps:
      - name: Download coverage
        uses: actions/download-artifact@v4
        continue-on-error: true
        with:
          name: coverage-report
          path: coverage/

      - name: Generate comprehensive CI summary
        run: |
          echo "# DrMuscleWebApp CI Pipeline Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Branch:** \`${{ github.ref_name }}\`" >> $GITHUB_STEP_SUMMARY
          echo "**Commit:** \`${{ github.sha }}\`" >> $GITHUB_STEP_SUMMARY
          echo "**Triggered by:** ${{ github.event_name }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          echo "## Job Results" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| Job | Status | Duration | Runner |" >> $GITHUB_STEP_SUMMARY
          echo "|-----|--------|----------|--------|" >> $GITHUB_STEP_SUMMARY

          # Validation
          if [[ "${{ needs.validation.result }}" == "success" ]]; then
            echo "| Validation & Build | ✅ Passed | ~2-3min | Ubuntu |" >> $GITHUB_STEP_SUMMARY
          elif [[ "${{ needs.validation.result }}" == "failure" ]]; then
            echo "| Validation & Build | 🔴 Failed | ~2-3min | Ubuntu |" >> $GITHUB_STEP_SUMMARY
          else
            echo "| Validation & Build | Skipped | - | Ubuntu |" >> $GITHUB_STEP_SUMMARY
          fi

          # Security Review
          if [[ "${{ needs.security-review.result }}" == "success" ]]; then
            echo "| Security Review | 🔒 Passed | ~2min | Ubuntu |" >> $GITHUB_STEP_SUMMARY
          elif [[ "${{ needs.security-review.result }}" == "failure" ]]; then
            echo "| Security Review | 🚨 Failed | ~2min | Ubuntu |" >> $GITHUB_STEP_SUMMARY
          elif [[ "${{ needs.security-review.result }}" == "skipped" ]]; then
            echo "| Security Review | ⏭️ Skipped (not PR to main) | - | Ubuntu |" >> $GITHUB_STEP_SUMMARY
          else
            echo "| Security Review | ⚠️ Not Required | - | Ubuntu |" >> $GITHUB_STEP_SUMMARY
          fi

          # Unit Tests with detailed breakdown
          if [[ "${{ needs.test-unit.result }}" == "success" ]]; then
            echo "| Unit Tests (Tiered) | ✅ Passed | ~39s-8min | Ubicloud |" >> $GITHUB_STEP_SUMMARY
          elif [[ "${{ needs.test-unit.result }}" == "failure" ]]; then
            echo "| Unit Tests (Tiered) | 🔴 Failed | varies | Ubicloud |" >> $GITHUB_STEP_SUMMARY
          elif [[ "${{ needs.test-unit.result }}" == "cancelled" ]]; then
            echo "| Unit Tests (Tiered) | ⚠️ Cancelled (15min timeout) | 15min+ | Ubicloud |" >> $GITHUB_STEP_SUMMARY
          else
            echo "| Unit Tests (Tiered) | ⚠️ Skipped | - | Ubicloud |" >> $GITHUB_STEP_SUMMARY
          fi

          # E2E Tests with detailed breakdown
          if [[ "${{ needs.e2e-tests.result }}" == "success" ]]; then
            echo "| E2E Tests (Optimized) | ✅ Passed | ~2min | Ubicloud |" >> $GITHUB_STEP_SUMMARY
          elif [[ "${{ needs.e2e-tests.result }}" == "failure" ]]; then
            echo "| E2E Tests (Optimized) | 🔴 Failed | ~2min | Ubicloud |" >> $GITHUB_STEP_SUMMARY
          else
            echo "| E2E Tests (Optimized) | ⏭️ Skipped (PR branch) | - | Ubicloud |" >> $GITHUB_STEP_SUMMARY
          fi

          echo "" >> $GITHUB_STEP_SUMMARY

          # Test Coverage Breakdown
          echo "## Test Coverage Breakdown" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          # Unit Tests Details with Tier Information
          echo "### 🧪 Unit Tests" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          if [[ "${{ needs.test-unit.result }}" == "success" ]]; then
            # Determine test tier for display
            if [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
              echo "- **Test Tier**: 🏆 **PRODUCTION** (Full Comprehensive Suite)" >> $GITHUB_STEP_SUMMARY
              echo "- **Tests Run**: 200+ tests (all test files)" >> $GITHUB_STEP_SUMMARY
              echo "- **Coverage**: Full codebase coverage with strict thresholds" >> $GITHUB_STEP_SUMMARY
              echo "- **Duration**: ~8 minutes (comprehensive validation)" >> $GITHUB_STEP_SUMMARY
            elif [[ "${{ github.ref }}" == "refs/heads/staging" ]] || [[ "${{ github.base_ref }}" == "main" ]]; then
              echo "- **Test Tier**: 🎯 **INTEGRATION** (Enhanced Test Suite)" >> $GITHUB_STEP_SUMMARY
              echo "- **Tests Run**: 100+ tests (core functionality)" >> $GITHUB_STEP_SUMMARY
              echo "- **Coverage**: Key components and user flows" >> $GITHUB_STEP_SUMMARY
              echo "- **Duration**: ~3 minutes (integration confidence)" >> $GITHUB_STEP_SUMMARY
            else
              echo "- **Test Tier**: ⚡ **DEVELOPMENT** (Minimal Critical Suite)" >> $GITHUB_STEP_SUMMARY
              echo "- **Tests Run**: 24 tests (3 critical test files)" >> $GITHUB_STEP_SUMMARY
              echo "- **Coverage**: ~4.69% statements (critical paths only)" >> $GITHUB_STEP_SUMMARY
              echo "- **Duration**: ~39 seconds (fast feedback)" >> $GITHUB_STEP_SUMMARY
              echo "" >> $GITHUB_STEP_SUMMARY
              echo "**Critical Test Files:**" >> $GITHUB_STEP_SUMMARY
              echo "- \`font-loading.test.ts\` - 7 tests (font optimization)" >> $GITHUB_STEP_SUMMARY
              echo "- \`login-integration.test.tsx\` - 15 tests (login flow)" >> $GITHUB_STEP_SUMMARY
              echo "- \`navigation-mock.test.ts\` - 2 tests (test utilities)" >> $GITHUB_STEP_SUMMARY
            fi
            echo "- **Success Rate**: 100% (all tests passed)" >> $GITHUB_STEP_SUMMARY
          elif [[ "${{ needs.test-unit.result }}" == "cancelled" ]]; then
            echo "- **Status**: ⚠️ **CANCELLED** (Timeout - likely tiered logic issue)" >> $GITHUB_STEP_SUMMARY
            echo "- **Issue**: Tests ran longer than expected (15min timeout)" >> $GITHUB_STEP_SUMMARY
            echo "- **Likely Cause**: Tiered logic not working, ran full suite instead of minimal" >> $GITHUB_STEP_SUMMARY
            echo "- **Next Steps**: Debug branch detection logic in workflow" >> $GITHUB_STEP_SUMMARY
          elif [[ "${{ needs.test-unit.result }}" == "failure" ]]; then
            echo "- **Status**: 🔴 **FAILED** (Check logs for details)" >> $GITHUB_STEP_SUMMARY
          else
            echo "- **Status**: ⏭️ **SKIPPED** (Not required for this branch)" >> $GITHUB_STEP_SUMMARY
          fi
          echo "" >> $GITHUB_STEP_SUMMARY

          # E2E Tests Details with Tier Information
          echo "### 🎭 E2E Tests" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          if [[ "${{ needs.e2e-tests.result }}" == "success" ]]; then
            # Determine E2E tier for display
            if [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
              echo "- **Test Tier**: 🏆 **PRODUCTION** (Full Comprehensive Suite)" >> $GITHUB_STEP_SUMMARY
              echo "- **Tests Run**: 200+ tests (all browsers & scenarios)" >> $GITHUB_STEP_SUMMARY
              echo "- **Browsers**: Mobile Safari, Mobile Chrome, Desktop Chrome, Firefox, WebKit" >> $GITHUB_STEP_SUMMARY
              echo "- **Coverage**: Complete user journeys, edge cases, accessibility" >> $GITHUB_STEP_SUMMARY
              echo "- **Duration**: ~30 minutes (production confidence)" >> $GITHUB_STEP_SUMMARY
              echo "- **Error Handling**: Strict validation, no error filtering" >> $GITHUB_STEP_SUMMARY
            elif [[ "${{ github.ref }}" == "refs/heads/staging" ]] || [[ "${{ github.base_ref }}" == "main" ]]; then
              echo "- **Test Tier**: 🎯 **INTEGRATION** (Enhanced Test Suite)" >> $GITHUB_STEP_SUMMARY
              echo "- **Tests Run**: 50+ tests (key user journeys)" >> $GITHUB_STEP_SUMMARY
              echo "- **Browsers**: Mobile Safari, Mobile Chrome" >> $GITHUB_STEP_SUMMARY
              echo "- **Coverage**: Core workflows, authentication, navigation" >> $GITHUB_STEP_SUMMARY
              echo "- **Duration**: ~15 minutes (integration confidence)" >> $GITHUB_STEP_SUMMARY
              echo "- **Error Handling**: Balanced validation with smart filtering" >> $GITHUB_STEP_SUMMARY
            else
              echo "- **Test Tier**: ⚡ **DEVELOPMENT** (Minimal Smoke Tests)" >> $GITHUB_STEP_SUMMARY
              echo "- **Tests Run**: 7 tests (2 critical test files)" >> $GITHUB_STEP_SUMMARY
              echo "- **Browser**: Mobile Chrome (Pixel 5 simulation)" >> $GITHUB_STEP_SUMMARY
              echo "- **Coverage**: Critical page loads and API connectivity" >> $GITHUB_STEP_SUMMARY
              echo "- **Duration**: ~2 minutes (fast feedback)" >> $GITHUB_STEP_SUMMARY
              echo "- **Error Handling**: Smart filtering of non-critical errors" >> $GITHUB_STEP_SUMMARY
              echo "" >> $GITHUB_STEP_SUMMARY
              echo "**Critical Test Files:**" >> $GITHUB_STEP_SUMMARY
              echo "- \`smoke-test.spec.ts\` - 5 tests (critical page loads)" >> $GITHUB_STEP_SUMMARY
              echo "- \`api-check.spec.ts\` - 2 tests (API connectivity)" >> $GITHUB_STEP_SUMMARY
            fi
            echo "- **Success Rate**: 100% (all tests passed)" >> $GITHUB_STEP_SUMMARY
          elif [[ "${{ needs.e2e-tests.result }}" == "failure" ]]; then
            echo "- **Status**: Tests failed (check logs for details)" >> $GITHUB_STEP_SUMMARY
          else
            echo "- **Status**: Skipped (only runs on important branches)" >> $GITHUB_STEP_SUMMARY
            echo "- **Full Suite Available**: Run locally with \`npm run test:e2e\`" >> $GITHUB_STEP_SUMMARY
          fi
          echo "" >> $GITHUB_STEP_SUMMARY

          # Overall Status
          echo "## Overall Status" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          FAILED_JOBS=0
          if [[ "${{ needs.validation.result }}" == "failure" ]]; then ((FAILED_JOBS++)); fi
          if [[ "${{ needs.security-review.result }}" == "failure" ]]; then ((FAILED_JOBS++)); fi
          if [[ "${{ needs.test-unit.result }}" == "failure" || "${{ needs.test-unit.result }}" == "cancelled" ]]; then ((FAILED_JOBS++)); fi
          if [[ "${{ needs.e2e-tests.result }}" == "failure" ]]; then ((FAILED_JOBS++)); fi

          if [[ $FAILED_JOBS -gt 0 ]]; then
            echo "### 🔴 **PIPELINE FAILED**" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "**$FAILED_JOBS job(s) failed**" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "**Next steps:**" >> $GITHUB_STEP_SUMMARY
            echo "1. Check the failed job logs above" >> $GITHUB_STEP_SUMMARY
            echo "2. Fix the identified issues" >> $GITHUB_STEP_SUMMARY
            echo "3. Push your fixes to re-trigger the pipeline" >> $GITHUB_STEP_SUMMARY
          else
            echo "### ✅ **PIPELINE PASSED**" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "All required checks completed successfully!" >> $GITHUB_STEP_SUMMARY
          fi

          echo "" >> $GITHUB_STEP_SUMMARY

          # Tiered Testing Strategy Overview
          echo "## 🎯 Tiered Testing Strategy" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| Branch/Event | Test Tier | Unit Tests | E2E Tests | Total Time | Purpose |" >> $GITHUB_STEP_SUMMARY
          echo "|--------------|-----------|------------|-----------|------------|---------|" >> $GITHUB_STEP_SUMMARY
          echo "| **main branch** | 🏆 Production | 200+ tests (~8min) | 200+ tests (~30min) | **~45min** | Production confidence |" >> $GITHUB_STEP_SUMMARY
          echo "| **staging / PR→main** | 🎯 Integration | 100+ tests (~3min) | 50+ tests (~15min) | **~20min** | Integration confidence |" >> $GITHUB_STEP_SUMMARY
          echo "| **feature branches** | ⚡ Development | 24 tests (~39s) | 7 tests (~2min) | **~5min** | Fast feedback |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          echo "## ⚡ Performance vs Quality Balance" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| Metric | Development | Integration | Production |" >> $GITHUB_STEP_SUMMARY
          echo "|--------|-------------|-------------|------------|" >> $GITHUB_STEP_SUMMARY
          echo "| **Speed** | ⚡⚡⚡ Ultra Fast | ⚡⚡ Fast | ⚡ Thorough |" >> $GITHUB_STEP_SUMMARY
          echo "| **Coverage** | 🎯 Critical Paths | 🎯🎯 Key Features | 🎯🎯🎯 Comprehensive |" >> $GITHUB_STEP_SUMMARY
          echo "| **Confidence** | ✅ Basic | ✅✅ High | ✅✅✅ Production Ready |" >> $GITHUB_STEP_SUMMARY
          echo "| **Use Case** | Daily development | Pre-merge validation | Production deployment |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          echo "## 🚀 CI Optimizations Active" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Test Suite Optimizations:" >> $GITHUB_STEP_SUMMARY
          echo "- **Smart Unit Testing**: Minimal critical suite (24 tests vs 200+)" >> $GITHUB_STEP_SUMMARY
          echo "- **Focused E2E Testing**: Ultra-minimal smoke tests (7 tests vs 200+)" >> $GITHUB_STEP_SUMMARY
          echo "- **Intelligent Error Filtering**: Ignores non-critical errors (404s, CSP, hydration)" >> $GITHUB_STEP_SUMMARY
          echo "- **Fast Loading Strategy**: \`domcontentloaded\` vs \`networkidle\`" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Infrastructure Optimizations:" >> $GITHUB_STEP_SUMMARY
          echo "- **API Mocking**: 60% faster E2E tests with mocked backend" >> $GITHUB_STEP_SUMMARY
          echo "- **Smart Branching**: E2E only on main/staging branches" >> $GITHUB_STEP_SUMMARY
          echo "- **Optimized Runners**: Ubicloud for cost-effective parallel testing" >> $GITHUB_STEP_SUMMARY
          echo "- **Parallel Execution**: All jobs run concurrently when possible" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Quality Assurance:" >> $GITHUB_STEP_SUMMARY
          echo "- **Full test suite available locally**: Run \`npm test\` for comprehensive testing" >> $GITHUB_STEP_SUMMARY
          echo "- **Critical path coverage**: All essential functionality verified" >> $GITHUB_STEP_SUMMARY
          echo "- **Security monitoring**: Automated vulnerability scanning" >> $GITHUB_STEP_SUMMARY
          echo "- **Performance tracking**: Bundle size and build time monitoring" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "---" >> $GITHUB_STEP_SUMMARY
          echo "*Generated at $(date -u '+%Y-%m-%d %H:%M:%S UTC')*" >> $GITHUB_STEP_SUMMARY

      - name: Final status check
        run: |
          echo "Performing final status evaluation..."
          echo "📊 Job Results:"
          echo "- Validation: ${{ needs.validation.result }}"
          echo "- Security Review: ${{ needs.security-review.result }}"
          echo "- Unit Tests: ${{ needs.test-unit.result }}"
          echo "- E2E Tests: ${{ needs.e2e-tests.result }}"

          # Check validation (always required)
          if [[ "${{ needs.validation.result }}" != "success" ]]; then
            echo "🔴 Validation failed!"
            exit 1
          fi

          # Check unit tests (allow cancelled if it was due to timeout during development)
          if [[ "${{ needs.test-unit.result }}" == "failure" ]]; then
            echo "🔴 Unit tests failed!"
            exit 1
          elif [[ "${{ needs.test-unit.result }}" == "cancelled" ]]; then
            echo "⚠️ Unit tests were cancelled (likely timeout)"
            echo "This suggests the tiered testing logic needs debugging"
            echo "For now, allowing this to pass for development branches"
            if [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
              echo "🔴 Cannot allow cancelled tests on main branch!"
              exit 1
            fi
          fi

          # Security Review is required for PRs to main
          if [[ "${{ github.event_name }}" == "pull_request" && "${{ github.base_ref }}" == "main" ]]; then
            if [[ "${{ needs.security-review.result }}" != "success" ]]; then
              echo "🚨 Security Review failed for PR to main!"
              echo "- Security review must pass for all PRs targeting main branch"
              echo "- Check the uploaded security artifacts for detailed findings"
              echo "- Run 'npm run security:review' locally to debug issues"
              exit 1
            fi
          fi

          # Check E2E tests
          if [[ "${{ needs.e2e-tests.result }}" == "failure" ]]; then
            echo "🔴 E2E tests failed!"
            exit 1
          fi

          echo "✅ All required checks passed!"
