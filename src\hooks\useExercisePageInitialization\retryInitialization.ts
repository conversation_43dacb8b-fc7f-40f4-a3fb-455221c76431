/**
 * Retry initialization logic for exercise page
 * Extracted from useExercisePageInitialization to reduce file size
 */

import { WorkoutTemplateGroupModel, RecommendationModel } from '@/types'
import { ExerciseWorkSetsModel } from '@/types/workout'
import { WorkoutSession } from '@/types/workoutSession'
import { debugLog } from '@/utils/debugLog'
import { RecommendationLoadingCoordinator } from '@/utils/RecommendationLoadingCoordinator'
import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime'

interface RetryInitializationParams {
  exerciseId: number
  workoutSession: WorkoutSession | null
  todaysWorkout: WorkoutTemplateGroupModel[] | null
  isLoadingWorkout: boolean
  exercises: ExerciseWorkSetsModel[] | null
  startWorkout: (
    workout: WorkoutTemplateGroupModel[]
  ) => Promise<{ success: boolean }>
  setCurrentExerciseById: (id: number) => void
  getCachedExerciseRecommendation: (
    id: number
  ) => RecommendationModel | null | undefined
  loadingStates: Map<number, boolean>
  loadRecommendation: (id: number, label: string) => void
  updateExerciseWorkSets: (id: number, sets: unknown[]) => void
  router: AppRouterInstance
  retryCount: number
  setIsInitializing: (value: boolean) => void
  setLoadingError: (error: Error | null) => void
  setRetryCount: (fn: (prev: number) => number) => void
}

export async function retryInitialization({
  exerciseId,
  workoutSession,
  todaysWorkout,
  isLoadingWorkout,
  exercises,
  startWorkout,
  setCurrentExerciseById,
  getCachedExerciseRecommendation,
  loadingStates,
  loadRecommendation,
  updateExerciseWorkSets,
  router,
  retryCount,
  setIsInitializing,
  setLoadingError,
  setRetryCount,
}: RetryInitializationParams): Promise<void> {
  try {
    setIsInitializing(true)
    setLoadingError(null)
    setRetryCount((prev) => prev + 1)

    // Note: Auth is handled by API client, not here

    // Start workout if needed
    if (!workoutSession && todaysWorkout && !isLoadingWorkout) {
      const workoutGroup = todaysWorkout[0]
      const workout = workoutGroup?.WorkoutTemplates?.[0]

      if (workout) {
        const result = await startWorkout(todaysWorkout)
        if (!result.success) {
          debugLog.error('Failed to start workout')
          router.replace('/workout')
          return
        }
      } else {
        router.replace('/workout')
        return
      }
    }

    // Set current exercise and load recommendations
    if (exerciseId) {
      // Skip validation if exercises haven't been loaded yet or if still loading workout
      // Also skip if we don't have a workout session yet (still initializing)
      // IMPORTANT: Keep initializing state until we have confirmed workout data
      if (
        !exercises ||
        exercises.length === 0 ||
        isLoadingWorkout ||
        !workoutSession
      ) {
        debugLog.log(
          '[useExercisePageInitialization] Exercises not loaded yet, workout still loading, or no session, skipping validation',
          {
            hasExercises: !!exercises,
            exercisesLength: exercises?.length || 0,
            isLoadingWorkout,
            hasWorkoutSession: !!workoutSession,
          }
        )
        // Don't mark as complete yet - we're still waiting for data
        return
      }

      // First validate that the exercise exists in the workout
      debugLog.log('[useExercisePageInitialization] Validating exercise:', {
        exerciseId,
        exercisesLength: exercises?.length,
        exerciseIds: exercises?.map((ex) => ex.Id),
        exerciseIdType: typeof exerciseId,
      })

      const exercise = exercises?.find(
        (ex) => Number(ex.Id) === Number(exerciseId)
      )

      if (!exercise) {
        // Exercise not found in workout
        debugLog.error(
          `Exercise ${exerciseId} not found in workout exercises`,
          {
            exerciseId,
            exerciseIdType: typeof exerciseId,
            availableExercises: exercises?.map((ex) => ({
              id: ex.Id,
              label: ex.Label,
              idType: typeof ex.Id,
            })),
          }
        )
        throw new Error(`Exercise ${exerciseId} not found in workout`)
      }

      setCurrentExerciseById(exerciseId)

      // Check if recommendation is loaded for this exercise
      const hasRecommendation = getCachedExerciseRecommendation(exerciseId)
      const isLoadingRecommendation = loadingStates.get(exerciseId)

      // If no recommendation and not loading, trigger loading
      if (!hasRecommendation && !isLoadingRecommendation) {
        // Check with coordinator before loading
        try {
          const coordinator = RecommendationLoadingCoordinator.getInstance()
          if (coordinator.canStartLoading(exerciseId)) {
            // Start loading with 30 second timeout to prevent infinite skeletons
            coordinator.startLoading(exerciseId, { timeout: 30000 })
            loadRecommendation(exerciseId, exercise.Label || 'Exercise')
          } else {
            debugLog.log(
              '[useExercisePageInitialization] Coordinator blocked loading - already in progress',
              { exerciseId }
            )
          }
        } catch (error) {
          // If coordinator fails, proceed with loading anyway
          debugLog.error(
            '[useExercisePageInitialization] Coordinator error, proceeding with load',
            error
          )
          loadRecommendation(exerciseId, exercise.Label || 'Exercise')
        }
      }

      // Pre-load recommendation if not already loaded using alternative method
      if (!exercise.sets || exercise.sets.length === 0) {
        updateExerciseWorkSets(exerciseId, [])
      }
    }
  } catch (error) {
    debugLog.error('Failed to retry initialization:', error)

    // Only check retry limit, not auth errors (handled by API client)
    if (retryCount >= 3) {
      debugLog('⚠️ [retryInitialization] Retry limit exceeded', {
        retryCount,
      })
      // Don't redirect - let user see the error and retry manually
    }

    setLoadingError(
      error instanceof Error
        ? error
        : new Error('Failed to retry initialization')
    )
  } finally {
    setIsInitializing(false)
  }
}
