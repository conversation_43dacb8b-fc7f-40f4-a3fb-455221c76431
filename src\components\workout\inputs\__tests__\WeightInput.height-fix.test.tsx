import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { WeightInput } from '../WeightInput'

describe('WeightInput height stability', () => {
  const defaultProps = {
    weight: 55,
    unit: 'lbs' as const,
    onChange: vi.fn(),
    onIncrement: vi.fn(),
    onDecrement: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should have a fixed height container for the input', () => {
    const { container } = render(<WeightInput {...defaultProps} />)

    // Check that the input container has fixed height styling
    const inputContainer = container.querySelector('.relative')
    expect(inputContainer).toBeTruthy()

    // The container should have a fixed height class
    expect(inputContainer?.className).toContain('h-28')
  })

  it('should maintain container height when changing from 2-digit to 3-digit number', async () => {
    const { container, rerender } = render(<WeightInput {...defaultProps} />)

    const inputContainer = container.querySelector('.relative')
    const initialHeight = inputContainer?.getBoundingClientRect().height

    // Change to 3-digit number
    rerender(<WeightInput {...defaultProps} weight={555} />)

    const updatedHeight = inputContainer?.getBoundingClientRect().height
    expect(updatedHeight).toBe(initialHeight)
  })

  it('should maintain container height across different weight values', () => {
    const testValues = [0, 5, 55, 555, 55.5, 555.55, 1000]
    const heights: number[] = []

    testValues.forEach((weight) => {
      const { container } = render(
        <WeightInput {...defaultProps} weight={weight} />
      )
      const inputContainer = container.querySelector('.relative')
      const height = inputContainer?.getBoundingClientRect().height || 0
      heights.push(height)
    })

    // All heights should be the same
    const uniqueHeights = [...new Set(heights)]
    expect(uniqueHeights).toHaveLength(1)
  })

  it('should accommodate the largest text size (text-7xl) without overflow', () => {
    const { container } = render(<WeightInput {...defaultProps} weight={55} />)

    const input = screen.getByLabelText('Weight')
    const inputContainer = container.querySelector('.relative')

    // Check that text-7xl class is applied for 2-digit numbers
    expect(input.className).toContain('text-7xl')

    // Container should have h-28 class which provides enough height for text-7xl
    expect(inputContainer?.className).toContain('h-28')
    expect(inputContainer?.className).toContain('flex')
    expect(inputContainer?.className).toContain('items-center')
  })

  it('should not cause layout shift when typing different length numbers', async () => {
    const user = userEvent.setup()
    const handleChange = vi.fn()
    const { container } = render(
      <WeightInput {...defaultProps} onChange={handleChange} />
    )

    const input = screen.getByLabelText('Weight') as HTMLInputElement
    const inputContainer = container.querySelector('.relative')
    const initialHeight = inputContainer?.getBoundingClientRect().height

    // Clear and type new value
    await user.clear(input)
    await user.type(input, '555')

    expect(handleChange).toHaveBeenCalledWith('555')

    // Height should remain the same
    const finalHeight = inputContainer?.getBoundingClientRect().height
    expect(finalHeight).toBe(initialHeight)
  })

  it('should handle decimal values without height change', () => {
    const { container: container1 } = render(
      <WeightInput {...defaultProps} weight={55} />
    )
    const { container: container2 } = render(
      <WeightInput {...defaultProps} weight={55.55} />
    )

    const height1 = container1
      .querySelector('.relative')
      ?.getBoundingClientRect().height
    const height2 = container2
      .querySelector('.relative')
      ?.getBoundingClientRect().height

    expect(height1).toBe(height2)
  })

  it('should work correctly with both kg and lbs units', () => {
    const { container: containerLbs } = render(
      <WeightInput {...defaultProps} unit="lbs" />
    )
    const { container: containerKg } = render(
      <WeightInput {...defaultProps} unit="kg" />
    )

    const heightLbs = containerLbs
      .querySelector('.relative')
      ?.getBoundingClientRect().height
    const heightKg = containerKg
      .querySelector('.relative')
      ?.getBoundingClientRect().height

    expect(heightLbs).toBe(heightKg)
  })
})
