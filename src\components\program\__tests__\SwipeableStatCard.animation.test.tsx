import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import { SwipeableStatCard } from '../SwipeableStatCard'
import type { UserStats } from '@/types'

// Mock framer-motion to avoid animation complexities in tests
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  useAnimation: () => ({
    start: vi.fn(),
  }),
}))

// Mock haptic feedback
vi.mock('@/utils/haptic', () => ({
  useHaptic: () => ({
    trigger: vi.fn(),
  }),
}))

// Mock auth store
vi.mock('@/stores/authStore', () => ({
  useAuthStore: vi.fn(() => ({
    user: { massUnit: 'lbs' },
  })),
}))

// Mock the animation hook to control animation values
const mockAnimatedValues = vi.fn()
const mockResetAnimation = vi.fn()

vi.mock('@/hooks/useStatCounterAnimation', () => ({
  useStatCounterAnimation: vi.fn(() => ({
    animatedValues: mockAnimatedValues(),
    resetAnimation: mockResetAnimation,
  })),
}))

describe('SwipeableStatCard Animation Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.useFakeTimers()
    mockAnimatedValues.mockReturnValue([0, 0, 0])
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  describe('Animation State Machine', () => {
    it('should start animation from 0 when component mounts with no data', () => {
      mockAnimatedValues.mockReturnValue([0, 0, 0])

      render(<SwipeableStatCard stats={null} isLoading />)

      expect(screen.getByTestId('stat-value')).toHaveTextContent('0')
      expect(screen.getByText('Loading stats...')).toBeInTheDocument()
    })

    it('should smoothly animate to estimated values during initial mount', async () => {
      // Simulate animation progress
      mockAnimatedValues
        .mockReturnValueOnce([0, 0, 0])
        .mockReturnValueOnce([25, 5, 7500])
        .mockReturnValueOnce([50, 10, 15000])

      const { rerender } = render(<SwipeableStatCard stats={null} isLoading />)

      // Initial state
      expect(screen.getByTestId('stat-value')).toHaveTextContent('0')

      // Mid-animation (750ms)
      vi.advanceTimersByTime(750)
      rerender(<SwipeableStatCard stats={null} isLoading />)
      expect(screen.getByTestId('stat-value')).toHaveTextContent('25')

      // Animation complete (1500ms)
      vi.advanceTimersByTime(750)
      rerender(<SwipeableStatCard stats={null} isLoading />)
      expect(screen.getByTestId('stat-value')).toHaveTextContent('50')
    })

    it('should NOT restart animation when real data arrives during animation', async () => {
      const mockStats: UserStats = {
        weekStreak: 8,
        workoutsCompleted: 52,
        lbsLifted: 15750,
      }

      // Start with loading state
      mockAnimatedValues.mockReturnValue([25, 5, 7500])
      const { rerender } = render(<SwipeableStatCard stats={null} isLoading />)

      // Animation in progress
      expect(screen.getByTestId('stat-value')).toHaveTextContent('25')
      const resetCallsBefore = mockResetAnimation.mock.calls.length

      // Data arrives mid-animation
      rerender(<SwipeableStatCard stats={mockStats} isLoading={false} />)

      // Should NOT reset animation
      expect(mockResetAnimation).toHaveBeenCalledTimes(resetCallsBefore)

      // Should continue showing animated values until animation completes
      expect(screen.getByTestId('stat-value')).toHaveTextContent('25')
    })

    it('should transition smoothly from estimated to real values after animation completes', async () => {
      const mockStats: UserStats = {
        weekStreak: 8,
        workoutsCompleted: 52,
        lbsLifted: 15750,
      }

      // Start animation
      mockAnimatedValues.mockReturnValue([50, 10, 15000])
      const { rerender } = render(<SwipeableStatCard stats={null} isLoading />)

      // Complete initial animation (1500ms)
      vi.advanceTimersByTime(1500)

      // Now provide real data
      rerender(<SwipeableStatCard stats={mockStats} isLoading={false} />)

      // Should transition to real values
      await waitFor(() => {
        expect(screen.getByTestId('stat-value')).toHaveTextContent('52')
      })

      // Loading message should be gone
      expect(screen.queryByText('Loading stats...')).not.toBeInTheDocument()
    })

    it('should handle instant data (cached) by still showing initial animation', () => {
      const mockStats: UserStats = {
        weekStreak: 8,
        workoutsCompleted: 52,
        lbsLifted: 15750,
      }

      // Data available immediately
      mockAnimatedValues.mockReturnValue([0, 0, 0])
      render(<SwipeableStatCard stats={mockStats} isLoading={false} />)

      // Should still show animation for perceived performance
      expect(screen.getByTestId('stat-value')).toHaveTextContent('0')
      expect(screen.getByText('Loading stats...')).toBeInTheDocument()
    })

    it('should NOT reset animation when swiping between stats during loading', () => {
      mockAnimatedValues.mockReturnValue([25, 5, 7500])

      render(<SwipeableStatCard stats={null} isLoading />)

      const resetCallsBefore = mockResetAnimation.mock.calls.length

      // Simulate swipe to next stat
      const indicators = screen.getAllByTestId('stat-indicator')
      indicators[1].click()

      // Should NOT reset animation on index change
      expect(mockResetAnimation).toHaveBeenCalledTimes(resetCallsBefore)
    })

    it('should maintain single animation flow without drops to zero', async () => {
      const animationSequence = [
        [0, 0, 0],
        [10, 2, 3000],
        [25, 5, 7500],
        [40, 8, 12000],
        [50, 10, 15000],
      ]

      let sequenceIndex = 0
      mockAnimatedValues.mockImplementation(
        () => animationSequence[sequenceIndex]
      )

      const { rerender } = render(<SwipeableStatCard stats={null} isLoading />)

      // Track all displayed values
      const displayedValues: string[] = []

      animationSequence.forEach((values) => {
        mockAnimatedValues.mockReturnValue(values)
        rerender(<SwipeableStatCard stats={null} isLoading />)
        displayedValues.push(screen.getByTestId('stat-value').textContent || '')
        sequenceIndex++
      })

      // Values should only increase, never drop back to 0
      const numericValues = displayedValues.map((v) => parseInt(v) || 0)
      for (let i = 1; i < numericValues.length; i++) {
        expect(numericValues[i]).toBeGreaterThanOrEqual(numericValues[i - 1])
      }
    })

    it('should handle component unmount during animation gracefully', () => {
      mockAnimatedValues.mockReturnValue([25, 5, 7500])

      const { unmount } = render(<SwipeableStatCard stats={null} isLoading />)

      // Should not throw when unmounting mid-animation
      expect(() => unmount()).not.toThrow()
    })

    it('should respect prefers-reduced-motion and skip animation', () => {
      // Mock reduced motion preference
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: vi.fn().mockImplementation((query) => ({
          matches: query === '(prefers-reduced-motion: reduce)',
          media: query,
          onchange: null,
          addListener: vi.fn(),
          removeListener: vi.fn(),
          addEventListener: vi.fn(),
          removeEventListener: vi.fn(),
          dispatchEvent: vi.fn(),
        })),
      })

      const mockStats: UserStats = {
        weekStreak: 8,
        workoutsCompleted: 52,
        lbsLifted: 15750,
      }

      render(<SwipeableStatCard stats={mockStats} isLoading={false} />)

      // Should immediately show real values without animation
      expect(screen.getByTestId('stat-value')).toHaveTextContent('52')
      expect(screen.queryByText('Loading stats...')).not.toBeInTheDocument()
    })
  })

  describe('Edge Cases', () => {
    it('should handle null stats gracefully', () => {
      mockAnimatedValues.mockReturnValue([0, 0, 0])

      const { container } = render(
        <SwipeableStatCard stats={null} isLoading={false} />
      )

      // Should still render without crashing
      expect(container.firstChild).toBeInTheDocument()
    })

    it('should handle very slow data arrival (after animation completes)', async () => {
      mockAnimatedValues.mockReturnValue([50, 10, 15000])

      const { rerender } = render(<SwipeableStatCard stats={null} isLoading />)

      // Animation completes
      vi.advanceTimersByTime(1500)

      // Data arrives much later
      vi.advanceTimersByTime(5000)

      const mockStats: UserStats = {
        weekStreak: 8,
        workoutsCompleted: 52,
        lbsLifted: 15750,
      }

      rerender(<SwipeableStatCard stats={mockStats} isLoading={false} />)

      // Should update to real values without re-animating
      await waitFor(() => {
        expect(screen.getByTestId('stat-value')).toHaveTextContent('52')
      })
    })
  })
})
