import { test, expect, devices } from '@playwright/test'

const loginSubtitle = "World's Fastest AI Personal Trainer"
const expectedSubtitleColor = '#b8b8bc' // text-text-secondary

test.describe('Login Subtitle Cross-Browser Testing', () => {
  // Core functionality tests
  test('should display subtitle correctly on Mobile Safari', async ({
    browser,
  }) => {
    const context = await browser.newContext(devices['iPhone 14'])
    const page = await context.newPage()

    await page.goto('/login')
    await page.waitForLoadState('networkidle')

    await page.waitForSelector('input[type="email"]', {
      state: 'visible',
      timeout: 15000,
    })

    const subtitle = page
      .locator('p')
      .filter({ hasText: loginSubtitle })
      .first()
    await expect(subtitle).toBeVisible()
    await expect(subtitle).toHaveText(loginSubtitle)

    // Check CSS variable support
    const cssVariableValue = await subtitle.evaluate((el) => {
      const rootStyle = window.getComputedStyle(document.documentElement)
      return rootStyle.getPropertyValue('--color-text-secondary').trim()
    })

    expect(cssVariableValue).toBe(expectedSubtitleColor)
    await context.close()
  })

  test('should display subtitle correctly on Chrome Android', async ({
    browser,
  }) => {
    const context = await browser.newContext(devices['Galaxy S21'])
    const page = await context.newPage()

    await page.goto('/login')
    await page.waitForLoadState('networkidle')

    await page.waitForSelector('input[type="email"]', {
      state: 'visible',
      timeout: 15000,
    })

    const subtitle = page
      .locator('p')
      .filter({ hasText: loginSubtitle })
      .first()
    await expect(subtitle).toBeVisible()
    await expect(subtitle).toHaveText(loginSubtitle)

    // Test advanced CSS features support
    const features = await subtitle.evaluate(() => {
      const style = window.getComputedStyle(document.documentElement)
      return {
        cssVariablesSupported:
          style.getPropertyValue('--color-text-secondary') !== '',
        backdropFilterSupported: 'backdropFilter' in document.body.style,
        gridSupported: 'grid' in document.body.style,
      }
    })

    expect(features.cssVariablesSupported).toBe(true)
    expect(features.backdropFilterSupported).toBe(true)
    expect(features.gridSupported).toBe(true)

    await context.close()
  })

  test('should handle different mobile viewports', async ({ browser }) => {
    const viewports = [
      { name: 'iPhone SE', device: devices['iPhone SE'] },
      { name: 'iPhone 14', device: devices['iPhone 14'] },
      { name: 'Galaxy S21', device: devices['Galaxy S21'] },
    ]

    for (const { name, device } of viewports) {
      const context = await browser.newContext(device)
      const page = await context.newPage()

      await page.goto('/login')
      await page.waitForLoadState('networkidle')

      await page.waitForSelector('input[type="email"]', {
        state: 'visible',
        timeout: 15000,
      })

      const subtitle = page
        .locator('p')
        .filter({ hasText: loginSubtitle })
        .first()
      await expect(subtitle).toBeVisible()

      // Check text doesn't overflow
      const subtitleBox = await subtitle.boundingBox()
      const viewport = page.viewportSize()
      if (subtitleBox && viewport) {
        expect(subtitleBox.width).toBeLessThanOrEqual(viewport.width - 32) // Account for padding
      }

      await context.close()
    }
  })

  test('should maintain correct color across browsers', async ({ browser }) => {
    const testDevices = [
      { name: 'Safari iOS', device: devices['iPhone 14'] },
      { name: 'Chrome Android', device: devices['Galaxy S21'] },
    ]

    const colors: string[] = []

    for (const { name, device } of testDevices) {
      const context = await browser.newContext(device)
      const page = await context.newPage()

      await page.goto('/login')
      await page.waitForLoadState('networkidle')

      await page.waitForSelector('input[type="email"]', {
        state: 'visible',
        timeout: 15000,
      })

      const subtitle = page
        .locator('p')
        .filter({ hasText: loginSubtitle })
        .first()
      await expect(subtitle).toBeVisible()

      const color = await subtitle.evaluate(
        (el) => window.getComputedStyle(el).color
      )
      colors.push(color)

      await context.close()
    }

    // Colors should be consistent across browsers
    expect(colors[0]).toBe(colors[1])
  })

  test('should meet accessibility standards', async ({ browser }) => {
    const context = await browser.newContext(devices['iPhone 14'])
    const page = await context.newPage()

    await page.goto('/login')
    await page.waitForLoadState('networkidle')

    await page.waitForSelector('input[type="email"]', {
      state: 'visible',
      timeout: 15000,
    })

    const subtitle = page
      .locator('p')
      .filter({ hasText: loginSubtitle })
      .first()
    await expect(subtitle).toBeVisible()

    // Check contrast ratio meets WCAG AA standard
    const contrast = await subtitle.evaluate(() => {
      const getLuminance = (hex: string) => {
        const rgb = parseInt(hex.slice(1), 16)
        const r = (rgb >> 16) & 255
        const g = (rgb >> 8) & 255
        const b = rgb & 255
        return (0.299 * r + 0.587 * g + 0.114 * b) / 255
      }

      const textLum = getLuminance('#b8b8bc')
      const bgLum = getLuminance('#0a0a0b')

      const lighter = Math.max(textLum, bgLum)
      const darker = Math.min(textLum, bgLum)

      return (lighter + 0.05) / (darker + 0.05)
    })

    // Should meet WCAG AA standard (4.5:1 for normal text)
    expect(contrast).toBeGreaterThan(4.5)

    await context.close()
  })

  test('should handle Safari-specific viewport changes', async ({
    browser,
  }) => {
    const context = await browser.newContext(devices['iPhone 14'])
    const page = await context.newPage()

    await page.goto('/login')
    await page.waitForLoadState('networkidle')

    await page.waitForSelector('input[type="email"]', {
      state: 'visible',
      timeout: 15000,
    })

    const subtitle = page
      .locator('p')
      .filter({ hasText: loginSubtitle })
      .first()
    await expect(subtitle).toBeVisible()

    // Test different heights simulating address bar behavior
    const heights = [667, 750, 600]

    for (const height of heights) {
      await page.setViewportSize({ width: 375, height })
      await page.waitForTimeout(500)

      await expect(subtitle).toBeVisible()

      // CSS variables should still work
      const cssValue = await subtitle.evaluate((el) => {
        const rootStyle = window.getComputedStyle(document.documentElement)
        return rootStyle.getPropertyValue('--color-text-secondary').trim()
      })
      expect(cssValue).toBe(expectedSubtitleColor)
    }

    await context.close()
  })

  test('should handle Chrome Android gestures', async ({ browser }) => {
    const context = await browser.newContext(devices['Galaxy S21'])
    const page = await context.newPage()

    await page.goto('/login')
    await page.waitForLoadState('networkidle')

    await page.waitForSelector('input[type="email"]', {
      state: 'visible',
      timeout: 15000,
    })

    const subtitle = page
      .locator('p')
      .filter({ hasText: loginSubtitle })
      .first()
    await expect(subtitle).toBeVisible()

    // Test pull-to-refresh simulation
    await page.touchscreen.tap(200, 100)
    await page.mouse.move(200, 100)
    await page.mouse.down()
    await page.mouse.move(200, 200)
    await page.mouse.up()

    // Subtitle should still be visible after gesture
    await expect(subtitle).toBeVisible()
    await expect(subtitle).toHaveText(loginSubtitle)

    await context.close()
  })
})
