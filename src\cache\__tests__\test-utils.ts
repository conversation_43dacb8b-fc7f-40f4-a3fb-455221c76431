/* eslint-disable max-classes-per-file, no-restricted-syntax, class-methods-use-this, @typescript-eslint/no-explicit-any */
/**
 * Test utilities and helpers for the cache system
 */

import { vi, expect } from 'vitest'
import type { CacheAdapter, CacheEntry, CacheMetadata } from '../types'
import { CACHE_ENTRY_VERSION } from '../types'

/**
 * Mock adapter that tracks all operations for testing
 */
export class MockCacheAdapter implements CacheAdapter {
  private storage = new Map<string, CacheEntry>()

  private operationLog: Array<{
    operation: string
    key?: string
    timestamp: number
  }> = []

  // Spy methods for testing
  public getSpy = vi.fn()

  public setSpy = vi.fn()

  public deleteSpy = vi.fn()

  public clearSpy = vi.fn()

  public getAllKeysSpy = vi.fn()

  public getSizeSpy = vi.fn()

  async get<T>(key: string): Promise<CacheEntry<T> | null> {
    this.getSpy(key)
    this.logOperation('get', key)

    const entry = this.storage.get(key) as CacheEntry<T> | undefined

    // Simulate TTL expiration
    if (entry && this.isExpired(entry.metadata)) {
      this.storage.delete(key)
      return null
    }

    return entry || null
  }

  async set<T>(key: string, value: T, metadata: CacheMetadata): Promise<void> {
    this.setSpy(key, value, metadata)
    this.logOperation('set', key)

    const entry: CacheEntry<T> = {
      value,
      metadata: {
        ...metadata,
        created: metadata.created || Date.now(),
        accessed: Date.now(),
      },
    }

    this.storage.set(key, entry)
  }

  async delete(key: string): Promise<void> {
    this.deleteSpy(key)
    this.logOperation('delete', key)
    this.storage.delete(key)
  }

  async clear(): Promise<void> {
    this.clearSpy()
    this.logOperation('clear')
    this.storage.clear()
  }

  async getAllKeys(): Promise<string[]> {
    this.getAllKeysSpy()
    this.logOperation('getAllKeys')

    // Filter out expired keys
    const validKeys: string[] = []
    for (const [key, entry] of this.storage.entries()) {
      if (!this.isExpired(entry.metadata)) {
        validKeys.push(key)
      }
    }

    return validKeys
  }

  async getSize(): Promise<number> {
    this.getSizeSpy()
    this.logOperation('getSize')

    let totalSize = 0
    for (const entry of this.storage.values()) {
      if (!this.isExpired(entry.metadata)) {
        totalSize += entry.metadata.size
      }
    }

    return totalSize
  }

  supportsBatch(): boolean {
    return false
  }

  // Test utilities
  getOperationLog() {
    return [...this.operationLog]
  }

  clearOperationLog() {
    this.operationLog = []
  }

  getStorageSize() {
    return this.storage.size
  }

  hasKey(key: string) {
    return this.storage.has(key)
  }

  // Simulate failures
  simulateFailure(operation: string, error: Error) {
    const originalMethod = this[operation as keyof this] as any
    if (typeof originalMethod === 'function') {
      vi.mocked(originalMethod).mockRejectedValueOnce(error)
    }
  }

  private logOperation(operation: string, key?: string) {
    this.operationLog.push({
      operation,
      key,
      timestamp: Date.now(),
    })
  }

  private isExpired(metadata: CacheMetadata): boolean {
    return metadata.expires > 0 && Date.now() > metadata.expires
  }
}

/**
 * Test data generators
 */
export const TestDataGenerators = {
  /**
   * Generate a basic cache metadata object
   */
  createMetadata(overrides: Partial<CacheMetadata> = {}): CacheMetadata {
    return {
      size: 100,
      created: Date.now(),
      accessed: Date.now(),
      expires: 0, // Never expires by default
      namespace: 'test',
      priority: 1,
      compressed: false,
      version: CACHE_ENTRY_VERSION,
      ...overrides,
    }
  },

  /**
   * Generate test data of various types
   */
  createTestData() {
    return {
      string: 'test-string-value',
      number: 42,
      boolean: true,
      null: null,
      array: [1, 2, 3, 'four', true],
      object: {
        id: 123,
        name: 'Test Object',
        nested: {
          value: 'nested-value',
          array: [1, 2, 3],
        },
      },
      largeString: 'x'.repeat(10000), // 10KB string
      complexObject: {
        users: Array.from({ length: 100 }, (_, i) => ({
          id: i,
          name: `User ${i}`,
          email: `user${i}@example.com`,
          metadata: {
            created: Date.now() - i * 1000,
            active: i % 2 === 0,
          },
        })),
      },
    }
  },

  /**
   * Generate edge case values for testing
   */
  createEdgeCaseData() {
    return {
      emptyString: '',
      emptyArray: [],
      emptyObject: {},
      specialChars: '!@#$%^&*()_+-=[]{}|;:,.<>?',
      unicode: '🚀 Unicode test 中文 العربية',
      longKey: 'a'.repeat(1000),
      deeplyNested: this.createDeeplyNestedObject(10),
    }
  },

  /**
   * Create a deeply nested object for testing
   */
  createDeeplyNestedObject(depth: number): any {
    if (depth === 0) {
      return { value: 'leaf' }
    }
    return {
      level: depth,
      nested: this.createDeeplyNestedObject(depth - 1),
    }
  },

  /**
   * Generate performance test datasets
   */
  createPerformanceData() {
    return {
      small: Array.from({ length: 10 }, (_, i) => [`key${i}`, `value${i}`]),
      medium: Array.from({ length: 100 }, (_, i) => [`key${i}`, `value${i}`]),
      large: Array.from({ length: 1000 }, (_, i) => [`key${i}`, `value${i}`]),
      xlarge: Array.from({ length: 10000 }, (_, i) => [`key${i}`, `value${i}`]),
    }
  },
}

/**
 * Time control utilities for testing TTL
 */
export class TimeController {
  private originalDateNow = Date.now

  private mockTime = Date.now()

  /**
   * Start controlling time
   */
  start(initialTime?: number) {
    this.mockTime = initialTime || Date.now()
    Date.now = vi.fn(() => this.mockTime)
  }

  /**
   * Advance time by the specified amount
   */
  advance(milliseconds: number) {
    this.mockTime += milliseconds
  }

  /**
   * Set time to a specific value
   */
  setTime(timestamp: number) {
    this.mockTime = timestamp
  }

  /**
   * Get current mock time
   */
  getCurrentTime() {
    return this.mockTime
  }

  /**
   * Stop controlling time and restore original Date.now
   */
  stop() {
    Date.now = this.originalDateNow
  }
}

/**
 * Size calculation helpers
 */
export const SizeHelpers = {
  /**
   * Calculate approximate size of a JavaScript value
   */
  calculateSize(value: any): number {
    const serialized = JSON.stringify(value)
    return serialized.length * 2 // UTF-16 encoding
  },

  /**
   * Generate data of specific size
   */
  generateDataOfSize(targetSizeBytes: number): string {
    const charSize = 2 // UTF-16
    const targetLength = Math.floor(targetSizeBytes / charSize)
    return 'x'.repeat(targetLength)
  },

  /**
   * Check if size is within expected range
   */
  isWithinRange(actual: number, expected: number, tolerance = 0.1): boolean {
    const diff = Math.abs(actual - expected)
    const maxDiff = expected * tolerance
    return diff <= maxDiff
  },
}

/**
 * Cache-specific assertion helpers
 */
export const CacheAssertions = {
  /**
   * Assert that a cache entry has expected metadata properties
   */
  assertValidMetadata(metadata: CacheMetadata | null) {
    expect(metadata).not.toBeNull()
    expect(metadata!.size).toBeGreaterThan(0)
    expect(metadata!.created).toBeGreaterThan(0)
    expect(metadata!.accessed).toBeGreaterThan(0)
    expect(metadata!.version).toBe(CACHE_ENTRY_VERSION)
    expect(typeof metadata!.namespace).toBe('string')
  },

  /**
   * Assert that TTL is set correctly
   */
  assertTTL(metadata: CacheMetadata, expectedTTL: number, tolerance = 100) {
    if (expectedTTL === 0) {
      expect(metadata.expires).toBe(0)
    } else {
      const expectedExpiry = metadata.created + expectedTTL
      expect(metadata.expires).toBeGreaterThanOrEqual(
        expectedExpiry - tolerance
      )
      expect(metadata.expires).toBeLessThanOrEqual(expectedExpiry + tolerance)
    }
  },

  /**
   * Assert that cache statistics are valid
   */
  assertValidStats(stats: any) {
    expect(stats).toHaveProperty('entryCount')
    expect(stats).toHaveProperty('totalSize')
    expect(stats).toHaveProperty('hits')
    expect(stats).toHaveProperty('misses')
    expect(stats).toHaveProperty('hitRatio')
    expect(stats.entryCount).toBeGreaterThanOrEqual(0)
    expect(stats.totalSize).toBeGreaterThanOrEqual(0)
    expect(stats.hits).toBeGreaterThanOrEqual(0)
    expect(stats.misses).toBeGreaterThanOrEqual(0)
    expect(stats.hitRatio).toBeGreaterThanOrEqual(0)
    expect(stats.hitRatio).toBeLessThanOrEqual(1)
  },
}

/**
 * Create a test cache manager with mock adapter
 * Note: This function is async to handle dynamic imports
 */
export async function createTestCacheManager(config = {}) {
  const mockAdapter = new MockCacheAdapter()
  const { CacheManager } = await import('../CacheManager')

  return {
    manager: new CacheManager(mockAdapter, {
      enableStats: true,
      defaultNamespace: 'test',
      ...config,
    }),
    mockAdapter,
  }
}

/**
 * Wait for a specified amount of time (for async testing)
 */
export function wait(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms))
}
