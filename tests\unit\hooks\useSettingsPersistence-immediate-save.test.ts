import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useSettingsPersistence } from '@/hooks/useSettingsPersistence'

// Mock dependencies
vi.mock('@/services/updateUserSettings', () => ({
  updateUserSettings: vi.fn(),
}))

vi.mock('@/hooks/useSettingsData', () => ({
  useSettingsData: vi.fn(() => ({
    data: {
      quickMode: false,
      weightUnit: 'lbs',
      setStyle: 'Normal',
      repRange: { min: 6, max: 12 },
      weightIncrement: 5,
      warmupSets: 0,
    },
    isLoading: false,
  })),
}))

vi.mock('@/stores/authStore', () => ({
  useAuthStore: vi.fn(() => ({
    clearUserInfoCache: vi.fn(),
  })),
}))

vi.mock('@/components/ui/toast', () => ({
  toast: vi.fn(),
}))

/**
 * Tests for immediate localStorage save functionality
 *
 * These tests verify that settings are saved to localStorage IMMEDIATELY
 * when changed, not just after the debounce delay. This prevents the
 * race condition where navigation happens before localStorage save.
 */
describe('useSettingsPersistence - Immediate localStorage Save', () => {
  // Mock localStorage
  const mockLocalStorage = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
    // Setup localStorage mock
    Object.defineProperty(window, 'localStorage', {
      value: mockLocalStorage,
      writable: true,
    })

    // Reset localStorage mock functions
    mockLocalStorage.getItem.mockReturnValue(null)
    mockLocalStorage.setItem.mockImplementation(() => {})
    mockLocalStorage.removeItem.mockImplementation(() => {})
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('should save to localStorage IMMEDIATELY when setting is updated', () => {
    // Given: Settings hook is mounted
    const { result } = renderHook(() => useSettingsPersistence())

    // When: Setting is changed
    act(() => {
      result.current.updateSetting('quickMode', true)
    })

    // Then: localStorage.setItem should be called IMMEDIATELY
    expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
      'drMuscle_pendingSettings',
      expect.stringContaining('"quickMode":true')
    )

    // Verify the localStorage call happens synchronously (not after debounce)
    const { calls } = mockLocalStorage.setItem.mock
    expect(calls.length).toBeGreaterThan(0)

    // Parse the stored data to verify correctness
    const [key, jsonString] = calls[calls.length - 1]
    expect(key).toBe('drMuscle_pendingSettings')
    const storedData = JSON.parse(jsonString)
    expect(storedData.quickMode).toBe(true)
  })

  it('should save to localStorage on every change, not just after debounce', () => {
    // Given: Settings hook is mounted
    const { result } = renderHook(() => useSettingsPersistence())

    // When: Multiple rapid changes are made
    act(() => {
      result.current.updateSetting('quickMode', true)
    })
    act(() => {
      result.current.updateSetting('weightUnit', 'kg')
    })
    act(() => {
      result.current.updateSetting('repsMin', 8)
    })

    // Then: localStorage should be called for each change
    expect(mockLocalStorage.setItem).toHaveBeenCalledTimes(3)

    // Verify final state in localStorage
    const lastCall = mockLocalStorage.setItem.mock.calls[2]
    const [key, jsonString] = lastCall
    expect(key).toBe('drMuscle_pendingSettings')

    const finalData = JSON.parse(jsonString)
    expect(finalData.quickMode).toBe(true)
    expect(finalData.weightUnit).toBe('kg')
    expect(finalData.repsMin).toBe(8)
    expect(finalData.weightIncrement).toBe(2.5) // Auto-converted for kg
  })

  it('should handle localStorage errors gracefully', () => {
    // Given: localStorage throws an error (quota exceeded, disabled, etc.)
    mockLocalStorage.setItem.mockImplementation(() => {
      throw new Error('QuotaExceededError: localStorage quota exceeded')
    })

    const consoleWarnSpy = vi
      .spyOn(console, 'warn')
      .mockImplementation(() => {})

    // When: Setting is changed
    const { result } = renderHook(() => useSettingsPersistence())

    act(() => {
      result.current.updateSetting('quickMode', true)
    })

    // Then: Should not throw error and should log warning
    expect(consoleWarnSpy).toHaveBeenCalledWith(
      'Failed to save settings to localStorage:',
      expect.any(Error)
    )

    // Component should still function normally
    expect(result.current.localSettings.quickMode).toBe(true)

    consoleWarnSpy.mockRestore()
  })

  it('should restore from localStorage on initialization', () => {
    // Given: localStorage contains pending settings
    const pendingSettings = {
      quickMode: true,
      weightUnit: 'kg',
      setStyle: 'Rest-pause',
      repsMin: 8,
      repsMax: 15,
      weightIncrement: 2.5,
      warmupSets: 3,
    }

    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(pendingSettings))

    // When: Hook is initialized
    const { result } = renderHook(() => useSettingsPersistence())

    // Then: Should restore from localStorage
    expect(mockLocalStorage.getItem).toHaveBeenCalledWith(
      'drMuscle_pendingSettings'
    )
    expect(result.current.localSettings.quickMode).toBe(true)
    expect(result.current.localSettings.weightUnit).toBe('kg')
    expect(result.current.localSettings.setStyle).toBe('Rest-pause')
    expect(result.current.hasChanges).toBe(true)
  })

  it('should handle corrupted localStorage data', () => {
    // Given: localStorage contains corrupted JSON
    mockLocalStorage.getItem.mockReturnValue('{"invalid": json}')

    const consoleWarnSpy = vi
      .spyOn(console, 'warn')
      .mockImplementation(() => {})

    // When: Hook is initialized
    const { result } = renderHook(() => useSettingsPersistence())

    // Then: Should handle gracefully and use server defaults
    expect(consoleWarnSpy).toHaveBeenCalledWith(
      'Failed to load settings from localStorage:',
      expect.any(Error)
    )

    // Should fall back to server settings
    expect(result.current.localSettings.quickMode).toBe(false) // Server default
    expect(result.current.hasChanges).toBe(false)

    consoleWarnSpy.mockRestore()
  })

  it('should validate and sanitize localStorage data for security', () => {
    // Given: localStorage contains potentially malicious data
    const maliciousData = {
      quickMode: null, // Falsy value should convert to false
      weightUnit: 'malicious_unit',
      setStyle: 'Evil Style',
      repsMin: -999,
      repsMax: 99999,
      weightIncrement: -50,
      warmupSets: 'not_a_number',
    }

    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(maliciousData))

    // When: Hook is initialized
    const { result } = renderHook(() => useSettingsPersistence())

    // Then: Should sanitize all values
    expect(result.current.localSettings.quickMode).toBe(false) // Boolean conversion
    expect(result.current.localSettings.weightUnit).toBe('lbs') // Fallback to valid value
    expect(result.current.localSettings.setStyle).toBe('Normal') // Fallback to valid value
    expect(result.current.localSettings.repsMin).toBe(1) // Clamped to minimum
    expect(result.current.localSettings.repsMax).toBe(100) // Clamped to maximum
    expect(result.current.localSettings.weightIncrement).toBe(0.5) // Clamped to minimum
    expect(result.current.localSettings.warmupSets).toBe(0) // Default value
  })

  it('should clear localStorage when resetChanges is called', () => {
    // Given: Settings have been modified and saved to localStorage
    const { result } = renderHook(() => useSettingsPersistence())

    act(() => {
      result.current.updateSetting('quickMode', true)
    })

    expect(mockLocalStorage.setItem).toHaveBeenCalled()

    // When: resetChanges is called
    act(() => {
      result.current.resetChanges()
    })

    // Then: localStorage should be cleared
    expect(mockLocalStorage.removeItem).toHaveBeenCalledWith(
      'drMuscle_pendingSettings'
    )
    expect(result.current.hasChanges).toBe(false)
    expect(result.current.localSettings.quickMode).toBe(false) // Back to server default
  })

  it('should handle weight unit conversion during immediate save', () => {
    // Given: Settings hook with lbs default
    const { result } = renderHook(() => useSettingsPersistence())

    // When: Weight unit is changed from lbs to kg
    act(() => {
      result.current.updateSetting('weightUnit', 'kg')
    })

    // Then: localStorage should contain auto-converted weight increment
    const lastCall =
      mockLocalStorage.setItem.mock.calls[
        mockLocalStorage.setItem.mock.calls.length - 1
      ]
    const [, jsonString] = lastCall
    const storedData = JSON.parse(jsonString)

    expect(storedData.weightUnit).toBe('kg')
    expect(storedData.weightIncrement).toBe(2.5) // 5 * 0.5 conversion factor
  })

  it('should preserve validation errors during immediate save', () => {
    // Given: Settings hook is mounted
    const { result } = renderHook(() => useSettingsPersistence())

    // When: Invalid rep range is set (min > max)
    act(() => {
      result.current.updateSetting('repsMin', 15)
      result.current.updateSetting('repsMax', 10)
    })

    // Then: Should save to localStorage despite validation error
    expect(mockLocalStorage.setItem).toHaveBeenCalled()

    // Verify validation error is set
    expect(result.current.saveError).toBeTruthy()
    expect(result.current.saveError).toContain(
      'Minimum reps cannot be greater than maximum reps'
    )

    // But localStorage should still contain the invalid data for user to fix
    const lastCall =
      mockLocalStorage.setItem.mock.calls[
        mockLocalStorage.setItem.mock.calls.length - 1
      ]
    const [, jsonString] = lastCall
    const storedData = JSON.parse(jsonString)

    expect(storedData.repsMin).toBe(15)
    expect(storedData.repsMax).toBe(10)
  })
})

/**
 * Tests for race condition prevention between navigation and save
 */
describe('useSettingsPersistence - Navigation Race Condition Prevention', () => {
  const mockLocalStorage = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
    Object.defineProperty(window, 'localStorage', {
      value: mockLocalStorage,
      writable: true,
    })
    mockLocalStorage.getItem.mockReturnValue(null)
  })

  it('should prevent race condition between rapid setting change and navigation', () => {
    // Given: Settings hook is mounted
    const { result } = renderHook(() => useSettingsPersistence())

    // When: User makes rapid change and "navigates" (component unmounts)
    act(() => {
      result.current.updateSetting('quickMode', true)
      // Simulate immediate navigation by checking localStorage was called
    })

    // Then: localStorage save should complete before any navigation
    expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
      'drMuscle_pendingSettings',
      expect.stringContaining('"quickMode":true')
    )

    // Verify the save is synchronous (immediate)
    const callTimes = mockLocalStorage.setItem.mock.invocationCallOrder
    expect(callTimes.length).toBeGreaterThan(0)
  })

  it('should maintain localStorage consistency during rapid sequential changes', () => {
    // Given: Settings hook is mounted
    const { result } = renderHook(() => useSettingsPersistence())

    // When: Multiple rapid sequential changes (simulating fast user input)
    act(() => {
      result.current.updateSetting('quickMode', true) // Change 1
    })
    act(() => {
      result.current.updateSetting('weightUnit', 'kg') // Change 2
    })
    act(() => {
      result.current.updateSetting('repsMin', 8) // Change 3
    })

    // Then: Each change should be saved to localStorage immediately
    expect(mockLocalStorage.setItem).toHaveBeenCalledTimes(3)

    // Verify final state contains all changes
    const finalCall = mockLocalStorage.setItem.mock.calls[2]
    const finalData = JSON.parse(finalCall[1])

    expect(finalData.quickMode).toBe(true)
    expect(finalData.weightUnit).toBe('kg')
    expect(finalData.repsMin).toBe(8)
    expect(finalData.weightIncrement).toBe(2.5) // Auto-converted
  })

  it('should ensure localStorage write completes before component unmount', () => {
    // Given: Settings hook is mounted
    const { result, unmount } = renderHook(() => useSettingsPersistence())

    // When: Change is made and component is immediately unmounted (navigation)
    act(() => {
      result.current.updateSetting('quickMode', true)
    })

    // Verify localStorage was called before unmount
    expect(mockLocalStorage.setItem).toHaveBeenCalled()

    // Unmount should not prevent localStorage from being called
    unmount()

    // localStorage should have been called and data preserved
    const storedCall = mockLocalStorage.setItem.mock.calls[0]
    expect(storedCall).toBeDefined()
    expect(storedCall[0]).toBe('drMuscle_pendingSettings')

    const storedData = JSON.parse(storedCall[1])
    expect(storedData.quickMode).toBe(true)
  })
})
