'use client'

import { Card } from '@/components/ui/Card'
import QuickModeToggle from '../controls/QuickModeToggle'
import SetStyleDropdown from '../controls/SetStyleDropdown'
import WeightUnitToggle from '../controls/WeightUnitToggle'
import RepRangeInput from '../controls/RepRangeInput'
import WeightIncrementInput from '../controls/WeightIncrementInput'
import WarmupSetsSelector from '../controls/WarmupSetsSelector'
import type {
  LocalSettings,
  UseSettingsPersistenceReturn,
} from '@/types/settings'

interface CardLayoutProps {
  settings: LocalSettings
  updateSetting: UseSettingsPersistenceReturn['updateSetting']
  isSaving: boolean
}

export default function CardLayout({
  settings,
  updateSetting,
  isSaving,
}: CardLayoutProps) {
  return (
    <div
      data-testid="card-layout-container"
      className="grid grid-cols-1 sm:grid-cols-2 gap-4 pb-20"
    >
      {/* Set Style Card */}
      <Card variant="elevated" padding="md" className="col-span-1">
        <h3 className="text-base font-semibold text-text-primary mb-3">
          Set Style
        </h3>
        <div className="space-y-3">
          <SetStyleDropdown
            value={settings.setStyle}
            onChange={(value) => updateSetting('setStyle', value)}
            disabled={isSaving}
          />
          <p className="text-xs text-text-secondary">
            How your sets are structured during workouts
          </p>
        </div>
      </Card>

      {/* Quick Mode Card */}
      <Card variant="elevated" padding="md" className="col-span-1">
        <h3 className="text-base font-semibold text-text-primary mb-3">
          Quick Mode
        </h3>
        <div className="space-y-3">
          <QuickModeToggle
            value={settings.quickMode}
            onChange={(value) => updateSetting('quickMode', value)}
            disabled={isSaving}
          />
        </div>
      </Card>

      {/* Weight Unit Card */}
      <Card variant="elevated" padding="md" className="col-span-1">
        <h3 className="text-base font-semibold text-text-primary mb-3">
          Weight Unit
        </h3>
        <div className="space-y-3">
          <WeightUnitToggle
            value={settings.weightUnit}
            onChange={(value) => updateSetting('weightUnit', value)}
            disabled={isSaving}
          />
          <p className="text-xs text-text-secondary">
            Display weights in kilograms or pounds
          </p>
        </div>
      </Card>

      {/* Rep Range Card */}
      <Card variant="elevated" padding="md" className="col-span-1">
        <h3 className="text-base font-semibold text-text-primary mb-3">
          Rep Range
        </h3>
        <div className="space-y-3">
          <RepRangeInput
            minReps={settings.repsMin}
            maxReps={settings.repsMax}
            onChange={(values) => {
              updateSetting('repsMin', values.minReps)
              updateSetting('repsMax', values.maxReps)
            }}
            disabled={isSaving}
          />
        </div>
      </Card>

      {/* Weight Increment Card */}
      <Card variant="elevated" padding="md" className="col-span-1">
        <h3 className="text-base font-semibold text-text-primary mb-3">
          Weight Increment
        </h3>
        <div className="space-y-3">
          <WeightIncrementInput
            value={settings.weightIncrement}
            unit={settings.weightUnit}
            onChange={(value) => updateSetting('weightIncrement', value)}
            disabled={isSaving}
          />
          <p className="text-xs text-text-secondary">
            Weight progression step size
          </p>
        </div>
      </Card>

      {/* Warmup Sets Card */}
      <Card variant="elevated" padding="md" className="col-span-1">
        <h3 className="text-base font-semibold text-text-primary mb-3">
          Warmup Sets
        </h3>
        <div className="space-y-3">
          <WarmupSetsSelector
            value={settings.warmupSets}
            onChange={(value) => updateSetting('warmupSets', value)}
            disabled={isSaving}
          />
          <p className="text-xs text-text-secondary">
            Number of warmup sets before working sets
          </p>
        </div>
      </Card>
    </div>
  )
}
