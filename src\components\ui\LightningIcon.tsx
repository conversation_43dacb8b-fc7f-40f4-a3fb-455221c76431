import React from 'react'

interface LightningIconProps {
  className?: string
  'aria-label'?: string
}

export function LightningIcon({
  className = 'w-3 h-3',
  'aria-label': ariaLabel,
}: LightningIconProps) {
  return (
    <svg
      className={className}
      viewBox="0 0 16 16"
      fill="currentColor"
      aria-label={ariaLabel}
      role={ariaLabel ? 'img' : 'presentation'}
    >
      <path d="M11.251.068a.5.5 0 0 1 .227.58L9.677 6.5H13a.5.5 0 0 1 .364.843l-8 8.5a.5.5 0 0 1-.842-.49L6.323 9.5H3a.5.5 0 0 1-.364-.843l8-8.5a.5.5 0 0 1 .615-.09z" />
    </svg>
  )
}
