import { test, expect } from '@playwright/test'
import { login } from './helpers/auth'

test.describe('Fix Try New UI Recommendation Loading', () => {
  // Mobile viewport
  test.use({ viewport: { width: 390, height: 844 } })

  test('should load recommendation when clicking Try New UI after returning from break', async ({
    page,
  }) => {
    // Step 1: Navigate to login and authenticate
    await page.goto('/login')
    await login(page)

    // Wait for redirect to workout page
    await page.waitForURL('/workout')

    // Wait for workout to load
    await page.waitForSelector('[data-testid="workout-overview-container"]')

    // Start workout by clicking Start workout button
    await page.click('[data-testid="start-workout-button"]')

    // Wait for navigation to first exercise
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Go back to workout page
    await page.goto('/workout')

    // Ensure workout session is active
    await page.waitForSelector('text=Continue Workout')

    // Step 2: Click Try New UI button with existing session
    await page.click('text=Start with new exercise view →')

    // Step 3: Verify navigation to v2 page
    await page.waitForURL(/\/workout\/exercise-v2\/\d+/, { timeout: 10000 })

    // Step 4: Verify recommendation loads properly (no infinite loading)
    // Should NOT see the skeleton loader for more than 5 seconds
    const skeletonLoader = page.locator('[data-testid="set-screen-skeleton"]')

    // If skeleton exists, it should disappear within 5 seconds
    if (await skeletonLoader.isVisible()) {
      await expect(skeletonLoader).not.toBeVisible({ timeout: 5000 })
    }

    // Should see exercise content
    await expect(
      page.locator('[data-testid="exercise-page-container"]')
    ).toBeVisible()

    // Should see current set card (indicates recommendation loaded)
    await expect(page.locator('[data-testid="current-set-card"]')).toBeVisible({
      timeout: 5000,
    })

    // Should see set inputs
    await expect(page.locator('[data-testid="weight-input"]')).toBeVisible()
    await expect(page.locator('[data-testid="reps-input"]')).toBeVisible()

    // Step 5: Verify can interact with the page
    const weightInput = page.locator('[data-testid="weight-input"]')
    await weightInput.click()
    await weightInput.fill('100')

    const repsInput = page.locator('[data-testid="reps-input"]')
    await repsInput.click()
    await repsInput.fill('10')

    // Should be able to save set
    await expect(page.locator('button:has-text("Save set")')).toBeEnabled()
  })

  test('should handle recommendation loading failure gracefully', async ({
    page,
  }) => {
    // Mock API to fail recommendation loading
    await page.route('**/api/workout/recommendation/*', (route) => {
      route.abort('failed')
    })

    // Authenticate and setup workout session
    await page.goto('/login')
    await login(page)
    await page.waitForURL('/workout')
    await page.waitForSelector('[data-testid="workout-overview-container"]')
    await page.click('[data-testid="start-workout-button"]')
    await page.waitForURL(/\/workout\/exercise\/\d+/)
    await page.goto('/workout')
    await page.waitForSelector('text=Continue Workout')

    // Click Try New UI button
    await page.click('text=Start with new exercise view →')

    // Should still navigate to v2 page despite recommendation failure
    await page.waitForURL(/\/workout\/exercise-v2\/\d+/, { timeout: 10000 })

    // Should show error state or handle gracefully
    // The page should not be stuck on loading

    // Wait a bit to ensure the page has tried to load
    await page.waitForTimeout(2000)

    // Should either show error state or exercise page
    const hasError = await page
      .locator('text=/error|failed|problem/i')
      .isVisible()
    const hasExercisePage = await page
      .locator('[data-testid="exercise-page-container"]')
      .isVisible()

    // One of these should be true - either error handling or graceful degradation
    expect(hasError || hasExercisePage).toBeTruthy()
  })
})
