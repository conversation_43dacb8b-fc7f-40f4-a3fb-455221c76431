# DrMuscle Web App: Complete Weight Increment Implementation Guide

## Executive Summary

This document provides a comprehensive specification for implementing the proven weight increment system from the DrMuscle MAUI mobile application into the web app. The system uses a three-tier preference hierarchy (exercise > equipment > global) with full equipment configuration support, ensuring weight recommendations match user's available equipment for practical workout execution.

**Current State Analysis:**

- ✅ Basic increment fields exist in UserInfosModel and RecommendationModel
- ✅ Equipment availability flags present in API responses
- ✅ Simple increment logic in weightUtils.ts and useSetInputHandlers.ts
- ❌ Equipment configuration strings (plates, dumbbells, etc.) missing
- ❌ Exercise-specific settings API integration missing
- ❌ Three-tier preference hierarchy not implemented
- ❌ MAUI-proven business logic not applied

**Implementation Approach:** Hybrid strategy leveraging existing web app API responses while adding missing MAUI functionality for complete feature parity.

## Architecture Overview

### Three-Tier Preference Hierarchy (MAUI-Proven)

```mermaid
graph TD
    A[Exercise Opened] --> B{Exercise-Level Settings?}
    B -->|Yes| C[Use Exercise Settings]
    B -->|No| D{Equipment-Based Settings?}
    D -->|Yes| E[Use Equipment Settings]
    D -->|No| F[Use Global Settings]

    C --> G[Apply MAUI Increment Logic]
    E --> G
    F --> G

    G --> H[Calculate Weight Recommendation]
    H --> I[Round to Nearest Valid Weight]
    I --> J[Apply Equipment Constraints]
    J --> K[Return Final Recommendation]
```

### Core Components Priority

| Component              | Purpose                                | Priority    | Implementation Status     |
| ---------------------- | -------------------------------------- | ----------- | ------------------------- |
| **Exercise Settings**  | User-specific preferences per exercise | 1 (Highest) | ❌ Need API integration   |
| **Equipment Settings** | Location/equipment-based preferences   | 2 (Medium)  | ❌ Need equipment strings |
| **Global Settings**    | User's default preferences             | 3 (Lowest)  | ✅ Partially exists       |

## Type System Enhancement

### Enhanced UserInfosModel (API Extension Required)

```typescript
// src/types/api/common.ts - Extend existing UserInfosModel
export interface UserInfosModel {
  // ... existing fields ...

  // Global increment preferences (✅ Already exists)
  Increments?: MultiUnityWeight | null
  Min?: MultiUnityWeight | null
  Max?: MultiUnityWeight | null

  // Equipment configuration strings (❌ Need to add)
  EquipmentModel?: EquipmentModel | null

  // Workout preferences (✅ Some exist)
  WorkoutIncrements?: number // Custom global increment value
  KgBarWeight?: number | null
  LbBarWeight?: number | null
}

// New equipment model based on MAUI structure
export interface EquipmentModel {
  // Location settings
  Active: 'gym' | 'home' | 'other' // Current active location

  // Gym equipment
  IsPlateEnabled: boolean
  IsDumbbellEnabled: boolean
  IsPullyEnabled: boolean // Note: API typo
  IsBands: boolean
  PlatesKg?: string // "25_20_True|20_20_True|..."
  PlatesLb?: string
  DumbbellKg?: string
  DumbbellLb?: string
  PulleyKg?: string
  PulleyLb?: string
  BandsKg?: string
  BandsLb?: string

  // Home equipment
  IsHomePlate: boolean
  IsHomeDumbbell: boolean
  IsHomePully: boolean
  IsHomeBands: boolean
  HomePlatesKg?: string
  HomePlatesLb?: string
  HomeDumbbellKg?: string
  HomeDumbbellLb?: string
  HomePulleyKg?: string
  HomePulleyLb?: string
  HomeBandsKg?: string
  HomeBandsLb?: string

  // Other location equipment
  IsOtherPlate: boolean
  IsOtherDumbbell: boolean
  IsOtherPully: boolean
  IsOtherBands: boolean
  OtherPlatesKg?: string
  OtherPlatesLb?: string
  OtherDumbbellKg?: string
  OtherDumbbellLb?: string
  OtherPulleyKg?: string
  OtherPulleyLb?: string
  OtherBandsKg?: string
  OtherBandsLb?: string
}
```

### Exercise Settings Model (New API Integration)

```typescript
// src/types/api/exercise.ts - Add new model for exercise-specific settings
export interface ExerciseSettingsModel {
  ExerciseSettingsId: number
  ExerciseId: number
  IsCustomIncrements: boolean
  Increments: MultiUnityWeight | null
  Min: MultiUnityWeight | null
  Max: MultiUnityWeight | null
  IsCustomReps: boolean
  RepsMaxValue: number | null
  RepsMinValue: number | null
  IsCustomSets: boolean
  IsNormalSets: boolean | null
  WarmupsValue: number | null
  SetCount: number | null
  IsFavorite: boolean
}

// Request model for exercise settings
export interface ExerciseSettingsRequest {
  ExerciseId: number
  Username: string
}
```

### Enhanced RecommendationModel

```typescript
// src/types/api/exercise.ts - Extend existing RecommendationModel
export interface RecommendationModel {
  // ... existing fields ...

  // Increment fields (✅ Already exist)
  Increments?: MultiUnityWeight
  Min?: MultiUnityWeight
  Max?: MultiUnityWeight

  // Equipment availability flags (✅ Already exist)
  isPlateAvailable?: boolean
  isDumbbellAvailable?: boolean
  isPulleyAvailable?: boolean
  isBandsAvailable?: boolean

  // Additional MAUI fields needed
  RecommendationInKg?: number // Base recommendation before increment rounding
}
```

### Application Types

```typescript
// src/types/app.ts - New application-specific types
export interface IncrementContext {
  globalIncrement: MultiUnityWeight | null
  exerciseIncrement?: MultiUnityWeight | null
  equipmentIncrement?: MultiUnityWeight | null
  effectiveIncrement: MultiUnityWeight
  minWeight?: MultiUnityWeight | null
  maxWeight?: MultiUnityWeight | null
  source: 'exercise' | 'equipment' | 'global'
}

export interface IncrementSettings {
  increments: MultiUnityWeight
  min?: MultiUnityWeight | null
  max?: MultiUnityWeight | null
  source: 'exercise' | 'equipment' | 'global'
}

export type EquipmentType = 'plates' | 'dumbbells' | 'pulley' | 'bands'

export interface PlateConfig {
  weight: number
  count: number
  isSystem: boolean
}

export interface BandConfig {
  color: string
  resistance: number
  count: number
  isSystem: boolean
}
```

### Settings Enhancement

```typescript
// src/types/settings.ts - Extend LocalSettings
export interface LocalSettings {
  // ... existing fields ...

  // Global increment preferences
  weightIncrement?: number
  minWeight?: number
  maxWeight?: number

  // Equipment preferences cache
  activeLocation?: 'gym' | 'home' | 'other'
  equipmentPreferences?: {
    [location: string]: {
      [equipment: string]: boolean
    }
  }
}
```

## API Integration Strategy

### Current Web App API Enhancement

**Approach:** Leverage existing API responses while adding missing MAUI functionality.

#### 1. Enhance Existing GetUserInfoPyramid Response

```typescript
// Current response already includes basic increment fields
// Need to add equipment configuration strings

interface EnhancedUserInfoResponse extends UserInfosModel {
  EquipmentModel: EquipmentModel // Add complete equipment configuration
  WorkoutIncrements?: number // Custom global increment
}
```

#### 2. Add Exercise Settings API Integration

```typescript
// New API endpoint integration needed
// POST /api/Exercise/GetExerciseSettingsPyramid

class ExerciseSettingsService {
  static async getExerciseSettings(
    exerciseId: number,
    username: string
  ): Promise<ExerciseSettingsModel | null> {
    try {
      const response = await apiClient.post(
        '/api/Exercise/GetExerciseSettingsPyramid',
        {
          ExerciseId: exerciseId,
          Username: username,
        }
      )
      return response.data
    } catch (error) {
      console.warn('Exercise settings not available, using defaults')
      return null
    }
  }
}
```

#### 3. Equipment String Parsing (MAUI-Proven Logic)

```typescript
// src/utils/equipmentParser.ts - Parse MAUI equipment strings

export function parsePlateString(plateString: string): PlateConfig[] {
  if (!plateString) return []

  return plateString
    .split('|')
    .map((item) => {
      const [weight, count, isSystem] = item.split('_')
      return {
        weight: parseFloat(weight),
        count: parseInt(count),
        isSystem: isSystem === 'True',
      }
    })
    .filter((plate) => plate.count > 0)
}

export function parseDumbbellString(dumbbellString: string): PlateConfig[] {
  if (!dumbbellString) return []

  return dumbbellString
    .split('|')
    .map((item) => {
      const [weight, count, isSystem] = item.split('_')
      return {
        weight: parseFloat(weight),
        count: Math.min(parseInt(count), 1), // MAUI limits to 1 per weight
        isSystem: isSystem === 'True',
      }
    })
    .filter((dumbbell) => dumbbell.count > 0)
}

export function parseBandString(bandString: string): BandConfig[] {
  if (!bandString) return []

  return bandString
    .split('|')
    .map((item) => {
      const [color, resistance, count, isSystem] = item.split('_')
      return {
        color,
        resistance: parseFloat(resistance),
        count: Math.min(parseInt(count), 1), // MAUI limits to 1 per resistance
        isSystem: isSystem === 'True',
      }
    })
    .filter((band) => band.count > 0)
}
```

## Core Implementation Files

### New Files (Priority Order)

1. **`src/utils/incrementUtils.ts`** - MAUI increment calculation logic
2. **`src/utils/equipmentParser.ts`** - Equipment string parsing
3. **`src/services/exerciseSettings.ts`** - Exercise settings API integration
4. **`src/hooks/useIncrementPreferences.ts`** - React hook for increment context
5. **`src/utils/equipmentDetector.ts`** - Equipment availability detection
6. **`src/utils/incrementResolver.ts`** - Three-tier preference resolution
7. **`src/utils/weightCalculator.ts`** - Complete weight calculation engine

### Enhanced Files (Existing + MAUI Logic)

1. **`src/types/api/common.ts`** - Add EquipmentModel interface
2. **`src/types/api/exercise.ts`** - Add ExerciseSettingsModel
3. **`src/types/app.ts`** - Add increment context types
4. **`src/utils/weightUtils.ts`** - Enhance with MAUI rounding logic
5. **`src/hooks/useSetInputHandlers.ts`** - Apply three-tier preferences
6. **`src/components/workout/SetCell.tsx`** - Use increment context
7. **`src/stores/workoutStore/index.ts`** - Cache increment settings

## MAUI-Proven Business Logic Implementation

### 1. Core Increment Calculation (Direct MAUI Port)

```typescript
// src/utils/incrementUtils.ts - Port of MAUI RecoComputation.cs

/**
 * Round weight to nearest increment with min/max constraints
 * Direct port of MAUI RoundToNearestIncrement method
 */
export function roundToNearestIncrement(
  numToRound: number,
  increments: number,
  min?: number,
  max?: number
): number {
  // Handle zero or tiny increments
  if (increments === 0 || increments === 0.01) {
    return numToRound
  }

  if (min !== undefined) {
    // When min is specified, find next valid increment above min
    let numAdjustedForMin = min
    while (numAdjustedForMin < numToRound) {
      numAdjustedForMin += increments
    }
    let numRounded = numAdjustedForMin

    // Apply max constraint
    if (max !== undefined && numRounded > max) {
      numRounded = max
    }

    return numRounded
  } else {
    // Calculate floor value
    const floor = Math.floor(numToRound / increments) * increments

    // Round up if more than 50% way to next increment
    let numRounded = floor
    const remainder = numToRound - floor
    if (remainder > increments * 0.5) {
      numRounded += increments
    }

    // Apply max constraint
    if (max !== undefined && numRounded > max) {
      numRounded = max
    }

    return numRounded
  }
}

/**
 * Get effective increment based on three-tier hierarchy
 * Priority: Exercise > Equipment > Global
 */
export function getEffectiveIncrement(
  exerciseSettings: ExerciseSettingsModel | null,
  equipmentType: EquipmentType[],
  userInfo: UserInfosModel,
  unit: 'kg' | 'lb'
): IncrementSettings {
  // Priority 1: Exercise-level settings
  if (exerciseSettings?.IsCustomIncrements && exerciseSettings.Increments) {
    return {
      increments: exerciseSettings.Increments,
      min: exerciseSettings.Min,
      max: exerciseSettings.Max,
      source: 'exercise',
    }
  }

  // Priority 2: Equipment-based settings
  const equipmentIncrement = getEquipmentIncrement(equipmentType, unit)
  if (equipmentIncrement) {
    return {
      increments: equipmentIncrement,
      min: null,
      max: null,
      source: 'equipment',
    }
  }

  // Priority 3: Global settings
  const globalIncrement = getGlobalIncrement(userInfo, unit)
  return {
    increments: globalIncrement,
    min: userInfo.Min,
    max: userInfo.Max,
    source: 'global',
  }
}

/**
 * Get equipment-specific increment (MAUI defaults)
 */
function getEquipmentIncrement(
  equipment: EquipmentType[],
  unit: 'kg' | 'lb'
): MultiUnityWeight | null {
  if (equipment.length === 0) return null

  const primaryEquipment = equipment[0]
  const isKg = unit === 'kg'

  switch (primaryEquipment) {
    case 'plates':
      return new MultiUnityWeight(isKg ? 2.5 : 5, unit)
    case 'dumbbells':
      return new MultiUnityWeight(isKg ? 2.5 : 5, unit)
    case 'pulley':
      return new MultiUnityWeight(isKg ? 1.5 : 2.5, unit)
    case 'bands':
      return new MultiUnityWeight(isKg ? 4 : 10, unit)
    default:
      return null
  }
}

/**
 * Get global increment with fallback defaults
 */
function getGlobalIncrement(
  userInfo: UserInfosModel,
  unit: 'kg' | 'lb'
): MultiUnityWeight {
  const isKg = unit === 'kg'
  const defaultIncrement = isKg ? 1 : 2.5

  // Check for custom global increment
  if (userInfo.WorkoutIncrements) {
    return new MultiUnityWeight(userInfo.WorkoutIncrements, unit)
  }

  // Check for API-provided global increment
  if (userInfo.Increments) {
    return userInfo.Increments
  }

  // Use MAUI defaults
  return new MultiUnityWeight(defaultIncrement, unit)
}
```

### 2. Equipment Detection and Availability

```typescript
// src/utils/equipmentDetector.ts - MAUI equipment detection logic

export class EquipmentDetector {
  static detectAvailableEquipment(userInfo: UserInfosModel): EquipmentType[] {
    const equipment: EquipmentType[] = []

    if (!userInfo.EquipmentModel) {
      return equipment // No equipment configured
    }

    const activeLocation = userInfo.EquipmentModel.Active || 'gym'

    // Check equipment availability based on active location
    switch (activeLocation) {
      case 'gym':
        if (userInfo.EquipmentModel.IsPlateEnabled) equipment.push('plates')
        if (userInfo.EquipmentModel.IsDumbbellEnabled)
          equipment.push('dumbbells')
        if (userInfo.EquipmentModel.IsPullyEnabled) equipment.push('pulley')
        if (userInfo.EquipmentModel.IsBands) equipment.push('bands')
        break

      case 'home':
        if (userInfo.EquipmentModel.IsHomePlate) equipment.push('plates')
        if (userInfo.EquipmentModel.IsHomeDumbbell) equipment.push('dumbbells')
        if (userInfo.EquipmentModel.IsHomePully) equipment.push('pulley')
        if (userInfo.EquipmentModel.IsHomeBands) equipment.push('bands')
        break

      case 'other':
        if (userInfo.EquipmentModel.IsOtherPlate) equipment.push('plates')
        if (userInfo.EquipmentModel.IsOtherDumbbell) equipment.push('dumbbells')
        if (userInfo.EquipmentModel.IsOtherPully) equipment.push('pulley')
        if (userInfo.EquipmentModel.IsOtherBands) equipment.push('bands')
        break
    }

    return equipment
  }

  static getEquipmentStrings(
    userInfo: UserInfosModel,
    unit: 'kg' | 'lb'
  ): Record<EquipmentType, string> {
    const equipmentModel = userInfo.EquipmentModel
    if (!equipmentModel) {
      return { plates: '', dumbbells: '', pulley: '', bands: '' }
    }

    const activeLocation = equipmentModel.Active || 'gym'
    const suffix = unit === 'kg' ? 'Kg' : 'Lb'

    const locationPrefix =
      activeLocation === 'gym'
        ? ''
        : activeLocation === 'home'
          ? 'Home'
          : 'Other'

    return {
      plates: equipmentModel[`${locationPrefix}Plates${suffix}`] || '',
      dumbbells: equipmentModel[`${locationPrefix}Dumbbell${suffix}`] || '',
      pulley: equipmentModel[`${locationPrefix}Pulley${suffix}`] || '',
      bands: equipmentModel[`${locationPrefix}Bands${suffix}`] || '',
    }
  }
}
```

### 3. Plate Weight Calculation (MAUI Algorithm)

```typescript
// src/utils/plateCalculator.ts - Port of MAUI GetPlatesWeight method

export class PlateCalculator {
  static calculatePlateWeight(
    availablePlates: string,
    targetWeight: number,
    barWeight: number,
    isKg: boolean
  ): MultiUnityWeight {
    if (targetWeight < barWeight) {
      // Handle weights below bar weight (e.g., assisted exercises)
      const plateWeight = this.findPlateWeightWithoutBar(
        availablePlates,
        targetWeight
      )
      return new MultiUnityWeight(plateWeight, isKg ? 'kg' : 'lb')
    }

    const remainingWeight = targetWeight - barWeight
    const plates = parsePlateString(availablePlates)

    // Sort plates by weight (heaviest first) - MAUI algorithm
    plates.sort((a, b) => b.weight - a.weight)

    let totalPlateWeight = 0

    // Calculate plates needed (uses pairs)
    for (const plate of plates) {
      let usedPairs = 0
      const maxPairs = Math.floor(plate.count / 2)

      while (
        usedPairs < maxPairs &&
        totalPlateWeight + plate.weight * 2 <= remainingWeight
      ) {
        totalPlateWeight += plate.weight * 2
        usedPairs++
      }
    }

    const finalWeight = barWeight + totalPlateWeight
    return new MultiUnityWeight(finalWeight, isKg ? 'kg' : 'lb')
  }

  private static findPlateWeightWithoutBar(
    availablePlates: string,
    targetWeight: number
  ): number {
    const plates = parsePlateString(availablePlates)

    // Find closest available plate weight
    let closestWeight = 0
    let minDifference = Infinity

    for (const plate of plates) {
      if (plate.count > 0) {
        const difference = Math.abs(plate.weight - targetWeight)
        if (difference < minDifference) {
          minDifference = difference
          closestWeight = plate.weight
        }
      }
    }

    return closestWeight
  }
}
```

## Edge Cases Implementation (MAUI-Proven)

### 1. Bodyweight Exercises

```typescript
// src/utils/edgeCaseHandlers.ts

export function handleBodyweightExercise(
  recommendation: RecommendationModel
): RecommendationModel {
  if (recommendation.IsBodyweight) {
    // Bodyweight exercises ignore increments
    return {
      ...recommendation,
      Increments: null,
      Min: null,
      Max: null,
      isPlateAvailable: false,
      isDumbbellAvailable: false,
      isPulleyAvailable: false,
      isBandsAvailable: false,
    }
  }
  return recommendation
}
```

### 2. Weighted Exercises (Pull-ups, Dips)

```typescript
export function handleWeightedExercise(
  exerciseId: number,
  recommendation: RecommendationModel,
  userBodyWeight: number
): RecommendationModel {
  // MAUI weighted exercise IDs
  const weightedExerciseIds = [
    18627, 18628, 21234, 862, 863, 6992, 6993, 13446, 13449, 14297,
  ]

  if (weightedExerciseIds.includes(exerciseId)) {
    // Weight represents additional weight beyond body weight
    const additionalWeight = recommendation.Weight.Kg

    // Set minimum to 0 (no additional weight)
    if (!recommendation.Min) {
      recommendation.Min = new MultiUnityWeight(0, 'kg')
    }

    return recommendation
  }
  return recommendation
}
```

### 3. Deload Scenarios

```typescript
export function handleDeloadRecommendation(
  recommendation: RecommendationModel,
  incrementSettings: IncrementSettings
): RecommendationModel {
  if (recommendation.IsDeload) {
    // Reduce weight by 15% (MAUI standard)
    const deloadWeight =
      (recommendation.RecommendationInKg || recommendation.Weight.Kg) * 0.85

    // Apply increment rounding to deload weight
    const roundedDeloadWeight = roundToNearestIncrement(
      deloadWeight,
      incrementSettings.increments.Kg,
      incrementSettings.min?.Kg,
      incrementSettings.max?.Kg
    )

    return {
      ...recommendation,
      Weight: new MultiUnityWeight(roundedDeloadWeight, 'kg'),
      Series: Math.max(Math.floor(recommendation.Series / 2), 2), // Reduce sets
    }
  }
  return recommendation
}
```

### 4. Equipment Unavailability

```typescript
export function handleEquipmentUnavailability(
  recommendation: RecommendationModel,
  availableEquipment: EquipmentType[]
): RecommendationModel {
  const requiredEquipment = getRequiredEquipment(recommendation)
  const hasRequiredEquipment = requiredEquipment.every((eq) =>
    availableEquipment.includes(eq)
  )

  if (!hasRequiredEquipment) {
    console.warn('Required equipment not available', {
      required: requiredEquipment,
      available: availableEquipment,
    })

    // Fallback to bodyweight if possible
    if (canConvertToBodyweight(recommendation)) {
      return {
        ...recommendation,
        IsBodyweight: true,
        Weight: new MultiUnityWeight(0, 'kg'),
        Increments: null,
      }
    }
  }

  return recommendation
}

function getRequiredEquipment(
  recommendation: RecommendationModel
): EquipmentType[] {
  const equipment: EquipmentType[] = []

  if (recommendation.isPlateAvailable) equipment.push('plates')
  if (recommendation.isDumbbellAvailable) equipment.push('dumbbells')
  if (recommendation.isPulleyAvailable) equipment.push('pulley')
  if (recommendation.isBandsAvailable) equipment.push('bands')

  return equipment
}

function canConvertToBodyweight(recommendation: RecommendationModel): boolean {
  // Logic to determine if exercise can be done as bodyweight
  // This would need exercise-specific knowledge
  return false // Conservative default
}
```

## React Integration Layer

### 1. Increment Preferences Hook

```typescript
// src/hooks/useIncrementPreferences.ts

export function useIncrementPreferences(exerciseId?: number): {
  incrementContext: IncrementContext | null
  isLoading: boolean
  error: string | null
  refetch: () => void
} {
  const [incrementContext, setIncrementContext] =
    useState<IncrementContext | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const { userInfo } = useAuth()

  const fetchIncrementContext = useCallback(async () => {
    if (!userInfo || !exerciseId) {
      setIsLoading(false)
      return
    }

    try {
      setIsLoading(true)
      setError(null)

      // Fetch exercise-specific settings
      const exerciseSettings =
        await ExerciseSettingsService.getExerciseSettings(
          exerciseId,
          userInfo.Email
        )

      // Detect available equipment
      const availableEquipment =
        EquipmentDetector.detectAvailableEquipment(userInfo)

      // Resolve effective increment
      const incrementSettings = getEffectiveIncrement(
        exerciseSettings,
        availableEquipment,
        userInfo,
        userInfo.MassUnit as 'kg' | 'lb'
      )

      // Build complete context
      const context: IncrementContext = {
        globalIncrement: userInfo.Increments || null,
        exerciseIncrement: exerciseSettings?.Increments || null,
        equipmentIncrement:
          availableEquipment.length > 0
            ? getEquipmentIncrement(
                availableEquipment,
                userInfo.MassUnit as 'kg' | 'lb'
              )
            : null,
        effectiveIncrement: incrementSettings.increments,
        minWeight: incrementSettings.min,
        maxWeight: incrementSettings.max,
        source: incrementSettings.source,
      }

      setIncrementContext(context)
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : 'Failed to load increment preferences'
      )
    } finally {
      setIsLoading(false)
    }
  }, [userInfo, exerciseId])

  useEffect(() => {
    fetchIncrementContext()
  }, [fetchIncrementContext])

  return {
    incrementContext,
    isLoading,
    error,
    refetch: fetchIncrementContext,
  }
}
```

### 2. Enhanced Weight Calculation Service

```typescript
// src/services/weightCalculationService.ts - Complete MAUI-web integration

export class WeightCalculationService {
  static async calculateRecommendation(
    exerciseId: number,
    userId: string,
    workoutId?: number
  ): Promise<RecommendationModel> {
    // 1. Get base recommendation from existing API
    const baseRecommendation = await this.getBaseRecommendation(
      exerciseId,
      userId,
      workoutId
    )

    // 2. Get exercise settings (new API integration)
    const exerciseSettings = await ExerciseSettingsService.getExerciseSettings(
      exerciseId,
      userId
    )

    // 3. Get user info (existing API)
    const userInfo = await getUserInfo()

    // 4. Detect available equipment
    const availableEquipment =
      EquipmentDetector.detectAvailableEquipment(userInfo)

    // 5. Resolve increment settings using three-tier hierarchy
    const incrementSettings = getEffectiveIncrement(
      exerciseSettings,
      availableEquipment,
      userInfo,
      userInfo.MassUnit as 'kg' | 'lb'
    )

    // 6. Apply MAUI increment logic
    const finalWeight = this.applyIncrementLogic(
      baseRecommendation.RecommendationInKg || baseRecommendation.Weight.Kg,
      incrementSettings,
      availableEquipment,
      userInfo
    )

    // 7. Handle edge cases
    let processedRecommendation = {
      ...baseRecommendation,
      Weight: finalWeight,
      Increments: incrementSettings.increments,
      Min: incrementSettings.min,
      Max: incrementSettings.max,
    }

    // Apply edge case handlers
    processedRecommendation = handleBodyweightExercise(processedRecommendation)
    processedRecommendation = handleWeightedExercise(
      exerciseId,
      processedRecommendation,
      userInfo.BodyWeight?.Kg || 70
    )
    processedRecommendation = handleDeloadRecommendation(
      processedRecommendation,
      incrementSettings
    )
    processedRecommendation = handleEquipmentUnavailability(
      processedRecommendation,
      availableEquipment
    )

    return processedRecommendation
  }

  private static applyIncrementLogic(
    targetWeight: number,
    settings: IncrementSettings,
    equipment: EquipmentType[],
    userInfo: UserInfosModel
  ): MultiUnityWeight {
    const unit = userInfo.MassUnit as 'kg' | 'lb'
    const targetInUnit = unit === 'kg' ? targetWeight : targetWeight * 2.20462

    // Apply MAUI increment rounding
    let roundedWeight = roundToNearestIncrement(
      targetInUnit,
      unit === 'kg' ? settings.increments.Kg : settings.increments.Lb,
      settings.min
        ? unit === 'kg'
          ? settings.min.Kg
          : settings.min.Lb
        : undefined,
      settings.max
        ? unit === 'kg'
          ? settings.max.Kg
          : settings.max.Lb
        : undefined
    )

    // Apply equipment-specific adjustments
    if (equipment.length > 0) {
      roundedWeight = this.applyEquipmentAdjustments(
        roundedWeight,
        equipment[0],
        userInfo
      )
    }

    return new MultiUnityWeight(roundedWeight, unit)
  }

  private static applyEquipmentAdjustments(
    weight: number,
    primaryEquipment: EquipmentType,
    userInfo: UserInfosModel
  ): number {
    const unit = userInfo.MassUnit as 'kg' | 'lb'
    const equipmentStrings = EquipmentDetector.getEquipmentStrings(
      userInfo,
      unit
    )

    switch (primaryEquipment) {
      case 'plates':
        const barWeight =
          unit === 'kg'
            ? userInfo.KgBarWeight || 20
            : userInfo.LbBarWeight || 45
        return PlateCalculator.calculatePlateWeight(
          equipmentStrings.plates,
          weight,
          barWeight,
          unit === 'kg'
        ).getValue(unit)

      case 'dumbbells':
        return this.calculateDumbbellWeight(equipmentStrings.dumbbells, weight)

      case 'pulley':
      case 'bands':
        // For pulley and bands, use weight as-is (already rounded to increment)
        return weight

      default:
        return weight
    }
  }

  private static calculateDumbbellWeight(
    availableDumbbells: string,
    targetWeight: number
  ): number {
    const dumbbells = parseDumbbellString(availableDumbbells)

    if (dumbbells.length === 0) return targetWeight

    // Find closest available dumbbell weight
    let closestWeight = targetWeight
    let minDifference = Infinity

    for (const dumbbell of dumbbells) {
      if (dumbbell.count > 0) {
        const difference = Math.abs(dumbbell.weight - targetWeight)
        if (difference < minDifference) {
          minDifference = difference
          closestWeight = dumbbell.weight
        }
      }
    }

    return closestWeight
  }
}
```

### 3. Settings Page Integration

```typescript
// src/components/settings/IncrementSettings.tsx

export default function IncrementSettings() {
  const { userInfo, updateUserInfo } = useAuth()
  const [globalIncrement, setGlobalIncrement] = useState<number>(
    userInfo?.WorkoutIncrements || (userInfo?.MassUnit === 'kg' ? 1 : 2.5)
  )
  const [minWeight, setMinWeight] = useState<number>(
    userInfo?.Min ? (userInfo.MassUnit === 'kg' ? userInfo.Min.Kg : userInfo.Min.Lb) : 0
  )
  const [maxWeight, setMaxWeight] = useState<number>(
    userInfo?.Max ? (userInfo.MassUnit === 'kg' ? userInfo.Max.Kg : userInfo.Max.Lb) : 500
  )

  const handleSave = async () => {
    const unit = userInfo?.MassUnit as 'kg' | 'lb'

    const updatedUserInfo = {
      ...userInfo,
      WorkoutIncrements: globalIncrement,
      Min: new MultiUnityWeight(minWeight, unit),
      Max: new MultiUnityWeight(maxWeight, unit)
    }

    await updateUserInfo(updatedUserInfo)

    // Apply immediately to current session
    toast.success('Increment preferences saved')
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Weight Increment Preferences</h3>
        <p className="text-sm text-gray-600">
          Configure how weights are rounded during workouts. Exercise-specific settings override these defaults.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium mb-2">
            Default Increment ({userInfo?.MassUnit})
          </label>
          <WeightIncrementInput
            value={globalIncrement}
            unit={userInfo?.MassUnit as 'kg' | 'lbs'}
            onChange={setGlobalIncrement}
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">
            Minimum Weight ({userInfo?.MassUnit})
          </label>
          <input
            type="number"
            value={minWeight}
            onChange={(e) => setMinWeight(Number(e.target.value))}
            className="w-full px-3 py-2 border rounded-md"
            min="0"
            step={userInfo?.MassUnit === 'kg' ? 0.5 : 1}
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">
            Maximum Weight ({userInfo?.MassUnit})
          </label>
          <input
            type="number"
            value={maxWeight}
            onChange={(e) => setMaxWeight(Number(e.target.value))}
            className="w-full px-3 py-2 border rounded-md"
            min={minWeight}
            step={userInfo?.MassUnit === 'kg' ? 0.5 : 1}
          />
        </div>
      </div>

      <button
        onClick={handleSave}
        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
      >
        Save Preferences
      </button>
    </div>
  )
}
```

## Implementation Phases

### Phase 1: Foundation (Week 1)

**Priority: Critical Path**

1. **Type System Enhancement**
   - Extend `UserInfosModel` with `EquipmentModel` interface
   - Add `ExerciseSettingsModel` to exercise types
   - Create application-specific increment types
   - Update existing interfaces with missing fields

2. **Core Utility Functions**
   - Implement `incrementUtils.ts` with MAUI `roundToNearestIncrement`
   - Create `equipmentParser.ts` for equipment string parsing
   - Build `equipmentDetector.ts` for availability detection
   - Port MAUI plate calculation logic

3. **API Integration Setup**
   - Add `ExerciseSettingsService` for exercise-specific settings
   - Enhance existing API calls to include equipment data
   - Update `userInfoCache` to handle new fields
   - Test API integration with staging environment

**Deliverables:**

- ✅ All type definitions complete
- ✅ Core utility functions tested
- ✅ API integration working
- ✅ Unit tests for all utilities (>90% coverage)

### Phase 2: Business Logic (Week 2)

**Priority: MAUI Parity**

1. **Three-Tier Preference System**
   - Implement `getEffectiveIncrement` with hierarchy logic
   - Create increment resolution service
   - Add equipment-based increment defaults
   - Handle global preference fallbacks

2. **Edge Case Handlers**
   - Bodyweight exercise handling
   - Weighted exercise logic (pull-ups, dips)
   - Deload scenario processing
   - Equipment unavailability fallbacks

3. **Weight Calculation Engine**
   - Complete `WeightCalculationService` implementation
   - Integrate all MAUI business rules
   - Add comprehensive error handling
   - Performance optimization for real-time calculations

**Deliverables:**

- ✅ Three-tier hierarchy working correctly
- ✅ All MAUI edge cases handled
- ✅ Weight calculations match MAUI behavior
- ✅ Integration tests passing

### Phase 3: React Integration (Week 3)

**Priority: User Experience**

1. **React Hooks**
   - Complete `useIncrementPreferences` hook
   - Add caching and error handling
   - Implement real-time updates
   - Add loading states and error boundaries

2. **Component Updates**
   - Enhance `SetCell` with increment context
   - Update `useSetInputHandlers` with three-tier logic
   - Modify weight display components
   - Add increment indicators to UI

3. **Settings Integration**
   - Create `IncrementSettings` component
   - Add to settings page navigation
   - Implement immediate application of changes
   - Add validation and user feedback

**Deliverables:**

- ✅ All React hooks functional
- ✅ UI components updated
- ✅ Settings page complete
- ✅ User experience smooth and intuitive

### Phase 4: Testing & Validation (Week 4)

**Priority: Quality Assurance**

1. **Comprehensive Test Suite**
   - Unit tests for all utilities (target: 95% coverage)
   - Integration tests for API flows
   - React component testing
   - E2E tests for critical user journeys

2. **MAUI Behavior Validation**
   - Cross-reference calculations with MAUI app
   - Test edge cases against known MAUI behavior
   - Validate equipment string parsing
   - Confirm increment hierarchy logic

3. **Performance & Error Handling**
   - Load testing for weight calculations
   - Error scenario testing
   - Fallback behavior validation
   - Memory leak detection

**Deliverables:**

- ✅ Test coverage >95%
- ✅ All edge cases validated
- ✅ Performance benchmarks met
- ✅ Error handling robust

## Testing Strategy

### Unit Tests (Target: 95% Coverage)

```typescript
// src/utils/__tests__/incrementUtils.test.ts

describe('roundToNearestIncrement', () => {
  it('should match MAUI behavior with min constraint', () => {
    // Test case from MAUI: min=20, increment=2.5, target=22.3
    const result = roundToNearestIncrement(22.3, 2.5, 20)
    expect(result).toBe(22.5) // Next valid increment above min
  })

  it('should handle 50% rounding rule without min', () => {
    // Test MAUI 50% rule: round up if remainder > 50% of increment
    expect(roundToNearestIncrement(11.3, 2.5)).toBe(12.5) // 1.3 > 1.25 (50% of 2.5)
    expect(roundToNearestIncrement(11.2, 2.5)).toBe(10) // 1.2 < 1.25
  })

  it('should respect max constraints', () => {
    const result = roundToNearestIncrement(98, 2.5, undefined, 95)
    expect(result).toBe(95) // Capped at max
  })

  it('should handle zero and tiny increments', () => {
    expect(roundToNearestIncrement(67.3, 0)).toBe(67.3)
    expect(roundToNearestIncrement(67.3, 0.01)).toBe(67.3)
  })
})

describe('getEffectiveIncrement', () => {
  it('should prioritize exercise settings over equipment', () => {
    const exerciseSettings = {
      IsCustomIncrements: true,
      Increments: new MultiUnityWeight(1.25, 'kg'),
    }

    const result = getEffectiveIncrement(
      exerciseSettings,
      ['plates'], // Would normally give 2.5kg
      mockUserInfo,
      'kg'
    )

    expect(result.source).toBe('exercise')
    expect(result.increments.Kg).toBe(1.25)
  })

  it('should use equipment defaults when no exercise settings', () => {
    const result = getEffectiveIncrement(null, ['plates'], mockUserInfo, 'kg')

    expect(result.source).toBe('equipment')
    expect(result.increments.Kg).toBe(2.5) // Plate default
  })

  it('should fallback to global settings', () => {
    const result = getEffectiveIncrement(
      null,
      [], // No equipment
      mockUserInfoWithGlobalIncrements,
      'kg'
    )

    expect(result.source).toBe('global')
  })
})
```

### Integration Tests

```typescript
// src/hooks/__tests__/incrementFlow.integration.test.tsx

describe('Increment Flow Integration', () => {
  it('should apply exercise-specific increments in workout flow', async () => {
    // Mock exercise with custom increments
    const mockExerciseSettings = {
      ExerciseId: 123,
      IsCustomIncrements: true,
      Increments: new MultiUnityWeight(1.25, 'kg'),
      Min: new MultiUnityWeight(10, 'kg'),
      Max: new MultiUnityWeight(100, 'kg'),
    }

    // Mock API responses
    mockApiResponse(
      '/api/Exercise/GetExerciseSettingsPyramid',
      mockExerciseSettings
    )
    mockApiResponse('/api/Exercise/GetRecommendationNormalRIRForExercise', {
      RecommendationInKg: 67.3,
      Weight: { Kg: 67.3, Lb: 148.4 },
      Series: 3,
      Reps: 8,
    })

    const { result } = renderHook(() => useIncrementPreferences(123))

    await waitFor(() => {
      expect(result.current.incrementContext?.source).toBe('exercise')
      expect(result.current.incrementContext?.effectiveIncrement.Kg).toBe(1.25)
    })

    // Test weight calculation with custom increment
    const calculatedWeight = roundToNearestIncrement(67.3, 1.25, 10, 100)
    expect(calculatedWeight).toBe(67.5) // Rounded to nearest 1.25kg increment
  })

  it('should fallback to equipment defaults when exercise settings unavailable', async () => {
    // Mock no exercise settings
    mockApiResponse('/api/Exercise/GetExerciseSettingsPyramid', null)

    // Mock user with plate equipment
    const mockUserWithPlates = {
      ...mockUserInfo,
      EquipmentModel: {
        Active: 'gym',
        IsPlateEnabled: true,
        PlatesKg:
          '25_20_True|20_20_True|15_20_True|10_20_True|5_20_True|2.5_20_True',
      },
    }

    mockApiResponse('/api/Account/GetUserInfoPyramid', mockUserWithPlates)

    const { result } = renderHook(() => useIncrementPreferences(123))

    await waitFor(() => {
      expect(result.current.incrementContext?.source).toBe('equipment')
      expect(result.current.incrementContext?.effectiveIncrement.Kg).toBe(2.5) // Plate default
    })
  })
})
```

### E2E Tests

```typescript
// tests/e2e/increment-preferences.spec.ts

test.describe('Increment Preferences', () => {
  test('should apply increment preferences in workout', async ({ page }) => {
    await page.goto('/login')
    await page.fill('[data-testid="email"]', '<EMAIL>')
    await page.fill('[data-testid="password"]', 'Dr123456')
    await page.click('[data-testid="login-button"]')

    // Navigate to settings and set custom increment
    await page.goto('/settings')
    await page.click('[data-testid="increment-settings-tab"]')

    // Set 1.25kg increment
    await page.fill('[data-testid="global-increment-input"]', '1.25')
    await page.click('[data-testid="save-increment-settings"]')

    // Navigate to workout and verify increment is applied
    await page.goto('/workout/exercise/123')

    // Wait for recommendation to load
    await page.waitForSelector('[data-testid="weight-recommendation"]')

    // Check that weight is rounded to 1.25kg increments
    const weightText = await page.textContent(
      '[data-testid="weight-recommendation"]'
    )
    const weight = parseFloat(weightText?.replace(/[^\d.]/g, '') || '0')

    // Verify weight is a multiple of 1.25
    expect(weight % 1.25).toBe(0)
  })

  test('should show increment source in UI', async ({ page }) => {
    await page.goto('/workout/exercise/123')

    // Check for increment indicator
    await expect(
      page.locator('[data-testid="increment-indicator"]')
    ).toBeVisible()

    // Should show source (exercise/equipment/global)
    const incrementSource = await page.textContent(
      '[data-testid="increment-source"]'
    )
    expect(['exercise', 'equipment', 'global']).toContain(incrementSource)
  })
})
```

## Error Handling & Validation

### Validation Rules

```typescript
// src/utils/incrementValidation.ts

export class IncrementValidator {
  static validateIncrementSettings(
    settings: IncrementSettings
  ): ValidationResult {
    const errors: string[] = []

    // Rule 1: Increment must be positive
    if (!settings.increments || settings.increments.Kg <= 0) {
      errors.push('Increment must be greater than 0')
    }

    // Rule 2: Reasonable increment range (0.25kg to 50kg)
    if (
      settings.increments &&
      (settings.increments.Kg < 0.25 || settings.increments.Kg > 50)
    ) {
      errors.push('Increment should be between 0.25kg and 50kg')
    }

    // Rule 3: Min cannot exceed max
    if (settings.min && settings.max && settings.min.Kg > settings.max.Kg) {
      errors.push('Minimum weight cannot exceed maximum weight')
    }

    // Rule 4: Min should be non-negative
    if (settings.min && settings.min.Kg < 0) {
      errors.push('Minimum weight cannot be negative')
    }

    // Rule 5: Max should be reasonable (up to 500kg)
    if (settings.max && settings.max.Kg > 500) {
      errors.push('Maximum weight should not exceed 500kg')
    }

    // Rule 6: Increment should create reasonable number of steps
    if (settings.min && settings.max && settings.increments) {
      const range = settings.max.Kg - settings.min.Kg
      const steps = range / settings.increments.Kg
      if (steps > 200) {
        errors.push(
          'Increment too small for the min-max range (would create too many steps)'
        )
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  }

  static validateRecommendationModel(
    recommendation: RecommendationModel
  ): ValidationResult {
    const errors: string[] = []

    // Rule 1: Weight must be positive for non-bodyweight exercises
    if (!recommendation.IsBodyweight && recommendation.Weight.Kg <= 0) {
      errors.push('Weight must be positive for weighted exercises')
    }

    // Rule 2: Reps must be reasonable
    if (recommendation.Reps < 1 || recommendation.Reps > 100) {
      errors.push('Reps must be between 1 and 100')
    }

    // Rule 3: Series must be reasonable
    if (recommendation.Series < 1 || recommendation.Series > 10) {
      errors.push('Series must be between 1 and 10')
    }

    // Rule 4: Equipment availability for weighted exercises
    if (!recommendation.IsBodyweight) {
      const hasEquipment =
        recommendation.isPlateAvailable ||
        recommendation.isDumbbellAvailable ||
        recommendation.isPulleyAvailable ||
        recommendation.isBandsAvailable
      if (!hasEquipment) {
        errors.push('No equipment available for weighted exercise')
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  }
}
```

### Error Recovery

```typescript
// src/utils/incrementErrorHandler.ts

export class IncrementErrorHandler {
  static handleCalculationError(
    error: Error,
    fallbackWeight: number,
    context: string
  ): MultiUnityWeight {
    console.error(`Weight calculation failed in ${context}:`, error)

    // Log error for monitoring
    this.logError('WEIGHT_CALCULATION_ERROR', error, {
      context,
      fallbackWeight,
    })

    // Return safe fallback
    return new MultiUnityWeight(fallbackWeight, 'kg')
  }

  static handleApiError(
    error: Error,
    endpoint: string,
    fallbackData?: any
  ): any {
    console.warn(`API call failed for ${endpoint}:`, error)

    // Log for monitoring but don't throw - use fallbacks
    this.logError('API_ERROR', error, { endpoint })

    return fallbackData
  }

  static handleValidationError(
    validationResult: ValidationResult,
    context: string
  ): void {
    if (!validationResult.isValid) {
      console.warn(`Validation failed in ${context}:`, validationResult.errors)

      // Show user-friendly error
      toast.error(`Invalid increment settings: ${validationResult.errors[0]}`)
    }
  }

  private static logError(type: string, error: Error, context?: any): void {
    // Send to monitoring service (e.g., Sentry, LogRocket)
    console.error(`${type}:`, error, context)
  }
}
```

## Performance Considerations

### Caching Strategy

```typescript
// src/services/incrementCache.ts

class IncrementCache {
  private static cache = new Map<string, CachedIncrementData>()
  private static readonly CACHE_TTL = 5 * 60 * 1000 // 5 minutes

  static getCachedIncrementContext(key: string): IncrementContext | null {
    const cached = this.cache.get(key)
    if (!cached || Date.now() - cached.timestamp > this.CACHE_TTL) {
      this.cache.delete(key)
      return null
    }
    return cached.context
  }

  static setCachedIncrementContext(
    key: string,
    context: IncrementContext
  ): void {
    this.cache.set(key, {
      context,
      timestamp: Date.now(),
    })
  }

  static generateCacheKey(exerciseId: number, userId: string): string {
    return `increment_${userId}_${exerciseId}`
  }

  static clearCache(): void {
    this.cache.clear()
  }
}

interface CachedIncrementData {
  context: IncrementContext
  timestamp: number
}
```

### Memory Management

```typescript
// src/hooks/useIncrementPreferences.ts - Enhanced with cleanup

export function useIncrementPreferences(exerciseId?: number) {
  // ... existing code ...

  useEffect(() => {
    // Cleanup function to prevent memory leaks
    return () => {
      // Cancel any pending API calls
      if (abortController.current) {
        abortController.current.abort()
      }

      // Clear component-specific cache entries
      if (exerciseId) {
        const cacheKey = IncrementCache.generateCacheKey(
          exerciseId,
          userInfo?.Email || ''
        )
        IncrementCache.clearCacheEntry(cacheKey)
      }
    }
  }, [exerciseId, userInfo?.Email])
}
```

## Success Metrics & Monitoring

### Key Performance Indicators

1. **Functional Metrics**
   - ✅ Weight calculation accuracy: 100% match with MAUI behavior
   - ✅ API response time: <200ms for increment resolution
   - ✅ Cache hit rate: >80% for repeat exercise access
   - ✅ Error rate: <0.1% for weight calculations

2. **User Experience Metrics**
   - ✅ Settings save time: <1 second
   - ✅ Immediate application: Changes visible within 100ms
   - ✅ User satisfaction: >95% positive feedback on increment accuracy
   - ✅ Support tickets: <5% increase related to weight calculations

3. **Technical Metrics**
   - ✅ Test coverage: >95% for all increment-related code
   - ✅ Bundle size impact: <50KB additional JavaScript
   - ✅ Memory usage: No memory leaks in 24-hour stress test
   - ✅ Performance regression: <5% impact on existing workout flows

### Monitoring Dashboard

```typescript
// src/utils/incrementMonitoring.ts

export class IncrementMonitoring {
  static trackCalculation(
    exerciseId: number,
    originalWeight: number,
    finalWeight: number,
    source: 'exercise' | 'equipment' | 'global',
    calculationTime: number
  ): void {
    // Send to analytics
    analytics.track('increment_calculation', {
      exerciseId,
      originalWeight,
      finalWeight,
      weightDifference: Math.abs(finalWeight - originalWeight),
      source,
      calculationTime,
      timestamp: Date.now(),
    })
  }

  static trackError(type: string, error: Error, context: any): void {
    // Send to error tracking
    errorTracking.captureException(error, {
      tags: { type },
      extra: context,
    })
  }

  static trackUserSatisfaction(
    exerciseId: number,
    wasWeightAccurate: boolean,
    userFeedback?: string
  ): void {
    analytics.track('increment_user_feedback', {
      exerciseId,
      wasWeightAccurate,
      userFeedback,
      timestamp: Date.now(),
    })
  }
}
```

## Conclusion

This comprehensive implementation guide provides a complete roadmap for implementing the MAUI-proven weight increment system in the DrMuscle web app. The approach prioritizes:

1. **MAUI Parity**: Direct port of proven business logic and algorithms
2. **Three-Tier Hierarchy**: Exercise > Equipment > Global preference system
3. **Comprehensive Edge Cases**: All MAUI edge cases handled
4. **React Integration**: Seamless integration with existing web app patterns
5. **Quality Assurance**: 95%+ test coverage with comprehensive validation
6. **Performance**: Optimized for real-time calculations with caching
7. **User Experience**: Immediate application of settings with clear feedback

The phased implementation approach ensures minimal risk while delivering maximum value, with each phase building upon the previous to create a robust, user-friendly increment system that matches the proven MAUI mobile app experience.
