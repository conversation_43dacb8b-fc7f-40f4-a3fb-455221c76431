# Implementation Plan

## Weight Increment Preferences Implementation

Implement proper handling of weight increment preferences from the API at both global (user) and exercise-specific levels, ensuring weight recommendations respect user preferences and available equipment.

The current application receives increment data from the API (`Increments` field in both UserInfosModel and RecommendationModel) but doesn't properly save or apply these preferences. Weight recommendations need to be rounded to match user's available weight increments for practical workout execution.

## Types

Define comprehensive type system for increment preferences.

### New Type Definitions

```typescript
// src/types/api/common.ts - Update MultiUnityWeight if needed
export interface IncrementSettings extends MultiUnityWeight {
  // Already has Lb and Kg properties
}

// src/types/api/auth.ts - Extend UserInfosModel documentation
// UserInfosModel already includes:
// - Increments: MultiUnityWeight | null (global user preference)
// - Min: MultiUnityWeight | null (minimum weight available)
// - Max: MultiUnityWeight | null (maximum weight available)

// src/types/api/exercise.ts - RecommendationModel already includes:
// - Increments?: MultiUnityWeight (exercise-specific preference)
// - Min?: MultiUnityWeight (exercise minimum weight)
// - Max?: MultiUnityWeight (exercise maximum weight)

// src/types/settings.ts - Add to LocalSettings interface
export interface LocalSettings {
  // ... existing fields ...
  /** Weight increment for progression (in selected unit) */
  weightIncrement: number
  /** Minimum weight available (in selected unit) */
  minWeight?: number
  /** Maximum weight available (in selected unit) */
  maxWeight?: number
}

// src/types/app.ts - Add increment context type
export interface IncrementContext {
  globalIncrement: MultiUnityWeight | null
  exerciseIncrement?: MultiUnityWeight | null
  effectiveIncrement: MultiUnityWeight
  minWeight?: MultiUnityWeight | null
  maxWeight?: MultiUnityWeight | null
}
```

## Files

Organize increment handling across the codebase.

### New Files

- `src/utils/incrementUtils.ts` - Centralized increment calculation utilities
- `src/utils/__tests__/incrementUtils.test.ts` - Unit tests for increment utilities
- `src/hooks/useIncrementPreferences.ts` - Hook for accessing increment preferences
- `src/hooks/__tests__/useIncrementPreferences.test.ts` - Tests for increment hook

### Modified Files

- `src/services/userInfoCache.ts` - Ensure increments are cached with user info
- `src/hooks/useSettingsPersistence.ts` - Save increment settings locally
- `src/utils/weightUtils.ts` - Update weight formatting to use proper increments
- `src/utils/generateAllSets.ts` - Apply increments when generating sets
- `src/components/workout/SetCell.tsx` - Use proper increments for display
- `src/components/workout/SetListMobile.tsx` - Apply increments in mobile view
- `src/stores/workoutStore/index.ts` - Store exercise increments with recommendations
- `src/types/settings.ts` - Add increment fields to settings types
- `src/hooks/useSetCellHandlers.ts` - Handle increment-based weight adjustments

## Functions

Create utility functions for increment management.

### New Functions

**File: src/utils/incrementUtils.ts**

- `getEffectiveIncrement(global, exercise, unit)` - Determine which increment to use
- `applyIncrement(weight, increment, min, max)` - Round weight to increment with constraints
- `getIncrementContext(userInfo, recommendation, unit)` - Build complete increment context
- `formatWeightWithIncrement(weight, increment, unit)` - Format weight respecting increment
- `calculateNextIncrement(current, increment, direction)` - Calculate next valid weight
- `isValidIncrement(weight, increment)` - Check if weight matches increment pattern
- `getDefaultIncrement(unit, isBodyweight)` - Get sensible default increments

**File: src/hooks/useIncrementPreferences.ts**

- Main hook function to access current increment preferences
- Combines global and exercise-specific settings
- Returns formatted increment for current unit

**Modified Functions**

**File: src/utils/weightUtils.ts**

- `formatRecommendationWeight()` - Use actual increments instead of hardcoded values
- `roundToNearestIncrement()` - Respect min/max constraints properly

**File: src/utils/generateAllSets.ts**

- `generateWorkoutSets()` - Apply increments when creating sets
- `calculateWarmupWeights()` - Round warmup weights to increments

## Classes

No new classes needed; existing store patterns sufficient.

## Dependencies

No new external dependencies required. Uses existing:

- React hooks for state management
- Zustand stores for global state
- TypeScript for type safety

## Testing

Comprehensive test coverage for increment functionality.

### Unit Tests

- Test increment calculation with various global/exercise combinations
- Test weight rounding with different increment values
- Test min/max constraint enforcement
- Test default increment selection
- Test increment persistence in localStorage
- Test increment context building

### Integration Tests

- Test increment flow from API to display
- Test exercise-specific override behavior
- Test increment updates when switching exercises
- Test increment persistence across sessions

### E2E Tests

- Test weight recommendations respect increments
- Test increment settings save correctly
- Test increment display in workout UI

## Implementation Order

Phased approach to minimize risk and ensure backward compatibility.

1. **Phase 1: Type Definitions and Utilities**
   - Add increment types to existing interfaces
   - Create incrementUtils.ts with core functions
   - Add comprehensive unit tests

2. **Phase 2: Data Persistence**
   - Update userInfoCache to preserve increment data
   - Modify useSettingsPersistence to save increments locally
   - Ensure increments survive page refreshes

3. **Phase 3: Hook Implementation**
   - Create useIncrementPreferences hook
   - Integrate with existing settings hooks
   - Add proper TypeScript types

4. **Phase 4: Weight Calculation Updates**
   - Update weightUtils functions to use increments
   - Modify generateAllSets to apply increments
   - Update warmup calculations

5. **Phase 5: UI Integration**
   - Update SetCell to display incremented weights
   - Modify SetListMobile for consistency
   - Update weight arrow handlers to use increments

6. **Phase 6: Testing and Validation**
   - Run all unit tests
   - Execute integration test suite
   - Perform manual testing with various increments
   - Test with both kg and lbs units

7. **Phase 7: Edge Cases and Polish**
   - Handle null/undefined increments gracefully
   - Add loading states where needed
   - Ensure proper error handling
   - Document increment behavior