'use client'

import { SetScreenLoadingState } from '@/components/workout/SetScreenLoadingState'
import { SetScreenErrorState } from '@/components/workout/SetScreenErrorState'
import {
  ExercisePageErrorBoundary,
  WorkoutErrorBoundary,
} from '@/components/workout/ExercisePageErrorBoundary'
import { ExerciseCompleteViewV2 } from './ExerciseCompleteViewV2'
import { WorkoutCompleteView } from '@/components/workout/WorkoutCompleteView'
import { useRecommendationRetry } from '@/hooks/useRecommendationRetry'
import type {
  ExerciseModel,
  WorkoutLogSerieModel,
  RecommendationModel,
} from '@/types'

interface ExercisePageStatesProps {
  loadingError: Error | string | null
  workoutError: Error | null
  retryInitialization: () => Promise<void>
  isInitializing: boolean
  isLoadingWorkout: boolean
  isLoadingRecommendation: boolean
  isLoading: boolean
  recommendation: RecommendationModel | null
  currentExercise: ExerciseModel | null
  workoutSession: unknown
  error: string | null
  refetchRecommendation: () => Promise<void>
  showComplete: boolean
  showExerciseComplete: boolean
  currentSet: WorkoutLogSerieModel | null
  isLastExercise: boolean
  handleSaveSet: () => void
  handleNavigateToNext?: () => void
  completedSets?: WorkoutLogSerieModel[]
}

export function ExercisePageStates({
  loadingError,
  workoutError,
  retryInitialization,
  isInitializing,
  isLoadingWorkout,
  isLoadingRecommendation,
  isLoading,
  recommendation,
  currentExercise,
  workoutSession,
  error,
  refetchRecommendation,
  showComplete,
  showExerciseComplete,
  currentSet,
  isLastExercise,
  handleSaveSet,
  handleNavigateToNext,
  completedSets,
}: ExercisePageStatesProps) {
  // Use the retry hook for automatic retry logic
  const { retryCount, hasExhaustedRetries, handleManualRetry, loadingMessage } =
    useRecommendationRetry({
      isLoadingRecommendation,
      recommendation,
      refetchRecommendation,
    })
  // Show error state with retry option
  if (loadingError) {
    const errorObj =
      typeof loadingError === 'string' ? new Error(loadingError) : loadingError
    return (
      <ExercisePageErrorBoundary
        error={errorObj}
        onRetry={retryInitialization}
      />
    )
  }

  // Handle workout errors
  if (workoutError) {
    return <WorkoutErrorBoundary error={workoutError} />
  }

  // Show loading state with retry information
  if ((!recommendation || isLoadingRecommendation) && !hasExhaustedRetries) {
    return (
      <SetScreenLoadingState
        exerciseName={currentExercise?.Label}
        showDetailedSkeleton
        isLoadingRecommendations={isLoadingRecommendation}
        loadingMessage={loadingMessage}
        retryAttempt={retryCount > 0 ? retryCount : undefined}
      />
    )
  }

  // Handle exhausted retries - show enhanced error state
  if (hasExhaustedRetries && !recommendation) {
    return (
      <SetScreenErrorState
        onRetry={handleManualRetry}
        errorMessage="Unable to load exercise data"
        subMessage="Check your internet connection and try again"
        showBackButton
      />
    )
  }

  // Show loading state if we're missing any critical data
  // This ensures we never show a blank page
  if (
    isInitializing ||
    isLoadingWorkout ||
    isLoading ||
    !currentExercise ||
    !workoutSession ||
    !recommendation // Also check for recommendation here
  ) {
    return (
      <SetScreenLoadingState
        exerciseName={currentExercise?.Label}
        showDetailedSkeleton={!isInitializing && !isLoadingWorkout}
        isLoadingRecommendations={false}
      />
    )
  }

  // Error state
  if (error) {
    return <SetScreenErrorState onRetry={refetchRecommendation} />
  }

  // Workout complete
  if (showComplete) {
    return <WorkoutCompleteView />
  }

  // Exercise complete - only show if we have loaded data and exercise is truly complete
  if (showExerciseComplete || (recommendation && !currentSet)) {
    return (
      <ExerciseCompleteViewV2
        exercise={currentExercise}
        isLastExercise={isLastExercise}
        onContinue={handleNavigateToNext || handleSaveSet}
        recommendation={recommendation}
        completedSets={completedSets}
      />
    )
  }

  return null
}
