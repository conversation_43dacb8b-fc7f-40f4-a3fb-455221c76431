import { test, expect } from '@playwright/test'
import { login } from './helpers/auth'

test.describe('Recommendation Retry Logic', () => {
  test.beforeEach(async ({ page }) => {
    await login(page)
    await page.goto('/workout')
  })

  test('should retry loading recommendations when API initially fails', async ({
    page,
  }) => {
    // Intercept recommendation API calls to simulate failures
    let callCount = 0
    await page.route(
      '**/api/workout/exercise-recommendation',
      async (route) => {
        callCount++

        // First two calls fail, third succeeds
        if (callCount < 3) {
          await route.fulfill({
            status: 500,
            contentType: 'application/json',
            body: JSON.stringify({ error: 'Internal Server Error' }),
          })
        } else {
          // Return a valid recommendation on the third attempt
          await route.fulfill({
            status: 200,
            contentType: 'application/json',
            body: JSON.stringify({
              Reps: 10,
              Weight: { Lb: 135, Kg: 61.2 },
              Series: 3,
              WarmupsCount: 2,
              WarmUpsList: [
                {
                  WarmUpReps: 5,
                  WarmUpWeightSet: { Lb: 45, Kg: 20.4 },
                },
                {
                  WarmUpReps: 5,
                  WarmUpWeightSet: { Lb: 95, Kg: 43.1 },
                },
              ],
              IsNormalSets: true,
            }),
          })
        }
      }
    )

    // Navigate to workout page
    await page.goto('/workout')

    // Wait for workout to load
    await page.waitForSelector('[data-testid="exercise-card"]', {
      timeout: 10000,
    })

    // Click on the first exercise
    await page.click('[data-testid="exercise-card"]:first-child')

    // Wait for exercise page to load
    await page.waitForURL(/\/workout\/exercise/)

    // The retry logic should have kicked in and eventually loaded the recommendation
    // Look for the sets grid (it should be visible, not showing "No sets")
    await expect(page.locator('[data-testid="sets-grid"]')).toBeVisible({
      timeout: 10000, // Give enough time for retries
    })

    // Verify we don't see the "No sets" message
    await expect(
      page.locator('text="No sets for this exercise"')
    ).not.toBeVisible()

    // Verify that the API was called multiple times due to retries
    expect(callCount).toBeGreaterThanOrEqual(3)
  })

  test('should show sets immediately when recommendation is cached', async ({
    page,
  }) => {
    // First, navigate to workout and wait for it to load
    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Click on the first exercise to cache its recommendation
    await page.click('[data-testid="exercise-card"]:first-child')
    await page.waitForURL(/\/workout\/exercise/)
    await expect(page.locator('[data-testid="sets-grid"]')).toBeVisible()

    // Go back to workout page
    await page.goto('/workout')

    // Click the same exercise again
    await page.click('[data-testid="exercise-card"]:first-child')
    await page.waitForURL(/\/workout\/exercise/)

    // The sets grid should be visible immediately (no loading state)
    await expect(page.locator('[data-testid="sets-grid"]')).toBeVisible({
      timeout: 1000, // Should be immediate from cache
    })

    // Verify we don't see the "No sets" message
    await expect(
      page.locator('text="No sets for this exercise"')
    ).not.toBeVisible()
  })
})
