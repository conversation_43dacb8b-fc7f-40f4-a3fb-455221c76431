import { test, expect } from '@playwright/test'

test.describe('OAuth Safari Fix', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/login')
    await page.waitForLoadState('networkidle')
  })

  test('should handle Google sign-in redirect on Safari @safari', async ({
    page,
    browserName,
  }) => {
    // Only run on WebKit (Safari)
    test.skip(browserName !== 'webkit', 'Safari-specific test')

    // Mock Firebase redirect result
    await page.addInitScript(() => {
      window.mockFirebaseRedirectResult = {
        user: {
          uid: 'test-uid',
          email: '<EMAIL>',
          emailVerified: true,
          displayName: 'Test User',
          getIdToken: () => Promise.resolve('test-token'),
        },
        providerId: 'google.com',
      }
    })

    // Click Google sign-in button
    const googleButton = page.locator('button:has-text("Sign in with Google")')
    await expect(googleButton).toBeVisible()

    // In Safari, this should trigger redirect flow instead of popup
    const [request] = await Promise.all([
      page.waitForRequest((req) => req.url().includes('signInWithRedirect')),
      googleButton.click(),
    ])

    expect(request).toBeTruthy()
  })

  test('should show improved error message for popup blocked @chrome', async ({
    page,
    browserName,
  }) => {
    // Only run on Chromium
    test.skip(browserName !== 'chromium', 'Chrome-specific test')

    // Mock popup blocked error
    await page.addInitScript(() => {
      window.mockFirebaseAuthError = {
        code: 'auth/popup-blocked',
        message: 'Popup blocked by browser',
      }
    })

    // Click Google sign-in button
    const googleButton = page.locator('button:has-text("Sign in with Google")')
    await googleButton.click()

    // Should show specific popup blocked message
    await expect(
      page.locator('text=Sign-in popup was blocked by your browser')
    ).toBeVisible({ timeout: 5000 })
    await expect(
      page.locator('text=Please allow popups for this site')
    ).toBeVisible()
  })

  test('should handle redirect result on page load', async ({ page }) => {
    // Simulate returning from OAuth redirect with auth code
    await page.goto('/login?code=test-auth-code&state=test-state')

    // Mock successful redirect result
    await page.addInitScript(() => {
      window.mockFirebaseRedirectResult = {
        user: {
          uid: 'test-uid',
          email: '<EMAIL>',
          emailVerified: true,
          displayName: 'Test User',
          getIdToken: () => Promise.resolve('test-token'),
        },
        providerId: 'google.com',
      }
    })

    // Should automatically process redirect result and navigate
    await expect(page).toHaveURL('/workout', { timeout: 10000 })
  })

  test('should detect Safari user agent correctly', async ({ page }) => {
    // Test Safari detection logic
    const isSafari = await page.evaluate(() => {
      const ua = navigator.userAgent
      return /^((?!chrome|android).)*safari/i.test(ua)
    })

    const browserName = await page.evaluate(() => {
      if (
        /Safari/.test(navigator.userAgent) &&
        !/Chrome/.test(navigator.userAgent)
      ) {
        return 'safari'
      } else if (/Chrome/.test(navigator.userAgent)) {
        return 'chrome'
      }
      return 'other'
    })

    // Test output - not console.log
    await expect(isSafari).toBeDefined()
    await expect(browserName).toBeDefined()
  })
})
