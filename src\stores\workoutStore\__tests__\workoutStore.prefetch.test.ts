import { describe, it, expect, beforeEach, vi } from 'vitest'
import { useWorkoutStore } from '../index'

// Unmock the store to test the real implementation
vi.unmock('@/stores/workoutStore')

describe('WorkoutStore Prefetch State', () => {
  beforeEach(() => {
    // Reset store to initial state
    useWorkoutStore.setState({
      prefetchStatus: {},
      prefetchedExerciseIds: [],
      exercises: [],
      currentWorkout: null,
    })
  })

  it('should initialize with empty prefetch state', () => {
    const { prefetchStatus, prefetchedExerciseIds } = useWorkoutStore.getState()

    expect(prefetchStatus).toEqual({})
    expect(prefetchedExerciseIds).toEqual([])
  })

  it('should update prefetch status for exercises', () => {
    const { setPrefetchStatus } = useWorkoutStore.getState()

    setPrefetchStatus({
      1: 'loading',
      2: 'loading',
    })

    const state1 = useWorkoutStore.getState()
    expect(state1.prefetchStatus).toEqual({
      1: 'loading',
      2: 'loading',
    })

    setPrefetchStatus({
      1: 'success',
      2: 'error',
    })

    const state2 = useWorkoutStore.getState()
    expect(state2.prefetchStatus).toEqual({
      1: 'success',
      2: 'error',
    })
  })

  it('should update prefetched exercise IDs', () => {
    const { setPrefetchedExerciseIds } = useWorkoutStore.getState()

    setPrefetchedExerciseIds([1, 2, 3])

    const { prefetchedExerciseIds } = useWorkoutStore.getState()
    expect(prefetchedExerciseIds).toEqual([1, 2, 3])
  })

  it('should check if exercise is prefetched', () => {
    const {
      setPrefetchStatus,
      setPrefetchedExerciseIds,
      isExercisePrefetched,
    } = useWorkoutStore.getState()

    setPrefetchStatus({
      1: 'success',
      2: 'error',
      3: 'loading',
    })
    setPrefetchedExerciseIds([1])

    expect(isExercisePrefetched(1)).toBe(true)
    expect(isExercisePrefetched(2)).toBe(false)
    expect(isExercisePrefetched(3)).toBe(false)
    expect(isExercisePrefetched(4)).toBe(false)
  })

  it('should clear prefetch state when resetting workout', () => {
    const { setPrefetchStatus, setPrefetchedExerciseIds, resetWorkout } =
      useWorkoutStore.getState()

    setPrefetchStatus({
      1: 'success',
      2: 'loading',
    })
    setPrefetchedExerciseIds([1])

    const state1 = useWorkoutStore.getState()
    expect(state1.prefetchStatus).not.toEqual({})
    expect(state1.prefetchedExerciseIds).not.toEqual([])

    resetWorkout()

    // Get fresh state after reset
    const state2 = useWorkoutStore.getState()
    expect(state2.prefetchStatus).toEqual({})
    expect(state2.prefetchedExerciseIds).toEqual([])
  })

  it('should clear prefetch state for specific exercise', () => {
    const {
      setPrefetchStatus,
      setPrefetchedExerciseIds,
      clearExercisePrefetch,
    } = useWorkoutStore.getState()

    setPrefetchStatus({
      1: 'success',
      2: 'error',
      3: 'loading',
    })
    setPrefetchedExerciseIds([1, 2])

    clearExercisePrefetch(1)

    const state = useWorkoutStore.getState()
    expect(state.prefetchStatus).toEqual({
      2: 'error',
      3: 'loading',
    })
    expect(state.prefetchedExerciseIds).toEqual([2])
  })

  it('should get prefetch progress information', () => {
    const { setPrefetchStatus, getPrefetchProgress } =
      useWorkoutStore.getState()

    setPrefetchStatus({
      1: 'success',
      2: 'success',
      3: 'error',
      4: 'loading',
      5: 'loading',
    })

    const progress = getPrefetchProgress()

    expect(progress.total).toBe(5)
    expect(progress.completed).toBe(2)
    expect(progress.failed).toBe(1)
    expect(progress.inProgress).toBe(2)
    expect(progress.percentage).toBe(40) // 2 out of 5 = 40%
  })

  // New test for initializing prefetch state
  it('should initialize prefetch state for all exercises as loading', () => {
    const { setWorkout, initializePrefetchState } = useWorkoutStore.getState()

    // Set workout with exercises
    setWorkout({
      Id: 1,
      Exercises: [
        { Id: 101, Label: 'Bench Press' },
        { Id: 102, Label: 'Squats' },
        { Id: 103, Label: 'Deadlifts' },
      ],
    })

    // Initialize prefetch state
    initializePrefetchState()

    const { prefetchStatus } = useWorkoutStore.getState()
    expect(prefetchStatus).toEqual({
      101: 'loading',
      102: 'loading',
      103: 'loading',
    })
  })

  it('should not overwrite existing prefetch status when initializing', () => {
    const { setPrefetchStatus, initializePrefetchState } =
      useWorkoutStore.getState()

    // Set some existing prefetch status
    setPrefetchStatus({
      101: 'success',
      102: 'error',
    })

    // Initialize should not overwrite existing status
    initializePrefetchState()

    const { prefetchStatus } = useWorkoutStore.getState()
    expect(prefetchStatus).toEqual({
      101: 'success',
      102: 'error',
    })
  })

  it('should handle empty exercise list when initializing prefetch state', () => {
    const { setWorkout, initializePrefetchState } = useWorkoutStore.getState()

    // Set workout with no exercises
    setWorkout({ Id: 1, Exercises: [] })

    // Initialize should handle empty list gracefully
    initializePrefetchState()

    const { prefetchStatus } = useWorkoutStore.getState()
    expect(prefetchStatus).toEqual({})
  })
})
