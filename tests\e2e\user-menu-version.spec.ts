import { test, expect } from '@playwright/test'

const APP_VERSION = '0.2025.08.19.1'

test.describe('User Menu Version Display @critical', () => {
  test('should display app version in user menu', async ({ page }) => {
    // Mock all necessary APIs for authenticated session
    await page.route('**/api/Workout/GetUserProgramInfo*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          GetUserProgramInfoResponseModel: {
            RecommendedProgram: {
              Id: 1,
              Label: 'Test Program',
              RemainingToLevelUp: 3,
            },
            NextWorkoutTemplate: {
              Id: 101,
              Label: 'Workout A',
              IsSystemExercise: false,
            },
          },
          TotalWorkoutCompleted: 10,
          ConsecutiveWeeks: 3,
        }),
      })
    })

    // Mock user info API
    await page.route('**/api/Account/GetUserInfo*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Email: '<EMAIL>',
          FirstName: 'Test',
          LastName: 'User',
          MassUnit: 'kg',
        }),
      })
    })

    // Mock auth token API
    await page.route('**/api/auth/token*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          token: 'mock-jwt-token',
          authenticated: true,
        }),
      })
    })

    // Set auth state
    await page.evaluate(() => {
      localStorage.setItem('auth-token', 'mock-jwt-token')
      localStorage.setItem(
        'auth-storage',
        JSON.stringify({
          state: {
            isAuthenticated: true,
            user: {
              email: '<EMAIL>',
              name: 'Test User',
            },
          },
        })
      )
    })

    // Navigate to program page
    await page.goto('/program')

    // Wait for page to load
    await page.waitForSelector('h1:has-text("Dr. Muscle X")')

    // Click the menu trigger
    await page.click('[aria-label="Open user menu"]')

    // Wait for menu to open
    await page.waitForSelector('[role="dialog"][aria-label="User menu"]')

    // Check version is displayed
    const versionText = page.locator(`text=v${APP_VERSION}`).first()
    await expect(versionText).toBeVisible()

    // Verify version styling is low-key
    await expect(versionText).toHaveClass(/text-xs/)
    await expect(versionText).toHaveClass(/text-text-tertiary/)

    // Verify it's at the bottom of the menu
    const versionContainer = versionText.locator('..') // parent div
    await expect(versionContainer).toHaveClass(/border-t/)
  })
})
