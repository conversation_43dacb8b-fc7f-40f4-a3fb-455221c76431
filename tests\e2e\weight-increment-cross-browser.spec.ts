import { test, expect } from '@playwright/test'

// Cross-browser test for WeightIncrementInput component
// This test will run across all configured browser projects in playwright.config.ts

test.describe('WeightIncrementInput Cross-Browser Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication to access settings page
    await page.addInitScript(() => {
      localStorage.setItem('auth-token', 'mock-token')
    })
    await page.goto('/settings')
    await page.waitForLoadState('networkidle')
  })

  test('should show decimal keyboard with inputMode="decimal"', async ({
    page,
    browserName,
  }) => {
    console.log(`Testing inputMode on ${browserName}`)

    const weightInput = page.locator(
      'input[aria-label="Weight increment value"]'
    )
    await expect(weightInput).toBeVisible()

    // Check inputMode attribute for mobile decimal keyboard
    await expect(weightInput).toHaveAttribute('inputMode', 'decimal')

    // Tap the input to trigger mobile keyboard
    await weightInput.click()

    console.log(`${browserName}: inputMode="decimal" verified`)
  })

  test('CRITICAL: should handle decimal input continuation without blocking', async ({
    page,
    browserName,
  }) => {
    console.log(`Testing decimal continuation on ${browserName}`)

    const weightInput = page.locator(
      'input[aria-label="Weight increment value"]'
    )
    await weightInput.click()

    // Clear and start typing decimal sequence
    await weightInput.clear()
    await weightInput.type('2', { delay: 100 })

    let currentValue = await weightInput.inputValue()
    console.log(`${browserName} - After "2": ${currentValue}`)
    expect(currentValue).toBe('2')

    // Type decimal point - THIS IS THE CORE FIX BEING TESTED
    await weightInput.type('.', { delay: 100 })

    // Verify decimal point is preserved (tests the intermediate state bug fix)
    currentValue = await weightInput.inputValue()
    console.log(`${browserName} - After "2.": ${currentValue}`)
    expect(currentValue).toBe('2.')

    // Continue typing to complete decimal value
    await weightInput.type('5', { delay: 100 })

    // Verify complete decimal value
    currentValue = await weightInput.inputValue()
    console.log(`${browserName} - Final "2.5": ${currentValue}`)
    expect(currentValue).toBe('2.5')

    console.log(`${browserName}: Decimal continuation PASSED`)
  })

  test('should have responsive 52px touch targets', async ({
    page,
    browserName,
  }) => {
    console.log(`Testing touch targets on ${browserName}`)

    // Check increment button
    const incrementBtn = page.locator(
      'button[aria-label="Increase weight increment"]'
    )
    const incrementBox = await incrementBtn.boundingBox()
    console.log(
      `${browserName} - Increment button: ${incrementBox?.width}x${incrementBox?.height}`
    )
    expect(incrementBox?.height).toBeGreaterThanOrEqual(52)
    expect(incrementBox?.width).toBeGreaterThanOrEqual(52)

    // Check decrement button
    const decrementBtn = page.locator(
      'button[aria-label="Decrease weight increment"]'
    )
    const decrementBox = await decrementBtn.boundingBox()
    console.log(
      `${browserName} - Decrement button: ${decrementBox?.width}x${decrementBox?.height}`
    )
    expect(decrementBox?.height).toBeGreaterThanOrEqual(52)
    expect(decrementBox?.width).toBeGreaterThanOrEqual(52)

    // Check input field
    const weightInput = page.locator(
      'input[aria-label="Weight increment value"]'
    )
    const inputBox = await weightInput.boundingBox()
    console.log(
      `${browserName} - Input field: ${inputBox?.width}x${inputBox?.height}`
    )
    expect(inputBox?.height).toBeGreaterThanOrEqual(52)

    console.log(`${browserName}: Touch targets PASSED`)
  })

  test('should handle focus/blur behavior correctly', async ({
    page,
    browserName,
  }) => {
    console.log(`Testing focus/blur on ${browserName}`)

    const weightInput = page.locator(
      'input[aria-label="Weight increment value"]'
    )

    // Get initial value
    const initialValue = await weightInput.inputValue()
    console.log(`${browserName} - Initial value: ${initialValue}`)

    // Focus and edit
    await weightInput.click()
    await weightInput.clear()
    await weightInput.type('4.5')

    const focusValue = await weightInput.inputValue()
    console.log(`${browserName} - Value while focused: ${focusValue}`)

    // Blur by clicking outside
    await page.click('h3') // Click a header to blur

    // Check final value after blur
    const blurValue = await weightInput.inputValue()
    console.log(`${browserName} - Value after blur: ${blurValue}`)

    // Should maintain the correct value
    expect(parseFloat(blurValue)).toBe(4.5)

    console.log(`${browserName}: Focus/blur PASSED`)
  })

  test('should validate boundaries correctly', async ({
    page,
    browserName,
  }) => {
    console.log(`Testing boundaries on ${browserName}`)

    const weightInput = page.locator(
      'input[aria-label="Weight increment value"]'
    )

    // Test minimum boundary (kg unit = 0.5 min)
    await weightInput.click()
    await weightInput.clear()
    await weightInput.type('0.1') // Below minimum
    await page.click('h3') // Blur to trigger validation

    const minValue = await weightInput.inputValue()
    console.log(`${browserName} - Min boundary result: ${minValue}`)
    expect(parseFloat(minValue)).toBeGreaterThanOrEqual(0.5)

    // Test maximum boundary (kg unit = 10.0 max)
    await weightInput.click()
    await weightInput.clear()
    await weightInput.type('15') // Above maximum
    await page.click('h3')

    const maxValue = await weightInput.inputValue()
    console.log(`${browserName} - Max boundary result: ${maxValue}`)
    expect(parseFloat(maxValue)).toBeLessThanOrEqual(10.0)

    console.log(`${browserName}: Boundary validation PASSED`)
  })

  test('should sanitize malicious input (XSS prevention)', async ({
    page,
    browserName,
  }) => {
    console.log(`Testing XSS prevention on ${browserName}`)

    const weightInput = page.locator(
      'input[aria-label="Weight increment value"]'
    )

    await weightInput.click()
    await weightInput.clear()

    // Try malicious input
    const maliciousInput = '<script>alert("xss")</script>2.5'
    await weightInput.type(maliciousInput)

    const sanitizedValue = await weightInput.inputValue()
    console.log(`${browserName} - Sanitized value: "${sanitizedValue}"`)
    expect(sanitizedValue).toBe('2.5')
    expect(sanitizedValue).not.toContain('<script>')
    expect(sanitizedValue).not.toContain('alert')

    console.log(`${browserName}: XSS prevention PASSED`)
  })

  test('should handle rapid input changes without race conditions', async ({
    page,
    browserName,
  }) => {
    console.log(`Testing rapid input on ${browserName}`)

    const weightInput = page.locator(
      'input[aria-label="Weight increment value"]'
    )
    const incrementBtn = page.locator(
      'button[aria-label="Increase weight increment"]'
    )

    // Get initial value
    const initialValue = parseFloat(await weightInput.inputValue())

    // Rapid button clicks
    await incrementBtn.click()
    await incrementBtn.click()
    await incrementBtn.click()

    // Wait for state to settle
    await page.waitForTimeout(100)

    const finalValue = parseFloat(await weightInput.inputValue())
    console.log(
      `${browserName} - Initial: ${initialValue}, Final: ${finalValue}`
    )

    // Should increment by 1.5 (3 clicks × 0.5 kg step)
    expect(finalValue).toBe(initialValue + 1.5)

    console.log(`${browserName}: Rapid input PASSED`)
  })

  test('should handle backspace through decimal values', async ({
    page,
    browserName,
  }) => {
    console.log(`Testing backspace on ${browserName}`)

    const weightInput = page.locator(
      'input[aria-label="Weight increment value"]'
    )

    await weightInput.click()
    await weightInput.clear()
    await weightInput.type('3.5')

    // Position cursor after the "5" and backspace
    await weightInput.press('End')
    await weightInput.press('Backspace') // Remove "5"

    let value = await weightInput.inputValue()
    console.log(`${browserName} - After backspace "5": ${value}`)
    expect(value).toBe('3.')

    // Continue typing
    await weightInput.type('0')
    value = await weightInput.inputValue()
    console.log(`${browserName} - After typing "0": ${value}`)
    expect(value).toBe('3.0')

    console.log(`${browserName}: Backspace handling PASSED`)
  })
})

// Browser-specific behavior tests
test.describe('Browser-Specific Behavior Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.addInitScript(() => {
      localStorage.setItem('auth-token', 'mock-token')
    })
    await page.goto('/settings')
    await page.waitForLoadState('networkidle')
  })

  test('should detect mobile user agent correctly', async ({
    page,
    browserName,
  }) => {
    const userAgent = await page.evaluate(() => navigator.userAgent)
    console.log(`${browserName} User Agent: ${userAgent}`)

    // Log for manual verification
    const isMobile = /Mobile|Android|iPhone|iPad/.test(userAgent)
    console.log(`${browserName} Detected as mobile: ${isMobile}`)
  })

  test('should measure touch response time', async ({ page, browserName }) => {
    const incrementBtn = page.locator(
      'button[aria-label="Increase weight increment"]'
    )

    // Measure touch response
    const startTime = Date.now()
    await incrementBtn.click()
    const endTime = Date.now()

    const responseTime = endTime - startTime
    console.log(`${browserName} - Touch response time: ${responseTime}ms`)

    // Should be under 300ms (to detect iOS tap delay issues)
    expect(responseTime).toBeLessThan(300)
  })

  test('should verify number input type support', async ({
    page,
    browserName,
  }) => {
    const weightInput = page.locator(
      'input[aria-label="Weight increment value"]'
    )

    // Check input attributes
    const inputType = await weightInput.getAttribute('type')
    const inputMode = await weightInput.getAttribute('inputMode')
    const step = await weightInput.getAttribute('step')
    const min = await weightInput.getAttribute('min')
    const max = await weightInput.getAttribute('max')

    console.log(`${browserName} Input attributes:`)
    console.log(`  type: ${inputType}`)
    console.log(`  inputMode: ${inputMode}`)
    console.log(`  step: ${step}`)
    console.log(`  min: ${min}`)
    console.log(`  max: ${max}`)

    expect(inputType).toBe('number')
    expect(inputMode).toBe('decimal')
    expect(step).toBe('0.5')
    expect(min).toBe('0.5')
    expect(max).toBe('10')
  })
})
