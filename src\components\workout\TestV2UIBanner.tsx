import { useRouter } from 'next/navigation'
import { debugLog } from '@/utils/debugLog'
import type { ExerciseWorkSetsModel, WorkoutTemplateGroupModel } from '@/types'

interface TestV2UIBannerProps {
  displayExercises: ExerciseWorkSetsModel[]
  todaysWorkout: WorkoutTemplateGroupModel[] | null
  workoutSession: unknown
  startWorkoutFromHook: (workout: WorkoutTemplateGroupModel[]) => Promise<{
    success: boolean
    firstExerciseId?: number
  }>
  exercises: ExerciseWorkSetsModel[] | undefined
  loadExerciseRecommendation: (exerciseId: number) => Promise<unknown>
  isStartingWorkout: boolean
  isLoadingRecommendation: boolean
  setIsLoadingRecommendation: (loading: boolean) => void
}

export function TestV2UIBanner({
  displayExercises,
  todaysWorkout,
  workoutSession,
  startWorkoutFromHook,
  exercises,
  loadExerciseRecommendation,
  isStartingWorkout,
  isLoadingRecommendation,
  setIsLoadingRecommendation,
}: TestV2UIBannerProps) {
  const router = useRouter()

  if (displayExercises.length === 0) {
    return null
  }

  const handleTestV2Click = async () => {
    // Prevent multiple clicks
    if (isStartingWorkout || isLoadingRecommendation) return

    // Start workout if not already started
    if (todaysWorkout && !workoutSession) {
      const result = await startWorkoutFromHook(todaysWorkout)

      if (!result.success || !result.firstExerciseId) {
        return
      }

      // Use the firstExerciseId from the result directly
      const exerciseId = result.firstExerciseId

      // Find the exercise to get its name from the current workout data
      const firstWorkout = todaysWorkout[0]?.WorkoutTemplates?.[0]
      const exercise = firstWorkout?.Exercises?.find(
        (ex) => ex.Id === exerciseId
      )
      const exerciseName = exercise?.Label || ''

      // Pre-load recommendation before navigation (same as Start Workout button)
      try {
        await loadExerciseRecommendation(exerciseId)
        debugLog.log(`Pre-loaded recommendation for exercise ${exerciseId}`)
      } catch (error) {
        // Continue with navigation even if recommendation fails
        debugLog.warn(
          'Failed to pre-load recommendation, continuing to exercise page:',
          error
        )
      }

      const v2Url = exerciseName
        ? `/workout/exercise-v2/${exerciseId}?exerciseName=${encodeURIComponent(exerciseName)}`
        : `/workout/exercise-v2/${exerciseId}`
      router.push(v2Url)
    } else if (workoutSession) {
      // Workout already started, find first incomplete exercise
      const firstIncompleteExercise = displayExercises.find(
        (ex) => !ex.IsFinished
      )
      const exerciseToOpen = firstIncompleteExercise || displayExercises[0]

      if (exerciseToOpen) {
        // Preload recommendation before navigation (following handleExerciseClick pattern)
        const exercise = exercises?.find((ex) => ex.Id === exerciseToOpen.Id)
        if (exercise && (!exercise.sets || exercise.sets.length === 0)) {
          try {
            setIsLoadingRecommendation(true)
            // Try to load recommendation before navigation
            await loadExerciseRecommendation(exerciseToOpen.Id)
            debugLog.log(
              `Pre-loaded recommendation for exercise ${exerciseToOpen.Id}`
            )
          } catch (error) {
            // Continue with navigation even if recommendation fails
            debugLog.warn(
              'Failed to pre-load recommendation, continuing to exercise page:',
              error
            )
          } finally {
            setIsLoadingRecommendation(false)
          }
        }

        const exerciseName = exerciseToOpen.Label || ''
        const v2Url = exerciseName
          ? `/workout/exercise-v2/${exerciseToOpen.Id}?exerciseName=${encodeURIComponent(exerciseName)}`
          : `/workout/exercise-v2/${exerciseToOpen.Id}`
        router.push(v2Url)
      }
    }
  }

  return (
    <div className="mb-4 p-3 bg-brand-primary/10 border border-brand-primary/20 rounded-lg">
      <p className="text-sm text-brand-primary font-medium mb-2">
        🚀 Try our new UI
      </p>
      <button
        onClick={handleTestV2Click}
        className="text-sm text-brand-primary underline hover:no-underline disabled:opacity-50 disabled:cursor-not-allowed"
        disabled={isStartingWorkout || isLoadingRecommendation}
      >
        {isStartingWorkout || isLoadingRecommendation
          ? 'Loading...'
          : 'Start with new exercise view →'}
      </button>
    </div>
  )
}
