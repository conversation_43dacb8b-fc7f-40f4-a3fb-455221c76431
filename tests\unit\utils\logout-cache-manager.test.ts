import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { performCompleteLogout } from '@/utils/logout'
import * as cacheModule from '@/cache'

// Mock the cache module
vi.mock('@/cache', () => ({
  clearAllCacheData: vi.fn(() =>
    Promise.resolve({
      localStorage: true,
      sessionStorage: true,
      memory: true,
    })
  ),
  getGlobalCacheManager: vi.fn(() => ({
    clear: vi.fn(() => Promise.resolve()),
    destroy: vi.fn(),
  })),
}))

// Mock queryClient
vi.mock('@/utils/queryClient', () => ({
  queryClient: {
    cancelQueries: vi.fn(() => Promise.resolve()),
    clear: vi.fn(),
    removeQueries: vi.fn(),
  },
}))

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
  writable: true,
})

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn(),
}
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
  writable: true,
})

// Mock caches API
const cachesMock = {
  keys: vi.fn(() => Promise.resolve([])),
  delete: vi.fn(() => Promise.resolve(true)),
}
Object.defineProperty(window, 'caches', {
  value: cachesMock,
  writable: true,
  configurable: true,
})

// Mock indexedDB
const indexedDBMock = {
  databases: vi.fn(() => Promise.resolve([])),
  deleteDatabase: vi.fn(() => Promise.resolve(undefined)),
}
Object.defineProperty(window, 'indexedDB', {
  value: indexedDBMock,
  writable: true,
})

// Mock console
vi.spyOn(console, 'log').mockImplementation(() => {})
vi.spyOn(console, 'error').mockImplementation(() => {})
vi.spyOn(console, 'warn').mockImplementation(() => {})

// Mock window.location.replace
const mockLocationReplace = vi.fn()
Object.defineProperty(window, 'location', {
  value: {
    replace: mockLocationReplace,
    href: 'http://localhost:3000',
    pathname: '/',
  },
  writable: true,
})

describe('performCompleteLogout - CacheManager Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockLocationReplace.mockClear()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('should clear the unified CacheManager during logout', async () => {
    // Test will fail initially because performCompleteLogout doesn't call clearAllCacheData
    await performCompleteLogout()

    // Verify that the unified cache system is cleared
    expect(cacheModule.clearAllCacheData).toHaveBeenCalledTimes(1)
  })

  it('should clear CacheManager before clearing localStorage', async () => {
    const callOrder: string[] = []

    // Track call order
    vi.mocked(cacheModule.clearAllCacheData).mockImplementation(async () => {
      callOrder.push('clearAllCacheData')
      return {
        localStorage: true,
        sessionStorage: true,
        memory: true,
      }
    })

    localStorageMock.removeItem.mockImplementation(() => {
      callOrder.push('localStorage.removeItem')
    })

    await performCompleteLogout()

    // CacheManager should be cleared before localStorage
    const cacheIndex = callOrder.indexOf('clearAllCacheData')
    const localStorageIndex = callOrder.indexOf('localStorage.removeItem')

    expect(cacheIndex).toBeGreaterThanOrEqual(0)
    expect(localStorageIndex).toBeGreaterThanOrEqual(0)
    expect(cacheIndex).toBeLessThan(localStorageIndex)
  })

  it('should handle CacheManager clearing errors gracefully', async () => {
    // Make clearAllCacheData reject
    vi.mocked(cacheModule.clearAllCacheData).mockRejectedValue(
      new Error('Cache clearing failed')
    )

    // Should not throw even if cache clearing fails
    await expect(performCompleteLogout()).resolves.not.toThrow()

    // Should still attempt to clear other storage
    expect(localStorageMock.removeItem).toHaveBeenCalled()
    expect(sessionStorageMock.clear).toHaveBeenCalled()
  })

  it('should complete logout even if CacheManager is unavailable', async () => {
    // Simulate cache module not being available
    vi.mocked(cacheModule.clearAllCacheData).mockImplementation(() => {
      throw new Error('Module not available')
    })

    await expect(performCompleteLogout()).resolves.not.toThrow()

    // Other clearing operations should still happen
    expect(localStorageMock.removeItem).toHaveBeenCalled()
    expect(sessionStorageMock.clear).toHaveBeenCalled()
  })

  it('should perform hard reload to /login after clearing all caches', async () => {
    await performCompleteLogout()

    // Should clear all caches first
    expect(cacheModule.clearAllCacheData).toHaveBeenCalled()
    expect(localStorageMock.removeItem).toHaveBeenCalled()
    expect(sessionStorageMock.clear).toHaveBeenCalled()

    // Then perform hard reload to login page
    expect(mockLocationReplace).toHaveBeenCalledTimes(1)
    expect(mockLocationReplace).toHaveBeenCalledWith('/login')
  })

  it('should redirect to login even if cache clearing fails', async () => {
    // Make various operations fail
    vi.mocked(cacheModule.clearAllCacheData).mockRejectedValue(
      new Error('Cache clearing failed')
    )
    localStorageMock.removeItem.mockImplementation(() => {
      throw new Error('localStorage error')
    })

    await performCompleteLogout()

    // Should still redirect to login
    expect(mockLocationReplace).toHaveBeenCalledWith('/login')
  })
})
