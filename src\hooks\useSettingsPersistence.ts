'use client'

import { useState, useCallback, useEffect, useRef } from 'react'
import { updateUserSettings } from '@/services/updateUserSettings'
import { useSettingsData, SettingsData } from '@/hooks/useSettingsData'
import { useAuthStore } from '@/stores/authStore'
import { useDebounce } from '@/hooks/useDebounce'
import { toast } from '@/components/ui/toast'
import type {
  LocalSettings,
  UseSettingsPersistenceReturn,
} from '@/types/settings'

const LOCALSTORAGE_KEY = 'drMuscle_pendingSettings'

/**
 * Validates and sanitizes settings object to prevent XSS and data corruption
 */
function validateAndSanitizeSettings(settings: LocalSettings): LocalSettings {
  return {
    quickMode: Boolean(settings.quickMode),
    weightUnit: ['lbs', 'kg'].includes(settings.weightUnit)
      ? settings.weightUnit
      : 'lbs',
    setStyle: [
      'Normal',
      'Rest-pause',
      'Drop',
      'Pyramid',
      'Reverse pyramid',
    ].includes(settings.setStyle)
      ? settings.setStyle
      : 'Normal',
    repsMin: Math.max(1, Math.min(100, Number(settings.repsMin) || 6)),
    repsMax: Math.max(1, Math.min(100, Number(settings.repsMax) || 12)),
    weightIncrement: Math.max(
      0.5,
      Math.min(50, Number(settings.weightIncrement) || 5)
    ),
    warmupSets: Math.max(0, Math.min(10, Number(settings.warmupSets) || 0)),
  }
}

/**
 * Save pending settings to localStorage with security validation
 */
function savePendingSettingsToStorage(settings: LocalSettings | null) {
  try {
    if (settings) {
      // Security: Validate and sanitize input before storage
      const sanitizedSettings = validateAndSanitizeSettings(settings)
      localStorage.setItem(LOCALSTORAGE_KEY, JSON.stringify(sanitizedSettings))
    } else {
      localStorage.removeItem(LOCALSTORAGE_KEY)
    }
  } catch (error) {
    console.warn('Failed to save settings to localStorage:', error)
  }
}

/**
 * Load pending settings from localStorage with validation
 */
function loadPendingSettingsFromStorage(): LocalSettings | null {
  try {
    const stored = localStorage.getItem(LOCALSTORAGE_KEY)
    if (!stored) return null

    const parsed = JSON.parse(stored)
    // Security: Validate loaded data to prevent corrupted/malicious data
    return validateAndSanitizeSettings(parsed)
  } catch (error) {
    console.warn('Failed to load settings from localStorage:', error)
    return null
  }
}

/**
 * Maps SettingsData to LocalSettings format for consistency
 */
function mapSettingsDataToLocalSettings(data: SettingsData): LocalSettings {
  return {
    quickMode: data.quickMode,
    weightUnit: data.weightUnit,
    setStyle: data.setStyle,
    repsMin: data.repRange.min,
    repsMax: data.repRange.max,
    weightIncrement: data.weightIncrement,
    warmupSets: data.warmupSets,
  }
}

/**
 * Hook for managing settings persistence with server synchronization.
 * Provides local state management and save functionality.
 */
const DEBOUNCE_DELAY = 3000 // 3 seconds - batch multiple changes together
const MAX_RETRY_ATTEMPTS = 5
const INITIAL_RETRY_DELAY = 1000 // 1 second

export function useSettingsPersistence(): UseSettingsPersistenceReturn {
  const { data: serverSettings } = useSettingsData()
  const { clearUserInfoCache } = useAuthStore()

  // Local state for settings modifications
  const [localSettings, setLocalSettings] = useState<LocalSettings | null>(
    () => {
      // Initialize from localStorage if available
      return loadPendingSettingsFromStorage()
    }
  )
  const [isSaving, setIsSaving] = useState(false)
  const [saveError, setSaveError] = useState<string | null>(null)

  // Auto-save state
  const retryCountRef = useRef(0)
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const isMountedRef = useRef(true)
  const saveInProgressRef = useRef(false)

  // Initialize local settings from server data
  const currentSettings =
    localSettings ||
    (serverSettings ? mapSettingsDataToLocalSettings(serverSettings) : null)

  // Check if there are unsaved changes
  const hasChanges = Boolean(
    localSettings &&
      serverSettings &&
      JSON.stringify(localSettings) !==
        JSON.stringify(mapSettingsDataToLocalSettings(serverSettings))
  )

  // Debounce settings for auto-save
  const debouncedSettings = useDebounce(localSettings, DEBOUNCE_DELAY)

  // Update a single setting
  const updateSetting = useCallback<
    UseSettingsPersistenceReturn['updateSetting']
  >(
    (key, value) => {
      // Use functional update to get latest state for sequential calls
      setLocalSettings((prevLocalSettings) => {
        // Get the current effective settings
        const baseSettings =
          prevLocalSettings ||
          (serverSettings
            ? mapSettingsDataToLocalSettings(serverSettings)
            : null)

        if (!baseSettings) return prevLocalSettings

        const updatedSettings = {
          ...baseSettings,
          [key]: value,
        }

        // Auto-convert weight increment when unit changes
        if (key === 'weightUnit' && value !== baseSettings.weightUnit) {
          const conversionFactor = value === 'kg' ? 0.5 : 2
          updatedSettings.weightIncrement =
            baseSettings.weightIncrement * conversionFactor
        }

        // Validate rep range and set error state accordingly
        if (
          (key === 'repsMin' || key === 'repsMax') &&
          updatedSettings.repsMin >= updatedSettings.repsMax
        ) {
          setSaveError('Minimum reps cannot be greater than maximum reps')
        } else {
          setSaveError(null) // Clear any previous errors
        }

        // IMMEDIATE SAVE TO LOCALSTORAGE - Prevents race condition with navigation
        // This saves immediately while the debounced auto-save handles server sync
        savePendingSettingsToStorage(updatedSettings)

        return updatedSettings
      })

      retryCountRef.current = 0 // Reset retry count on new changes
    },
    [serverSettings]
  )

  // Auto-save function with retry logic
  const performAutoSave = useCallback(
    async (settings: LocalSettings) => {
      if (!isMountedRef.current || saveInProgressRef.current) return

      // RACE CONDITION FIX: Check for existing validation errors
      // This prevents auto-save when validation errors exist, maintaining the same
      // blocking behavior as before while preventing effect dependency loops
      if (saveError) {
        return // Block auto-save when validation errors exist
      }

      // Validate settings before save
      if (settings.repsMin > settings.repsMax) {
        setSaveError(
          'Settings validation failed: Minimum reps cannot be greater than maximum reps'
        )
        return
      }

      saveInProgressRef.current = true
      setIsSaving(true)
      setSaveError(null)

      try {
        await updateUserSettings(settings)

        // Success
        if (isMountedRef.current) {
          clearUserInfoCache()
          setLocalSettings(null)
          savePendingSettingsToStorage(null) // Clear localStorage on successful save
          setIsSaving(false)
          saveInProgressRef.current = false
          retryCountRef.current = 0

          // Show success toast only if this was an actual server save
          // (not shown during initial loads or when API isn't available)
          if (retryCountRef.current > 0 || hasChanges) {
            toast({
              description: 'Settings saved',
              variant: 'subtle',
              duration: 2000,
            })
          }
        }
      } catch (error) {
        if (!isMountedRef.current) return

        setIsSaving(false)
        saveInProgressRef.current = false

        // Extract error message
        const errorMessage =
          error instanceof Error ? error.message : 'Failed to save settings'

        // Check for auth errors (don't retry)
        const errorWithResponse = error as { response?: { status?: number } }
        if (errorWithResponse?.response?.status === 401) {
          setSaveError('Authentication failed')
          toast({
            description: 'Please log in again to save settings',
            variant: 'error',
            duration: 7000,
          })
          return
        }

        // Set error message for display
        setSaveError(errorMessage)

        // Handle other errors with retry
        if (retryCountRef.current < MAX_RETRY_ATTEMPTS - 1) {
          retryCountRef.current++
          const retryDelay =
            INITIAL_RETRY_DELAY * Math.pow(2, retryCountRef.current - 1)

          // Silent retry - no toast notification during retries to avoid spam
          // Users can continue making changes while retries happen in background

          // Schedule retry with exponential backoff
          retryTimeoutRef.current = setTimeout(() => {
            if (isMountedRef.current) {
              performAutoSave(settings)
            }
          }, retryDelay)
        } else {
          // Max retries reached - only show error after all retries exhausted
          toast({
            description:
              'Settings saved locally but could not sync to server. They will sync when connection is restored.',
            variant: 'subtle',
            duration: 5000,
          })
          retryCountRef.current = 0
        }
      }
    },
    [clearUserInfoCache, saveError, hasChanges]
  )

  // Save all changes to server (manual save for compatibility)
  const saveSettings = useCallback(async () => {
    // Check if authenticated (has server settings)
    if (!serverSettings) {
      setSaveError('Authentication required to save settings')
      return
    }

    if (!localSettings) {
      setSaveError('No changes to save')
      return
    }

    await performAutoSave(localSettings)
  }, [localSettings, serverSettings, performAutoSave])

  // Reset changes to server values
  const resetChanges = useCallback(() => {
    setLocalSettings(null)
    savePendingSettingsToStorage(null) // Clear localStorage on reset
    setSaveError(null)
    retryCountRef.current = 0

    // Clear any pending retry timeouts
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current)
      retryTimeoutRef.current = null
    }
  }, [])

  // Auto-save effect
  useEffect(() => {
    // Skip if no changes or already saving
    if (!debouncedSettings || !hasChanges || isSaving) return

    // RACE CONDITION FIX: Removed saveError from dependencies
    // Validation check moved inside performAutoSave to prevent infinite loops
    // when setSaveError(null) triggers effect re-run
    performAutoSave(debouncedSettings)
  }, [debouncedSettings, hasChanges, isSaving, performAutoSave])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false

      // Clear retry timeout
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current)
        retryTimeoutRef.current = null
      }
    }
  }, [])

  return {
    localSettings: currentSettings || {
      quickMode: false,
      weightUnit: 'lbs',
      setStyle: 'Normal',
      repsMin: 6,
      repsMax: 12,
      weightIncrement: 5,
      warmupSets: 0,
    },
    hasChanges,
    isSaving,
    saveError,
    updateSetting,
    saveSettings,
    resetChanges,
  }
}
