import { test, expect } from '@playwright/test'

test.describe('Settings Page', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication to access settings page
    await page.addInitScript(() => {
      localStorage.setItem('auth-token', 'mock-token')
    })
    await page.goto('/settings')
  })

  test('should display card layout with all settings', async ({ page }) => {
    // Check card layout container exists
    await expect(
      page.locator('[data-testid="card-layout-container"]')
    ).toBeVisible()

    // Check all setting cards are present
    const cards = page.locator('.bg-surface-primary')
    const cardCount = await cards.count()

    // Should have 6 setting cards (set style, quick mode, weight unit, rep range, weight increment, warmup sets)
    expect(cardCount).toBeGreaterThanOrEqual(6)
  })

  test('should show save/reset buttons when settings change', async ({
    page,
  }) => {
    // Initially no save buttons should be visible
    await expect(
      page.locator('button:has-text("Save Changes")')
    ).not.toBeVisible()

    // Make a change (toggle quick mode)
    const quickModeToggle = page
      .locator('[role="switch"][aria-label="Quick Mode"]')
      .first()
    await quickModeToggle.click()

    // Save/reset buttons should appear
    await expect(page.locator('button:has-text("Save Changes")')).toBeVisible()
    await expect(page.locator('button:has-text("Reset Changes")')).toBeVisible()
  })

  test('should have proper touch targets for mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })

    // Check minimum touch target sizes
    const buttons = await page.locator('button').all()

    // Check each button has minimum height of 52px
    await Promise.all(
      buttons.map(async (button) => {
        const box = await button.boundingBox()
        if (box) {
          expect(box.height).toBeGreaterThanOrEqual(52)
        }
      })
    )
  })

  test('should display interactive controls in card layout', async ({
    page,
  }) => {
    // Check that interactive controls are present (not just static text)
    await expect(
      page.locator('[role="switch"][aria-label="Quick Mode"]')
    ).toBeVisible()
    await expect(page.locator('select').first()).toBeVisible() // Set style dropdown

    // Check weight unit toggle
    const weightUnitButtons = page.locator(
      'button:has-text("kg"), button:has-text("lbs")'
    )
    const weightUnitCount = await weightUnitButtons.count()
    expect(weightUnitCount).toBeGreaterThan(0)

    // Check rep range inputs
    await expect(page.locator('input[type="number"]').first()).toBeVisible()

    // Check warmup sets selector
    const warmupButtons = page.locator('button[aria-label*="warmup sets"]')
    const warmupCount = await warmupButtons.count()
    expect(warmupCount).toBeGreaterThan(0)
  })

  test('should save settings and show success message', async ({ page }) => {
    // Make a change
    const quickModeToggle = page
      .locator('[role="switch"][aria-label="Quick Mode"]')
      .first()
    await quickModeToggle.click()

    // Click save
    await page.locator('button:has-text("Save Changes")').click()

    // Should show success message
    await expect(page.locator('text=Settings saved successfully')).toBeVisible()

    // Save/reset buttons should disappear after successful save
    await expect(
      page.locator('button:has-text("Save Changes")')
    ).not.toBeVisible()
  })

  test('should reset changes when reset button clicked', async ({ page }) => {
    // Get initial state of quick mode toggle
    const quickModeToggle = page
      .locator('[role="switch"][aria-label="Quick Mode"]')
      .first()
    const initialState = await quickModeToggle.getAttribute('aria-checked')

    // Make a change
    await quickModeToggle.click()

    // Verify state changed
    const changedState = await quickModeToggle.getAttribute('aria-checked')
    expect(changedState).not.toBe(initialState)

    // Click reset
    await page.locator('button:has-text("Reset Changes")').click()

    // Verify state reverted
    const resetState = await quickModeToggle.getAttribute('aria-checked')
    expect(resetState).toBe(initialState)

    // Save/reset buttons should disappear
    await expect(
      page.locator('button:has-text("Save Changes")')
    ).not.toBeVisible()
  })

  test('should be accessible from user menu', async ({ page }) => {
    // Navigate to any other page first
    await page.goto('/program')

    // Open user menu
    await page.locator('[aria-label="Open user menu"]').click()

    // Click settings link
    await page.locator('button:has-text("Settings")').click()

    // Should navigate to settings page
    await expect(page).toHaveURL('/settings')
    await expect(
      page.locator('[data-testid="card-layout-container"]')
    ).toBeVisible()
  })
})
