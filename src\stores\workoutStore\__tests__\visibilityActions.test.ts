/**
 * Tests for visibility actions - background refresh functionality
 *
 * Test Rationale:
 * - Verify handleAppForeground refreshes ALL prefetched exercises, not just current one
 * - Ensure no duplicate API calls for current exercise
 * - Test error handling during background refresh
 * - Verify empty prefetch status is handled gracefully
 * - Test debouncing of rapid visibility changes
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { createVisibilityActions } from '../visibilityActions'
import { logger } from '@/utils/logger'

// Mock logger to avoid console noise in tests
vi.mock('@/utils/logger', () => ({
  logger: {
    log: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  },
}))

describe('visibilityActions - Background Refresh', () => {
  let store: any
  let mockLoadExerciseRecommendation: any
  let mockSet: any
  let mockGet: any

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks()

    // Mock loadExerciseRecommendation function
    mockLoadExerciseRecommendation = vi.fn().mockResolvedValue({
      Id: 1,
      WeightIncrement: 5,
      RepsIncrement: 1,
      Weight: { Kg: 100, Lb: 220 },
      Reps: 8,
      RIR: 2,
    })

    // Mock refreshAllPrefetchedExercises function
    const mockRefreshAllPrefetchedExercises = vi
      .fn()
      .mockResolvedValue(undefined)

    // Mock state and functions
    const initialState = {
      workoutSession: {
        id: 'session-123',
        startTime: new Date(),
        exercises: [],
        exerciseRIRStatus: {},
      },
      currentWorkout: {
        Id: 123,
        Name: 'Test Workout',
        Exercises: [
          {
            Id: 1,
            Name: 'Bench Press',
            IsFlexibility: false,
            SetStyle: 'Normal',
          },
          { Id: 2, Name: 'Squats', IsFlexibility: false, SetStyle: 'Pyramid' },
          { Id: 3, Name: 'Deadlifts', IsFlexibility: false, SetStyle: 'Drop' },
          { Id: 4, Name: 'Rows', IsFlexibility: false, SetStyle: 'Normal' },
          { Id: 5, Name: 'Curls', IsFlexibility: false, SetStyle: 'Normal' },
        ],
      },
      exercises: [
        {
          Id: 1,
          Name: 'Bench Press',
          IsFlexibility: false,
          SetStyle: 'Normal',
        },
        { Id: 2, Name: 'Squats', IsFlexibility: false, SetStyle: 'Pyramid' },
        { Id: 3, Name: 'Deadlifts', IsFlexibility: false, SetStyle: 'Drop' },
        { Id: 4, Name: 'Rows', IsFlexibility: false, SetStyle: 'Normal' },
        { Id: 5, Name: 'Curls', IsFlexibility: false, SetStyle: 'Normal' },
      ],
      currentExerciseIndex: 1, // Currently on exercise ID 2 (Squats)
      prefetchedExerciseIds: [1, 2, 3], // Exercises 1, 2, 3 are prefetched
      prefetchStatus: {
        1: 'success' as const,
        2: 'success' as const,
        3: 'success' as const,
      },
      restTimerState: {
        isActive: false,
        duration: 0,
        pausedAt: undefined,
      },
      loadingStates: new Map(),
      errors: new Map(),
      error: null,
      loadExerciseRecommendation: mockLoadExerciseRecommendation,
      refreshAllPrefetchedExercises: mockRefreshAllPrefetchedExercises,
    }

    // Mock set and get functions
    let state = { ...initialState }
    mockSet = vi.fn((updater) => {
      if (typeof updater === 'function') {
        state = { ...state, ...updater(state) }
      } else {
        state = { ...state, ...updater }
      }
    })
    mockGet = vi.fn(() => state)

    // Create visibility actions
    const visibilityActions = createVisibilityActions(mockSet, mockGet)
    store = visibilityActions
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('handleAppForeground - Background Refresh', () => {
    it('should refresh all prefetched exercises when returning from background', async () => {
      // Given: User has workout with 5 exercises, 3 prefetched (1, 2, 3)
      // Current exercise is index 1 (exercise ID 2)

      // When: App returns from background
      await store.handleAppForeground()

      // Then: Should refresh all prefetched exercises (1, 2, 3)
      expect(mockLoadExerciseRecommendation).toHaveBeenCalledTimes(3)
      expect(mockLoadExerciseRecommendation).toHaveBeenCalledWith(1)
      expect(mockLoadExerciseRecommendation).toHaveBeenCalledWith(2)
      expect(mockLoadExerciseRecommendation).toHaveBeenCalledWith(3)

      // Should NOT call for non-prefetched exercises (4, 5)
      expect(mockLoadExerciseRecommendation).not.toHaveBeenCalledWith(4)
      expect(mockLoadExerciseRecommendation).not.toHaveBeenCalledWith(5)
    })

    it('should handle empty prefetched exercises gracefully', async () => {
      // Given: No prefetched exercises
      mockGet.mockReturnValue({
        ...mockGet(),
        prefetchedExerciseIds: [],
        prefetchStatus: {},
        currentWorkout: null,
        workoutSession: null,
      })

      // When: App returns from background
      await store.handleAppForeground()

      // Then: Should not make any recommendation calls
      expect(mockLoadExerciseRecommendation).not.toHaveBeenCalled()
    })

    it('should handle refresh errors gracefully without throwing', async () => {
      // Given: One exercise will fail to refresh
      mockLoadExerciseRecommendation
        .mockResolvedValueOnce({ Id: 1 }) // Exercise 1 succeeds
        .mockRejectedValueOnce(new Error('Network timeout')) // Exercise 2 fails
        .mockResolvedValueOnce({ Id: 3 }) // Exercise 3 succeeds

      // When: Background refresh runs
      await expect(store.handleAppForeground()).resolves.not.toThrow()

      // Then: Should attempt to refresh all prefetched exercises
      expect(mockLoadExerciseRecommendation).toHaveBeenCalledTimes(3)
      expect(mockLoadExerciseRecommendation).toHaveBeenCalledWith(1)
      expect(mockLoadExerciseRecommendation).toHaveBeenCalledWith(2)
      expect(mockLoadExerciseRecommendation).toHaveBeenCalledWith(3)
    })

    it('should not duplicate current exercise refresh', async () => {
      // Given: Current exercise (ID 2) is also in prefetched list
      // This tests that we don't call loadExerciseRecommendation twice for the same exercise

      // When: App returns from background
      await store.handleAppForeground()

      // Then: Should only call each exercise once, even if current exercise is prefetched
      const callCounts = {
        1: 0,
        2: 0,
        3: 0,
      }

      mockLoadExerciseRecommendation.mock.calls.forEach(([exerciseId]) => {
        callCounts[exerciseId] = (callCounts[exerciseId] || 0) + 1
      })

      expect(callCounts[1]).toBe(1) // Called once
      expect(callCounts[2]).toBe(1) // Called once (not duplicated)
      expect(callCounts[3]).toBe(1) // Called once
    })

    it('should skip background refresh when no workout session exists', async () => {
      // Given: No active workout session
      mockGet.mockReturnValue({
        ...mockGet(),
        workoutSession: null,
        currentWorkout: null,
      })

      // When: App returns from background
      await store.handleAppForeground()

      // Then: Should not attempt background refresh
      expect(mockLoadExerciseRecommendation).not.toHaveBeenCalled()
    })

    it('should log appropriate messages during background refresh', async () => {
      // Given: Normal state with prefetched exercises

      // When: App returns from background
      await store.handleAppForeground()

      // Then: Should log foreground return
      expect(logger.log).toHaveBeenCalledWith(
        '[WorkoutStore] App returning from background, restoring states'
      )
    })
  })

  describe('Current Exercise Refresh (Existing Functionality)', () => {
    it('should still refresh current exercise when returning from background', async () => {
      // Given: Current exercise index 1 (exercise ID 2) with active workout

      // When: App returns from background
      await store.handleAppForeground()

      // Then: Current exercise should be refreshed (as part of prefetched or separately)
      expect(mockLoadExerciseRecommendation).toHaveBeenCalledWith(2)
    })

    it('should handle rest timer resumption (existing functionality)', async () => {
      // Given: Rest timer was paused when app went to background
      const pausedAt = Date.now() - 5000 // 5 seconds ago
      mockGet.mockReturnValue({
        ...mockGet(),
        restTimerState: {
          isActive: false,
          duration: 60, // 60 seconds original
          pausedAt,
        },
      })

      // When: App returns from background
      await store.handleAppForeground()

      // Then: Rest timer should be resumed with adjusted duration
      expect(mockSet).toHaveBeenCalledWith(
        expect.objectContaining({
          restTimerState: expect.objectContaining({
            isActive: true,
            duration: expect.any(Number),
            pausedAt: undefined,
          }),
        })
      )
    })
  })

  describe('Error Scenarios', () => {
    it('should handle workout session age check (existing functionality)', async () => {
      // Given: Very old workout session (over 12 hours)
      const oldStartTime = new Date(Date.now() - 13 * 60 * 60 * 1000) // 13 hours ago
      mockGet.mockReturnValue({
        ...mockGet(),
        workoutSession: {
          id: 'old-session',
          startTime: oldStartTime,
          exercises: [],
          exerciseRIRStatus: {},
        },
      })

      // When: App returns from background
      await store.handleAppForeground()

      // Then: Should set warning about old session
      expect(mockSet).toHaveBeenCalledWith(
        expect.objectContaining({
          error: expect.stringContaining('over 12 hours'),
        })
      )
    })
  })
})
