import { useState, useCallback, useRef } from 'react'
import { SetLoader } from '@/api/setLoader'
import { useRobustPrefetchErrorHandling } from './useRobustPrefetchErrorHandling'
import { usePrefetchMemoryOptimization } from './usePrefetchMemoryOptimization'
import { useWorkoutStore } from '@/stores/workoutStore'

type PrefetchStatus = 'loading' | 'success' | 'error'

interface PrefetchState {
  [exerciseId: number]: PrefetchStatus
}

interface CacheEntry {
  exerciseId: number
  sets: unknown[]
  timestamp: number
  accessCount?: number
  lastAccessed?: number
}

interface UseExerciseSetsPrefetchReturn {
  prefetchStatus: PrefetchState
  isPrefetching: boolean
  prefetchedExerciseIds: number[]
  prefetchExerciseSets: (exerciseIds: number[]) => Promise<void>
  isExercisePrefetched: (exerciseId: number) => boolean
  clearPrefetchCache: () => void
  onProgress?: (progress: number) => void
  retryCount: number
  errorHistory: Array<{
    timestamp: number
    error: Error
    exerciseIds: number[]
    retryAttempt: number
  }>
  hasExceededMaxRetries: boolean
  isCircuitBreakerOpen: boolean
  // Memory optimization properties
  cacheSize: number
  memoryUsage: number
  isMemoryPressure: boolean
  shouldReducePrefetching: boolean
  recommendedBatchSize: number
}

export function useExerciseSetsPrefetch(): UseExerciseSetsPrefetchReturn {
  // Use the store as the single source of truth for prefetch status
  const prefetchStatus = useWorkoutStore((state) => state.prefetchStatus)
  const prefetchedExerciseIds = useWorkoutStore(
    (state) => state.prefetchedExerciseIds
  )
  const setPrefetchStatus = useWorkoutStore((state) => state.setPrefetchStatus)
  const setPrefetchedExerciseIds = useWorkoutStore(
    (state) => state.setPrefetchedExerciseIds
  )
  const isExercisePrefetchedStore = useWorkoutStore(
    (state) => state.isExercisePrefetched
  )

  // Local state only for hook-specific concerns (loading state, progress tracking)
  const [isPrefetching, setIsPrefetching] = useState(false)

  const setLoader = useRef(new SetLoader())
  const prefetchPromiseRef = useRef<Promise<void> | null>(null)
  const onProgressRef = useRef<((progress: number) => void) | undefined>(
    undefined
  )

  // Cache for prefetched data with memory optimization
  const prefetchCacheRef = useRef<Map<number, CacheEntry>>(new Map())

  // Memory optimization
  const {
    cacheSize,
    memoryUsage,
    isMemoryPressure,
    shouldReducePrefetching,
    recommendedBatchSize,
    recordCacheHit,
    recordCacheMiss,
    performMemoryCleanup,
  } = usePrefetchMemoryOptimization({
    prefetchedData: prefetchCacheRef.current,
    clearCache: () => {
      prefetchCacheRef.current.clear()
      // Clear store state when clearing cache
      setPrefetchStatus({})
      setPrefetchedExerciseIds([])
    },
    maxCacheSize: 50,
    maxCacheAge: 300000, // 5 minutes
    memoryThreshold: 100, // 100MB
    autoCleanupInterval: 60000, // 1 minute
  })

  // Core prefetch function without error handling
  const basePrefetchExerciseSets = useCallback(
    async (exerciseIds: number[]): Promise<void> => {
      // Check cache first and filter out already cached exercises
      const exercisesToFetch = exerciseIds.filter((id) => {
        const cached = prefetchCacheRef.current.get(id)
        if (cached) {
          // Update access info
          cached.lastAccessed = Date.now()
          cached.accessCount = (cached.accessCount || 0) + 1
          recordCacheHit()
          return false // Don't need to fetch
        }
        recordCacheMiss()
        return true // Need to fetch
      })

      // If all exercises are cached, no need to fetch
      if (exercisesToFetch.length === 0) {
        return
      }

      const results =
        await setLoader.current.batchLoadExerciseSets(exercisesToFetch)

      // Update store state based on results
      const newStatus: Record<number, PrefetchStatus> = {}
      Object.entries(results).forEach(([idStr, result]) => {
        const id = parseInt(idStr)
        newStatus[id] = result.success ? 'success' : 'error'

        // Add to cache if successful
        if (result.success) {
          prefetchCacheRef.current.set(id, {
            exerciseId: id,
            sets: (result as { data?: unknown[] }).data || [],
            timestamp: Date.now(),
            accessCount: 1,
            lastAccessed: Date.now(),
          })
        }
      })

      // Update store with new statuses
      setPrefetchStatus({ ...prefetchStatus, ...newStatus })

      // Update successful IDs in store
      const successfulIds = Object.entries(results)
        .filter(([, result]) => result.success)
        .map(([idStr]) => parseInt(idStr))

      if (successfulIds.length > 0) {
        const newIds = [
          ...new Set([...prefetchedExerciseIds, ...successfulIds]),
        ]
        setPrefetchedExerciseIds(newIds)
      }

      // Call progress callback if set
      if (onProgressRef.current) {
        onProgressRef.current(1)
      }
    },
    [
      recordCacheHit,
      recordCacheMiss,
      setPrefetchStatus,
      setPrefetchedExerciseIds,
      prefetchStatus,
      prefetchedExerciseIds,
    ]
  )

  // Wrap with error handling
  const {
    prefetchWithRetry,
    retryCount,
    errorHistory,
    hasExceededMaxRetries,
    isCircuitBreakerOpen,
  } = useRobustPrefetchErrorHandling({
    prefetchExerciseSets: basePrefetchExerciseSets,
    maxRetries: 3,
    retryDelayMs: 1000,
    exponentialBackoff: true,
    circuitBreakerThreshold: 5,
    circuitBreakerTimeoutMs: 30000,
  })

  const prefetchExerciseSets = useCallback(
    async (exerciseIds: number[]): Promise<void> => {
      // If already prefetching, wait for it to complete
      if (prefetchPromiseRef.current) {
        await prefetchPromiseRef.current
      }

      // Adjust batch size based on memory pressure
      const adjustedExerciseIds = shouldReducePrefetching
        ? exerciseIds.slice(0, recommendedBatchSize)
        : exerciseIds

      setIsPrefetching(true)

      // Mark all exercises as loading in the store
      const newLoadingStatus: Record<number, PrefetchStatus> = {}
      adjustedExerciseIds.forEach((id) => {
        newLoadingStatus[id] = 'loading'
      })
      setPrefetchStatus({ ...prefetchStatus, ...newLoadingStatus })

      const prefetchPromise = (async () => {
        try {
          // Perform memory cleanup if under pressure
          if (isMemoryPressure) {
            await performMemoryCleanup()
          }

          // Call the robust prefetch with retry logic
          await prefetchWithRetry(adjustedExerciseIds)
        } catch (error) {
          console.error('Error prefetching exercise sets after retries:', error)
          // Mark all as error in the store - the robust handler already tried retries
          const newErrorStatus: Record<number, PrefetchStatus> = {}
          adjustedExerciseIds.forEach((id) => {
            newErrorStatus[id] = 'error'
          })
          setPrefetchStatus({ ...prefetchStatus, ...newErrorStatus })
        } finally {
          setIsPrefetching(false)
          prefetchPromiseRef.current = null
        }
      })()

      prefetchPromiseRef.current = prefetchPromise
      await prefetchPromise
    },
    [
      prefetchWithRetry,
      shouldReducePrefetching,
      recommendedBatchSize,
      isMemoryPressure,
      performMemoryCleanup,
      setPrefetchStatus,
      prefetchStatus,
    ]
  )

  const isExercisePrefetched = useCallback(
    (exerciseId: number): boolean => {
      return isExercisePrefetchedStore(exerciseId)
    },
    [isExercisePrefetchedStore]
  )

  const clearPrefetchCache = useCallback(() => {
    // Clear local cache
    prefetchCacheRef.current.clear()
    // Clear store state
    setPrefetchStatus({})
    setPrefetchedExerciseIds([])
  }, [setPrefetchStatus, setPrefetchedExerciseIds])

  return {
    prefetchStatus,
    isPrefetching,
    prefetchedExerciseIds,
    prefetchExerciseSets,
    isExercisePrefetched,
    clearPrefetchCache,
    retryCount,
    errorHistory,
    hasExceededMaxRetries,
    isCircuitBreakerOpen,
    // Memory optimization properties
    cacheSize,
    memoryUsage,
    isMemoryPressure,
    shouldReducePrefetching,
    recommendedBatchSize,
    get onProgress() {
      return onProgressRef.current
    },
    set onProgress(callback: ((progress: number) => void) | undefined) {
      onProgressRef.current = callback
    },
  }
}
