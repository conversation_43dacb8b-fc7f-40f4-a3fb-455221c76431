import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useDebounce } from '../useDebounce'

describe('useDebounce', () => {
  beforeEach(() => {
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.clearAllTimers()
    vi.restoreAllMocks()
  })

  it('should debounce value changes', () => {
    const { result, rerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      { initialProps: { value: 'initial', delay: 1000 } }
    )

    expect(result.current).toBe('initial')

    // Change value
    rerender({ value: 'changed', delay: 1000 })

    // Value should not change immediately
    expect(result.current).toBe('initial')

    // Advance time by half the delay
    act(() => {
      vi.advanceTimersByTime(500)
    })
    expect(result.current).toBe('initial')

    // Advance time to complete the delay
    act(() => {
      vi.advanceTimersByTime(500)
    })
    expect(result.current).toBe('changed')
  })

  it('should reset timer on rapid changes', () => {
    const { result, rerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      { initialProps: { value: 'initial', delay: 1000 } }
    )

    // Make multiple rapid changes
    rerender({ value: 'change1', delay: 1000 })
    act(() => {
      vi.advanceTimersByTime(500)
    })

    rerender({ value: 'change2', delay: 1000 })
    act(() => {
      vi.advanceTimersByTime(500)
    })

    rerender({ value: 'change3', delay: 1000 })

    // Value should still be initial
    expect(result.current).toBe('initial')

    // Advance time to complete the delay from last change
    act(() => {
      vi.advanceTimersByTime(1000)
    })

    // Should have final value
    expect(result.current).toBe('change3')
  })

  it('should handle zero delay', () => {
    const { result, rerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      { initialProps: { value: 'initial', delay: 0 } }
    )

    expect(result.current).toBe('initial')

    rerender({ value: 'changed', delay: 0 })

    // With zero delay, should update immediately
    act(() => {
      vi.runAllTimers()
    })

    expect(result.current).toBe('changed')
  })

  it('should cleanup timer on unmount', () => {
    const clearTimeoutSpy = vi.spyOn(global, 'clearTimeout')

    const { unmount, rerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      { initialProps: { value: 'initial', delay: 1000 } }
    )

    // Change value to start a timer
    rerender({ value: 'changed', delay: 1000 })

    // Unmount before timer completes
    unmount()

    // Verify clearTimeout was called
    expect(clearTimeoutSpy).toHaveBeenCalled()
  })

  it('should handle different data types', () => {
    // Test with object
    const { result: objectResult, rerender: rerenderObject } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      { initialProps: { value: { key: 'initial' }, delay: 500 } }
    )

    rerenderObject({ value: { key: 'changed' }, delay: 500 })
    act(() => {
      vi.advanceTimersByTime(500)
    })
    expect(objectResult.current).toEqual({ key: 'changed' })

    // Test with number
    const { result: numberResult, rerender: rerenderNumber } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      { initialProps: { value: 42, delay: 500 } }
    )

    rerenderNumber({ value: 100, delay: 500 })
    act(() => {
      vi.advanceTimersByTime(500)
    })
    expect(numberResult.current).toBe(100)

    // Test with boolean
    const { result: boolResult, rerender: rerenderBool } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      { initialProps: { value: false, delay: 500 } }
    )

    rerenderBool({ value: true, delay: 500 })
    act(() => {
      vi.advanceTimersByTime(500)
    })
    expect(boolResult.current).toBe(true)
  })
})
