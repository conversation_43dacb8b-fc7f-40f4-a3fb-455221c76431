/* eslint-disable no-console */
import { test, expect } from '@playwright/test'

test.describe('Drop Sets Unit Test - MAUI Parity', () => {
  test('should apply MAUI drop set rules in unit test page', async ({
    page,
  }) => {
    // Navigate to test page that uses generateAllSets directly
    await page.goto('/test-rest-pause')

    // Wait for page to load
    await page.waitForLoadState('networkidle')

    // Simulate drop set scenario by modifying the test data
    await page.evaluate(() => {
      // Override window fetch to return drop set recommendation
      const originalFetch = window.fetch
      window.fetch = async (url: string, ...args: any[]) => {
        if (url.includes('GetRecommendation')) {
          return {
            ok: true,
            json: async () => ({
              IsDropSet: true,
              Series: 1,
              NbPauses: 1, // Only 2 total sets - should expand to 3
              Reps: 12,
              Weight: { Kg: 50, Lb: 110 },
              WarmupsCount: 2,
              IsNormalSets: false,
              IsPyramid: false,
              IsReversePyramid: false,
              IsBackOffSet: false,
              NbRepsPauses: 0,
              RpRest: 0,
              IsBodyweight: false,
            }),
          }
        }
        return originalFetch(url, ...args)
      }
    })

    // Trigger a re-render or refresh
    await page.reload()
    await page.waitForLoadState('networkidle')

    // Count the number of sets displayed
    const sets = page.locator(
      '[data-testid*="set"], .set-row, div:has(input[type="number"])'
    )
    const setCount = await sets.count()

    console.log(`Total sets found: ${setCount}`)

    // Should have at least 5 sets (2 warmups + minimum 3 work sets)
    expect(setCount).toBeGreaterThanOrEqual(5)

    // Check for drop set labels
    const dropSetLabels = page.locator('text="Drop set"')
    const dropSetCount = await dropSetLabels.count()

    console.log(`Drop set labels found: ${dropSetCount}`)

    // Should have at least 3 drop set labels
    expect(dropSetCount).toBeGreaterThanOrEqual(3)

    // Check reps values
    const repsInputs = page.locator(
      'input[type="number"], input[inputmode="numeric"]'
    )
    const inputCount = await repsInputs.count()

    if (inputCount > 2) {
      // Skip warmups
      // First work set should have 12 reps
      const firstWorkSetReps = await repsInputs.nth(2).getAttribute('value')
      console.log(`First work set reps: ${firstWorkSetReps}`)

      // Drop sets should have 4 reps (12 / 3)
      const dropSet1Reps = await repsInputs.nth(3).getAttribute('value')
      const dropSet2Reps = await repsInputs.nth(4).getAttribute('value')

      console.log(`Drop set 1 reps: ${dropSet1Reps}`)
      console.log(`Drop set 2 reps: ${dropSet2Reps}`)

      // Verify one-third reps rule
      expect(parseInt(dropSet1Reps || '0')).toBe(4)
      expect(parseInt(dropSet2Reps || '0')).toBe(4)
    }
  })
})
