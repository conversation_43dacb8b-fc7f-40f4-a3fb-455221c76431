import { test, expect, Page } from '@playwright/test'

test.describe('App Visibility Recovery Tests', () => {
  let page: Page
  const TEST_USER = {
    email: '<EMAIL>',
    password: 'Dr123456',
  }

  test.beforeEach(async ({ page: p }) => {
    page = p

    // Login first
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')
  })

  test('should recover from background state when on exercise page', async () => {
    // Navigate to workout and then exercise
    await page.click('button:has-text("Continue to workout")')
    await page.waitForURL('/workout')
    await page.click('button:has-text("Start Workout")')

    // Wait for exercise page to load
    await page.waitForSelector('[data-testid="save-set-button"]', {
      timeout: 10000,
    })

    // Capture initial state
    const initialWeight = await page.inputValue('input[placeholder*="weight"]')
    const initialReps = await page.inputValue('input[placeholder*="reps"]')

    // Simulate app going to background using Page Visibility API
    await page.evaluate(() => {
      Object.defineProperty(document, 'visibilityState', {
        configurable: true,
        get: () => 'hidden',
      })
      document.dispatchEvent(new Event('visibilitychange'))
    })

    // Wait a bit to ensure background handler runs
    await page.waitForTimeout(100)

    // Simulate app returning from background after 10 seconds
    await page.waitForTimeout(10000)

    await page.evaluate(() => {
      Object.defineProperty(document, 'visibilityState', {
        configurable: true,
        get: () => 'visible',
      })
      document.dispatchEvent(new Event('visibilitychange'))
    })

    // Wait for recovery
    await page.waitForTimeout(1000)

    // Verify page is still functional
    await expect(page.locator('[data-testid="save-set-button"]')).toBeEnabled()

    // Verify form data is preserved
    const currentWeight = await page.inputValue('input[placeholder*="weight"]')
    const currentReps = await page.inputValue('input[placeholder*="reps"]')
    expect(currentWeight).toBe(initialWeight)
    expect(currentReps).toBe(initialReps)

    // Verify we can still save a set
    await page.fill('input[placeholder*="weight"]', '100')
    await page.fill('input[placeholder*="reps"]', '10')
    await page.click('[data-testid="save-set-button"]')

    // Should navigate to rest timer
    await expect(page).toHaveURL(/rest-timer/, { timeout: 5000 })
  })

  test('should handle interrupted loading when app goes to background', async () => {
    // Navigate to workout
    await page.click('button:has-text("Continue to workout")')
    await page.waitForURL('/workout')

    // Intercept recommendation loading to make it slow
    await page.route(
      '**/GetActivePlanExerciseRecommendation**',
      async (route) => {
        // Delay response by 5 seconds
        await new Promise((resolve) => setTimeout(resolve, 5000))
        await route.continue()
      }
    )

    // Start workout (this will trigger recommendation loading)
    await page.click('button:has-text("Start Workout")')

    // Immediately simulate background while loading
    await page.evaluate(() => {
      Object.defineProperty(document, 'visibilityState', {
        configurable: true,
        get: () => 'hidden',
      })
      document.dispatchEvent(new Event('visibilitychange'))
    })

    // Wait a bit
    await page.waitForTimeout(1000)

    // Return from background
    await page.evaluate(() => {
      Object.defineProperty(document, 'visibilityState', {
        configurable: true,
        get: () => 'visible',
      })
      document.dispatchEvent(new Event('visibilitychange'))
    })

    // Should eventually load successfully
    await expect(page.locator('[data-testid="save-set-button"]')).toBeVisible({
      timeout: 15000,
    })
  })

  test('should pause and resume rest timer when app goes to background', async () => {
    // Navigate to workout and complete a set
    await page.click('button:has-text("Continue to workout")')
    await page.waitForURL('/workout')
    await page.click('button:has-text("Start Workout")')

    // Wait for exercise page
    await page.waitForSelector('[data-testid="save-set-button"]', {
      timeout: 10000,
    })

    // Complete a set to trigger rest timer
    await page.fill('input[placeholder*="weight"]', '100')
    await page.fill('input[placeholder*="reps"]', '10')
    await page.click('[data-testid="save-set-button"]')

    // Wait for rest timer
    await page.waitForURL(/rest-timer/)
    await page.waitForSelector('[data-testid="timer-display"]')

    // Get initial timer value
    const initialTime = await page.textContent('[data-testid="timer-display"]')
    const [minutes, seconds] = initialTime!.split(':').map(Number)
    const initialSeconds = minutes * 60 + seconds

    // Simulate background after 5 seconds
    await page.waitForTimeout(5000)

    await page.evaluate(() => {
      Object.defineProperty(document, 'visibilityState', {
        configurable: true,
        get: () => 'hidden',
      })
      document.dispatchEvent(new Event('visibilitychange'))
    })

    // Stay in background for 10 seconds
    await page.waitForTimeout(10000)

    // Return from background
    await page.evaluate(() => {
      Object.defineProperty(document, 'visibilityState', {
        configurable: true,
        get: () => 'visible',
      })
      document.dispatchEvent(new Event('visibilitychange'))
    })

    // Check timer value - should have counted down ~15 seconds total (5 before + 10 during background)
    const currentTime = await page.textContent('[data-testid="timer-display"]')
    const [currentMinutes, currentSeconds] = currentTime!.split(':').map(Number)
    const currentTotalSeconds = currentMinutes * 60 + currentSeconds

    // Timer should have decreased by approximately 15 seconds
    const timeDiff = initialSeconds - currentTotalSeconds
    expect(timeDiff).toBeGreaterThanOrEqual(14)
    expect(timeDiff).toBeLessThanOrEqual(16)
  })

  test('should handle multiple rapid visibility changes', async () => {
    // Navigate to exercise page
    await page.click('button:has-text("Continue to workout")')
    await page.waitForURL('/workout')
    await page.click('button:has-text("Start Workout")')
    await page.waitForSelector('[data-testid="save-set-button"]', {
      timeout: 10000,
    })

    // Simulate rapid visibility changes using page.evaluate
    await page.evaluate(() => {
      // Multiple rapid changes within page context
      Object.defineProperty(document, 'visibilityState', {
        configurable: true,
        get: () => 'hidden',
      })
      document.dispatchEvent(new Event('visibilitychange'))

      Object.defineProperty(document, 'visibilityState', {
        configurable: true,
        get: () => 'visible',
      })
      document.dispatchEvent(new Event('visibilitychange'))

      Object.defineProperty(document, 'visibilityState', {
        configurable: true,
        get: () => 'hidden',
      })
      document.dispatchEvent(new Event('visibilitychange'))

      Object.defineProperty(document, 'visibilityState', {
        configurable: true,
        get: () => 'visible',
      })
      document.dispatchEvent(new Event('visibilitychange'))
    })

    // Wait for changes to process
    await page.waitForTimeout(500)

    // App should still be functional
    await expect(page.locator('[data-testid="save-set-button"]')).toBeEnabled()

    // Should be able to save a set
    await page.fill('input[placeholder*="weight"]', '100')
    await page.fill('input[placeholder*="reps"]', '10')
    await page.click('[data-testid="save-set-button"]')

    // Should navigate successfully
    await expect(page).toHaveURL(/rest-timer/, { timeout: 5000 })
  })

  test('should show stale data warning for very old sessions', async () => {
    // Navigate to workout
    await page.click('button:has-text("Continue to workout")')
    await page.waitForURL('/workout')
    await page.click('button:has-text("Start Workout")')
    await page.waitForSelector('[data-testid="save-set-button"]', {
      timeout: 10000,
    })

    // Manipulate session start time to be 13 hours ago
    await page.evaluate(() => {
      const store = (window as any).__WORKOUT_STORE__
      if (store && store.workoutSession) {
        // Set session start time to 13 hours ago
        store.workoutSession.startTime = new Date(
          Date.now() - 13 * 60 * 60 * 1000
        )
      }
    })

    // Simulate returning from background
    await page.evaluate(() => {
      Object.defineProperty(document, 'visibilityState', {
        configurable: true,
        get: () => 'visible',
      })
      document.dispatchEvent(new Event('visibilitychange'))
    })

    // Should show warning about old session
    await expect(page.locator('text=/over 12 hours/i')).toBeVisible({
      timeout: 5000,
    })
  })
})
