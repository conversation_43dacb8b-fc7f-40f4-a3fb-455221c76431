import React from 'react'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useLoginPrefetch } from '../useLoginPrefetch'

import { getUserWorkoutProgramInfo } from '@/services/api/workout'
import { workoutApi } from '@/api/workouts'
import { useWorkoutStore } from '@/stores/workoutStore'
import { RecommendationLoadingCoordinator } from '@/utils/RecommendationLoadingCoordinator'

// Mock the prefetch hook
vi.mock('../useUserStats', () => ({
  usePrefetchUserStats: vi.fn(() => ({
    prefetch: vi.fn(),
  })),
}))

// Mock workout API
vi.mock('@/services/api/workout', () => ({
  getUserWorkoutProgramInfo: vi.fn(),
}))

vi.mock('@/api/workouts', () => ({
  workoutApi: {
    getUserWorkout: vi.fn(),
  },
}))

// Mock workout store
vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: vi.fn(),
}))

// Mock the RecommendationLoadingCoordinator
vi.mock('@/utils/RecommendationLoadingCoordinator', () => ({
  RecommendationLoadingCoordinator: {
    getInstance: vi.fn(() => ({
      isLoading: vi.fn(() => false),
      isAnyLoading: vi.fn(() => false),
    })),
  },
}))

// Mock logger
vi.mock('@/utils/logger', () => ({
  logger: {
    log: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
  },
}))

describe('useLoginPrefetch - Exercise recommendations loading', () => {
  let queryClient: QueryClient

  beforeEach(() => {
    vi.clearAllMocks()
    vi.useFakeTimers()
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
      },
    })

    // Set up default mocks
    vi.mocked(getUserWorkoutProgramInfo).mockResolvedValue({
      GetUserProgramInfoResponseModel: {
        NextWorkoutTemplate: { Id: 123 },
      },
    })
    vi.mocked(workoutApi.getUserWorkout).mockResolvedValue([
      { Id: 123, Exercises: [] },
    ])
    vi.mocked(useWorkoutStore).mockReturnValue({
      loadAllExerciseRecommendations: vi.fn().mockResolvedValue(undefined),
      loadExerciseRecommendation: vi.fn().mockResolvedValue(undefined),
      setWorkout: vi.fn(),
      exercises: [],
    } as any)
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )

  it('should load first exercise recommendation immediately, then others in background', async () => {
    // Mock the responses
    const mockProgramInfo = {
      GetUserProgramInfoResponseModel: {
        NextWorkoutTemplate: { Id: 123 },
      },
    }
    const mockWorkout = {
      Id: 123,
      Exercises: [
        { Id: 1, Name: 'Exercise 1' },
        { Id: 2, Name: 'Exercise 2' },
        { Id: 3, Name: 'Exercise 3' },
      ],
    }
    const mockLoadExerciseRecommendation = vi.fn().mockResolvedValue(undefined)
    const mockLoadAllExerciseRecommendations = vi
      .fn()
      .mockResolvedValue(undefined)

    vi.mocked(getUserWorkoutProgramInfo).mockResolvedValue(mockProgramInfo)
    vi.mocked(workoutApi.getUserWorkout).mockResolvedValue([mockWorkout])
    vi.mocked(useWorkoutStore).mockReturnValue({
      loadAllExerciseRecommendations: mockLoadAllExerciseRecommendations,
      loadExerciseRecommendation: mockLoadExerciseRecommendation,
      setWorkout: vi.fn(),
      exercises: mockWorkout.Exercises,
    } as any)

    const { result } = renderHook(() => useLoginPrefetch(), { wrapper })

    await act(async () => {
      result.current.startPrefetch()
      // Allow first promise to resolve
      await vi.runAllTimersAsync()
    })

    // Verify that first exercise was loaded immediately
    expect(mockLoadExerciseRecommendation).toHaveBeenCalledWith(1)

    // Advance timers to trigger background loading
    await act(async () => {
      await vi.advanceTimersByTimeAsync(500)
    })

    // Verify that all recommendations were loaded in background
    expect(mockLoadAllExerciseRecommendations).toHaveBeenCalledTimes(1)
  })

  it('should not load recommendations if no exercises in workout', async () => {
    // Mock the responses
    const mockProgramInfo = {
      GetUserProgramInfoResponseModel: {
        NextWorkoutTemplate: { Id: 123 },
      },
    }
    const mockWorkout = {
      Id: 123,
      Exercises: [], // No exercises
    }
    const mockLoadAllExerciseRecommendations = vi
      .fn()
      .mockResolvedValue(undefined)
    const mockLoadExerciseRecommendation = vi.fn().mockResolvedValue(undefined)

    vi.mocked(getUserWorkoutProgramInfo).mockResolvedValue(mockProgramInfo)
    vi.mocked(workoutApi.getUserWorkout).mockResolvedValue([mockWorkout])
    vi.mocked(useWorkoutStore).mockReturnValue({
      loadAllExerciseRecommendations: mockLoadAllExerciseRecommendations,
      loadExerciseRecommendation: mockLoadExerciseRecommendation,
      setWorkout: vi.fn(),
      exercises: [],
    } as any)

    const { result } = renderHook(() => useLoginPrefetch(), { wrapper })

    await act(async () => {
      result.current.startPrefetch()
      // Allow promises to resolve
      await vi.runAllTimersAsync()
    })

    // Verify that recommendations were NOT loaded
    expect(mockLoadExerciseRecommendation).not.toHaveBeenCalled()
    expect(mockLoadAllExerciseRecommendations).not.toHaveBeenCalled()
  })

  it('should continue loading even if first exercise recommendation fails', async () => {
    // Mock the responses
    const mockProgramInfo = {
      GetUserProgramInfoResponseModel: {
        NextWorkoutTemplate: { Id: 123 },
      },
    }
    const mockWorkout = {
      Id: 123,
      Exercises: [{ Id: 1, Name: 'Exercise 1' }],
    }
    const mockLoadExerciseRecommendation = vi
      .fn()
      .mockRejectedValue(new Error('First exercise failed'))
    const mockLoadAllExerciseRecommendations = vi
      .fn()
      .mockResolvedValue(undefined)

    vi.mocked(getUserWorkoutProgramInfo).mockResolvedValue(mockProgramInfo)
    vi.mocked(workoutApi.getUserWorkout).mockResolvedValue([mockWorkout])
    vi.mocked(useWorkoutStore).mockReturnValue({
      loadAllExerciseRecommendations: mockLoadAllExerciseRecommendations,
      loadExerciseRecommendation: mockLoadExerciseRecommendation,
      setWorkout: vi.fn(),
      exercises: mockWorkout.Exercises,
    } as any)

    const { result } = renderHook(() => useLoginPrefetch(), { wrapper })

    await act(async () => {
      result.current.startPrefetch()
      // Allow promises to resolve
      await vi.runAllTimersAsync()
    })

    // Verify that the prefetch completes despite recommendation failure
    expect(result.current.isComplete).toBe(true)
    expect(result.current.error).toBeNull()

    // Verify that fallback to loadAll was called
    expect(mockLoadAllExerciseRecommendations).toHaveBeenCalledTimes(1)
  })

  it('should set workout in store before loading recommendations', async () => {
    // Mock the responses
    const mockProgramInfo = {
      GetUserProgramInfoResponseModel: {
        NextWorkoutTemplate: { Id: 123 },
      },
    }
    const mockWorkout = {
      Id: 123,
      Exercises: [{ Id: 1, Name: 'Exercise 1' }],
    }
    const mockSetWorkout = vi.fn()
    const mockLoadExerciseRecommendation = vi.fn().mockResolvedValue(undefined)
    const mockLoadAllExerciseRecommendations = vi
      .fn()
      .mockResolvedValue(undefined)

    vi.mocked(getUserWorkoutProgramInfo).mockResolvedValue(mockProgramInfo)
    vi.mocked(workoutApi.getUserWorkout).mockResolvedValue([mockWorkout])
    vi.mocked(useWorkoutStore).mockReturnValue({
      loadAllExerciseRecommendations: mockLoadAllExerciseRecommendations,
      loadExerciseRecommendation: mockLoadExerciseRecommendation,
      setWorkout: mockSetWorkout,
      exercises: mockWorkout.Exercises,
    } as any)

    const { result } = renderHook(() => useLoginPrefetch(), { wrapper })

    await act(async () => {
      result.current.startPrefetch()
      // Allow promises to resolve
      await vi.runAllTimersAsync()
    })

    // Verify that setWorkout was called before loadExerciseRecommendation
    expect(mockSetWorkout).toHaveBeenCalledWith(mockWorkout)
    expect(mockSetWorkout).toHaveBeenCalledBefore(
      mockLoadExerciseRecommendation as any
    )
  })

  it('should NOT call setWorkout if workout already exists in store', async () => {
    // Mock the responses
    const mockProgramInfo = {
      GetUserProgramInfoResponseModel: {
        NextWorkoutTemplate: { Id: 123 },
      },
    }
    const existingWorkout = {
      Id: 123,
      Exercises: [
        { Id: 1, Name: 'Exercise 1' },
        { Id: 2, Name: 'Exercise 2' },
      ],
    }
    const mockWorkout = {
      Id: 123,
      Exercises: [
        { Id: 1, Name: 'Exercise 1' },
        { Id: 2, Name: 'Exercise 2' },
      ],
    }
    const mockSetWorkout = vi.fn()
    const mockLoadExerciseRecommendation = vi.fn().mockResolvedValue(undefined)
    const mockLoadAllExerciseRecommendations = vi
      .fn()
      .mockResolvedValue(undefined)

    vi.mocked(getUserWorkoutProgramInfo).mockResolvedValue(mockProgramInfo)
    vi.mocked(workoutApi.getUserWorkout).mockResolvedValue([mockWorkout])
    vi.mocked(useWorkoutStore).mockReturnValue({
      loadAllExerciseRecommendations: mockLoadAllExerciseRecommendations,
      loadExerciseRecommendation: mockLoadExerciseRecommendation,
      setWorkout: mockSetWorkout,
      exercises: existingWorkout.Exercises,
      currentWorkout: existingWorkout, // Workout already exists
    } as any)

    const { result } = renderHook(() => useLoginPrefetch(), { wrapper })

    await act(async () => {
      result.current.startPrefetch()
      // Allow promises to resolve
      await vi.runAllTimersAsync()
    })

    // Verify that setWorkout was NOT called since workout already exists
    expect(mockSetWorkout).not.toHaveBeenCalled()
    // But first exercise recommendation should still be loaded
    expect(mockLoadExerciseRecommendation).toHaveBeenCalledWith(1)

    // Advance timers to trigger background loading
    await act(async () => {
      await vi.advanceTimersByTimeAsync(500)
    })

    // And then all recommendations
    expect(mockLoadAllExerciseRecommendations).toHaveBeenCalledTimes(1)
  })

  it('should call setWorkout if existing workout has different ID', async () => {
    // Mock the responses
    const mockProgramInfo = {
      GetUserProgramInfoResponseModel: {
        NextWorkoutTemplate: { Id: 456 }, // Different ID
      },
    }
    const existingWorkout = {
      Id: 123, // Old workout ID
      Exercises: [{ Id: 1, Name: 'Exercise 1' }],
    }
    const mockWorkout = {
      Id: 456, // New workout ID
      Exercises: [
        { Id: 2, Name: 'Exercise 2' },
        { Id: 3, Name: 'Exercise 3' },
      ],
    }
    const mockSetWorkout = vi.fn()
    const mockLoadExerciseRecommendation = vi.fn().mockResolvedValue(undefined)
    const mockLoadAllExerciseRecommendations = vi
      .fn()
      .mockResolvedValue(undefined)

    vi.mocked(getUserWorkoutProgramInfo).mockResolvedValue(mockProgramInfo)
    vi.mocked(workoutApi.getUserWorkout).mockResolvedValue([mockWorkout])
    vi.mocked(useWorkoutStore).mockReturnValue({
      loadAllExerciseRecommendations: mockLoadAllExerciseRecommendations,
      loadExerciseRecommendation: mockLoadExerciseRecommendation,
      setWorkout: mockSetWorkout,
      exercises: existingWorkout.Exercises,
      currentWorkout: existingWorkout,
    } as any)

    const { result } = renderHook(() => useLoginPrefetch(), { wrapper })

    await act(async () => {
      result.current.startPrefetch()
      // Allow promises to resolve
      await vi.runAllTimersAsync()
    })

    // Verify that setWorkout WAS called since it's a different workout
    expect(mockSetWorkout).toHaveBeenCalledWith(mockWorkout)
    // And first exercise should be loaded
    expect(mockLoadExerciseRecommendation).toHaveBeenCalledWith(2)

    // Advance timers to trigger background loading
    await act(async () => {
      await vi.advanceTimersByTimeAsync(500)
    })

    expect(mockLoadAllExerciseRecommendations).toHaveBeenCalledTimes(1)
  })

  it('should only load first exercise when workout has single exercise', async () => {
    // Mock the responses
    const mockProgramInfo = {
      GetUserProgramInfoResponseModel: {
        NextWorkoutTemplate: { Id: 123 },
      },
    }
    const mockWorkout = {
      Id: 123,
      Exercises: [{ Id: 1, Name: 'Exercise 1' }], // Only one exercise
    }
    const mockLoadExerciseRecommendation = vi.fn().mockResolvedValue(undefined)
    const mockLoadAllExerciseRecommendations = vi
      .fn()
      .mockResolvedValue(undefined)

    vi.mocked(getUserWorkoutProgramInfo).mockResolvedValue(mockProgramInfo)
    vi.mocked(workoutApi.getUserWorkout).mockResolvedValue([mockWorkout])
    vi.mocked(useWorkoutStore).mockReturnValue({
      loadAllExerciseRecommendations: mockLoadAllExerciseRecommendations,
      loadExerciseRecommendation: mockLoadExerciseRecommendation,
      setWorkout: vi.fn(),
      exercises: mockWorkout.Exercises,
    } as any)

    const { result } = renderHook(() => useLoginPrefetch(), { wrapper })

    await act(async () => {
      result.current.startPrefetch()
      // Allow promises to resolve
      await vi.runAllTimersAsync()
    })

    // Verify that first exercise was loaded
    expect(mockLoadExerciseRecommendation).toHaveBeenCalledWith(1)

    // Advance timers to see if background loading happens
    await act(async () => {
      await vi.advanceTimersByTimeAsync(500)
    })

    // Should NOT call loadAll since there's only one exercise
    expect(mockLoadAllExerciseRecommendations).not.toHaveBeenCalled()
  })

  it('should check coordinator before loading to prevent duplicate loads', async () => {
    const mockProgramInfo = {
      GetUserProgramInfoResponseModel: {
        NextWorkoutTemplate: { Id: 123 },
      },
    }
    const mockWorkout = {
      Id: 123,
      Exercises: [{ Id: 1, Name: 'Exercise 1' }],
    }
    const mockLoadExerciseRecommendation = vi.fn()
    const mockIsLoading = vi.fn(() => true) // Simulate already loading

    vi.mocked(getUserWorkoutProgramInfo).mockResolvedValue(mockProgramInfo)
    vi.mocked(workoutApi.getUserWorkout).mockResolvedValue([mockWorkout])
    vi.mocked(useWorkoutStore).mockReturnValue({
      loadAllExerciseRecommendations: vi.fn().mockResolvedValue(undefined),
      loadExerciseRecommendation: mockLoadExerciseRecommendation,
      setWorkout: vi.fn(),
      exercises: mockWorkout.Exercises,
    } as any)

    // Mock coordinator to indicate exercise is already loading
    const mockCoordinator = {
      isLoading: mockIsLoading,
      isAnyLoading: vi.fn(() => false),
    }
    vi.mocked(RecommendationLoadingCoordinator.getInstance).mockReturnValue(
      mockCoordinator as any
    )

    const { result } = renderHook(() => useLoginPrefetch(), { wrapper })

    await act(async () => {
      result.current.startPrefetch()
      await vi.runAllTimersAsync()
    })

    // Should check if already loading
    expect(mockIsLoading).toHaveBeenCalledWith(1)
    // Should NOT load since it's already loading
    expect(mockLoadExerciseRecommendation).not.toHaveBeenCalled()
  })
})
