import { test, expect } from '@playwright/test'

test.describe('SwipeableStatCard Animation', () => {
  test('shows initial mount animation even with cached data', async ({
    page,
  }) => {
    // Clear localStorage to ensure fresh state
    await page.goto('/login')
    await page.evaluate(() => localStorage.clear())

    // Login and let stats get cached
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'Dr123456')
    await page.click('button[type="submit"]')

    // Wait for redirect to program page
    await page.waitForURL('/program', { timeout: 10000 })

    // Wait for stats to load and be cached
    await page.waitForSelector('[data-testid="stat-value"]')
    await page.waitForTimeout(3000) // Ensure animation completes

    // Get the real value
    const realValue = await page
      .locator('[data-testid="stat-value"]')
      .first()
      .textContent()

    // Navigate away and back to trigger cached load
    await page.goto('/workout')
    await page.goto('/program')

    // Immediately check the value - should show animation starting at 0
    const statValue = page.locator('[data-testid="stat-value"]').first()
    const initialValue = await statValue.textContent()

    // Should start at 0 even with cached data
    expect(initialValue).toBe('0')

    // Should show loading message during initial animation
    await expect(
      page.locator('text=Loading stats and workout...')
    ).toBeVisible()

    // Wait a bit and check animation is progressing
    await page.waitForTimeout(1000)
    const midValue = await statValue.textContent()
    const midNumber = parseInt(midValue?.replace(/,/g, '') || '0')

    // Should be animating upward
    expect(midNumber).toBeGreaterThan(0)

    // Wait for animation to complete
    await page.waitForTimeout(2000)

    // Should now show the real cached value
    const finalValue = await statValue.textContent()
    expect(finalValue).toBe(realValue)

    // Loading message should be gone
    await expect(
      page.locator('text=Loading stats and workout...')
    ).not.toBeVisible()
  })

  test('counter animates from 0 during loading on program page', async ({
    page,
  }) => {
    // Login first
    await page.goto('/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'Dr123456')
    await page.click('button[type="submit"]')

    // Wait for login to complete
    await page.waitForURL('/program', { timeout: 10000 })

    // Check initial state - should show 0 if loading
    const statValue = page.locator('[data-testid="stat-value"]').first()

    // If it's loading, verify animation
    const loadingMessage = page.locator('text=Loading stats and workout...')
    const isLoading = await loadingMessage.isVisible().catch(() => false)

    if (isLoading) {
      // Get initial value
      const initialText = await statValue.textContent()
      const initialValue = parseInt(initialText?.replace(/,/g, '') || '0')

      // console.log('Initial value:', initialValue)

      // Wait a bit for animation
      await page.waitForTimeout(1000)

      // Check value has increased
      const animatedText = await statValue.textContent()
      const animatedValue = parseInt(animatedText?.replace(/,/g, '') || '0')

      // console.log('Animated value after 1s:', animatedValue)

      // Value should increase during animation
      expect(animatedValue).toBeGreaterThanOrEqual(initialValue)
    } else {
      // If not loading, just verify the stat displays correctly
      const value = await statValue.textContent()
      expect(value).toBeTruthy()
      // console.log('Stats already loaded, value:', value)
    }
  })

  test('swipeable navigation works between stats', async ({ page }) => {
    // Assume already logged in from previous test or use existing session
    await page.goto('/program')

    // Wait for stats to load
    await page.waitForSelector('[data-testid="swipeable-stat-card"]')

    // Check we're on first stat (Workouts)
    await expect(page.locator('text=Workouts')).toBeVisible()

    // Click second indicator
    const indicators = page.locator('[data-testid="stat-indicator"]')
    await indicators.nth(1).click()

    // Should show Week streak
    await expect(page.locator('text=Week streak').first()).toBeVisible()

    // Click third indicator
    await indicators.nth(2).click()

    // Should show Lbs Lifted
    await expect(page.locator('text=Lbs Lifted')).toBeVisible()
  })
})
