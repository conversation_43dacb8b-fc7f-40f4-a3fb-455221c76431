import { test, expect } from '@playwright/test'

test.describe('Cross-User Cache Isolation After Logout', () => {
  const user1 = {
    email: '<EMAIL>',
    password: 'Dr123456',
  }

  const user2 = {
    email: '<EMAIL>',
    password: 'Dr123456',
  }

  test('should not show previous user workout data after logout and login with different account', async ({
    page,
  }) => {
    // Login as user 1
    await page.goto('/login')
    await page.fill('#email', user1.email)
    await page.fill('#password', user1.password)
    await page.click('[type="submit"]')

    // Wait for program page
    await page.waitForURL('/program')

    // Navigate to workout page to load user 1's workout data
    await page.click('button:has-text("Continue to Workout")')
    await page.waitForURL('/workout')

    // Ensure user 1's workout is loaded
    await expect(page.locator('h1:has-text("Today\'s Workout")')).toBeVisible()

    // Go back to access logout
    await page.goto('/program')

    // Logout user 1
    await page.click('[data-testid="user-menu-button"]')
    await page.click('button:has-text("Log out")')

    // Wait for redirect to login
    await page.waitForURL('/login')

    // Verify all caches are cleared by checking localStorage
    const localStorageKeys = await page.evaluate(() => {
      const keys = []
      for (let i = 0; i < localStorage.length; i++) {
        keys.push(localStorage.key(i))
      }
      return keys
    })

    // Should not have any dr-muscle related keys
    const drMuscleKeys = localStorageKeys.filter((key) =>
      key.includes('drmuscle')
    )
    expect(drMuscleKeys).toHaveLength(0)

    // Login as user 2
    await page.fill('#email', user2.email)
    await page.fill('#password', user2.password)
    await page.click('[type="submit"]')

    // Wait for program page
    await page.waitForURL('/program')

    // Navigate to workout page
    await page.click('button:has-text("Continue to Workout")')
    await page.waitForURL('/workout')

    // Verify user 2 sees their own workout data without stale cache issues
    await expect(page.locator('h1:has-text("Today\'s Workout")')).toBeVisible()

    // Verify no error states
    await expect(
      page.locator('text="Failed to load workout"')
    ).not.toBeVisible()
    await expect(page.locator('text="No workout scheduled"')).not.toBeVisible()
  })

  test('should clear React Query cache on logout preventing stale data', async ({
    page,
  }) => {
    // Enable console log monitoring to detect cache issues
    const consoleLogs: string[] = []
    page.on('console', (msg) => {
      if (msg.type() === 'error' || msg.type() === 'warn') {
        consoleLogs.push(msg.text())
      }
    })

    // Login as user 1
    await page.goto('/login')
    await page.fill('#email', user1.email)
    await page.fill('#password', user1.password)
    await page.click('[type="submit"]')

    await page.waitForURL('/program')

    // Load workout data
    await page.click('button:has-text("Continue to Workout")')
    await page.waitForURL('/workout')

    // Wait for workout to fully load
    await expect(
      page.locator('[data-testid^="exercise-card-"]').first()
    ).toBeVisible()

    // Navigate back and logout
    await page.goto('/program')
    await page.click('[data-testid="user-menu-button"]')
    await page.click('button:has-text("Log out")')

    await page.waitForURL('/login')

    // Login again as same user
    await page.fill('#email', user1.email)
    await page.fill('#password', user1.password)
    await page.click('[type="submit"]')

    await page.waitForURL('/program')

    // Navigate to workout
    await page.click('button:has-text("Continue to Workout")')
    await page.waitForURL('/workout')

    // Verify fresh data is loaded (no stale cache)
    await expect(
      page.locator('[data-testid^="exercise-card-"]').first()
    ).toBeVisible()

    // Check that no cache-related errors occurred
    const cacheErrors = consoleLogs.filter(
      (log) =>
        log.includes('cache') ||
        log.includes('stale') ||
        log.includes('conflict')
    )
    expect(cacheErrors).toHaveLength(0)
  })
})
