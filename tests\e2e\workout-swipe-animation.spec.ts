import { test, expect } from '@playwright/test'
import { loginAndNavigateToWorkout } from './helpers'

test.describe('Workout Swipe Animation', () => {
  test.beforeEach(async ({ page }) => {
    await loginAndNavigateToWorkout(page)
  })

  test('should animate card entry from opposite side after swipe', async ({
    page,
  }) => {
    // Navigate to exercise page v2
    await page.goto('/workout')
    await page.waitForSelector('[data-testid="exercise-card"]', {
      timeout: 30000,
    })

    // Click first exercise
    const firstExercise = page.locator('[data-testid="exercise-card"]').first()
    await firstExercise.click()

    // Wait for exercise page to load
    await page.waitForURL(/\/workout\/exercise-v2\/\d+/)
    await page.waitForSelector('[data-testid="current-set-card"]', {
      timeout: 30000,
    })

    // Get the swipeable card element
    const setCard = page.locator('[data-testid="current-set-card"]').first()

    // Capture initial position
    const initialBox = await setCard.boundingBox()
    expect(initialBox).toBeTruthy()

    // Simulate right swipe (complete)
    await setCard.hover()
    await page.mouse.down()
    await page.mouse.move(initialBox!.x + 200, initialBox!.y, { steps: 10 })
    await page.mouse.up()

    // Wait for animation to complete
    await page.waitForTimeout(500)

    // Verify the card animates from left side
    // The new card should start from x: -300 and animate to center
    const cardAfterSwipe = page
      .locator('[data-testid="current-set-card"]')
      .first()

    // Check that we're on the next set
    const setLabel = await cardAfterSwipe.locator('h2').textContent()
    expect(setLabel).toMatch(/Set \d+|Warmup/)

    // Now test left swipe
    const currentBox = await cardAfterSwipe.boundingBox()
    expect(currentBox).toBeTruthy()

    // Simulate left swipe (skip)
    await cardAfterSwipe.hover()
    await page.mouse.down()
    await page.mouse.move(currentBox!.x - 200, currentBox!.y, { steps: 10 })
    await page.mouse.up()

    // Wait for animation
    await page.waitForTimeout(500)

    // Verify we've moved to another set
    const finalSetLabel = await page
      .locator('.bg-surface-secondary.rounded-2xl')
      .first()
      .locator('h2')
      .textContent()
    expect(finalSetLabel).toMatch(/Set \d+|Warmup/)
  })

  test('should handle rapid swipes gracefully', async ({ page }) => {
    // Navigate to exercise page v2
    await page.goto('/workout')
    await page.waitForSelector('[data-testid="exercise-card"]', {
      timeout: 30000,
    })

    // Click first exercise
    const firstExercise = page.locator('[data-testid="exercise-card"]').first()
    await firstExercise.click()

    // Wait for exercise page to load
    await page.waitForURL(/\/workout\/exercise-v2\/\d+/)
    await page.waitForSelector('.bg-surface-secondary.rounded-2xl', {
      timeout: 30000,
    })

    const setCard = page.locator('.bg-surface-secondary.rounded-2xl').first()
    const box = await setCard.boundingBox()
    expect(box).toBeTruthy()

    // Perform rapid swipes
    // eslint-disable-next-line no-await-in-loop
    for (let i = 0; i < 3; i++) {
      // eslint-disable-next-line no-await-in-loop
      await setCard.hover()
      // eslint-disable-next-line no-await-in-loop
      await page.mouse.down()
      // eslint-disable-next-line no-await-in-loop
      await page.mouse.move(box!.x + 200, box!.y, { steps: 5 })
      // eslint-disable-next-line no-await-in-loop
      await page.mouse.up()
      // eslint-disable-next-line no-await-in-loop
      await page.waitForTimeout(100) // Small delay between swipes
    }

    // Verify the UI is still responsive
    const saveButton = page.locator('button:has-text("Save set")')
    await expect(saveButton).toBeVisible()
    await expect(saveButton).toBeEnabled()
  })

  test('should snap back when swipe is below threshold', async ({ page }) => {
    // Navigate to exercise page v2
    await page.goto('/workout')
    await page.waitForSelector('[data-testid="exercise-card"]', {
      timeout: 30000,
    })

    // Click first exercise
    const firstExercise = page.locator('[data-testid="exercise-card"]').first()
    await firstExercise.click()

    // Wait for exercise page to load
    await page.waitForURL(/\/workout\/exercise-v2\/\d+/)
    await page.waitForSelector('.bg-surface-secondary.rounded-2xl', {
      timeout: 30000,
    })

    const setCard = page.locator('.bg-surface-secondary.rounded-2xl').first()
    const box = await setCard.boundingBox()
    expect(box).toBeTruthy()

    // Get initial set label
    const initialSetLabel = await setCard.locator('h2').textContent()

    // Simulate small swipe (below threshold)
    await setCard.hover()
    await page.mouse.down()
    await page.mouse.move(box!.x + 50, box!.y, { steps: 5 }) // Only 50px, below 100px threshold
    await page.mouse.up()

    // Wait for potential animation
    await page.waitForTimeout(300)

    // Verify we're still on the same set
    const currentSetLabel = await setCard.locator('h2').textContent()
    expect(currentSetLabel).toBe(initialSetLabel)
  })
})
