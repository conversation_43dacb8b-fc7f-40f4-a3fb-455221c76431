import { test, expect } from '@playwright/test'

test.describe('Exercise Loading Transition', () => {
  test.beforeEach(async ({ page }) => {
    // Mock API responses
    await page.route('**/api/Account/AccountDr', async (route) => {
      await route.fulfill({
        status: 200,
        body: JSON.stringify({
          Token: 'test-token',
          UserId: 'test-user-id',
          RefreshToken: 'test-refresh',
          TokenExpires: new Date(Date.now() + ********).toISOString(),
          UserInfo: {
            Email: '<EMAIL>',
            MassUnit: 'lbs',
          },
        }),
      })
    })

    await page.route(
      '**/api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo',
      async (route) => {
        await route.fulfill({
          status: 200,
          body: JSON.stringify({
            GetUserProgramInfoResponseModel: {
              TodaysWorkoutId: '12345',
              TodaysWorkoutText: 'Push Day',
              IsInIntroWorkout: false,
            },
          }),
        })
      }
    )

    await page.route(
      '**/api/Workout/GetUserCustomizedCurrentWorkout',
      async (route) => {
        await route.fulfill({
          status: 200,
          body: JSON.stringify({
            Id: '12345',
            Label: 'Push Day',
            Exercises: [
              {
                Id: 1001,
                Label: 'Bench Press',
                IsBodyweight: false,
                Sets: 3,
                Reps: 10,
              },
              {
                Id: 1002,
                Label: 'Squats',
                IsBodyweight: false,
                Sets: 4,
                Reps: 8,
              },
            ],
          }),
        })
      }
    )

    // Mock recommendation response with delay to test loading state
    await page.route(
      '**/api/Workout/GetRecommendation?eid=*',
      async (route) => {
        const url = new URL(route.request().url())
        const exerciseId = url.searchParams.get('eid')

        // Add delay to simulate loading
        await new Promise((resolve) => setTimeout(resolve, 1000))

        await route.fulfill({
          status: 200,
          body: JSON.stringify({
            Id: parseInt(exerciseId || '0'),
            Weight: { Kg: 60, Lb: 132 },
            Reps: 10,
            WeightIncrement: 5,
            Sets: {
              warmup: [{ WarmUpReps: 8, WarmUpWeightSet: { Kg: 30, Lb: 66 } }],
              work: [
                { Reps: 10, Weight: { Kg: 60, Lb: 132 } },
                { Reps: 10, Weight: { Kg: 60, Lb: 132 } },
                { Reps: 10, Weight: { Kg: 60, Lb: 132 } },
              ],
            },
          }),
        })
      }
    )

    // Navigate to login
    await page.goto('http://localhost:3000/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')

    // Wait for navigation to workout page
    await page.waitForURL('**/workout')
  })

  test('should show golden checkmark loading screen only when loading recommendations', async ({
    page,
  }) => {
    // Navigate to first exercise
    await page.getByRole('button', { name: 'Start workout' }).click()

    // Should show loading screen with golden checkmark while loading recommendations
    await expect(page.getByTestId('golden-checkmark')).toBeVisible()
    await expect(page.getByText('loading')).toBeVisible()
    await expect(page.getByText('Bench Press...')).toBeVisible()

    // After recommendations load, should show exercise page
    await expect(page.getByTestId('exercise-page-container')).toBeVisible({
      timeout: 2000,
    })
    await expect(page.getByTestId('golden-checkmark')).not.toBeVisible()

    // Navigate to next exercise
    await page.getByRole('button', { name: 'Skip' }).click()
    await page.getByRole('button', { name: 'Continue' }).click()

    // Should show loading screen again for second exercise
    await expect(page.getByTestId('golden-checkmark')).toBeVisible()
    await expect(page.getByText('loading')).toBeVisible()
    await expect(page.getByText('Squats...')).toBeVisible()

    // After recommendations load, should show exercise page
    await expect(page.getByTestId('exercise-page-container')).toBeVisible({
      timeout: 2000,
    })
  })

  test('should not show loading screen when recommendation is already loaded', async ({
    page,
  }) => {
    // Pre-load recommendations by mocking instant response
    await page.route(
      '**/api/Workout/GetRecommendation?eid=*',
      async (route) => {
        const url = new URL(route.request().url())
        const exerciseId = url.searchParams.get('eid')

        // No delay - instant response
        await route.fulfill({
          status: 200,
          body: JSON.stringify({
            Id: parseInt(exerciseId || '0'),
            Weight: { Kg: 60, Lb: 132 },
            Reps: 10,
            WeightIncrement: 5,
            Sets: {
              warmup: [{ WarmUpReps: 8, WarmUpWeightSet: { Kg: 30, Lb: 66 } }],
              work: [
                { Reps: 10, Weight: { Kg: 60, Lb: 132 } },
                { Reps: 10, Weight: { Kg: 60, Lb: 132 } },
                { Reps: 10, Weight: { Kg: 60, Lb: 132 } },
              ],
            },
          }),
        })
      }
    )

    // Navigate to first exercise
    await page.getByRole('button', { name: 'Start workout' }).click()

    // Should go directly to exercise page without showing loading screen
    await expect(page.getByTestId('exercise-page-container')).toBeVisible({
      timeout: 1000,
    })
    await expect(page.getByTestId('golden-checkmark')).not.toBeVisible()
  })
})
