import { test, expect } from '@playwright/test'

test.describe('Auth Redirect on 401', () => {
  test.beforeEach(async ({ page }) => {
    // Login first to get authenticated
    await page.goto('/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'Dr123456')
    await page.click('button[type="submit"]')

    // Wait for successful login
    await page.waitForURL('/program', { timeout: 10000 })
  })

  test('should redirect to login when API returns 401 on workout page', async ({
    page,
  }) => {
    // Navigate to workout page
    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Mock API to return 401 for workout requests
    await page.route('**/GetTodaysWorkoutForDrMuscleWebApp', (route) => {
      route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Unauthorized' }),
      })
    })

    // Trigger a refresh that will cause 401
    await page.reload()

    // Should redirect to login page
    await page.waitForURL('/login', { timeout: 5000 })

    // Verify we're on login page
    expect(page.url()).toContain('/login')
    await expect(page.locator('h1')).toContainText('Dr. Muscle X')
  })

  test('should redirect to login when API returns 401 on exercise page', async ({
    page,
  }) => {
    // Navigate to workout page first
    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Click on first exercise if available
    const exerciseCard = page.locator('[data-testid="exercise-card"]').first()

    // Mock API to return 401 for exercise requests
    await page.route('**/GetRecommendation*', (route) => {
      route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Unauthorized' }),
      })
    })

    if (await exerciseCard.isVisible()) {
      await exerciseCard.click()

      // Should redirect to login page instead of showing error
      await page.waitForURL('/login', { timeout: 5000 })
      expect(page.url()).toContain('/login')
    }
  })

  test('should show login option in error boundary for auth errors', async ({
    page,
  }) => {
    // Navigate to workout page
    await page.goto('/workout')

    // Mock API to return 401
    await page.route('**/GetTodaysWorkoutForDrMuscleWebApp', (route) => {
      route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({ error_description: 'Your session has expired' }),
      })
    })

    // Reload to trigger 401
    await page.reload()

    // If error boundary shows before redirect, it should have login button
    const loginButton = page.locator('button:has-text("Go to Login")')
    if (await loginButton.isVisible({ timeout: 1000 })) {
      await loginButton.click()
      await page.waitForURL('/login')
    } else {
      // Otherwise should already be redirected
      await page.waitForURL('/login', { timeout: 5000 })
    }

    expect(page.url()).toContain('/login')
  })

  test('should handle 401 during workout save operation', async ({ page }) => {
    // Navigate to workout and start it
    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Start workout if button is available
    const startButton = page.locator('button:has-text("Start workout")')
    if (await startButton.isVisible()) {
      await startButton.click()
      await page.waitForTimeout(1000)

      // Navigate to first exercise
      const exerciseCard = page.locator('[data-testid="exercise-card"]').first()
      if (await exerciseCard.isVisible()) {
        await exerciseCard.click()
        await page.waitForLoadState('networkidle')

        // Mock save request to return 401
        await page.route('**/SaveSetLog', (route) => {
          route.fulfill({
            status: 401,
            contentType: 'application/json',
            body: JSON.stringify({ error: 'Unauthorized' }),
          })
        })

        // Try to save a set
        const saveButton = page.locator('button:has-text("Save")')
        if (await saveButton.isVisible()) {
          await saveButton.click()

          // Should redirect to login
          await page.waitForURL('/login', { timeout: 5000 })
          expect(page.url()).toContain('/login')
        }
      }
    }
  })
})
