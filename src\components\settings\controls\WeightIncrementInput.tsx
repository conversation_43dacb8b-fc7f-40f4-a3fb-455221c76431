'use client'

import { useState } from 'react'

interface WeightIncrementInputProps {
  value: number
  unit: 'kg' | 'lbs'
  onChange: (value: number) => void
  disabled?: boolean
}

export default function WeightIncrementInput({
  value,
  unit,
  onChange,
  disabled = false,
}: WeightIncrementInputProps) {
  // State to track intermediate input values (e.g., "2." while typing)
  const [inputValue, setInputValue] = useState<string | null>(null)
  const step = unit === 'kg' ? 0.5 : 1
  const min = unit === 'kg' ? 0.5 : 1
  const max = unit === 'kg' ? 10 : 20

  const handleChange = (newValue: number) => {
    const clamped = Math.max(min, Math.min(newValue, max))
    // Round to step
    const rounded = Math.round(clamped / step) * step
    onChange(rounded)
  }

  // Sanitize input to prevent XSS and ensure only valid characters
  const sanitizeInput = (input: string): string => {
    // Remove any non-numeric characters except decimal point and minus sign
    return input.replace(/[^0-9.-]/g, '')
  }

  const handleInputChange = (newInputValue: string) => {
    // Sanitize input for security
    const sanitizedValue = sanitizeInput(newInputValue)
    setInputValue(sanitizedValue)

    // Allow intermediate states like "2." without triggering onChange
    if (
      sanitizedValue === '' ||
      sanitizedValue.endsWith('.') ||
      sanitizedValue === '.'
    ) {
      // Don't trigger onChange for intermediate decimal states
      return
    }

    const numValue = parseFloat(sanitizedValue)
    if (!Number.isNaN(numValue)) {
      // Add boundary validation for security and UX
      if (numValue < min) {
        handleChange(min)
      } else if (numValue > max) {
        handleChange(max)
      } else {
        handleChange(numValue)
      }
    } else {
      // For invalid input, fallback to current value
      onChange(value)
    }
  }

  const handleBlur = () => {
    // Reset to formatted value when user finishes editing
    setInputValue(null)
  }

  const handleFocus = () => {
    // Start tracking raw input when user begins editing
    const currentDisplayValue =
      unit === 'kg' && value % 1 !== 0 ? value.toFixed(1) : value.toString()
    setInputValue(currentDisplayValue)
  }

  // Get formatted display value
  const getDisplayValue = () => {
    if (inputValue !== null) {
      return inputValue
    }
    return unit === 'kg' && value % 1 !== 0
      ? value.toFixed(1)
      : value.toString()
  }

  // Reset intermediate input state when value changes externally (e.g., arrow buttons)
  const handleArrowChange = (newValue: number) => {
    setInputValue(null) // Clear intermediate state
    handleChange(newValue)
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center">
        <button
          onClick={() => handleArrowChange(value - step)}
          disabled={disabled || value <= min}
          className="min-w-[52px] min-h-[52px] rounded-l-lg bg-surface-secondary text-text-primary disabled:opacity-50 flex items-center justify-center"
          aria-label="Decrease weight increment"
        >
          -
        </button>
        <div className="flex-1 relative">
          <input
            type="number"
            inputMode="decimal"
            value={getDisplayValue()}
            onChange={(e) => handleInputChange(e.target.value)}
            onFocus={handleFocus}
            onBlur={handleBlur}
            disabled={disabled}
            min={min}
            max={max}
            step={step}
            className="w-full min-h-[52px] px-4 py-2 bg-surface-primary border-y border-surface-tertiary text-center text-sm font-medium text-text-primary disabled:opacity-50"
            aria-label="Weight increment value"
            aria-describedby="weight-unit"
          />
          <span
            id="weight-unit"
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-text-secondary pointer-events-none"
          >
            {unit}
          </span>
        </div>
        <button
          onClick={() => handleArrowChange(value + step)}
          disabled={disabled || value >= max}
          className="min-w-[52px] min-h-[52px] rounded-r-lg bg-surface-secondary text-text-primary disabled:opacity-50 flex items-center justify-center"
          aria-label="Increase weight increment"
        >
          +
        </button>
      </div>
    </div>
  )
}
