/**
 * Unified Cache System - Main Entry Point
 *
 * This module exports the main cache system components and provides
 * a factory function to create a configured cache manager instance.
 */

// Core types and interfaces
// Import adapters and core classes for internal use
import { CacheManager } from './CacheManager'
import { MemoryAdapter } from './adapters/MemoryAdapter'
import { LocalStorageAdapter } from './adapters/LocalStorageAdapter'
import { SessionStorageAdapter } from './adapters/SessionStorageAdapter'

export type {
  CacheManager as ICacheManager,
  CacheAdapter,
  CacheOptions,
  CacheMetadata,
  CacheEntry,
  CacheStats,
  CacheManagerConfig,
  NamespaceStats,
} from './types'

// Error types
export {
  CacheError,
  CacheQuotaExceededError,
  CacheSerializationError,
  CacheAdapterError,
  CacheKeyError,
  DEFAULT_CACHE_CONFIG,
  CACHE_ENTRY_VERSION,
} from './types'

// Core implementation
export { CacheManager } from './CacheManager'

// Adapters
export { MemoryAdapter } from './adapters/MemoryAdapter'
export { BaseStorageAdapter } from './adapters/BaseStorageAdapter'
export { LocalStorageAdapter } from './adapters/LocalStorageAdapter'
export { SessionStorageAdapter } from './adapters/SessionStorageAdapter'

/**
 * Cache adapter types for factory function
 */
export type CacheAdapterType = 'memory' | 'localStorage' | 'sessionStorage'

/**
 * Configuration for cache factory
 */
export interface CacheFactoryConfig {
  /** Primary adapter type */
  adapter: CacheAdapterType
  /** Cache manager configuration */
  config?: Partial<import('./types').CacheManagerConfig>
  /** Adapter-specific configuration */
  adapterConfig?: Record<string, unknown>
}

/**
 * Create a configured cache manager instance
 *
 * This factory function creates a cache manager with the specified adapter
 * and configuration. It's the recommended way to create cache instances.
 *
 * @param factoryConfig Configuration for the cache system
 * @returns Configured CacheManager instance
 */
export function createCacheManager(factoryConfig: CacheFactoryConfig) {
  const {
    adapter: adapterType,
    config = {},
    adapterConfig = {},
  } = factoryConfig

  let adapter: import('./types').CacheAdapter

  switch (adapterType) {
    case 'memory':
      adapter = new MemoryAdapter(adapterConfig)
      break

    case 'localStorage':
      if (!LocalStorageAdapter.isAvailable()) {
        console.warn(
          'localStorage is not available, falling back to memory adapter'
        )
        adapter = new MemoryAdapter(adapterConfig)
      } else {
        adapter = new LocalStorageAdapter(adapterConfig)
      }
      break

    case 'sessionStorage':
      if (!SessionStorageAdapter.isAvailable()) {
        console.warn(
          'sessionStorage is not available, falling back to memory adapter'
        )
        adapter = new MemoryAdapter(adapterConfig)
      } else {
        adapter = new SessionStorageAdapter(adapterConfig)
      }
      break

    default:
      throw new Error(`Unknown adapter type: ${adapterType}`)
  }

  return new CacheManager(adapter, config)
}

/**
 * Create a default cache manager for the Dr. Muscle app
 *
 * This creates a cache manager with sensible defaults for the Dr. Muscle
 * application, using localStorage as the primary storage with memory fallback.
 */
export function createDefaultCacheManager() {
  return createCacheManager({
    adapter: 'localStorage',
    config: {
      defaultNamespace: 'drmuscle',
      defaultTTL: 24 * 60 * 60 * 1000, // 24 hours
      maxSize: 50 * 1024 * 1024, // 50MB
      enableStats: true,
      enableCompression: true,
      compressionThreshold: 1024, // 1KB
    },
    adapterConfig: {
      keyPrefix: 'drmuscle-cache:',
      enableCompression: true,
      compressionThreshold: 1024,
      maxEntries: 1000,
      enableAutoCleanup: true,
    },
  })
}

/**
 * Create a session-scoped cache manager
 *
 * This creates a cache manager that uses sessionStorage, suitable for
 * temporary data that should be cleared when the browser tab closes.
 */
export function createSessionCacheManager() {
  return createCacheManager({
    adapter: 'sessionStorage',
    config: {
      defaultNamespace: 'drmuscle-session',
      defaultTTL: 60 * 60 * 1000, // 1 hour
      maxSize: 10 * 1024 * 1024, // 10MB
      enableStats: true,
      enableCompression: false, // Less compression for session data
    },
    adapterConfig: {
      keyPrefix: 'drmuscle-session:',
      enableCompression: false,
      maxEntries: 500,
      enableAutoCleanup: true,
    },
  })
}

/**
 * Create a memory-only cache manager
 *
 * This creates a cache manager that only uses memory storage,
 * suitable for temporary caching during the current page session.
 */
export function createMemoryCacheManager() {
  return createCacheManager({
    adapter: 'memory',
    config: {
      defaultNamespace: 'drmuscle-memory',
      defaultTTL: 30 * 60 * 1000, // 30 minutes
      maxSize: 25 * 1024 * 1024, // 25MB
      enableStats: true,
    },
    adapterConfig: {
      maxEntries: 500,
      enableAutoCleanup: true,
      cleanupInterval: 5 * 60 * 1000, // 5 minutes
    },
  })
}

/**
 * Global cache manager instance
 *
 * This is a singleton instance that can be used throughout the application.
 * It's created lazily on first access.
 */
let globalCacheManager: import('./CacheManager').CacheManager | null = null

/**
 * Get the global cache manager instance
 *
 * This returns a singleton cache manager instance that can be used
 * throughout the application. The instance is created on first access
 * with default configuration.
 */
export function getGlobalCacheManager() {
  if (!globalCacheManager) {
    globalCacheManager = createDefaultCacheManager()
  }
  return globalCacheManager
}

/**
 * Set a custom global cache manager instance
 *
 * This allows you to override the default global cache manager with
 * a custom instance. Useful for testing or custom configurations.
 */
export function setGlobalCacheManager(
  manager: import('./CacheManager').CacheManager
) {
  globalCacheManager = manager
}

/**
 * Clear the global cache manager instance
 *
 * This clears the global cache manager instance, forcing it to be
 * recreated on next access. Useful for testing or cleanup.
 */
export function clearGlobalCacheManager() {
  if (globalCacheManager) {
    globalCacheManager.destroy()
    globalCacheManager = null
  }
}

/**
 * Utility function to check cache system health
 *
 * This function performs basic health checks on the cache system
 * and returns a report of the current state.
 */
export async function checkCacheHealth(
  manager?: import('./CacheManager').CacheManager
) {
  const cacheManager = manager || getGlobalCacheManager()

  const health = {
    isHealthy: true,
    errors: [] as string[],
    stats: null as import('./types').CacheStats | null,
    adapters: {
      localStorage: LocalStorageAdapter.isAvailable(),
      sessionStorage: SessionStorageAdapter.isAvailable(),
    },
  }

  try {
    // Test basic operations
    await cacheManager.set('__health_check__', 'test', { ttl: 1000 })
    const result = await cacheManager.get('__health_check__')

    if (result !== 'test') {
      health.isHealthy = false
      health.errors.push('Basic get/set operations failed')
    }

    await cacheManager.delete('__health_check__')

    // Get statistics
    health.stats = await cacheManager.getStats()
  } catch (error) {
    health.isHealthy = false
    health.errors.push(
      `Cache operations failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    )
  }

  return health
}

/**
 * Utility function to clear all cache data
 *
 * This function clears all cache data from all available storage mechanisms.
 * Use with caution as this will remove all cached data.
 */
export async function clearAllCacheData() {
  const results = {
    localStorage: false,
    sessionStorage: false,
    memory: false,
  }

  // Clear localStorage
  try {
    LocalStorageAdapter.clearAll()
    results.localStorage = true
  } catch (error) {
    console.warn('Failed to clear localStorage cache:', error)
  }

  // Clear sessionStorage
  try {
    SessionStorageAdapter.clearAll()
    results.sessionStorage = true
  } catch (error) {
    console.warn('Failed to clear sessionStorage cache:', error)
  }

  // Clear global cache manager
  try {
    if (globalCacheManager) {
      await globalCacheManager.clear()
      results.memory = true
    }
  } catch (error) {
    console.warn('Failed to clear memory cache:', error)
  }

  return results
}
