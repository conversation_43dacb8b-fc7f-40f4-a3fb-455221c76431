import { describe, it, expect } from 'vitest'
import { getStatsConfig } from '../swipeableStatConfig'
import type { UserStats } from '@/types'

describe('swipeableStatConfig formatLargeNumber integration', () => {
  describe('weight lifted formatting', () => {
    it('should format large weight values with K suffix', () => {
      const stats: UserStats = {
        weekStreak: 5,
        workoutsCompleted: 42,
        lbsLifted: 12345,
      }

      const config = getStatsConfig(stats, [], false, 'lbs')
      const weightStat = config[0]

      expect(weightStat.formatter).toBeDefined()
      // Currently fails - uses toLocaleString() instead of formatLargeNumber
      expect(weightStat.formatter!(12345)).toBe('12.3 K')
    })

    it('should format very large weight values with M suffix', () => {
      const stats: UserStats = {
        weekStreak: 12,
        workoutsCompleted: 150,
        lbsLifted: 2500000,
      }

      const config = getStatsConfig(stats, [], false, 'lbs')
      const weightStat = config[0]

      // Currently fails - uses toLocaleString() instead of formatLargeNumber
      expect(weightStat.formatter!(2500000)).toBe('2.5 M')
    })

    it('should format billion weight values with B suffix', () => {
      const stats: UserStats = {
        weekStreak: 25,
        workoutsCompleted: 500,
        lbsLifted: 1500000000,
      }

      const config = getStatsConfig(stats, [], false, 'lbs')
      const weightStat = config[0]

      // Currently fails - uses toLocaleString() instead of formatLargeNumber
      expect(weightStat.formatter!(1500000000)).toBe('1.5 B')
    })

    it('should not format small weight values', () => {
      const stats: UserStats = {
        weekStreak: 2,
        workoutsCompleted: 10,
        lbsLifted: 875,
      }

      const config = getStatsConfig(stats, [], false, 'lbs')
      const weightStat = config[0]

      // This should pass as both formatters handle small numbers similarly
      expect(weightStat.formatter!(875)).toBe('875')
    })

    it('should handle zero weight values', () => {
      const stats: UserStats = {
        weekStreak: 0,
        workoutsCompleted: 0,
        lbsLifted: 0,
      }

      const config = getStatsConfig(stats, [], false, 'lbs')
      const weightStat = config[0]

      expect(weightStat.formatter!(0)).toBe('0')
    })

    it('should format weight values for kg unit', () => {
      const stats: UserStats = {
        weekStreak: 8,
        workoutsCompleted: 100,
        lbsLifted: 567890,
      }

      const config = getStatsConfig(stats, [], false, 'kg')
      const weightStat = config[0]

      expect(weightStat.label).toBe('kg lifted')
      // Currently fails - uses toLocaleString() instead of formatLargeNumber
      expect(weightStat.formatter!(567890)).toBe('568 K')
    })
  })

  describe('other stat formatting should remain unchanged', () => {
    it('should not format workout stat', () => {
      const stats: UserStats = {
        weekStreak: 5,
        workoutsCompleted: 42000,
        lbsLifted: 10000,
      }

      const config = getStatsConfig(stats, [], false, 'lbs')
      const workoutStat = config[1]

      // Workouts should not have formatter defined
      expect(workoutStat.formatter).toBeUndefined()
    })

    it('should not format week streak stat', () => {
      const stats: UserStats = {
        weekStreak: 5000,
        workoutsCompleted: 42,
        lbsLifted: 10000,
      }

      const config = getStatsConfig(stats, [], false, 'lbs')
      const streakStat = config[2]

      // Week streak should not have formatter defined
      expect(streakStat.formatter).toBeUndefined()
    })
  })

  describe('edge cases', () => {
    it('should handle null stats gracefully', () => {
      const config = getStatsConfig(null, [0, 0, 0], false, 'lbs')
      const weightStat = config[0]

      expect(weightStat.value).toBe(0)
      expect(weightStat.formatter!(0)).toBe('0')
    })

    it('should use animated values when specified', () => {
      const stats: UserStats = {
        weekStreak: 5,
        workoutsCompleted: 42,
        lbsLifted: 12345,
      }

      const animatedValues = [100, 10, 99999]
      const config = getStatsConfig(stats, animatedValues, true, 'lbs')
      const weightStat = config[0]

      expect(weightStat.value).toBe(99999) // Should use animated value
      // Currently fails - uses toLocaleString() instead of formatLargeNumber
      expect(weightStat.formatter!(99999)).toBe('100 K')
    })

    it('should handle extremely large numbers', () => {
      const config = getStatsConfig(null, [], false, 'lbs')
      const weightStat = config[0]

      // Test with Number.MAX_SAFE_INTEGER
      const maxSafe = Number.MAX_SAFE_INTEGER
      // Currently fails - uses toLocaleString() instead of formatLargeNumber
      expect(weightStat.formatter!(maxSafe)).toMatch(/^\d+(\.\d+)?\s*[KMB]$/)
    })
  })
})
