import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import SettingsPageClient from '../SettingsPageClient'

// Mock the skeleton component
vi.mock('../SettingsPageSkeleton', () => ({
  default: () => <div data-testid="settings-skeleton">Loading...</div>,
}))

// Mock the settings header component
vi.mock('@/components/settings/SettingsHeader', () => ({
  default: () => <div data-testid="settings-header">Settings Header</div>,
}))

// Mock useAuthStore
const mockGetCachedUserInfo = vi.fn()
vi.mock('@/stores/authStore', () => ({
  useAuthStore: () => ({
    getCachedUserInfo: mockGetCachedUserInfo,
  }),
}))

describe('SettingsPageClient', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockGetCachedUserInfo.mockReturnValue({
      Email: '<EMAIL>',
      Name: 'Test User',
      MassUnit: 'lbs',
      IsNormalSet: true,
    })
  })

  it('renders settings immediately without loading state', () => {
    render(<SettingsPageClient />)

    // Settings should render immediately with default values
    expect(screen.getByTestId('settings-content')).toBeInTheDocument()
    expect(screen.queryByTestId('settings-skeleton')).not.toBeInTheDocument()
  })

  it('renders settings header after loading', async () => {
    render(<SettingsPageClient />)

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByTestId('settings-skeleton')).not.toBeInTheDocument()
    })

    expect(screen.getByTestId('settings-header')).toBeInTheDocument()
  })

  it('displays user settings when loaded', async () => {
    render(<SettingsPageClient />)

    // Settings content should be visible
    expect(screen.getByTestId('settings-content')).toBeInTheDocument()

    // Should have settings controls visible
    await waitFor(() => {
      // Check for one of the settings sections
      expect(screen.getByText(/Set Style/i)).toBeInTheDocument()
    })
  })

  it('handles missing user data gracefully', async () => {
    mockGetCachedUserInfo.mockReturnValue(null)

    render(<SettingsPageClient />)

    await waitFor(() => {
      expect(screen.queryByTestId('settings-skeleton')).not.toBeInTheDocument()
    })

    // Should still render without crashing
    expect(screen.getByTestId('settings-header')).toBeInTheDocument()
  })

  it('uses mobile-optimized layout', async () => {
    const { container } = render(<SettingsPageClient />)

    await waitFor(() => {
      expect(screen.queryByTestId('settings-skeleton')).not.toBeInTheDocument()
    })

    // Check for mobile-first classes
    const mainContent = container.querySelector('main')
    expect(mainContent).toHaveClass('min-h-screen')
    expect(mainContent).toHaveClass('bg-background')
  })

  it('displays settings in a scrollable container', async () => {
    const { container } = render(<SettingsPageClient />)

    await waitFor(() => {
      expect(screen.queryByTestId('settings-skeleton')).not.toBeInTheDocument()
    })

    // Should have scrollable content area
    const scrollContainer = container.querySelector(
      '[data-testid="settings-content"]'
    )
    expect(scrollContainer).toBeInTheDocument()
  })
})
