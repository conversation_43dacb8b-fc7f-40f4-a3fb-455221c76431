/**
 * Utility function to handle exercise setup and recommendation loading
 * Extracted from useExercisePageInitialization to reduce file size
 */

import { ExerciseWorkSetsModel } from '@/types/workout'
import { WorkoutSession } from '@/types/workoutSession'
import { WorkoutTemplateGroupModel, RecommendationModel } from '@/types'
import { debugLog } from '@/utils/debugLog'
import { RecommendationLoadingCoordinator } from '@/utils/RecommendationLoadingCoordinator'

interface SetupExerciseParams {
  exerciseId: number
  exercises: ExerciseWorkSetsModel[] | null
  isLoadingWorkout: boolean
  workoutSession: WorkoutSession | null
  todaysWorkout: WorkoutTemplateGroupModel[] | null
  setCurrentExerciseById: (id: number) => void
  loadingStates: Map<number, boolean>
  getCachedExerciseRecommendation: (
    id: number
  ) => RecommendationModel | null | undefined
  loadRecommendation: (id: number, label: string) => void
  updateExerciseWorkSets: (id: number, sets: unknown[]) => void
  setLoadingError: (error: Error | null) => void
  setIsInitializing: (value: boolean) => void
}

export async function setupExercise({
  exerciseId,
  exercises,
  isLoadingWorkout,
  workoutSession,
  todaysWorkout,
  setCurrentExerciseById,
  loadingStates,
  getCachedExerciseRecommendation,
  loadRecommendation,
  updateExerciseWorkSets,
  setLoadingError,
  setIsInitializing,
}: SetupExerciseParams): Promise<void> {
  try {
    if (!exerciseId) return

    debugLog('🎯 [ExercisePageClient] Setting up exercise', {
      exerciseId,
      hasExercises: !!exercises,
      exercisesCount: exercises?.length || 0,
      hasWorkoutSession: !!workoutSession,
      isInitializing: true,
    })

    // Skip validation if exercises haven't been loaded yet or if still loading workout
    // Also skip if we're still in the process of starting a workout (no session yet)
    // CRITICAL: Don't mark initialization complete until we have valid data
    if (
      !exercises ||
      exercises.length === 0 ||
      isLoadingWorkout ||
      (!workoutSession && todaysWorkout)
    ) {
      debugLog.log(
        '⏳ [ExercisePageClient] Exercises not loaded yet, workout still loading, or session being created, waiting...',
        {
          hasExercises: !!exercises,
          exercisesLength: exercises?.length || 0,
          isLoadingWorkout,
          hasWorkoutSession: !!workoutSession,
          hasTodaysWorkout: !!todaysWorkout,
        }
      )
      // Keep isInitializing true - don't mark complete yet
      return
    }

    // First validate that the exercise exists in the workout
    const exercise = exercises?.find(
      (ex) => Number(ex.Id) === Number(exerciseId)
    )

    if (!exercise) {
      debugLog.error(
        '❌ [ExercisePageClient] Exercise not found in exercises list',
        {
          exerciseId,
          availableIds: exercises?.map((ex) => ex.Id),
        }
      )
      throw new Error(`Exercise ${exerciseId} not found in workout`)
    }

    // Set current exercise by ID
    setCurrentExerciseById(exerciseId)

    // Check if recommendation is loaded for this exercise
    const hasRecommendation = getCachedExerciseRecommendation(exerciseId)
    const isLoadingRecommendation = loadingStates.get(exerciseId)

    debugLog('💭 [ExercisePageClient] Recommendation status', {
      exerciseId,
      hasRecommendation: !!hasRecommendation,
      isLoadingRecommendation,
    })

    // If no recommendation and not loading, trigger loading
    if (!hasRecommendation && !isLoadingRecommendation) {
      debugLog('📡 [ExercisePageClient] Loading recommendation...', {
        exerciseId,
        exerciseLabel: exercise.Label,
      })

      // Check with coordinator before loading
      try {
        const coordinator = RecommendationLoadingCoordinator.getInstance()
        if (coordinator.canStartLoading(exerciseId)) {
          // Start loading with 30 second timeout to prevent infinite skeletons
          coordinator.startLoading(exerciseId, { timeout: 30000 })
          loadRecommendation(exerciseId, exercise.Label || 'Exercise')
        } else {
          debugLog.log(
            '[ExercisePageClient] Coordinator blocked loading - already in progress',
            { exerciseId }
          )
        }
      } catch (error) {
        // If coordinator fails, proceed with loading anyway
        debugLog.error(
          '[ExercisePageClient] Coordinator error, proceeding with load',
          error
        )
        loadRecommendation(exerciseId, exercise.Label || 'Exercise')
      }
    }

    // Pre-load recommendation if not already loaded using alternative method
    if (!exercise.sets || exercise.sets.length === 0) {
      debugLog(
        '🔧 [ExercisePageClient] Updating exercise work sets to empty array'
      )
      updateExerciseWorkSets(exerciseId, [])
    }
  } catch (error: unknown) {
    debugLog.error('Failed to setup exercise:', error)
    // Auth errors are handled by API client interceptor
    setLoadingError(
      error instanceof Error ? error : new Error('Failed to setup exercise')
    )
    // Mark as complete on error
    setIsInitializing(false)
  } finally {
    // Only mark initialization as complete if we have validated the exercise
    if (exercises && exercises.length > 0 && workoutSession) {
      setIsInitializing(false)
    }
  }
}
