import React, { useEffect } from 'react'

interface WelcomeModalProps {
  isOpen: boolean
  onDismiss: () => void
}

/**
 * Welcome modal for users logging into the web app
 * Displays as an overlay introducing the web app benefits
 * Following the documented modal pattern from patterns-and-gotchas.md
 */
export function WelcomeModal({ isOpen, onDismiss }: WelcomeModalProps) {
  // Handle escape key
  useEffect(() => {
    if (!isOpen) return

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onDismiss()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isOpen, onDismiss])

  // Handle body scroll lock
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
      return () => {
        document.body.style.overflow = 'unset'
      }
    }
    return undefined
  }, [isOpen])

  if (!isOpen) return null

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/60 z-[70] animate-in fade-in duration-200"
        onClick={onDismiss}
        aria-hidden="true"
        data-testid="modal-backdrop"
      />

      {/* Modal */}
      <div
        role="dialog"
        aria-modal="true"
        aria-labelledby="welcome-modal-title"
        className="fixed inset-x-4 top-[20vh] max-w-md mx-auto z-[80] animate-in zoom-in-95 slide-in-from-bottom-2 duration-300"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Gold gradient border wrapper (matching ExerciseV2 pattern) */}
        <div className="p-[2px] bg-gradient-to-r from-brand-gold-start to-brand-gold-end rounded-3xl shadow-2xl">
          {/* Dark background content */}
          <div className="relative p-6 bg-[#0A0B0E] rounded-3xl">
            {/* Dismiss X button */}
            <button
              onClick={onDismiss}
              className="absolute top-4 right-4 text-text-tertiary hover:text-text-secondary transition-colors p-2"
              aria-label="Dismiss welcome message"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>

            <h2
              id="welcome-modal-title"
              className="text-xl font-semibold text-text-primary mb-4 pr-8"
            >
              Welcome to Dr. Muscle X!
            </h2>

            <div className="mb-6 space-y-3">
              <p className="text-text-secondary">
                🚀 Faster, smoother, bug-free
              </p>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span className="text-brand-primary">📱</span>
                  <p className="text-text-secondary text-sm">Offline support</p>
                </div>

                <div className="flex items-center gap-2">
                  <span className="text-brand-primary">🛡️</span>
                  <p className="text-text-secondary text-sm">
                    Rock-solid reliability
                  </p>
                </div>

                <div className="flex items-center gap-2">
                  <span className="text-brand-primary">⚡</span>
                  <p className="text-text-secondary text-sm">
                    Lightning-fast on any device
                  </p>
                </div>
              </div>
            </div>

            <button
              onClick={onDismiss}
              className="w-full bg-gradient-metallic-gold text-text-inverse font-semibold rounded-theme shadow-theme-md hover:shadow-theme-lg transition-theme-normal shimmer-hover text-shadow-sm"
              style={{ minHeight: '52px', minWidth: '52px' }}
            >
              Continue
            </button>
          </div>
        </div>
      </div>
    </>
  )
}
