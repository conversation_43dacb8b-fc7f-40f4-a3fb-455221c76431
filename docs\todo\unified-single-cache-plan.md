# Unified Cache Manager Implementation Plan

## Executive Summary

This document outlines a step-by-step plan to refactor the Dr. Muscle X PWA's multiple caching mechanisms into a single, unified cache management system. The goal is to create a single source of truth for all cache operations while maintaining performance and functionality.

**Important: Since the app is in alpha, we will NOT implement data migration. This allows us to make breaking changes and significantly simplifies the implementation from 6 weeks to 5 weeks.**

## Current State Analysis

### Existing Cache Mechanisms

1. **React Query Cache** - In-memory server state management
2. **Zustand Stores** - Persisted application state (auth, program, stats, workout)
3. **Custom localStorage Caches** - Workout cache, recommendation cache, swap cache
4. **Service Worker Caches** - PWA offline assets and API responses
5. **IndexedDB** - Large data storage (potentially unused)
6. **SessionStorage** - Session-specific data

### Problems with Current Architecture

- Data persists across user sessions
- Complex logout process requiring manual clearing of each cache
- No centralized monitoring or debugging
- Inconsistent cache invalidation strategies
- Difficult to track what data is cached where

## Architectural Blueprint

### Core Design Principles

1. **Single Interface** - All cache operations go through one API
2. **Type Safety** - Full TypeScript support with generics
3. **Pluggable Adapters** - Different storage backends for different needs
4. **Automatic Cleanup** - Built-in TTL and size management
5. **Observable** - Cache operations can be monitored and debugged
6. **Testable** - Easy to mock and test

### High-Level Architecture

```typescript
interface CacheManager {
  // Core operations
  get<T>(key: string, namespace?: string): Promise<T | null>
  set<T>(key: string, value: T, options?: CacheOptions): Promise<void>
  delete(key: string, namespace?: string): Promise<void>
  clear(namespace?: string): Promise<void>

  // Batch operations
  getMany<T>(keys: string[], namespace?: string): Promise<Map<string, T>>
  setMany<T>(entries: Map<string, T>, options?: CacheOptions): Promise<void>

  // Management
  getMetadata(key: string): Promise<CacheMetadata | null>
  getAllKeys(namespace?: string): Promise<string[]>
  getSize(namespace?: string): Promise<number>

  // Lifecycle
  invalidate(pattern: string | RegExp): Promise<void>
  cleanup(): Promise<void>
}

interface CacheAdapter {
  get<T>(key: string): Promise<T | null>
  set<T>(key: string, value: T, metadata: CacheMetadata): Promise<void>
  delete(key: string): Promise<void>
  clear(): Promise<void>
  getAllKeys(): Promise<string[]>
}
```

## Implementation Chunks

### Phase 1: Foundation (Week 1)

1. Core interfaces and types
2. Basic in-memory adapter
3. Cache manager skeleton
4. Unit test infrastructure

### Phase 2: Storage Adapters (Week 2)

1. localStorage adapter
2. SessionStorage adapter
3. IndexedDB adapter
4. Service Worker adapter

### Phase 3: Feature Parity (Week 3)

1. TTL and expiration
2. Size limits and eviction
3. Namespace support
4. Batch operations

### Phase 4: Integration (Week 4)

1. Replace existing cache calls
2. Update logout process
3. Add monitoring/debugging
4. Performance optimization

## Detailed Step Breakdown

### Phase 1: Foundation

#### Step 1.1: Core Types and Interfaces

- Define CacheManager interface
- Define CacheAdapter interface
- Define CacheOptions, CacheMetadata types
- Create error types
- **Size**: ~100 lines
- **Risk**: Low
- **Testing**: Type tests only

#### Step 1.2: In-Memory Adapter

- Implement basic Map-based adapter
- Add TTL support
- Add size tracking
- **Size**: ~150 lines
- **Risk**: Low
- **Testing**: Full unit tests

#### Step 1.3: Cache Manager Core

- Implement CacheManager with in-memory adapter
- Basic get/set/delete operations
- Error handling
- **Size**: ~200 lines
- **Risk**: Low
- **Testing**: Unit tests with mocks

#### Step 1.4: Test Infrastructure

- Create test utilities
- Mock factories
- Test helpers
- **Size**: ~100 lines
- **Risk**: Low
- **Testing**: Meta-tests for test utils

### Phase 2: Storage Adapters

#### Step 2.1: Base Storage Adapter

- Abstract class for browser storage
- Serialization/deserialization
- Error handling
- **Size**: ~100 lines
- **Risk**: Low
- **Testing**: Unit tests

#### Step 2.2: LocalStorage Adapter

- Extend base adapter for localStorage
- Handle quota errors
- Add compression for large values
- **Size**: ~150 lines
- **Risk**: Medium (quota handling)
- **Testing**: Unit + integration tests

#### Step 2.3: SessionStorage Adapter

- Extend base adapter for sessionStorage
- Share logic with localStorage
- **Size**: ~50 lines
- **Risk**: Low
- **Testing**: Unit tests

#### Step 2.4: IndexedDB Adapter

- Implement for large data
- Async operations
- Schema versioning
- **Size**: ~250 lines
- **Risk**: Medium (complexity)
- **Testing**: Integration tests

#### Step 2.5: Service Worker Adapter

- Cache API integration
- Network-first/cache-first strategies
- **Size**: ~200 lines
- **Risk**: High (browser compatibility)
- **Testing**: E2E tests needed

### Phase 3: Feature Parity

#### Step 3.1: TTL Implementation

- Add expiration checking
- Background cleanup
- **Size**: ~100 lines
- **Risk**: Low
- **Testing**: Unit tests with time mocking

#### Step 3.2: Size Management

- Track cache sizes
- Implement LRU eviction
- Configure limits per namespace
- **Size**: ~150 lines
- **Risk**: Medium (performance)
- **Testing**: Unit + performance tests

#### Step 3.3: Namespace Support

- Prefix keys by namespace
- Namespace-specific operations
- **Size**: ~100 lines
- **Risk**: Low
- **Testing**: Unit tests

#### Step 3.4: Batch Operations

- Optimize bulk reads/writes
- Transaction support for adapters
- **Size**: ~150 lines
- **Risk**: Medium (atomicity)
- **Testing**: Unit + integration tests

### Phase 4: Integration

#### Step 4.1: Replace Cache Calls

- Update all direct localStorage calls
- Update service references
- **Size**: ~500 lines (across files)
- **Risk**: High (wide impact)
- **Testing**: Existing tests + new integration

#### Step 4.2: Unified Logout

- Implement single clear operation
- Update logout flow
- **Size**: ~50 lines
- **Risk**: Medium
- **Testing**: E2E tests

#### Step 4.3: Monitoring & Debugging

- Add cache inspector
- Performance metrics
- Debug utilities
- **Size**: ~200 lines
- **Risk**: Low
- **Testing**: Unit tests

#### Step 4.4: Performance Optimization

- Add caching layers
- Optimize hot paths
- **Size**: ~150 lines
- **Risk**: Medium
- **Testing**: Performance tests

## Step Size Analysis

After review, the steps have been sized to be:

- **Small enough**: Each step is 50-300 lines, focused on one concern
- **Safe**: Critical paths have extensive testing requirements
- **Progressive**: Each builds on previous work
- **Integrated**: No orphaned code, each step connects to the system

## Risk Mitigation

1. **Feature Flags**: Each phase behind a flag
2. **Parallel Running**: Old and new systems run together initially
3. **Rollback Plan**: Each migration is reversible
4. **Extensive Testing**: TDD for all new code
5. **Gradual Rollout**: Test with internal users first

## Implementation Prompts

**Note: Since the app is in alpha, we can make breaking changes without data migration. This significantly simplifies implementation - we can replace the old system entirely without preserving existing cached data.**

### Prompt 1: Core Types and Interfaces

```text
I need to implement the foundation for a Unified Cache Manager system. This is step 1.1 of a larger refactoring effort.

Create TypeScript interfaces and types for a cache management system with these requirements:

1. A CacheManager interface with methods for:
   - get/set/delete operations (with generic type support)
   - clear operations (with optional namespace)
   - batch operations (getMany/setMany)
   - metadata and management (getMetadata, getAllKeys, getSize)
   - lifecycle methods (invalidate, cleanup)

2. A CacheAdapter interface that storage backends will implement:
   - Basic CRUD operations
   - Should be simpler than CacheManager (manager adds features on top)

3. Supporting types:
   - CacheOptions (ttl, namespace, priority, etc.)
   - CacheMetadata (size, created, accessed, expires, etc.)
   - CacheEntry<T> combining value and metadata
   - Custom error types for cache operations

4. The design should support:
   - Multiple storage backends (memory, localStorage, IndexedDB, etc.)
   - TTL/expiration
   - Size limits
   - Namespacing
   - Type safety with TypeScript generics

Place all types in src/cache/types.ts. Include comprehensive JSDoc comments. Write the code following the project's TypeScript standards (no any, explicit return types, etc.).
```

### Prompt 2: In-Memory Adapter Implementation

```text
Building on the cache types from step 1.1, implement an in-memory cache adapter.

Create src/cache/adapters/MemoryAdapter.ts that:

1. Implements the CacheAdapter interface
2. Uses a Map for storage
3. Stores both value and metadata for each entry
4. Implements automatic TTL expiration:
   - Check expiration on get
   - Clean expired entries periodically
5. Tracks total cache size in bytes (approximate)
6. Handles serialization (even though it's in-memory, for consistency)

Also create comprehensive tests in src/cache/adapters/__tests__/MemoryAdapter.test.ts:
- Test all CRUD operations
- Test TTL expiration
- Test size tracking
- Test clear operations
- Use vitest and follow project testing patterns

The implementation should be production-ready with proper error handling and edge cases covered.
```

### Prompt 3: Cache Manager Core Implementation

```text
Implement the core CacheManager class using the types from step 1.1 and MemoryAdapter from step 1.2.

Create src/cache/CacheManager.ts that:

1. Implements the CacheManager interface
2. Constructor accepts a CacheAdapter instance
3. Adds these features on top of the adapter:
   - Namespace support (prefix keys)
   - Default options (TTL, etc.)
   - Key validation
   - Value serialization/deserialization
   - Error handling with custom errors
   - Basic metrics tracking

4. For now, implement only:
   - get/set/delete
   - clear
   - getAllKeys
   Focus on getting these core operations solid.

Create tests in src/cache/__tests__/CacheManager.test.ts:
- Mock the adapter to test manager logic
- Test namespace isolation
- Test error scenarios
- Test serialization of different types

Keep the implementation focused and don't add features not yet needed. We'll add batch operations, metadata, etc. in later steps.
```

### Prompt 4: Test Infrastructure

```text
Create test utilities and helpers for the cache system.

Create src/cache/__tests__/test-utils.ts with:

1. Mock adapter factory that tracks all operations
2. Test data generators for various types
3. Time control utilities for testing TTL
4. Size calculation helpers
5. Assertion helpers for cache-specific checks

Create src/cache/__tests__/fixtures.ts with:
- Sample cache entries of different types
- Edge case values (large strings, nested objects, etc.)
- Performance test datasets

These utilities will be used across all cache tests to ensure consistency. Follow the project's test patterns and use vitest utilities where appropriate.
```

### Prompt 5: Base Storage Adapter

```text
Create a base class for browser storage adapters (localStorage, sessionStorage).

Create src/cache/adapters/BaseStorageAdapter.ts that:

1. Implements CacheAdapter interface
2. Abstract class with:
   - Constructor accepting a Storage object
   - Shared serialization/deserialization logic
   - Quota exceeded error handling
   - Key prefixing for isolation
   - Metadata storage strategy (store alongside or separate)

3. Protected methods that subclasses can override:
   - serializeValue
   - deserializeValue
   - handleQuotaExceeded

Create tests in src/cache/adapters/__tests__/BaseStorageAdapter.test.ts:
- Use a mock Storage implementation
- Test serialization of various types
- Test error handling
- Test storage limit scenarios

This base class will make implementing localStorage and sessionStorage adapters trivial.
```

### Prompt 6: LocalStorage Adapter

```text
Implement the LocalStorage adapter extending BaseStorageAdapter.

Create src/cache/adapters/LocalStorageAdapter.ts that:

1. Extends BaseStorageAdapter
2. Passes window.localStorage to parent
3. Implements quota handling:
   - Try compression on quota exceeded
   - Clear oldest entries if needed
   - Report what was cleared

4. Add static method to check available space

Create tests in src/cache/adapters/__tests__/LocalStorageAdapter.test.ts:
- Test with real localStorage (use beforeEach to clear)
- Test quota handling
- Test compression fallback
- Test interaction with existing localStorage data

Also create an integration test that verifies it works with CacheManager.
```

### Prompt 7: SessionStorage Adapter

```text
Implement the SessionStorage adapter, reusing logic from LocalStorageAdapter.

Create src/cache/adapters/SessionStorageAdapter.ts that:
1. Extends BaseStorageAdapter
2. Passes window.sessionStorage to parent
3. Reuses quota handling logic from LocalStorageAdapter (consider extracting shared logic)

Create minimal tests in src/cache/adapters/__tests__/SessionStorageAdapter.test.ts:
- Verify it uses sessionStorage
- Test that data doesn't persist across sessions (in test environment)
- Verify it behaves identically to LocalStorageAdapter otherwise

Keep this implementation minimal since it shares 95% of logic with LocalStorageAdapter.
```

### Prompt 8: IndexedDB Adapter

```text
Implement an IndexedDB adapter for large data storage.

Create src/cache/adapters/IndexedDBAdapter.ts that:

1. Implements CacheAdapter interface
2. Uses IndexedDB with:
   - Database name: 'DrMuscleCache'
   - Object store: 'cache'
   - Indexes on 'expires' and 'accessed' for cleanup

3. Handles:
   - Async initialization
   - Database versioning
   - Large value support (no size limits)
   - Transaction management
   - Browser compatibility

4. Features:
   - Lazy database connection
   - Automatic migration on schema change

Create tests in src/cache/adapters/__tests__/IndexedDBAdapter.test.ts:
- Mock IndexedDB or use fake-indexeddb
- Test async operations
- Test large values (1MB+)
- Test concurrent access
- Test database upgrade scenarios

Note: This is one of the more complex adapters. Focus on correctness over performance initially.
```

### Prompt 9: Service Worker Adapter

```text
Implement a Service Worker cache adapter for PWA functionality.

Create src/cache/adapters/ServiceWorkerAdapter.ts that:

1. Implements CacheAdapter interface
2. Uses Cache API with:
   - Custom cache name
   - Request/Response wrapping for non-HTTP data
   - Metadata in response headers

3. Features:
   - Works in both window and service worker contexts
   - Handles cache versioning
   - Network/cache strategies (configurable)

4. Special handling:
   - Convert cache data to/from Response objects
   - Store metadata in headers
   - Handle browser cache limits

Create tests in src/cache/adapters/__tests__/ServiceWorkerAdapter.test.ts:
- Mock Cache API
- Test Response conversion
- Test metadata storage
- Test cleanup of old caches

Also create an E2E test that verifies it works in a real service worker context.
```

### Prompt 10: TTL Implementation

```text
Add TTL (Time To Live) expiration support to the CacheManager.

Enhance src/cache/CacheManager.ts with:

1. Automatic expiration checking on get()
2. Background cleanup task:
   - Use setTimeout for periodic cleanup
   - Configurable cleanup interval
   - Clean expired entries from all namespaces

3. Methods to add:
   - startCleanup() - begins background task
   - stopCleanup() - stops background task
   - cleanupExpired() - manually trigger cleanup

4. Configuration in CacheOptions:
   - defaultTTL
   - cleanupInterval
   - maxExpiredEntries (cleanup trigger)

Update tests in src/cache/__tests__/CacheManager.test.ts:
- Test automatic expiration
- Test background cleanup
- Use fake timers for time control
- Test cleanup performance with many entries

Ensure cleanup is efficient and doesn't block the main thread.
```

### Prompt 11: Size Management and Eviction

```text
Implement cache size limits and LRU eviction in CacheManager.

Enhance src/cache/CacheManager.ts with:

1. Size tracking:
   - Track size per namespace
   - Calculate size of serialized values
   - Update metadata on each operation

2. Eviction policy (LRU):
   - Configure maxSize per namespace
   - When limit exceeded, evict least recently used
   - Optional callback when items evicted

3. New methods:
   - getSize(namespace?): Get current size
   - setMaxSize(namespace, bytes): Set limit
   - getEvictionStats(): Get eviction metrics

4. Size calculation:
   - Accurate for strings
   - Approximate for objects
   - Consider metadata overhead

Update tests to cover:
- Size limit enforcement
- LRU eviction order
- Eviction callbacks
- Size calculation accuracy

Make eviction efficient using a combination of access tracking and periodic cleanup.
```

### Prompt 12: Namespace Support

```text
Add full namespace support to isolate different cache domains.

Enhance src/cache/CacheManager.ts with:

1. Namespace features:
   - Automatic key prefixing: `${namespace}:${key}`
   - Namespace-scoped operations
   - Default namespace option
   - List namespaces method

2. Update all methods to support namespace parameter
3. Add namespace-specific methods:
   - clearNamespace(namespace)
   - getNamespaceKeys(namespace)
   - getNamespaceSize(namespace)
   - copyNamespace(from, to)

4. Configuration:
   - Per-namespace TTL defaults
   - Per-namespace size limits
   - Namespace access control (future)

Update tests:
- Test namespace isolation
- Test cross-namespace operations
- Test namespace listing
- Test default namespace behavior

Ensure namespaces are truly isolated and operations in one don't affect others.
```

### Prompt 13: Batch Operations

```text
Implement efficient batch operations for the CacheManager.

Add to src/cache/CacheManager.ts:

1. Batch methods:
   - getMany<T>(keys[], namespace?): Map<string, T>
   - setMany<T>(entries: Map<string, T>, options?)
   - deleteMany(keys[], namespace?)

2. Optimization:
   - Single adapter call where possible
   - Transaction support for adapters that support it
   - Parallel operations for adapters that don't

3. Atomic behavior:
   - All or nothing for setMany
   - Best effort for getMany/deleteMany
   - Return results/errors per key

4. Update adapters to support batch operations:
   - Add optional batch methods to interface
   - Implement in each adapter
   - Fall back to individual operations

Create new tests:
- Test batch performance vs individual
- Test atomicity
- Test partial failures
- Test with different adapters

Batch operations should significantly improve performance for bulk operations.
```

### Prompt 14: Zustand Persistence Adapter

```text
Create a Zustand persistence adapter that uses our CacheManager.

Create src/cache/integrations/ZustandPersistAdapter.ts:

1. Implement Zustand's StateStorage interface:
   - getItem(name): string | null
   - setItem(name, value): void
   - removeItem(name): void

2. Features:
   - Use CacheManager with configurable namespace
   - Direct replacement for localStorage adapter
   - Async operations with sync interface (use promises)
   - Custom serialization support

3. Factory function:
   - createZustandAdapter(cacheManager, options)
   - Options: namespace, ttl

Create tests in src/cache/integrations/__tests__/ZustandPersistAdapter.test.ts:
- Test with actual Zustand store
- Test persistence across instances
- Test error handling
- Test namespace isolation

This adapter will replace the current localStorage persistence in Zustand stores.
```

### Prompt 15: Update Zustand Stores

````text
Update all Zustand stores to use the new cache system.

Update these stores to use ZustandPersistAdapter:
1. src/stores/authStore.ts
2. src/stores/programStore.ts
3. src/stores/userStatsStore.ts
4. src/stores/workoutStore/index.ts

For each store:
- Replace localStorage with createZustandAdapter
- Use appropriate namespace (e.g., 'auth', 'program', 'stats', 'workout')
- Set reasonable TTLs based on data type
- Keep the same store API

Example for authStore:
```typescript
const cacheAdapter = createZustandAdapter(cacheManager, {
  namespace: 'auth',
  ttl: 7 * 24 * 60 * 60 * 1000 // 7 days
})

persist(
  (set, get) => ({ ... }),
  {
    name: 'drmuscle-auth',
    storage: cacheAdapter
  }
)
````

Update tests to verify stores work with new persistence.

Note: Since we're in alpha, we can break existing persisted data.

````

### Prompt 16: Update Custom Cache Services

```text
Update all custom cache services to use CacheManager.

Replace direct localStorage usage in:
1. src/utils/workoutCache.ts
2. src/services/cache/recommendationCache.ts
3. src/services/swapExerciseService.ts

For each service:
- Replace localStorage with CacheManager
- Use appropriate namespaces
- Set proper TTLs
- Keep the same external API

Example for WorkoutCache:
```typescript
export const WorkoutCache = {
  async get(): Promise<WorkoutTemplateGroupModel[] | null> {
    return cacheManager.get('workouts', 'workout-cache')
  },

  async set(data: WorkoutTemplateGroupModel[]): Promise<void> {
    await cacheManager.set('workouts', data, {
      namespace: 'workout-cache',
      ttl: 24 * 60 * 60 * 1000 // 24 hours
    })
  },

  async clear(): Promise<void> {
    await cacheManager.delete('workouts', 'workout-cache')
  }
}
````

Update tests for each service.

Note: Since we're in alpha, we don't need to preserve existing cached data.

````

### Prompt 17: React Query Integration

```text
Create React Query persistence integration with CacheManager.

Create src/cache/integrations/ReactQueryPersister.ts:

1. Implement React Query's Persister interface:
   - persistClient()
   - restoreClient()
   - removeClient()

2. Features:
   - Store query cache in CacheManager
   - Use 'react-query' namespace
   - Compress large queries
   - Selective persistence (filter queries)

3. Configuration:
   - TTL for cached queries
   - Max cache size
   - Query filters

Create tests and update src/utils/queryClient.ts to use the new persister.

Since we're in alpha, we don't need to migrate existing React Query cache.
````

### Prompt 18: Replace Direct Cache Calls

```text
Replace all direct localStorage/sessionStorage calls with CacheManager.

1. Search and replace in:
   - Services (userSettings.ts, etc.)
   - Utilities
   - Components
   - Any remaining direct usage

2. For each replacement:
   - Use appropriate namespace
   - Set reasonable TTL
   - Keep same key names for clarity

3. Common replacements:
   - localStorage.getItem(key) -> await cacheManager.get(key, namespace)
   - localStorage.setItem(key, value) -> await cacheManager.set(key, value, { namespace })
   - localStorage.removeItem(key) -> await cacheManager.delete(key, namespace)

Update all affected tests.

Since we're in alpha, we can break compatibility with existing stored data.
```

### Prompt 19: Unified Logout Implementation

````text
Simplify the logout process using the unified cache system.

1. Update src/utils/logout.ts:
   - Replace list of localStorage keys with cacheManager.clear()
   - Keep service worker cache clearing
   - Much simpler implementation

2. New logout implementation:
```typescript
export async function performCompleteLogout() {
  console.log('[Logout] Starting logout...')

  // Clear all CacheManager data
  await cacheManager.clear()

  // Clear service worker caches
  if ('caches' in window) {
    const cacheNames = await caches.keys()
    await Promise.all(cacheNames.map(name => caches.delete(name)))
  }

  console.log('[Logout] Logout complete')
}
````

3. Update tests to verify all data is cleared.

The new logout is much simpler than manually tracking all storage keys.

````

### Prompt 20: Cache Monitoring and Debugging

```text
Add monitoring and debugging capabilities to the cache system.

Create src/cache/CacheInspector.ts:

1. Features:
   - Real-time cache statistics
   - Cache content browser
   - Performance metrics
   - Debug logging

2. Methods:
   - getStats(): Overall cache statistics
   - inspect(namespace?): Detailed view
   - monitor(callback): Real-time updates
   - exportCache(): Export for debugging

3. Dev tools integration:
   - Window.cacheInspector in development
   - Chrome extension support (future)
   - Performance timing

Create src/cache/debug.ts:
- Debug utilities
- Logging configuration
- Test data generators

Add tests and integrate with development builds only.
````

### Prompt 21: Performance Optimization

```text
Optimize the cache system for production performance.

1. Add performance optimizations:
   - Memory pooling for frequent operations
   - Batch writes to storage
   - Lazy loading of adapters
   - Web Worker for background operations

2. Create benchmarks in src/cache/__benchmarks__/:
   - Compare with old system
   - Test under load
   - Memory usage tests
   - Mobile performance

3. Optimizations to implement:
   - Request coalescing
   - Write buffering
   - Compression for large values
   - Efficient serialization

4. Add performance monitoring:
   - Track operation times
   - Memory usage alerts
   - Slow operation warnings

Focus on optimizations that matter for mobile devices and slow networks.
```

### Prompt 22: Final Integration and Cleanup

```text
Complete the cache system integration and remove old code.

1. Final integration tasks:
   - Enable cache system globally
   - Remove feature flags
   - Update documentation
   - Add to architecture docs

2. Cleanup:
   - Remove old cache utilities
   - Remove direct storage access
   - Clean up old tests
   - Archive unused code

3. Verification:
   - Full E2E test suite passes
   - Performance benchmarks pass
   - No regressions in functionality
   - Lighthouse scores maintained

4. Documentation:
   - Update architecture.md
   - Add cache system guide
   - API documentation
   - Troubleshooting guide

This completes the unified cache system implementation.

Note: Since we're in alpha, we don't need migration guides or backwards compatibility notes.
```

## Success Criteria

1. **Single Source of Truth**: All cache operations go through CacheManager
2. **Simplified Logout**: One call clears all user data
3. **Better Performance**: No regression, ideally improvement
4. **Type Safety**: Full TypeScript support
5. **Testability**: >95% test coverage
6. **Monitoring**: Can inspect and debug cache state
7. **Clean Cutover**: Since we're in alpha, no migration needed

## Timeline Estimate

- Phase 1 (Foundation): 1 week
- Phase 2 (Adapters): 1 week
- Phase 3 (Features): 1 week
- Phase 4 (Integration): 1 week
- Buffer/Testing: 1 week

**Total: 5 weeks**

## Rollback Plan

Each phase can be rolled back independently:

1. **Feature Flags**: Each phase behind a flag
2. **Parallel Running**: Old system remains until fully migrated
3. **Data Backup**: Export all cache data before migration
4. **Gradual Rollout**: Test with internal users first
5. **Monitoring**: Watch for errors and performance issues

## Conclusion

This plan provides a safe, incremental path to unifying the cache system. Each step is small enough to be implemented and tested independently, while building towards a much cleaner architecture. The prompts are designed to guide an LLM through TDD implementation of each component.
