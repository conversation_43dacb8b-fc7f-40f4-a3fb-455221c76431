import { test, expect, devices } from '@playwright/test'

test.describe('StatusIndicators Browser-Specific Issues', () => {
  test('Safari iOS - Touch targets and flexbox gap fallback', async ({
    browser,
  }) => {
    const context = await browser.newContext({
      ...devices['iPhone 13'],
      locale: 'en-US',
    })
    const page = await context.newPage()

    await page.setContent(`
      <!DOCTYPE html>
      <html>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
        <style>
          .flex { display: flex; }
          .gap-2 { gap: 0.5rem; }
          @supports not (gap: 0.5rem) {
            .gap-2 > * + * { margin-left: 0.5rem; }
          }
          .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.75rem 1rem;
            min-height: 52px;
            background: #f3f4f6;
            border-radius: 0.5rem;
            touch-action: manipulation;
            -webkit-tap-highlight-color: transparent;
          }
        </style>
      </head>
      <body>
        <div class="flex gap-2">
          <div role="status" class="status-badge" aria-label="Test status">
            <span>Loading exercises... 75%</span>
          </div>
        </div>
      </body>
      </html>
    `)

    const statusBadge = page.locator('[role="status"]')
    await expect(statusBadge).toBeVisible()

    // Test touch target size (iOS requirement: 44px minimum)
    const boundingBox = await statusBadge.boundingBox()
    expect(boundingBox?.height).toBeGreaterThanOrEqual(44)

    // Test tap without 300ms delay
    const startTime = Date.now()
    await statusBadge.tap()
    const endTime = Date.now()
    expect(endTime - startTime).toBeLessThan(300)

    await context.close()
  })

  test('Chrome Android - Overscroll and gesture handling', async ({
    browser,
  }) => {
    const context = await browser.newContext({
      ...devices['Pixel 5'],
      locale: 'en-US',
    })
    const page = await context.newPage()

    await page.setContent(`
      <!DOCTYPE html>
      <html>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          body { overscroll-behavior: none; }
          .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.75rem 1rem;
            min-height: 52px;
            background: #f3f4f6;
            border-radius: 0.5rem;
          }
        </style>
      </head>
      <body>
        <div role="status" class="status-badge">
          <span>5 exercises ready</span>
        </div>
      </body>
      </html>
    `)

    // Test overscroll prevention
    const overscrollBehavior = await page.evaluate(() => {
      return window.getComputedStyle(document.body).overscrollBehavior
    })
    expect(overscrollBehavior).toBe('none')

    const statusBadge = page.locator('[role="status"]')
    await expect(statusBadge).toBeVisible()
    await statusBadge.tap()

    await context.close()
  })
})
