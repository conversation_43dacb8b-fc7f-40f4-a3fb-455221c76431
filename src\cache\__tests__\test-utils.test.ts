/**
 * Tests for cache test utilities
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import {
  MockCacheAdapter,
  TestDataGenerators,
  TimeController,
  SizeHelpers,
  CacheAssertions,
  createTestCacheManager,
} from './test-utils'
import { sampleEntries, createCacheEntry } from './fixtures'
import { CACHE_ENTRY_VERSION } from '../types'

describe('Test Utilities', () => {
  describe('MockCacheAdapter', () => {
    let mockAdapter: MockCacheAdapter

    beforeEach(() => {
      mockAdapter = new MockCacheAdapter()
    })

    it('should track operations', async () => {
      await mockAdapter.set(
        'key1',
        'value1',
        TestDataGenerators.createMetadata()
      )
      await mockAdapter.get('key1')
      await mockAdapter.delete('key1')

      const log = mockAdapter.getOperationLog()
      expect(log).toHaveLength(3)
      expect(log[0].operation).toBe('set')
      expect(log[1].operation).toBe('get')
      expect(log[2].operation).toBe('delete')
    })

    it('should support spy methods', async () => {
      await mockAdapter.set(
        'key1',
        'value1',
        TestDataGenerators.createMetadata()
      )

      expect(mockAdapter.setSpy).toHaveBeenCalledWith(
        'key1',
        'value1',
        expect.any(Object)
      )
    })

    it('should simulate TTL expiration', async () => {
      const expiredMetadata = TestDataGenerators.createMetadata({
        expires: Date.now() - 1000, // Expired 1 second ago
      })

      await mockAdapter.set('expired-key', 'value', expiredMetadata)
      const result = await mockAdapter.get('expired-key')

      expect(result).toBeNull()
    })

    it('should provide test utilities', () => {
      expect(mockAdapter.getStorageSize()).toBe(0)
      expect(mockAdapter.hasKey('nonexistent')).toBe(false)

      mockAdapter.clearOperationLog()
      expect(mockAdapter.getOperationLog()).toHaveLength(0)
    })
  })

  describe('TestDataGenerators', () => {
    it('should create valid metadata', () => {
      const metadata = TestDataGenerators.createMetadata()

      expect(metadata.size).toBeGreaterThan(0)
      expect(metadata.created).toBeGreaterThan(0)
      expect(metadata.accessed).toBeGreaterThan(0)
      expect(metadata.namespace).toBe('test')
      expect(metadata.version).toBe(CACHE_ENTRY_VERSION)
    })

    it('should create test data of various types', () => {
      const data = TestDataGenerators.createTestData()

      expect(typeof data.string).toBe('string')
      expect(typeof data.number).toBe('number')
      expect(typeof data.boolean).toBe('boolean')
      expect(Array.isArray(data.array)).toBe(true)
      expect(typeof data.object).toBe('object')
      expect(data.largeString.length).toBe(10000)
    })

    it('should create edge case data', () => {
      const edgeData = TestDataGenerators.createEdgeCaseData()

      expect(edgeData.emptyString).toBe('')
      expect(Array.isArray(edgeData.emptyArray)).toBe(true)
      expect(edgeData.emptyArray.length).toBe(0)
      expect(typeof edgeData.emptyObject).toBe('object')
      expect(edgeData.longKey.length).toBe(1000)
    })

    it('should create performance data', () => {
      const perfData = TestDataGenerators.createPerformanceData()

      expect(perfData.small.length).toBe(10)
      expect(perfData.medium.length).toBe(100)
      expect(perfData.large.length).toBe(1000)
      expect(perfData.xlarge.length).toBe(10000)
    })

    it('should create deeply nested objects', () => {
      const nested = TestDataGenerators.createDeeplyNestedObject(5)

      expect(nested.level).toBe(5)
      expect(nested.nested.level).toBe(4)
      expect(nested.nested.nested.level).toBe(3)
    })
  })

  describe('TimeController', () => {
    let timeController: TimeController

    beforeEach(() => {
      timeController = new TimeController()
    })

    afterEach(() => {
      timeController.stop()
    })

    it('should control time', () => {
      const startTime = 1640995200000 // 2022-01-01 00:00:00 UTC
      timeController.start(startTime)

      expect(Date.now()).toBe(startTime)

      timeController.advance(5000)
      expect(Date.now()).toBe(startTime + 5000)

      timeController.setTime(startTime + 10000)
      expect(Date.now()).toBe(startTime + 10000)
    })

    it('should restore original Date.now when stopped', () => {
      const originalNow = Date.now
      timeController.start()
      timeController.stop()

      expect(Date.now).toBe(originalNow)
    })
  })

  describe('SizeHelpers', () => {
    it('should calculate size of values', () => {
      expect(SizeHelpers.calculateSize('hello')).toBeGreaterThan(0)
      expect(SizeHelpers.calculateSize({ key: 'value' })).toBeGreaterThan(0)
      expect(SizeHelpers.calculateSize([1, 2, 3])).toBeGreaterThan(0)
    })

    it('should generate data of specific size', () => {
      const data = SizeHelpers.generateDataOfSize(1000)
      const actualSize = SizeHelpers.calculateSize(data)

      expect(SizeHelpers.isWithinRange(actualSize, 1000, 0.1)).toBe(true)
    })

    it('should check if size is within range', () => {
      expect(SizeHelpers.isWithinRange(100, 100, 0.1)).toBe(true)
      expect(SizeHelpers.isWithinRange(105, 100, 0.1)).toBe(true)
      expect(SizeHelpers.isWithinRange(95, 100, 0.1)).toBe(true)
      expect(SizeHelpers.isWithinRange(120, 100, 0.1)).toBe(false)
    })
  })

  describe('CacheAssertions', () => {
    it('should assert valid metadata', () => {
      const metadata = TestDataGenerators.createMetadata()

      expect(() => CacheAssertions.assertValidMetadata(metadata)).not.toThrow()
    })

    it('should assert TTL correctly', () => {
      const now = Date.now()
      const ttl = 5000
      const metadata = TestDataGenerators.createMetadata({
        created: now,
        expires: now + ttl,
      })

      expect(() => CacheAssertions.assertTTL(metadata, ttl)).not.toThrow()
    })

    it('should assert valid stats', () => {
      const stats = {
        entryCount: 5,
        totalSize: 1000,
        hits: 10,
        misses: 2,
        hitRatio: 0.83,
        evictions: 0,
        expirations: 1,
        namespaces: {},
      }

      expect(() => CacheAssertions.assertValidStats(stats)).not.toThrow()
    })
  })

  describe('createTestCacheManager', () => {
    it('should create a test cache manager with mock adapter', async () => {
      const { manager, mockAdapter } = await createTestCacheManager()

      expect(manager).toBeDefined()
      expect(mockAdapter).toBeInstanceOf(MockCacheAdapter)
    })

    it('should accept custom configuration', async () => {
      const { manager } = await createTestCacheManager({
        defaultNamespace: 'custom',
      })

      expect(manager).toBeDefined()
    })
  })

  describe('Fixtures', () => {
    it('should provide sample entries', () => {
      expect(sampleEntries.string.key).toBe('string-key')
      expect(sampleEntries.string.value).toBe('Hello, World!')
      expect(sampleEntries.number.value).toBe(42)
      expect(sampleEntries.boolean.value).toBe(true)
    })

    it('should create cache entries', () => {
      const entry = createCacheEntry('test-value', { namespace: 'custom' })

      expect(entry.value).toBe('test-value')
      expect(entry.metadata.namespace).toBe('custom')
      expect(entry.metadata.version).toBe(CACHE_ENTRY_VERSION)
    })
  })
})
