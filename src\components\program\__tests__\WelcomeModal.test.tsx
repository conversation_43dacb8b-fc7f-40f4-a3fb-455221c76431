import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { WelcomeModal } from '../WelcomeModal'

describe('WelcomeModal', () => {
  const mockOnDismiss = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('displays welcome modal with correct content', () => {
    render(<WelcomeModal isOpen onDismiss={mockOnDismiss} />)

    expect(screen.getByText('Welcome to Dr. Muscle X!')).toBeInTheDocument()
    expect(
      screen.getByText('🚀 Faster, smoother, bug-free')
    ).toBeInTheDocument()
  })

  it('shows web app benefits', () => {
    render(<WelcomeModal isOpen onDismiss={mockOnDismiss} />)

    expect(screen.getByText('Lightning-fast on any device')).toBeInTheDocument()
    expect(screen.getByText('Offline support')).toBeInTheDocument()
    expect(screen.getByText('Rock-solid reliability')).toBeInTheDocument()
  })

  it('displays Continue button with proper touch target', () => {
    render(<WelcomeModal isOpen onDismiss={mockOnDismiss} />)

    const button = screen.getByRole('button', { name: /continue/i })
    expect(button).toBeInTheDocument()
    expect(button).toHaveStyle({ minHeight: '52px' })
  })

  it('calls onDismiss when Continue button is clicked', () => {
    render(<WelcomeModal isOpen onDismiss={mockOnDismiss} />)

    const button = screen.getByRole('button', { name: /continue/i })
    fireEvent.click(button)

    expect(mockOnDismiss).toHaveBeenCalledTimes(1)
  })

  it('calls onDismiss when backdrop is clicked', () => {
    render(<WelcomeModal isOpen onDismiss={mockOnDismiss} />)

    const backdrop = screen.getByTestId('modal-backdrop')
    fireEvent.click(backdrop)

    expect(mockOnDismiss).toHaveBeenCalledTimes(1)
  })

  it('calls onDismiss when Escape key is pressed', () => {
    render(<WelcomeModal isOpen onDismiss={mockOnDismiss} />)

    fireEvent.keyDown(document, { key: 'Escape' })

    expect(mockOnDismiss).toHaveBeenCalledTimes(1)
  })

  it('prevents event propagation when clicking modal content', () => {
    render(<WelcomeModal isOpen onDismiss={mockOnDismiss} />)

    const modalContent = screen.getByRole('dialog')
    fireEvent.click(modalContent)

    expect(mockOnDismiss).not.toHaveBeenCalled()
  })

  it('locks body scroll when open', () => {
    const { rerender } = render(
      <WelcomeModal isOpen={false} onDismiss={mockOnDismiss} />
    )

    expect(document.body.style.overflow).not.toBe('hidden')

    rerender(<WelcomeModal isOpen onDismiss={mockOnDismiss} />)
    expect(document.body.style.overflow).toBe('hidden')

    rerender(<WelcomeModal isOpen={false} onDismiss={mockOnDismiss} />)
    expect(document.body.style.overflow).toBe('unset')
  })

  it('does not render when isOpen is false', () => {
    render(<WelcomeModal isOpen={false} onDismiss={mockOnDismiss} />)

    expect(screen.queryByRole('dialog')).not.toBeInTheDocument()
  })

  it('has proper ARIA attributes', () => {
    render(<WelcomeModal isOpen onDismiss={mockOnDismiss} />)

    const dialog = screen.getByRole('dialog')
    expect(dialog).toHaveAttribute('aria-modal', 'true')
    expect(dialog).toHaveAttribute('aria-labelledby', 'welcome-modal-title')
  })

  it('uses proper theme tokens for styling', () => {
    render(<WelcomeModal isOpen onDismiss={mockOnDismiss} />)

    const dialog = screen.getByRole('dialog')
    // Modal now has dark background matching ExerciseV2 pattern
    const modalContent = dialog.querySelector('.bg-\\[\\#0A0B0E\\]')
    expect(modalContent).toBeInTheDocument()
  })

  it('has golden gradient button styling', () => {
    render(<WelcomeModal isOpen onDismiss={mockOnDismiss} />)

    const button = screen.getByRole('button', { name: /continue/i })
    expect(button).toHaveClass('bg-gradient-metallic-gold')
    expect(button).toHaveClass('text-text-inverse')
  })

  it('animates in smoothly', () => {
    render(<WelcomeModal isOpen onDismiss={mockOnDismiss} />)

    const dialog = screen.getByRole('dialog')
    expect(dialog).toHaveClass('animate-in')
    expect(dialog).toHaveClass('zoom-in-95')
    expect(dialog).toHaveClass('slide-in-from-bottom-2')
  })

  it('has correct z-index layering', () => {
    render(<WelcomeModal isOpen onDismiss={mockOnDismiss} />)

    const backdrop = screen.getByTestId('modal-backdrop')
    const dialog = screen.getByRole('dialog')

    expect(backdrop).toHaveClass('z-[70]')
    expect(dialog).toHaveClass('z-[80]')
  })

  it('displays dismiss X button', () => {
    render(<WelcomeModal isOpen onDismiss={mockOnDismiss} />)

    const dismissButton = screen.getByLabelText('Dismiss welcome message')
    expect(dismissButton).toBeInTheDocument()
  })

  it('calls onDismiss when X button is clicked', () => {
    render(<WelcomeModal isOpen onDismiss={mockOnDismiss} />)

    const dismissButton = screen.getByLabelText('Dismiss welcome message')
    fireEvent.click(dismissButton)

    expect(mockOnDismiss).toHaveBeenCalledTimes(1)
  })
})
