'use client'

import { useNetworkStatus } from '@/hooks/useNetworkStatus'
import { useOfflineStore } from '@/stores/offlineStore'
import { cn } from '@/lib/utils'

/**
 * OfflineIndicator Component
 *
 * Shows the current offline status and sync state in the app header
 */
export function OfflineIndicator() {
  const { isOnline, isOffline } = useNetworkStatus()
  const { syncStatus, queuedRequestsCount, isOfflineWorkoutLoaded } =
    useOfflineStore()

  // Don't show anything if online and no offline workout loaded
  if (isOnline && !isOfflineWorkoutLoaded && queuedRequestsCount === 0) {
    return null
  }

  const getStatusInfo = () => {
    if (isOffline) {
      return {
        text: 'Offline',
        icon: '📴',
        variant: 'offline' as const,
        description: 'You are currently offline',
      }
    }

    if (syncStatus === 'syncing') {
      return {
        text: 'Syncing...',
        icon: '🔄',
        variant: 'syncing' as const,
        description: 'Syncing workout data',
      }
    }

    if (queuedRequestsCount > 0) {
      return {
        text: `${queuedRequestsCount} pending`,
        icon: '⏳',
        variant: 'pending' as const,
        description: `${queuedRequestsCount} requests waiting to sync`,
      }
    }

    if (isOfflineWorkoutLoaded) {
      return {
        text: 'Offline Ready',
        icon: '💾',
        variant: 'ready' as const,
        description: 'Workout cached for offline use',
      }
    }

    return null
  }

  const statusInfo = getStatusInfo()
  if (!statusInfo) return null

  const variantStyles = {
    offline: 'bg-red-50 text-red-700 border-red-200',
    syncing: 'bg-blue-50 text-blue-700 border-blue-200',
    pending: 'bg-yellow-50 text-yellow-700 border-yellow-200',
    ready: 'bg-green-50 text-green-700 border-green-200',
  }

  return (
    <div
      className={cn(
        'flex items-center gap-2 px-3 py-1 rounded-full border text-xs font-medium',
        'transition-all duration-200',
        variantStyles[statusInfo.variant]
      )}
      title={statusInfo.description}
      role="status"
      aria-label={statusInfo.description}
    >
      <span className="text-sm" role="img" aria-hidden="true">
        {statusInfo.icon}
      </span>
      <span>{statusInfo.text}</span>
    </div>
  )
}

/**
 * Compact version for mobile/small screens
 */
export function OfflineIndicatorCompact() {
  const { isOnline, isOffline } = useNetworkStatus()
  const { syncStatus, queuedRequestsCount, isOfflineWorkoutLoaded } =
    useOfflineStore()

  // Don't show anything if online and no offline workout loaded
  if (isOnline && !isOfflineWorkoutLoaded && queuedRequestsCount === 0) {
    return null
  }

  const getStatusInfo = () => {
    if (isOffline) {
      return {
        icon: '📴',
        variant: 'offline' as const,
        description: 'You are currently offline',
      }
    }

    if (syncStatus === 'syncing') {
      return {
        icon: '🔄',
        variant: 'syncing' as const,
        description: 'Syncing workout data',
      }
    }

    if (queuedRequestsCount > 0) {
      return {
        icon: '⏳',
        variant: 'pending' as const,
        description: `${queuedRequestsCount} requests waiting to sync`,
      }
    }

    if (isOfflineWorkoutLoaded) {
      return {
        icon: '💾',
        variant: 'ready' as const,
        description: 'Workout cached for offline use',
      }
    }

    return null
  }

  const statusInfo = getStatusInfo()
  if (!statusInfo) return null

  const variantStyles = {
    offline: 'bg-red-100 text-red-600',
    syncing: 'bg-blue-100 text-blue-600',
    pending: 'bg-yellow-100 text-yellow-600',
    ready: 'bg-green-100 text-green-600',
  }

  return (
    <div
      className={cn(
        'flex items-center justify-center w-8 h-8 rounded-full',
        'transition-all duration-200',
        variantStyles[statusInfo.variant]
      )}
      title={statusInfo.description}
      role="status"
      aria-label={statusInfo.description}
    >
      <span className="text-sm" role="img" aria-hidden="true">
        {statusInfo.icon}
      </span>
    </div>
  )
}

/**
 * Toast-style notification for offline mode changes
 */
export function OfflineToast({
  message,
  type = 'info',
  onDismiss,
}: {
  message: string
  type?: 'info' | 'warning' | 'success' | 'error'
  onDismiss?: () => void
}) {
  const typeStyles = {
    info: 'bg-blue-50 text-blue-800 border-blue-200',
    warning: 'bg-yellow-50 text-yellow-800 border-yellow-200',
    success: 'bg-green-50 text-green-800 border-green-200',
    error: 'bg-red-50 text-red-800 border-red-200',
  }

  const typeIcons = {
    info: 'ℹ️',
    warning: '⚠️',
    success: '✅',
    error: '❌',
  }

  return (
    <div
      className={cn(
        'fixed top-4 right-4 z-50 max-w-sm',
        'flex items-center gap-3 p-4 rounded-lg border shadow-lg',
        'animate-in slide-in-from-right-full duration-300',
        typeStyles[type]
      )}
      role="alert"
      aria-live="polite"
    >
      <span className="text-lg" role="img" aria-hidden="true">
        {typeIcons[type]}
      </span>
      <div className="flex-1">
        <p className="text-sm font-medium">{message}</p>
      </div>
      {onDismiss && (
        <button
          onClick={onDismiss}
          className="text-current opacity-70 hover:opacity-100 transition-opacity"
          aria-label="Dismiss notification"
        >
          ✕
        </button>
      )}
    </div>
  )
}
