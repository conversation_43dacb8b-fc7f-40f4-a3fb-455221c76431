import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { IOSNavigationBar } from '../IOSNavigationBar'

describe('IOSNavigationBar - Gradient Background', () => {
  describe('Exercise Page Gradient', () => {
    it('should apply gradient background when isExercisePage is true', () => {
      const { container } = render(
        <IOSNavigationBar title="Bench Press" isExercisePage />
      )
      const header = container.querySelector('header')

      // Should have gradient classes instead of default background
      expect(header?.classList.contains('bg-gradient-to-r')).toBe(true)
      expect(header?.classList.contains('from-brand-gold-start')).toBe(true)
      expect(header?.classList.contains('to-brand-gold-end')).toBe(true)

      // Should not have default background classes
      expect(header?.classList.contains('bg-bg-primary')).toBe(false)
      expect(header?.classList.contains('bg-bg-primary/80')).toBe(false)
    })

    it('should use inverse text color for readability on gradient', () => {
      render(<IOSNavigationBar title="Squat" isExercisePage />)
      const titleElement = screen.getByText('Squat')

      // Title should use inverse color for contrast against gradient
      expect(titleElement.classList.contains('text-text-inverse')).toBe(true)
      expect(titleElement.classList.contains('text-text-primary')).toBe(false)
    })

    it('should style back button for gradient background', () => {
      render(
        <IOSNavigationBar title="Deadlift" showBackButton isExercisePage />
      )
      const backButton = screen.getByLabelText('Go back')
      const chevronIcon = backButton.querySelector('svg')

      // Back button should have appropriate styling for gradient
      expect(backButton.classList.contains('hover:bg-white/20')).toBe(true)
      expect(backButton.classList.contains('hover:bg-bg-secondary')).toBe(false)

      // Icon should use inverse color - check the svg element directly
      expect(chevronIcon?.classList.contains('text-text-inverse')).toBe(true)
    })

    it('should maintain border visibility on gradient', () => {
      const { container } = render(
        <IOSNavigationBar title="Rows" isExercisePage />
      )
      const header = container.querySelector('header')

      // Should have border but with different opacity for gradient
      expect(header?.classList.contains('border-b')).toBe(true)
      expect(header?.classList.contains('border-white/20')).toBe(true)
      expect(header?.classList.contains('border-brand-primary/10')).toBe(false)
    })
  })

  describe('Non-Exercise Page Default Style', () => {
    it('should use default background when isExercisePage is false', () => {
      const { container } = render(
        <IOSNavigationBar title="Workout Overview" isExercisePage={false} />
      )
      const header = container.querySelector('header')

      // Should have default background classes
      expect(header?.classList.contains('bg-bg-primary')).toBe(true)
      expect(header?.classList.contains('bg-bg-primary/80')).toBe(true)

      // Should not have gradient classes
      expect(header?.classList.contains('bg-gradient-to-r')).toBe(false)
      expect(header?.classList.contains('from-brand-gold-start')).toBe(false)
      expect(header?.classList.contains('to-brand-gold-end')).toBe(false)
    })

    it('should use default background when isExercisePage is undefined', () => {
      const { container } = render(<IOSNavigationBar title="Home" />)
      const header = container.querySelector('header')

      // Should have default background classes
      expect(header?.classList.contains('bg-bg-primary')).toBe(true)
      expect(header?.classList.contains('bg-bg-primary/80')).toBe(true)
    })
  })

  describe('Right Element Styling', () => {
    it('should style right element appropriately on gradient', () => {
      const rightElement = <button className="text-text-primary">Menu</button>
      render(
        <IOSNavigationBar
          title="Exercise"
          rightElement={rightElement}
          isExercisePage
        />
      )

      // Note: The component should handle styling internally
      // This tests that right elements are rendered correctly
      const button = screen.getByText('Menu')
      expect(button).toBeInTheDocument()
    })
  })

  describe('Edge Cases', () => {
    it('should handle very long titles with gradient', () => {
      const longTitle = 'Very Long Exercise Name That Should Be Truncated'
      render(<IOSNavigationBar title={longTitle} isExercisePage />)
      const titleElement = screen.getByText(longTitle)

      // Should still truncate and use inverse color
      expect(titleElement.classList.contains('truncate')).toBe(true)
      expect(titleElement.classList.contains('text-text-inverse')).toBe(true)
    })

    it('should handle empty title with gradient', () => {
      const { container } = render(<IOSNavigationBar title="" isExercisePage />)
      const header = container.querySelector('header')

      // Should still apply gradient even with empty title
      expect(header?.classList.contains('bg-gradient-to-r')).toBe(true)
    })
  })
})
