import { test, expect } from '@playwright/test'

test.describe('ExerciseCompleteViewV2 - E2E', () => {
  test.beforeEach(async ({ page }) => {
    // Mock API responses
    await page.route('**/api/**', async (route) => {
      const url = route.request().url()

      if (url.includes('recommendation')) {
        await route.fulfill({
          status: 200,
          body: JSON.stringify({
            ExerciseId: 123,
            Series: 3,
            Reps: 10,
            Weight: { Kg: 60, Lb: 132 },
            OneRMProgress: 5.2,
            WarmupsCount: 0,
            RpRest: 120,
            NbPauses: 0,
            NbRepsPauses: 0,
          }),
        })
      } else {
        await route.continue()
      }
    })
  })

  test('should display golden success icon and percentage increase', async ({
    page,
  }) => {
    // Navigate to a mock exercise complete state
    await page.goto('/test/exercise-complete-v2')

    // Check for golden success icon
    const successIcon = await page.locator('[data-testid="success-icon"]')
    await expect(successIcon).toBeVisible()

    // Check for percentage display
    const percentageText = await page.locator('text=+5.2% stronger!')
    await expect(percentageText).toBeVisible()
    await expect(percentageText).toHaveClass(/text-yellow-500/)

    // Check for confetti animation
    const confetti = await page.locator('[data-testid*="confetti-particle"]')
    expect(await confetti.count()).toBeGreaterThan(0)
  })

  test('should handle negative progress', async ({ page }) => {
    await page.route('**/api/**', async (route) => {
      if (route.request().url().includes('recommendation')) {
        await route.fulfill({
          status: 200,
          body: JSON.stringify({
            ExerciseId: 123,
            Series: 3,
            Reps: 10,
            Weight: { Kg: 60, Lb: 132 },
            OneRMProgress: -2.5,
            WarmupsCount: 0,
            RpRest: 120,
            NbPauses: 0,
            NbRepsPauses: 0,
          }),
        })
      } else {
        await route.continue()
      }
    })

    await page.goto('/test/exercise-complete-v2')

    // Check for negative percentage display
    const percentageText = await page.locator('text=-2.5% today')
    await expect(percentageText).toBeVisible()
    await expect(percentageText).toHaveClass(/text-text-secondary/)

    // No confetti for negative progress
    const confetti = await page.locator('[data-testid*="confetti-particle"]')
    expect(await confetti.count()).toBe(0)
  })
})
