<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cross-Browser localStorage Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .results {
            margin-top: 20px;
            font-family: monospace;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>Dr. Muscle localStorage Cross-Browser Test</h1>
    <p>This page tests the localStorage implementation behavior across different mobile browsers.</p>

    <div class="test-section">
        <h2>Browser Information</h2>
        <div id="browser-info"></div>
    </div>

    <div class="test-section">
        <h2>localStorage Tests</h2>
        <button onclick="testBasicLocalStorage()">Test Basic localStorage</button>
        <button onclick="testImmediateSave()">Test Immediate Save</button>
        <button onclick="testRaceCondition()">Test Race Condition Prevention</button>
        <button onclick="testErrorHandling()">Test Error Handling</button>
        <button onclick="testDataValidation()">Test Data Validation</button>
        <button onclick="runAllTests()">Run All Tests</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <div class="test-section">
        <h2>Test Results</h2>
        <div id="test-results" class="results"></div>
    </div>

    <script>
        // Constants from Dr. Muscle app
        const LOCALSTORAGE_KEY = 'drMuscle_pendingSettings';
        
        // Test utilities
        function log(message, type = 'info') {
            const results = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'success' ? 'green' : type === 'error' ? 'red' : type === 'warning' ? 'orange' : 'black';
            results.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            results.scrollTop = results.scrollHeight;
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        // Settings validation function (from Dr. Muscle app)
        function validateAndSanitizeSettings(settings) {
            return {
                quickMode: Boolean(settings.quickMode),
                weightUnit: ['lbs', 'kg'].includes(settings.weightUnit) ? settings.weightUnit : 'lbs',
                setStyle: ['Normal', 'Rest-pause', 'Drop', 'Pyramid', 'Reverse pyramid'].includes(settings.setStyle) 
                    ? settings.setStyle : 'Normal',
                repsMin: Math.max(1, Math.min(100, Number(settings.repsMin) || 6)),
                repsMax: Math.max(1, Math.min(100, Number(settings.repsMax) || 12)),
                weightIncrement: Math.max(0.5, Math.min(50, Number(settings.weightIncrement) || 5)),
                warmupSets: Math.max(0, Math.min(10, Number(settings.warmupSets) || 0)),
            };
        }

        // localStorage save function (from Dr. Muscle app)
        function savePendingSettingsToStorage(settings) {
            try {
                if (settings) {
                    const sanitizedSettings = validateAndSanitizeSettings(settings);
                    localStorage.setItem(LOCALSTORAGE_KEY, JSON.stringify(sanitizedSettings));
                    return true;
                } else {
                    localStorage.removeItem(LOCALSTORAGE_KEY);
                    return true;
                }
            } catch (error) {
                console.warn('Failed to save settings to localStorage:', error);
                return false;
            }
        }

        // localStorage load function (from Dr. Muscle app)
        function loadPendingSettingsFromStorage() {
            try {
                const stored = localStorage.getItem(LOCALSTORAGE_KEY);
                if (!stored) return null;
                const parsed = JSON.parse(stored);
                return validateAndSanitizeSettings(parsed);
            } catch (error) {
                console.warn('Failed to load settings from localStorage:', error);
                return null;
            }
        }

        // Browser detection
        function getBrowserInfo() {
            const ua = navigator.userAgent;
            let browser = 'Unknown';
            let version = 'Unknown';
            let platform = 'Unknown';

            if (ua.includes('Chrome') && ua.includes('Mobile')) {
                browser = 'Chrome Mobile';
                platform = ua.includes('Android') ? 'Android' : 'iOS';
            } else if (ua.includes('Safari') && ua.includes('Mobile')) {
                browser = 'Safari Mobile';
                platform = 'iOS';
            } else if (ua.includes('Firefox') && ua.includes('Mobile')) {
                browser = 'Firefox Mobile';
                platform = ua.includes('Android') ? 'Android' : 'Unknown';
            }

            return { browser, platform, userAgent: ua };
        }

        // Test functions
        function testBasicLocalStorage() {
            log('Testing basic localStorage functionality...', 'info');
            
            try {
                // Test write
                localStorage.setItem('test', 'value');
                const retrieved = localStorage.getItem('test');
                
                if (retrieved === 'value') {
                    log('✅ Basic localStorage read/write: PASS', 'success');
                } else {
                    log('❌ Basic localStorage read/write: FAIL', 'error');
                }

                // Test delete
                localStorage.removeItem('test');
                const deleted = localStorage.getItem('test');
                
                if (deleted === null) {
                    log('✅ localStorage delete: PASS', 'success');
                } else {
                    log('❌ localStorage delete: FAIL', 'error');
                }

            } catch (error) {
                log(`❌ Basic localStorage: FAIL - ${error.message}`, 'error');
            }
        }

        function testImmediateSave() {
            log('Testing immediate save functionality...', 'info');
            
            const testSettings = {
                quickMode: true,
                weightUnit: 'kg',
                setStyle: 'Normal',
                repsMin: 6,
                repsMax: 12,
                weightIncrement: 2.5,
                warmupSets: 3
            };

            // Clear existing data
            localStorage.removeItem(LOCALSTORAGE_KEY);
            
            // Test immediate save
            const saved = savePendingSettingsToStorage(testSettings);
            
            if (saved) {
                // Immediately try to load
                const loaded = loadPendingSettingsFromStorage();
                
                if (loaded && loaded.quickMode === true && loaded.weightUnit === 'kg') {
                    log('✅ Immediate save and load: PASS', 'success');
                } else {
                    log('❌ Immediate save and load: FAIL - Data mismatch', 'error');
                }
            } else {
                log('❌ Immediate save: FAIL - Save operation failed', 'error');
            }
        }

        function testRaceCondition() {
            log('Testing race condition prevention...', 'info');
            
            // Simulate rapid setting changes
            const changes = [
                { quickMode: true },
                { weightUnit: 'kg' },
                { repsMin: 8 },
                { warmupSets: 2 }
            ];

            localStorage.removeItem(LOCALSTORAGE_KEY);
            
            let currentSettings = {
                quickMode: false,
                weightUnit: 'lbs',
                setStyle: 'Normal',
                repsMin: 6,
                repsMax: 12,
                weightIncrement: 5,
                warmupSets: 0
            };

            // Apply rapid changes
            changes.forEach((change, index) => {
                currentSettings = { ...currentSettings, ...change };
                const saved = savePendingSettingsToStorage(currentSettings);
                
                if (!saved) {
                    log(`❌ Race condition test: FAIL at change ${index + 1}`, 'error');
                    return;
                }
            });

            // Verify final state
            const finalSettings = loadPendingSettingsFromStorage();
            
            if (finalSettings && 
                finalSettings.quickMode === true && 
                finalSettings.weightUnit === 'kg' &&
                finalSettings.repsMin === 8 &&
                finalSettings.warmupSets === 2) {
                log('✅ Race condition prevention: PASS', 'success');
            } else {
                log('❌ Race condition prevention: FAIL - Final state incorrect', 'error');
            }
        }

        function testErrorHandling() {
            log('Testing error handling...', 'info');
            
            // Test with invalid JSON
            try {
                localStorage.setItem(LOCALSTORAGE_KEY, '{invalid json}');
                const loaded = loadPendingSettingsFromStorage();
                
                if (loaded === null) {
                    log('✅ Corrupted data handling: PASS', 'success');
                } else {
                    log('❌ Corrupted data handling: FAIL', 'error');
                }
            } catch (error) {
                log('⚠️ Error handling: Unexpected exception', 'warning');
            }

            // Test quota simulation (difficult to test reliably)
            log('⚠️ localStorage quota testing requires manual simulation', 'warning');
        }

        function testDataValidation() {
            log('Testing data validation and sanitization...', 'info');
            
            const maliciousData = {
                quickMode: '<script>alert("xss")</script>',
                weightUnit: 'invalid-unit',
                setStyle: 'javascript:alert("xss")',
                repsMin: -999,
                repsMax: 'not-a-number',
                weightIncrement: 'eval("alert(1)")',
                warmupSets: '<img onerror="alert(1)" src="x">'
            };

            const sanitized = validateAndSanitizeSettings(maliciousData);
            
            // Check sanitization results
            const checks = [
                { name: 'quickMode boolean conversion', pass: sanitized.quickMode === false },
                { name: 'weightUnit fallback', pass: sanitized.weightUnit === 'lbs' },
                { name: 'setStyle fallback', pass: sanitized.setStyle === 'Normal' },
                { name: 'repsMin boundary', pass: sanitized.repsMin === 1 },
                { name: 'repsMax boundary', pass: sanitized.repsMax === 12 },
                { name: 'weightIncrement boundary', pass: sanitized.weightIncrement === 0.5 },
                { name: 'warmupSets boundary', pass: sanitized.warmupSets === 0 }
            ];

            let allPassed = true;
            checks.forEach(check => {
                if (check.pass) {
                    log(`✅ ${check.name}: PASS`, 'success');
                } else {
                    log(`❌ ${check.name}: FAIL`, 'error');
                    allPassed = false;
                }
            });

            if (allPassed) {
                log('✅ Data validation: ALL CHECKS PASSED', 'success');
            } else {
                log('❌ Data validation: SOME CHECKS FAILED', 'error');
            }
        }

        function runAllTests() {
            log('=== Running All Cross-Browser localStorage Tests ===', 'info');
            clearResults();
            
            setTimeout(() => testBasicLocalStorage(), 100);
            setTimeout(() => testImmediateSave(), 500);
            setTimeout(() => testRaceCondition(), 1000);
            setTimeout(() => testErrorHandling(), 1500);
            setTimeout(() => testDataValidation(), 2000);
            setTimeout(() => {
                log('=== All Tests Completed ===', 'info');
                log('Please test this page on different mobile browsers:', 'info');
                log('- Chrome Mobile (Android)', 'info');
                log('- Safari Mobile (iOS)', 'info');
                log('- Firefox Mobile', 'info');
                log('- Samsung Internet', 'info');
            }, 2500);
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', () => {
            const browserInfo = getBrowserInfo();
            document.getElementById('browser-info').innerHTML = `
                <strong>Browser:</strong> ${browserInfo.browser}<br>
                <strong>Platform:</strong> ${browserInfo.platform}<br>
                <strong>User Agent:</strong> ${browserInfo.userAgent}
            `;
            
            log('Cross-browser localStorage test page loaded', 'info');
            log('Click "Run All Tests" to start automated testing', 'info');
        });
    </script>
</body>
</html>
