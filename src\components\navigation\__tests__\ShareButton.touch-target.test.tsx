import React from 'react'
import { render, screen } from '@testing-library/react'
import { ShareButton } from '../ShareButton'

describe('ShareButton Touch Target Compliance', () => {
  describe('52px minimum touch target requirement', () => {
    it('should have 52px touch target with default styles', () => {
      render(<ShareButton />)
      const button = screen.getByRole('button', { name: 'Share' })

      // Current: p-2 (8px padding) + 24px icon = 40px total
      // Need: p-3 (12px padding) + 24px icon = 48px, or better yet use min-h-[52px] min-w-[52px]
      expect(button).toHaveClass('min-h-[52px]')
      expect(button).toHaveClass('min-w-[52px]')
    })

    it('should maintain 52px touch target with custom className', () => {
      render(<ShareButton className="custom-class" />)
      const button = screen.getByRole('button', { name: 'Share' })

      // Custom className should include touch target classes
      expect(button).toHaveClass('custom-class')
      expect(button.className).toContain('min-h-[52px]')
      expect(button.className).toContain('min-w-[52px]')
    })

    it('should have 52px touch target when showing label', () => {
      render(<ShareButton showLabel />)
      const button = screen.getByRole('button', { name: 'Share' })

      // With label, button should still meet 52px requirement
      expect(button).toHaveClass('min-h-[52px]')
      expect(button).toHaveClass('min-w-[52px]')
    })

    it('should apply proper padding for touch target', () => {
      // Testing default className path
      render(<ShareButton />)
      const button = screen.getByRole('button', { name: 'Share' })

      // Should have p-3 (12px) instead of p-2 (8px) for better touch target
      expect(button.className).toMatch(/p-3/)
      expect(button.className).not.toMatch(/\bp-2\b/) // p-2 as a word boundary
    })
  })

  describe('Icon and label arrangement', () => {
    it('should properly arrange icon when no label', () => {
      const { container } = render(<ShareButton />)
      const svg = container.querySelector('svg')

      expect(svg).toBeInTheDocument()
      expect(svg).toHaveClass('text-text-primary')

      // Icon should be 24px as specified
      const iconContainer = svg?.parentElement
      expect(iconContainer).toHaveAttribute('aria-label', 'Share')
    })

    it('should properly arrange icon and label when showLabel is true', () => {
      render(<ShareButton showLabel />)

      const button = screen.getByRole('button', { name: 'Share' })
      const span = screen.getByText('Share')

      expect(span).toBeInTheDocument()
      expect(button).toContainElement(span)
    })
  })
})
