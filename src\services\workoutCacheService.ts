import { createDefaultCacheManager } from '@/cache'
import { workoutApi } from '@/api/workouts'
import type { CacheManager } from '@/cache/CacheManager'
import type {
  WorkoutTemplateModel,
  RecommendationModel,
  GetUserWorkoutLogAverageResponse,
} from '@/types'
import type { GetRecommendationForExerciseRequest } from '@/services/api/workout-types'

/**
 * Cache configuration constants
 */
const CACHE_CONFIG = {
  WORKOUT_TTL: 24 * 60 * 60 * 1000, // 24 hours
  RECOMMENDATIONS_TTL: 2 * 60 * 60 * 1000, // 2 hours
  PROGRAM_INFO_TTL: 24 * 60 * 60 * 1000, // 24 hours
  WORKOUT_NAMESPACE: 'offline-workouts',
  RECOMMENDATIONS_NAMESPACE: 'offline-recommendations',
  PROGRAM_INFO_NAMESPACE: 'offline-program-info',
} as const

/**
 * Result type for preload operations
 */
export interface PreloadResult {
  success: boolean
  error?: string
  programInfo?: GetUserWorkoutLogAverageResponse
  workoutDetails?: WorkoutTemplateModel
  recommendations?: RecommendationModel[]
  exerciseCount?: number
  recommendationCount?: number
}

/**
 * Workout cache service for offline mode
 *
 * Provides caching for:
 * - Program info (24h TTL)
 * - Workout details (24h TTL)
 * - Exercise recommendations (2h TTL)
 */
export class WorkoutCacheService {
  private cacheManager: CacheManager

  constructor() {
    this.cacheManager = createDefaultCacheManager()
  }

  /**
   * Get cached program info or fetch from API
   */
  async getCachedProgramInfo(
    forceRefresh = false
  ): Promise<GetUserWorkoutLogAverageResponse | null> {
    const cacheKey = 'program-info'

    try {
      // Check cache first unless force refresh
      if (!forceRefresh) {
        const cached = await this.cacheManager.get(
          cacheKey,
          CACHE_CONFIG.PROGRAM_INFO_NAMESPACE
        )
        if (cached) {
          return cached as GetUserWorkoutLogAverageResponse
        }
      }

      // Fetch from API
      const programInfo = await workoutApi.getUserProgramInfo()
      if (programInfo) {
        // Cache the result
        await this.cacheManager.set(cacheKey, programInfo, {
          ttl: CACHE_CONFIG.PROGRAM_INFO_TTL,
          namespace: CACHE_CONFIG.PROGRAM_INFO_NAMESPACE,
        })
      }

      return programInfo
    } catch (error) {
      // If cache fails, try API directly
      if (error instanceof Error && error.message.includes('cache')) {
        return await workoutApi.getUserProgramInfo()
      }
      throw error
    }
  }

  /**
   * Get cached workout details or fetch from API
   */
  async getCachedWorkoutDetails(
    workoutId: string,
    forceRefresh = false
  ): Promise<WorkoutTemplateModel | null> {
    const cacheKey = `workout-${workoutId}`

    try {
      // Check cache first unless force refresh
      if (!forceRefresh) {
        const cached = await this.cacheManager.get(
          cacheKey,
          CACHE_CONFIG.WORKOUT_NAMESPACE
        )
        if (cached) {
          return cached as WorkoutTemplateModel
        }
      }

      // Fetch from API
      const workoutDetails = await workoutApi.getWorkoutDetails(workoutId)
      if (workoutDetails) {
        // Cache the result
        await this.cacheManager.set(cacheKey, workoutDetails, {
          ttl: CACHE_CONFIG.WORKOUT_TTL,
          namespace: CACHE_CONFIG.WORKOUT_NAMESPACE,
        })
      }

      return workoutDetails
    } catch (error) {
      // If cache fails, try API directly
      if (error instanceof Error && error.message.includes('cache')) {
        return await workoutApi.getWorkoutDetails(workoutId)
      }
      throw error
    }
  }

  /**
   * Get cached exercise recommendation or fetch from API
   */
  async getCachedExerciseRecommendation(
    request: GetRecommendationForExerciseRequest,
    forceRefresh = false
  ): Promise<RecommendationModel | null> {
    const cacheKey = this.generateRecommendationCacheKey(request)

    try {
      // Check cache first unless force refresh
      if (!forceRefresh) {
        const cached = await this.cacheManager.get(
          cacheKey,
          CACHE_CONFIG.RECOMMENDATIONS_NAMESPACE
        )
        if (cached) {
          return cached as RecommendationModel
        }
      }

      // Fetch from API
      const recommendation = await workoutApi.getExerciseRecommendation(
        request.ExerciseId
      )
      if (recommendation) {
        // Cache the result
        await this.cacheManager.set(cacheKey, recommendation, {
          ttl: CACHE_CONFIG.RECOMMENDATIONS_TTL,
          namespace: CACHE_CONFIG.RECOMMENDATIONS_NAMESPACE,
        })
      }

      return recommendation
    } catch (error) {
      // If cache fails, try API directly
      if (error instanceof Error && error.message.includes('cache')) {
        return await workoutApi.getExerciseRecommendation(request.ExerciseId)
      }
      throw error
    }
  }

  /**
   * Preload complete workout data for offline use
   */
  async preloadWorkoutData(username: string): Promise<PreloadResult> {
    try {
      // Step 1: Get program info
      const programInfo = await this.getCachedProgramInfo(true) // Force refresh for preload
      if (
        !programInfo?.GetUserProgramInfoResponseModel?.NextWorkoutTemplate?.Id
      ) {
        return {
          success: false,
          error: 'No workout template found in program info',
        }
      }

      // Step 2: Get workout details
      const workoutDetails = await this.getCachedWorkoutDetails(
        programInfo.GetUserProgramInfoResponseModel.NextWorkoutTemplate.Id.toString(),
        true // Force refresh for preload
      )
      if (!workoutDetails?.Exercises) {
        return {
          success: false,
          error: 'No exercises found in workout details',
        }
      }

      // Step 3: Preload recommendations for all exercises
      const recommendations: RecommendationModel[] = []
      const exerciseCount = workoutDetails.Exercises.length

      // Process exercises in parallel to avoid performance issues and satisfy ESLint
      const exerciseRecommendations = await Promise.allSettled(
        workoutDetails.Exercises.map(async (exercise) => {
          if (exercise.Id) {
            try {
              const request: GetRecommendationForExerciseRequest = {
                ExerciseId: exercise.Id,
                WorkoutId:
                  programInfo.GetUserProgramInfoResponseModel
                    ?.NextWorkoutTemplate?.Id || 0,
                Username: username,
                IsQuickMode: false,
                LightSessionDays: 0,
                IsStrengthPhashe: false,
                IsFreePlan: false,
                VersionNo: 1,
              }

              const recommendation = await this.getCachedExerciseRecommendation(
                request,
                true // Force refresh for preload
              )
              return recommendation
            } catch (error) {
              // Continue with other exercises if one fails
              // Failed to preload recommendation for exercise - logged for debugging
              return null
            }
          }
          return null
        })
      )

      // Collect successful recommendations
      exerciseRecommendations.forEach((result) => {
        if (result.status === 'fulfilled' && result.value) {
          recommendations.push(result.value)
        }
      })

      return {
        success: true,
        programInfo,
        workoutDetails,
        recommendations,
        exerciseCount,
        recommendationCount: recommendations.length,
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      }
    }
  }

  /**
   * Clear all cached workout data
   */
  async clearAllCache(): Promise<void> {
    await Promise.all([
      this.cacheManager.clear(CACHE_CONFIG.WORKOUT_NAMESPACE),
      this.cacheManager.clear(CACHE_CONFIG.RECOMMENDATIONS_NAMESPACE),
    ])
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return this.cacheManager.getStats()
  }

  /**
   * Generate cache key for exercise recommendations
   */
  // eslint-disable-next-line class-methods-use-this
  private generateRecommendationCacheKey(
    request: GetRecommendationForExerciseRequest
  ): string {
    return `recommendation-${request.ExerciseId}-${request.WorkoutId}-${request.Username}`
  }

  /**
   * Check if workout data is cached
   */
  async isWorkoutCached(workoutId: string): Promise<boolean> {
    const cacheKey = `workout-${workoutId}`
    const cached = await this.cacheManager.get(
      cacheKey,
      CACHE_CONFIG.WORKOUT_NAMESPACE
    )
    return cached !== null
  }

  /**
   * Check if program info is cached
   */
  async isProgramInfoCached(): Promise<boolean> {
    const cached = await this.cacheManager.get(
      'program-info',
      CACHE_CONFIG.PROGRAM_INFO_NAMESPACE
    )
    return cached !== null
  }

  /**
   * Get cache size information
   */
  async getCacheSize(): Promise<{
    workoutNamespaceSize: number
    recommendationsNamespaceSize: number
    totalSize: number
  }> {
    const stats = await this.cacheManager.getStats()
    return {
      workoutNamespaceSize: 0, // CacheManager doesn't provide per-namespace size yet
      recommendationsNamespaceSize: 0,
      totalSize: stats.totalSize || 0,
    }
  }
}
