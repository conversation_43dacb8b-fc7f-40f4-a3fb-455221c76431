import { describe, it, expect, vi, beforeEach } from 'vitest'
import { generateAllSets } from '../generateAllSets'
import type { RecommendationModel, UserInfosModel } from '@/types'

// Mock the logger
vi.mock('@/utils/logger', () => ({
  logger: {
    debug: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  },
}))

// Mock data helpers
const createMockRecommendation = (
  overrides?: Partial<RecommendationModel>
): RecommendationModel => ({
  ExerciseId: 1,
  Series: 3,
  Reps: 10,
  Weight: { Kg: 50, Lb: 110 },
  WarmupsCount: 2,
  IsDropSet: false,
  IsPyramid: false,
  IsReversePyramid: false,
  IsBackOffSet: false,
  IsNormalSets: true,
  NbPauses: 0,
  NbRepsPauses: 0,
  RpRest: 0,
  IsBodyweight: false,
  Min: { Kg: 20, Lb: 44 },
  Max: { Kg: 100, Lb: 220 },
  Increments: { Kg: 2.5, Lb: 5 },
  ...overrides,
})

describe('generateAllSets - Bodyweight Drop Set Prevention', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('normalizeDropSetRecommendation', () => {
    it('should NOT convert bodyweight pyramid exercise to drop set', () => {
      // Test data: Bodyweight exercise with pyramid recommendation
      const bodyweightPyramidRec: RecommendationModel = {
        ExerciseId: 1001,
        ExerciseName: 'Push-ups',
        IsBodyweight: true,
        IsPyramid: true,
        IsDropSet: false,
        IsNormalSets: false,
        Series: 3,
        NbPauses: 0,
        Reps: 12,
        Weight: { Kg: 0, Lb: 0 },
        WarmupsCount: 0,
        Min: { Kg: 0, Lb: 0 },
        Max: { Kg: 100, Lb: 220 },
        Increments: { Kg: 2.5, Lb: 5 },
        IsReversePyramid: false,
        IsBackOffSet: false,
        NbRepsPauses: 0,
        RpRest: 0,
      }

      const result = generateAllSets({
        recommendation: bodyweightPyramidRec,
        completedSets: [],
        currentSetIndex: 0,
        setData: null,
        unit: 'kg',
        userInfo: null, // No user preference, should not convert
      })

      // Verify pyramid is preserved for bodyweight
      const workSets = result.filter((s) => !s.IsWarmups)
      expect(workSets.length).toBeGreaterThan(0)

      // Check that drop set flag is NOT set on any work set
      workSets.forEach((set) => {
        expect(set.IsDropSet).toBe(false)
      })

      // Verify pyramid title is preserved
      const firstWorkSet = workSets[0]
      expect(firstWorkSet?.SetTitle).toContain('Pyramid')
    })

    it('should NOT convert bodyweight rest-pause exercise to drop set', () => {
      // Test data: Bodyweight exercise with rest-pause recommendation
      const bodyweightRestPauseRec: RecommendationModel = {
        ExerciseId: 1002,
        ExerciseName: 'Pull-ups',
        IsBodyweight: true,
        IsPyramid: false,
        IsDropSet: false,
        IsNormalSets: false,
        Series: 2,
        NbPauses: 2,
        Reps: 8,
        NbRepsPauses: 4,
        Weight: { Kg: 0, Lb: 0 },
        WarmupsCount: 0,
        Min: { Kg: 0, Lb: 0 },
        Max: { Kg: 100, Lb: 220 },
        Increments: { Kg: 2.5, Lb: 5 },
        IsReversePyramid: false,
        IsBackOffSet: false,
        RpRest: 15,
      }

      const result = generateAllSets({
        recommendation: bodyweightRestPauseRec,
        completedSets: [],
        currentSetIndex: 0,
        setData: null,
        unit: 'kg',
        userInfo: null, // No user preference
      })

      // Verify rest-pause is preserved for bodyweight
      const workSets = result.filter((s) => !s.IsWarmups)

      // Should have Series + NbPauses sets (2 + 2 = 4)
      expect(workSets.length).toBe(4)

      // Check that drop set flag is NOT set
      workSets.forEach((set) => {
        expect(set.IsDropSet).toBe(false)
      })

      // Verify rest-pause titles are preserved
      const restPauseSets = workSets.slice(2) // After Series sets
      expect(restPauseSets[0]?.SetTitle).toContain("All right! Now let's try:")
    })

    it('should ALLOW drop set conversion for non-bodyweight pyramid exercise', () => {
      // Test data: Non-bodyweight exercise with pyramid recommendation
      const weightedPyramidRec: RecommendationModel = {
        ExerciseId: 1003,
        ExerciseName: 'Bench Press',
        IsBodyweight: false,
        IsPyramid: true,
        IsDropSet: false,
        IsNormalSets: false,
        Series: 3,
        NbPauses: 0,
        Reps: 8,
        Weight: { Kg: 100, Lb: 220 },
        WarmupsCount: 2,
        Min: { Kg: 20, Lb: 45 },
        Max: { Kg: 200, Lb: 440 },
        Increments: { Kg: 2.5, Lb: 5 },
        IsReversePyramid: false,
        IsBackOffSet: false,
        NbRepsPauses: 0,
        RpRest: 0,
      }

      // User with drop set preference
      const userWithDropSetPref: Partial<UserInfosModel> = {
        IsDropSet: true,
        Email: '<EMAIL>',
      }

      const result = generateAllSets({
        recommendation: weightedPyramidRec,
        completedSets: [],
        currentSetIndex: 0,
        setData: null,
        unit: 'kg',
        userInfo: userWithDropSetPref as UserInfosModel,
      })

      // Should convert pyramid to drop set for non-bodyweight
      const workSets = result.filter((s) => !s.IsWarmups)

      // Check that drop set flag IS set
      workSets.forEach((set) => {
        expect(set.IsDropSet).toBe(true)
      })

      // Verify drop set title
      const firstWorkSet = workSets[0]
      expect(firstWorkSet?.SetTitle).toBe('Drop set')
    })

    it('should correct bodyweight exercise incorrectly marked as drop set', () => {
      // Test data: Bodyweight exercise incorrectly marked as drop set by server
      const incorrectBodyweightDropRec: RecommendationModel = {
        ExerciseId: 1004,
        ExerciseName: 'Dips',
        IsBodyweight: true,
        IsPyramid: false,
        IsDropSet: true, // Incorrectly marked as drop set
        IsNormalSets: false,
        Series: 1,
        NbPauses: 2,
        Reps: 10,
        Weight: { Kg: 0, Lb: 0 },
        WarmupsCount: 0,
        Min: { Kg: 0, Lb: 0 },
        Max: { Kg: 100, Lb: 220 },
        Increments: { Kg: 2.5, Lb: 5 },
        IsReversePyramid: false,
        IsBackOffSet: false,
        NbRepsPauses: 5,
        RpRest: 15,
      }

      const result = generateAllSets({
        recommendation: incorrectBodyweightDropRec,
        completedSets: [],
        currentSetIndex: 0,
        setData: null,
        unit: 'kg',
        userInfo: { IsDropSet: true } as UserInfosModel, // Even with drop preference, bodyweight should not be drop
      })

      // Should correct the drop set flag for bodyweight
      const workSets = result.filter((s) => !s.IsWarmups)

      // Check that drop set flag is corrected to false
      workSets.forEach((set) => {
        expect(set.IsDropSet).toBe(false)
      })

      // Should convert to rest-pause instead
      expect(workSets.length).toBe(3) // 1 Series + 2 NbPauses
      const restPauseSets = workSets.slice(1)
      expect(restPauseSets[0]?.SetTitle).toContain("All right! Now let's try:")
    })

    it('should handle missing IsBodyweight property safely', () => {
      // Test data: Exercise with undefined IsBodyweight
      const undefinedBodyweightRec: RecommendationModel = {
        ExerciseId: 1005,
        ExerciseName: 'Unknown Exercise',
        IsBodyweight: undefined as any, // Missing property
        IsPyramid: true,
        IsDropSet: false,
        IsNormalSets: false,
        Series: 3,
        NbPauses: 0,
        Reps: 10,
        Weight: { Kg: 50, Lb: 110 },
        WarmupsCount: 2,
        Min: { Kg: 20, Lb: 45 },
        Max: { Kg: 100, Lb: 220 },
        Increments: { Kg: 2.5, Lb: 5 },
        IsReversePyramid: false,
        IsBackOffSet: false,
        NbRepsPauses: 0,
        RpRest: 0,
      }

      const result = generateAllSets({
        recommendation: undefinedBodyweightRec,
        completedSets: [],
        currentSetIndex: 0,
        setData: null,
        unit: 'kg',
        userInfo: { IsDropSet: true } as UserInfosModel, // User wants drop sets
      })

      // Should treat as non-bodyweight (safe default) and allow drop set conversion
      const workSets = result.filter((s) => !s.IsWarmups)

      // Should convert to drop set since undefined is treated as non-bodyweight
      workSets.forEach((set) => {
        expect(set.IsDropSet).toBe(true)
      })
    })

    it('should handle conflicting bodyweight and weight data', () => {
      // Test data: Bodyweight exercise with non-zero weight (conflicting data)
      const conflictingRec: RecommendationModel = {
        ExerciseId: 1006,
        ExerciseName: 'Weighted Pull-ups',
        IsBodyweight: true, // Marked as bodyweight
        IsPyramid: true,
        IsDropSet: false,
        IsNormalSets: false,
        Series: 3,
        NbPauses: 0,
        Reps: 8,
        Weight: { Kg: 20, Lb: 44 }, // But has weight values
        WarmupsCount: 0,
        Min: { Kg: 0, Lb: 0 },
        Max: { Kg: 50, Lb: 110 },
        Increments: { Kg: 2.5, Lb: 5 },
        IsReversePyramid: false,
        IsBackOffSet: false,
        NbRepsPauses: 0,
        RpRest: 0,
      }

      const result = generateAllSets({
        recommendation: conflictingRec,
        completedSets: [],
        currentSetIndex: 0,
        setData: null,
        unit: 'kg',
        userInfo: { IsDropSet: true } as UserInfosModel, // User wants drop sets but bodyweight prevents it
      })

      // Should prioritize IsBodyweight flag and NOT convert to drop set
      const workSets = result.filter((s) => !s.IsWarmups)

      // Check that drop set flag is NOT set (respecting IsBodyweight flag)
      workSets.forEach((set) => {
        expect(set.IsDropSet).toBe(false)
      })

      // Should preserve pyramid
      const firstWorkSet = workSets[0]
      expect(firstWorkSet?.SetTitle).toContain('Pyramid')
    })

    it('should preserve weight consistency for bodyweight exercises', () => {
      // Test data: Bodyweight exercise
      const bodyweightRec: RecommendationModel = {
        ExerciseId: 1007,
        ExerciseName: 'Push-ups',
        IsBodyweight: true,
        IsPyramid: false,
        IsDropSet: false,
        IsNormalSets: true,
        Series: 3,
        NbPauses: 0,
        Reps: 15,
        Weight: { Kg: 0, Lb: 0 },
        WarmupsCount: 0,
        Min: { Kg: 0, Lb: 0 },
        Max: { Kg: 0, Lb: 0 },
        Increments: { Kg: 0, Lb: 0 },
        IsReversePyramid: false,
        IsBackOffSet: false,
        NbRepsPauses: 0,
        RpRest: 0,
      }

      const result = generateAllSets({
        recommendation: bodyweightRec,
        completedSets: [],
        currentSetIndex: 0,
        setData: null,
        unit: 'kg',
        userInfo: null, // No user preference
      })

      // All bodyweight sets should have consistent zero weight
      const workSets = result.filter((s) => !s.IsWarmups)

      workSets.forEach((set) => {
        expect(set.Weight.Kg).toBe(0)
        expect(set.Weight.Lb).toBe(0)
        expect(set.IsDropSet).toBe(false)
      })
    })

    it('should NOT convert pyramid to drop set when user has no drop preference', () => {
      // Test data: Non-bodyweight pyramid exercise
      const pyramidRec: RecommendationModel = {
        ExerciseId: 1009,
        ExerciseName: 'Barbell Squat',
        IsBodyweight: false,
        IsPyramid: true,
        IsDropSet: false,
        IsNormalSets: false,
        Series: 3,
        NbPauses: 0,
        Reps: 8,
        Weight: { Kg: 100, Lb: 220 },
        WarmupsCount: 2,
        Min: { Kg: 20, Lb: 45 },
        Max: { Kg: 200, Lb: 440 },
        Increments: { Kg: 2.5, Lb: 5 },
        IsReversePyramid: false,
        IsBackOffSet: false,
        NbRepsPauses: 0,
        RpRest: 0,
      }

      // User WITHOUT drop set preference
      const userWithoutDropSetPref: Partial<UserInfosModel> = {
        IsDropSet: false,
        Email: '<EMAIL>',
      }

      const result = generateAllSets({
        recommendation: pyramidRec,
        completedSets: [],
        currentSetIndex: 0,
        setData: null,
        unit: 'kg',
        userInfo: userWithoutDropSetPref as UserInfosModel,
      })

      // Should NOT convert pyramid to drop set
      const workSets = result.filter((s) => !s.IsWarmups)

      // Check that drop set flag is NOT set
      workSets.forEach((set) => {
        expect(set.IsDropSet).toBe(false)
      })

      // Should preserve pyramid title
      const firstWorkSet = workSets[0]
      expect(firstWorkSet?.SetTitle).toContain('Pyramid')
    })

    it('should apply correct weight reduction for non-bodyweight drop sets', () => {
      // Test data: Non-bodyweight drop set
      const dropSetRec: RecommendationModel = {
        ExerciseId: 1008,
        ExerciseName: 'Cable Curl',
        IsBodyweight: false,
        IsPyramid: false,
        IsDropSet: true,
        IsNormalSets: false,
        Series: 1,
        NbPauses: 2,
        Reps: 10,
        Weight: { Kg: 50, Lb: 110 },
        WarmupsCount: 2,
        Min: { Kg: 10, Lb: 22 },
        Max: { Kg: 100, Lb: 220 },
        Increments: { Kg: 2.5, Lb: 5 },
        IsReversePyramid: false,
        IsBackOffSet: false,
        NbRepsPauses: 0,
        RpRest: 0,
      }

      const result = generateAllSets({
        recommendation: dropSetRec,
        completedSets: [],
        currentSetIndex: 0,
        setData: null,
        unit: 'kg',
        userInfo: { IsDropSet: true } as UserInfosModel, // User wants drop sets (non-bodyweight)
      })

      const workSets = result.filter((s) => !s.IsWarmups)

      // Should have 3 sets (1 Series + 2 NbPauses)
      expect(workSets.length).toBe(3)

      // Verify 10% cumulative weight reduction
      expect(workSets[0]?.Weight.Kg).toBe(50) // 100% of base weight
      expect(workSets[1]?.Weight.Kg).toBeCloseTo(45, 1) // 90% of base weight
      expect(workSets[2]?.Weight.Kg).toBeCloseTo(40.5, 1) // 81% of base weight

      // All should be marked as drop sets
      workSets.forEach((set) => {
        expect(set.IsDropSet).toBe(true)
        expect(set.SetTitle).toBe('Drop set')
      })
    })
  })
})

describe('generateAllSets - Guide Compliance Tests', () => {
  describe('Drop Sets (Guide Section: Drop Sets)', () => {
    it('should render total sets as Series + NbPauses for drop sets', () => {
      const recommendation = createMockRecommendation({
        IsDropSet: true,
        Series: 1,
        NbPauses: 3,
        Weight: { Kg: 50, Lb: 110 },
      })

      const result = generateAllSets({
        recommendation,
        completedSets: [],
        currentSetIndex: 0,
        setData: null,
        unit: 'lbs',
        isBodyweight: false,
      })

      // Should have 2 warmups + 4 work sets (1 Series + 3 NbPauses)
      const workSets = result.filter((s) => !s.IsWarmups)
      expect(workSets).toHaveLength(4)
    })

    it('should label ALL sets as "Drop set" when IsDropSet=true', () => {
      const recommendation = createMockRecommendation({
        IsDropSet: true,
        Series: 1,
        NbPauses: 3,
      })

      const result = generateAllSets({
        recommendation,
        completedSets: [],
        currentSetIndex: 0,
        setData: null,
        unit: 'lbs',
        isBodyweight: false,
      })

      const workSets = result.filter((s) => !s.IsWarmups)
      workSets.forEach((set) => {
        expect(set.SetTitle).toBe('Drop set')
        expect(set.IsDropSet).toBe(true)
      })
    })

    it('should apply 10% cumulative weight reduction for drop sets (corrected per guide)', () => {
      const recommendation = createMockRecommendation({
        IsDropSet: true,
        Series: 1,
        NbPauses: 3,
        Weight: { Kg: 50, Lb: 110 },
      })

      const result = generateAllSets({
        recommendation,
        completedSets: [],
        currentSetIndex: 0,
        setData: null,
        unit: 'lbs',
        isBodyweight: false,
      })

      const workSets = result.filter((s) => !s.IsWarmups)

      // Per guide: Series sets use base weight, NbPauses sets get reductions
      // Set 1 (Series): base weight (110 lbs)
      expect(workSets[0]?.Weight?.Lb).toBe(110)

      // Set 2 (NbPauses[0]): 90% of base (99 lbs)
      expect(workSets[1]?.Weight?.Lb).toBe(99)

      // Set 3 (NbPauses[1]): 81% of base (89.1 → 89 lbs rounded)
      expect(workSets[2]?.Weight?.Lb).toBe(89)

      // Set 4 (NbPauses[2]): 72.9% of base (80.19 → 80 lbs rounded)
      expect(workSets[3]?.Weight?.Lb).toBe(80)
    })

    describe('MAUI Parity Rules', () => {
      it('should enforce minimum 3 total sets for drop sets', () => {
        const recommendation = createMockRecommendation({
          IsDropSet: true,
          Series: 1,
          NbPauses: 1, // Only 2 total sets (1 + 1)
          Weight: { Kg: 50, Lb: 110 },
          Reps: 12,
        })

        const result = generateAllSets({
          recommendation,
          completedSets: [],
          currentSetIndex: 0,
          setData: null,
          unit: 'kg',
          isBodyweight: false,
        })

        const workSets = result.filter((s) => !s.IsWarmups)

        // Should have minimum 3 sets even though Series + NbPauses = 2
        expect(workSets).toHaveLength(3)

        // All should be labeled as drop sets
        workSets.forEach((set) => {
          expect(set.SetTitle).toBe('Drop set')
          expect(set.IsDropSet).toBe(true)
        })
      })

      it('should apply one-third reps rule for drop sets (MAUI parity)', () => {
        const recommendation = createMockRecommendation({
          IsDropSet: true,
          Series: 1,
          NbPauses: 2,
          Weight: { Kg: 50, Lb: 110 },
          Reps: 12, // Base reps
        })

        const result = generateAllSets({
          recommendation,
          completedSets: [],
          currentSetIndex: 0,
          setData: null,
          unit: 'kg',
          isBodyweight: false,
        })

        const workSets = result.filter((s) => !s.IsWarmups)

        // First set (Series): Full reps
        expect(workSets[0]?.Reps).toBe(12)

        // Drop sets: One-third of base reps (12 / 3 = 4)
        expect(workSets[1]?.Reps).toBe(4)
        expect(workSets[2]?.Reps).toBe(4)
      })

      it('should handle one-third reps with minimum of 1 rep', () => {
        const recommendation = createMockRecommendation({
          IsDropSet: true,
          Series: 1,
          NbPauses: 2,
          Weight: { Kg: 50, Lb: 110 },
          Reps: 2, // Base reps that would give < 1 when divided by 3
        })

        const result = generateAllSets({
          recommendation,
          completedSets: [],
          currentSetIndex: 0,
          setData: null,
          unit: 'kg',
          isBodyweight: false,
        })

        const workSets = result.filter((s) => !s.IsWarmups)

        // First set: Full reps
        expect(workSets[0]?.Reps).toBe(2)

        // Drop sets: Math.max(1, Math.floor(2 / 3)) = 1
        expect(workSets[1]?.Reps).toBe(1)
        expect(workSets[2]?.Reps).toBe(1)
      })

      it('should apply both min 3 sets AND one-third reps together', () => {
        const recommendation = createMockRecommendation({
          IsDropSet: true,
          Series: 1,
          NbPauses: 0, // Only 1 total set - should expand to 3
          Weight: { Kg: 100, Lb: 220 },
          Reps: 15,
        })

        const result = generateAllSets({
          recommendation,
          completedSets: [],
          currentSetIndex: 0,
          setData: null,
          unit: 'kg',
          isBodyweight: false,
        })

        const workSets = result.filter((s) => !s.IsWarmups)

        // Should have minimum 3 sets
        expect(workSets).toHaveLength(3)

        // First set: Full reps (15)
        expect(workSets[0]?.Reps).toBe(15)

        // Added drop sets: One-third reps (15 / 3 = 5)
        expect(workSets[1]?.Reps).toBe(5)
        expect(workSets[2]?.Reps).toBe(5)

        // Weight reduction check
        expect(workSets[0]?.Weight?.Kg).toBe(100) // Base weight
        expect(workSets[1]?.Weight?.Kg).toBeCloseTo(90, 1) // 10% reduction
        expect(workSets[2]?.Weight?.Kg).toBeCloseTo(81, 1) // 10% reduction from previous
      })
    })
  })

  describe('Rest-Pause Sets (Guide Section: Rest-Pause Sets)', () => {
    it('should create Series work sets + NbPauses micro-sets', () => {
      const recommendation = createMockRecommendation({
        IsDropSet: false,
        IsNormalSets: false,
        Series: 2,
        NbPauses: 3,
        NbRepsPauses: 6,
      })

      const result = generateAllSets({
        recommendation,
        completedSets: [],
        currentSetIndex: 0,
        setData: null,
        unit: 'lbs',
        isBodyweight: false,
      })

      const workSets = result.filter((s) => !s.IsWarmups)
      expect(workSets).toHaveLength(5) // 2 Series + 3 NbPauses
    })

    it('should use NbRepsPauses for rest-pause micro-sets', () => {
      const recommendation = createMockRecommendation({
        IsDropSet: false,
        IsNormalSets: false,
        Series: 1,
        NbPauses: 2,
        Reps: 10,
        NbRepsPauses: 6,
      })

      const result = generateAllSets({
        recommendation,
        completedSets: [],
        currentSetIndex: 0,
        setData: null,
        unit: 'lbs',
        isBodyweight: false,
      })

      const workSets = result.filter((s) => !s.IsWarmups)

      // First set (Series): uses main Reps
      expect(workSets[0]?.Reps).toBe(10)

      // Rest-pause sets (NbPauses): use NbRepsPauses
      expect(workSets[1]?.Reps).toBe(6)
      expect(workSets[2]?.Reps).toBe(6)
    })
  })

  describe('Normal Sets (Guide Section: Normal Sets)', () => {
    it('should build Series sets using same Weight/Reps', () => {
      const recommendation = createMockRecommendation({
        Series: 3,
        Reps: 12,
        Weight: { Kg: 40, Lb: 88 },
      })

      const result = generateAllSets({
        recommendation,
        completedSets: [],
        currentSetIndex: 0,
        setData: null,
        unit: 'lbs',
        isBodyweight: false,
      })

      const workSets = result.filter((s) => !s.IsWarmups)
      expect(workSets).toHaveLength(3)

      workSets.forEach((set) => {
        expect(set.Reps).toBe(12)
        expect(set.Weight?.Lb).toBe(88)
      })
    })
  })

  describe('Pyramid Sets (Guide Section: Pyramid Sets)', () => {
    it('should label first set as "Pyramid set:" when IsPyramid=true', () => {
      const recommendation = createMockRecommendation({
        IsPyramid: true,
        Series: 3,
      })

      const result = generateAllSets({
        recommendation,
        completedSets: [],
        currentSetIndex: 0,
        setData: null,
        unit: 'lbs',
        isBodyweight: false,
      })

      const workSets = result.filter((s) => !s.IsWarmups)
      expect(workSets[0]?.SetTitle).toBe('Pyramid set:')
    })
  })

  describe('Reverse Pyramid Sets (Guide Section: Reverse Pyramid Sets)', () => {
    it('should label first set as "Reverse pyramid set:" when IsReversePyramid=true', () => {
      const recommendation = createMockRecommendation({
        IsReversePyramid: true,
        Series: 3,
      })

      const result = generateAllSets({
        recommendation,
        completedSets: [],
        currentSetIndex: 0,
        setData: null,
        unit: 'lbs',
        isBodyweight: false,
      })

      const workSets = result.filter((s) => !s.IsWarmups)
      expect(workSets[0]?.SetTitle).toBe('Reverse pyramid set:')
    })
  })

  describe('Back-off Sets (Guide Section: Back-off Sets)', () => {
    it('should label sets as "Back-off set:" when IsBackOffSet=true', () => {
      const recommendation = createMockRecommendation({
        IsBackOffSet: true,
        Series: 2,
      })

      const result = generateAllSets({
        recommendation,
        completedSets: [],
        currentSetIndex: 0,
        setData: null,
        unit: 'lbs',
        isBodyweight: false,
      })

      const workSets = result.filter((s) => !s.IsWarmups)
      workSets.forEach((set) => {
        expect(set.SetTitle).toBe('Back-off set:')
        expect(set.IsBackOffSet).toBe(true)
      })
    })
  })

  describe('Warm-ups (Guide Section: Warm-Ups)', () => {
    it('should generate warm-ups locally and prepend to work sets', () => {
      const recommendation = createMockRecommendation({
        WarmupsCount: 2,
        Series: 3,
      })

      const result = generateAllSets({
        recommendation,
        completedSets: [],
        currentSetIndex: 0,
        setData: null,
        unit: 'lbs',
        isBodyweight: false,
      })

      const warmupSets = result.filter((s) => s.IsWarmups)
      const workSets = result.filter((s) => !s.IsWarmups)

      expect(warmupSets).toHaveLength(2)
      expect(workSets).toHaveLength(3)

      // Warmups should come first
      expect(result[0]?.IsWarmups).toBe(true)
      expect(result[1]?.IsWarmups).toBe(true)
      expect(result[2]?.IsWarmups).toBe(false)
    })
  })

  describe('Set Type Precedence (Guide Section: Server Decision Priority)', () => {
    it('should prioritize Drop Set over Pyramid', () => {
      const recommendation = createMockRecommendation({
        IsDropSet: true,
        IsPyramid: true, // Should be ignored
        Series: 1,
        NbPauses: 2,
      })

      const result = generateAllSets({
        recommendation,
        completedSets: [],
        currentSetIndex: 0,
        setData: null,
        unit: 'lbs',
        isBodyweight: false,
      })

      const workSets = result.filter((s) => !s.IsWarmups)
      workSets.forEach((set) => {
        expect(set.SetTitle).toBe('Drop set')
        expect(set.IsDropSet).toBe(true)
      })
    })
  })

  describe('Bodyweight Exercise Handling (MAUI Parity)', () => {
    it('should convert bodyweight drop sets to rest-pause', () => {
      const recommendation = createMockRecommendation({
        IsDropSet: true,
        Series: 1,
        NbPauses: 3,
        IsBodyweight: true,
      })

      const result = generateAllSets({
        recommendation,
        completedSets: [],
        currentSetIndex: 0,
        setData: null,
        unit: 'lbs',
        isBodyweight: true, // Bodyweight exercise
      })

      const workSets = result.filter((s) => !s.IsWarmups)

      // Should be treated as rest-pause, not drop sets
      expect(workSets).toHaveLength(4) // 1 Series + 3 NbPauses

      // Should not have drop set labels
      workSets.forEach((set) => {
        expect(set.IsDropSet).toBe(false)
      })
    })

    it('should convert bodyweight pyramid to rest-pause', () => {
      const recommendation = createMockRecommendation({
        IsPyramid: true,
        IsDropSet: false,
        Series: 1,
        NbPauses: 2,
        IsBodyweight: true,
      })

      const result = generateAllSets({
        recommendation,
        completedSets: [],
        currentSetIndex: 0,
        setData: null,
        unit: 'lbs',
        isBodyweight: true, // Bodyweight exercise
      })

      const workSets = result.filter((s) => !s.IsWarmups)

      // Should be treated as rest-pause, not pyramid
      expect(workSets).toHaveLength(3) // 1 Series + 2 NbPauses

      // Should not have pyramid or drop set flags
      workSets.forEach((set) => {
        expect(set.IsDropSet).toBe(false)
      })
    })
  })
})
