import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ExerciseInfoHeader } from '../ExerciseInfoHeader'
import type { SetType } from '@/utils/setTypeUtils'

describe('ExerciseInfoHeader', () => {
  it('should NOT display exercise name', () => {
    render(<ExerciseInfoHeader currentSet={1} totalSets={5} />)

    expect(screen.queryByText('Bench Press')).not.toBeInTheDocument()
  })

  it('should have header container without exercise name', () => {
    render(<ExerciseInfoHeader currentSet={1} totalSets={5} />)

    const header = screen.getByTestId('exercise-info-header')
    expect(header).toBeInTheDocument()
    // Should not contain any h2 element for exercise name
    expect(header.querySelector('h2')).not.toBeInTheDocument()
  })

  it('should display set progress', () => {
    render(<ExerciseInfoHeader currentSet={3} totalSets={8} />)

    expect(screen.getByText('Set 3 of 8')).toBeInTheDocument()
  })

  it('should display progress bar with correct width', () => {
    render(
      <ExerciseInfoHeader currentSet={3} totalSets={4} completedSets={2} />
    )

    const progressBar = screen.getByTestId('exercise-progress-bar')
    const progressFill = progressBar.firstChild as HTMLElement

    // 2 completed out of 4 total = 50%
    expect(progressFill).toHaveStyle({ width: '50%' })
  })

  it('should align with page content', () => {
    const { container } = render(
      <ExerciseInfoHeader currentSet={1} totalSets={3} />
    )

    // Check that the container has proper padding for alignment
    const header = container.firstChild as HTMLElement
    expect(header).toHaveClass('px-4')
  })

  describe('set type display', () => {
    it('should display set type tag when provided', () => {
      render(
        <ExerciseInfoHeader currentSet={2} totalSets={6} setType="Rest-pause" />
      )

      expect(screen.getByText('Rest-pause')).toBeInTheDocument()
    })

    it('should display set type tag with gold gradient', () => {
      render(
        <ExerciseInfoHeader currentSet={1} totalSets={4} setType="Drop set" />
      )

      const setTypeTag = screen.getByRole('button', {
        name: /Drop set.*tap for more info/,
      })
      expect(setTypeTag).toBeInTheDocument()
      expect(setTypeTag).toHaveClass(/bg-gradient-to-r/)
    })

    it('should handle all set types correctly', () => {
      const setTypes: SetType[] = [
        'Normal',
        'Rest-pause',
        'Back-off',
        'Drop set',
        'Pyramid',
        'Reverse pyramid',
        'Warm-up',
      ]

      setTypes.forEach((setType) => {
        const { unmount } = render(
          <ExerciseInfoHeader currentSet={1} totalSets={3} setType={setType} />
        )

        expect(
          screen.getByRole('button', {
            name: new RegExp(`${setType}.*tap for more info`),
          })
        ).toBeInTheDocument()
        unmount()
      })
    })

    it('should not display set type tag when not provided', () => {
      render(<ExerciseInfoHeader currentSet={1} totalSets={5} />)

      // Check that no set type tags are displayed
      expect(screen.queryByText('Normal')).not.toBeInTheDocument()
      expect(screen.queryByText('Rest-pause')).not.toBeInTheDocument()
      expect(screen.queryByText('Drop set')).not.toBeInTheDocument()
    })

    it('should display set counter, set type, and progress bar on same row', () => {
      const { container } = render(
        <ExerciseInfoHeader currentSet={3} totalSets={8} setType="Pyramid" />
      )

      // Check that all elements are in a flex container
      const flexContainer = container.querySelector('.flex.items-center.gap-3')
      expect(flexContainer).toBeInTheDocument()

      // Check that all three elements exist within the flex container
      expect(flexContainer).toContainElement(screen.getByText('Set 3 of 8'))
      expect(flexContainer).toContainElement(
        screen.getByRole('button', { name: /Pyramid.*tap for more info/ })
      )
      expect(flexContainer).toContainElement(
        screen.getByTestId('exercise-progress-bar')
      )
    })

    it('should maintain proper layout with long set type names', () => {
      render(
        <ExerciseInfoHeader
          currentSet={1}
          totalSets={5}
          setType="Reverse pyramid"
        />
      )

      const setTypeTag = screen.getByRole('button', {
        name: /Reverse pyramid.*tap for more info/,
      })
      expect(setTypeTag).toBeInTheDocument()

      // Check that it doesn't break the layout
      const flexContainer = setTypeTag.closest('.flex.items-center.gap-3')
      expect(flexContainer).toBeInTheDocument()
    })

    it('should open modal when set type badge is clicked', async () => {
      const user = userEvent.setup()
      render(
        <ExerciseInfoHeader currentSet={2} totalSets={4} setType="Rest-pause" />
      )

      // Click the set type badge
      const setTypeBadge = screen.getByRole('button', {
        name: /Rest-pause.*tap for more info/,
      })
      await user.click(setTypeBadge)

      // Check that modal appears
      expect(
        screen.getByRole('dialog', { name: /Rest-pause sets/ })
      ).toBeInTheDocument()
      expect(
        screen.getByText(/After reaching failure, take short rest periods/)
      ).toBeInTheDocument()

      // Check that close button exists
      expect(screen.getByRole('button', { name: 'Got it' })).toBeInTheDocument()
    })

    it('should close modal when close button is clicked', async () => {
      const user = userEvent.setup()
      render(
        <ExerciseInfoHeader currentSet={1} totalSets={3} setType="Pyramid" />
      )

      // Open modal
      const setTypeBadge = screen.getByRole('button', {
        name: /Pyramid.*tap for more info/,
      })
      await user.click(setTypeBadge)

      // Click close button
      const closeButton = screen.getByRole('button', { name: 'Got it' })
      await user.click(closeButton)

      // Check that modal is closed
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument()
    })
  })
})
