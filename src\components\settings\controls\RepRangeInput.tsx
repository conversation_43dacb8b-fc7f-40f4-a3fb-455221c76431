'use client'

interface RepRangeInputProps {
  minReps: number
  maxReps: number
  onChange: (values: { minReps: number; maxReps: number }) => void
  disabled?: boolean
}

export default function RepRangeInput({
  minReps,
  maxReps,
  onChange,
  disabled = false,
}: RepRangeInputProps) {
  const handleMinChange = (value: number) => {
    const newMin = Math.max(1, Math.min(value, maxReps - 1))
    onChange({ minReps: newMin, maxReps })
  }

  const handleMaxChange = (value: number) => {
    const newMax = Math.max(minReps + 1, Math.min(value, 50))
    onChange({ minReps, maxReps: newMax })
  }

  return (
    <div className="space-y-3">
      <div
        data-testid="rep-range-container"
        className="flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-4"
      >
        <div className="flex-1">
          <label className="text-xs text-text-secondary">Min Reps</label>
          <div className="flex items-center mt-1">
            <button
              onClick={() => handleMinChange(minReps - 1)}
              disabled={disabled || minReps <= 1}
              className="min-w-[52px] min-h-[52px] rounded-l-lg bg-surface-secondary text-text-primary disabled:opacity-50 flex items-center justify-center"
              aria-label="Decrease minimum reps"
            >
              -
            </button>
            <input
              type="number"
              value={minReps}
              onChange={(e) => handleMinChange(parseInt(e.target.value) || 1)}
              disabled={disabled}
              className="w-16 min-h-[52px] text-center bg-surface-primary border-y border-surface-tertiary text-sm"
              min={1}
              max={maxReps - 1}
              aria-label="Minimum reps"
            />
            <button
              onClick={() => handleMinChange(minReps + 1)}
              disabled={disabled || minReps >= maxReps - 1}
              className="min-w-[52px] min-h-[52px] rounded-r-lg bg-surface-secondary text-text-primary disabled:opacity-50 flex items-center justify-center"
              aria-label="Increase minimum reps"
            >
              +
            </button>
          </div>
        </div>

        <div className="flex-1">
          <label className="text-xs text-text-secondary">Max Reps</label>
          <div className="flex items-center mt-1">
            <button
              onClick={() => handleMaxChange(maxReps - 1)}
              disabled={disabled || maxReps <= minReps + 1}
              className="min-w-[52px] min-h-[52px] rounded-l-lg bg-surface-secondary text-text-primary disabled:opacity-50 flex items-center justify-center"
              aria-label="Decrease maximum reps"
            >
              -
            </button>
            <input
              type="number"
              value={maxReps}
              onChange={(e) =>
                handleMaxChange(parseInt(e.target.value) || minReps + 1)
              }
              disabled={disabled}
              className="w-16 min-h-[52px] text-center bg-surface-primary border-y border-surface-tertiary text-sm"
              min={minReps + 1}
              max={50}
              aria-label="Maximum reps"
            />
            <button
              onClick={() => handleMaxChange(maxReps + 1)}
              disabled={disabled || maxReps >= 50}
              className="min-w-[52px] min-h-[52px] rounded-r-lg bg-surface-secondary text-text-primary disabled:opacity-50 flex items-center justify-center"
              aria-label="Increase maximum reps"
            >
              +
            </button>
          </div>
        </div>
      </div>

      <div className="text-xs text-text-secondary text-center">
        Target range: {minReps}-{maxReps} reps
      </div>
    </div>
  )
}
