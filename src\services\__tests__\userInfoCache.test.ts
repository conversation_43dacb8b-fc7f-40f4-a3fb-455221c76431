import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import type { UserInfosModel } from '@/types/api'

// Mock the CacheManager
vi.mock('@/cache/CacheManager')

// Create a stable mock reference
const mockGetUserInfoStandalone = vi.fn()

// Mock the userInfoApi module
vi.mock('../userInfoApi', () => ({
  getUserInfoStandalone: mockGetUserInfoStandalone,
}))

// Mock logger
vi.mock('@/utils/logger', () => ({
  logger: {
    debug: vi.fn(),
    error: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
  },
}))

describe('userInfoCache - CacheManager Migration', () => {
  const mockUserInfo: UserInfosModel = {
    Id: 123,
    Firstname: '<PERSON>',
    Lastname: '<PERSON>e',
    Email: '<EMAIL>',
    MassUnit: 'kg',
    IsNormalSet: true,
    IsPyramid: false,
    IsQuickMode: false,
    CreatedDate: new Date().toISOString(),
    Username: 'johndoe',
    SelectedTab: 0,
    ConfirmDialogReps: true,
    ConfirmDialogWeight: true,
    MeasureSystem: 'metric',
    IsOfflineMode: false,
    RemindDayOfWeek: '1,3,5',
    RemindHourOfDay: 9,
    RemindMinuteOfDay: 0,
    IsMondayFirst: true,
    IsKg: true,
    IsLb: false,
    Is1RM: false,
    DefaultIncrementValue: 2.5,
    IsDropSet: false,
    IsReversePyramid: false,
    IsRestPause: false,
    IsBodyweight: false,
    RememberPerExercise: false,
    IsBackOff: false,
    IsNormalSets: true,
  } as UserInfosModel

  beforeEach(async () => {
    vi.useFakeTimers()

    // Clear module cache to reset singleton state
    vi.resetModules()

    // Reset mock to clear previous calls and set default behavior
    mockGetUserInfoStandalone.mockReset()
    mockGetUserInfoStandalone.mockResolvedValue(mockUserInfo)
  })

  afterEach(() => {
    vi.clearAllMocks()
    vi.useRealTimers()
    vi.resetModules()
  })

  describe('Migration from simple cache to CacheManager', () => {
    it('should maintain 5-minute TTL when using CacheManager', async () => {
      // Test rationale: Verify that migrating to CacheManager preserves the existing 5-minute TTL behavior

      // Import the new implementation (will be created)
      const { getServerUserInfoCached } = await import('../userInfoCache')

      // First call should fetch from API
      const result1 = await getServerUserInfoCached()
      expect(mockGetUserInfoStandalone).toHaveBeenCalledTimes(1)
      expect(result1).toEqual(mockUserInfo)

      // Advance time by 4 minutes (within TTL)
      vi.advanceTimersByTime(4 * 60 * 1000)

      // Second call should use cache
      const result2 = await getServerUserInfoCached()
      expect(mockGetUserInfoStandalone).toHaveBeenCalledTimes(1) // Still 1 call
      expect(result2).toEqual(mockUserInfo)

      // Advance time by 2 more minutes (total 6 minutes, exceeds TTL)
      vi.advanceTimersByTime(2 * 60 * 1000)

      // Third call should fetch fresh data
      const result3 = await getServerUserInfoCached()
      expect(mockGetUserInfoStandalone).toHaveBeenCalledTimes(2) // Now 2 calls
      expect(result3).toEqual(mockUserInfo)
    })

    it('should preserve request deduplication behavior', async () => {
      // Test rationale: Ensure multiple concurrent requests only trigger one API call

      const { getServerUserInfoCached } = await import('../userInfoCache')

      // Create delayed API response
      let resolvePromise: (value: UserInfosModel) => void
      const delayedPromise = new Promise<UserInfosModel>((resolve) => {
        resolvePromise = resolve
      })
      mockGetUserInfoStandalone.mockReturnValue(delayedPromise)

      // Make multiple concurrent requests
      const promise1 = getServerUserInfoCached()
      const promise2 = getServerUserInfoCached()
      const promise3 = getServerUserInfoCached()

      // API should only be called once
      expect(mockGetUserInfoStandalone).toHaveBeenCalledTimes(1)

      // Resolve the delayed promise
      resolvePromise!(mockUserInfo)

      // All promises should resolve with same data
      const [result1, result2, result3] = await Promise.all([
        promise1,
        promise2,
        promise3,
      ])
      expect(result1).toEqual(mockUserInfo)
      expect(result2).toEqual(mockUserInfo)
      expect(result3).toEqual(mockUserInfo)

      // Still only one API call
      expect(mockGetUserInfoStandalone).toHaveBeenCalledTimes(1)
    })

    it('should handle API errors gracefully', async () => {
      // Test rationale: Verify error handling remains consistent after migration

      const { getServerUserInfoCached } = await import('../userInfoCache')

      // Setup API to fail
      mockGetUserInfoStandalone.mockRejectedValue(new Error('Network error'))

      // Should return null on error
      const result = await getServerUserInfoCached()
      expect(result).toBeNull()

      // Should have attempted API call
      expect(mockGetUserInfoStandalone).toHaveBeenCalledTimes(1)
    })

    it('should handle null responses from API', async () => {
      // Test rationale: Ensure null responses are cached and handled correctly

      const { getServerUserInfoCached } = await import('../userInfoCache')

      // API returns null
      mockGetUserInfoStandalone.mockResolvedValue(null)

      // First call
      const result1 = await getServerUserInfoCached()
      expect(result1).toBeNull()
      expect(mockGetUserInfoStandalone).toHaveBeenCalledTimes(1)

      // Second call within TTL should use cached null
      const result2 = await getServerUserInfoCached()
      expect(result2).toBeNull()
      expect(mockGetUserInfoStandalone).toHaveBeenCalledTimes(1) // Still 1 call
    })

    it('should clear cache on clearUserInfoCache()', async () => {
      // Test rationale: Verify cache invalidation works correctly

      const { getServerUserInfoCached, clearUserInfoCache } = await import(
        '../userInfoCache'
      )

      // Populate cache
      await getServerUserInfoCached()
      expect(mockGetUserInfoStandalone).toHaveBeenCalledTimes(1)

      // Clear cache
      clearUserInfoCache()

      // Next call should fetch fresh data
      await getServerUserInfoCached()
      expect(mockGetUserInfoStandalone).toHaveBeenCalledTimes(2)
    })

    it('should handle invalid response types', async () => {
      // Test rationale: Security - ensure invalid responses are rejected

      const { getServerUserInfoCached } = await import('../userInfoCache')

      // API returns invalid type
      mockGetUserInfoStandalone.mockResolvedValue(
        'invalid string response' as any
      )

      const result = await getServerUserInfoCached()
      expect(result).toBeNull()
    })

    it('should support optional token parameter', async () => {
      // Test rationale: Ensure token passing works correctly

      const { getServerUserInfoCached } = await import('../userInfoCache')

      const token = 'test-auth-token'
      await getServerUserInfoCached(token)

      expect(mockGetUserInfoStandalone).toHaveBeenCalledWith(token)
    })

    it('should handle concurrent requests after cache expiry', async () => {
      // Test rationale: Ensure deduplication works even after cache expires

      const { getServerUserInfoCached } = await import('../userInfoCache')

      // Initial request to populate cache
      await getServerUserInfoCached()
      expect(mockGetUserInfoStandalone).toHaveBeenCalledTimes(1)

      // Advance time past TTL
      vi.advanceTimersByTime(6 * 60 * 1000)

      // Create delayed response for next request
      let resolvePromise: (value: UserInfosModel) => void
      const delayedPromise = new Promise<UserInfosModel>((resolve) => {
        resolvePromise = resolve
      })
      mockGetUserInfoStandalone.mockReturnValue(delayedPromise)

      // Multiple concurrent requests after expiry
      const promise1 = getServerUserInfoCached()
      const promise2 = getServerUserInfoCached()

      // Should only make one new API call
      expect(mockGetUserInfoStandalone).toHaveBeenCalledTimes(2) // 1 initial + 1 new

      // Resolve
      resolvePromise!(mockUserInfo)
      const [result1, result2] = await Promise.all([promise1, promise2])

      expect(result1).toEqual(mockUserInfo)
      expect(result2).toEqual(mockUserInfo)
    })
  })

  describe('CacheManager-specific features', () => {
    it('should integrate with CacheManager namespace isolation', async () => {
      // Test rationale: Verify that userInfo cache uses proper namespace

      // This test will validate the implementation uses CacheManager correctly
      // Currently will fail as implementation doesn't use CacheManager yet

      const { getServerUserInfoCached } = await import('../userInfoCache')

      // Should use 'userInfo' namespace in CacheManager
      await getServerUserInfoCached()

      // Once implemented, we can verify CacheManager was called with correct namespace
      // expect(cacheManager.get).toHaveBeenCalledWith('userInfo', expect.any(Object))
    })

    it('should benefit from CacheManager statistics tracking', async () => {
      // Test rationale: Verify cache hit/miss statistics are tracked

      // This will validate that the implementation properly tracks cache performance
      // Currently will fail as not implemented

      const { getServerUserInfoCached } = await import('../userInfoCache')

      // First call - cache miss
      await getServerUserInfoCached()

      // Second call - cache hit
      await getServerUserInfoCached()

      // Once implemented, check statistics
      // const stats = cacheManager.getStats()
      // expect(stats.hits).toBe(1)
      // expect(stats.misses).toBe(1)
    })

    it('should handle storage adapter fallback gracefully', async () => {
      // Test rationale: Verify system works even if localStorage is unavailable

      // This will test that CacheManager fallback to memory adapter works
      // Currently will fail as not implemented

      const { getServerUserInfoCached } = await import('../userInfoCache')

      // Simulate localStorage being unavailable
      const originalLocalStorage = global.localStorage
      Object.defineProperty(global, 'localStorage', {
        value: undefined,
        writable: true,
      })

      // Should still work with memory adapter
      const result = await getServerUserInfoCached()
      expect(result).toEqual(mockUserInfo)

      // Restore
      global.localStorage = originalLocalStorage
    })
  })
})
