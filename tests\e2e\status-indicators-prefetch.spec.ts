import { test, expect, Page, devices } from '@playwright/test'
import { login } from './helpers/auth'

// Test data
const TEST_ACCOUNT = {
  email: '<EMAIL>',
  password: 'Dr123456',
}

// Mobile viewport configurations
const MOBILE_VIEWPORTS = {
  iphoneSE: devices['iPhone SE'],
  pixel5: devices['Pixel 5'],
  galaxyS8: { viewport: { width: 360, height: 740 } },
}

// Helper to wait for StatusIndicators to be visible
async function waitForStatusIndicators(page: Page) {
  await page.waitForSelector('[data-testid="status-indicators-container"]', {
    state: 'visible',
    timeout: 10000,
  })
}

// Helper to get StatusIndicators text content
async function getStatusIndicatorsText(page: Page): Promise<string> {
  const container = await page.locator(
    '[data-testid="status-indicators-container"]'
  )
  return (await container.textContent()) || ''
}

test.describe('StatusIndicators Prefetch Flow', () => {
  test.describe.configure({ mode: 'serial' })

  test('should show prefetch progress after login and navigation to workout page', async ({
    page,
  }) => {
    // Given: User is not logged in
    await page.goto('/login')

    // When: User logs in successfully
    await login(page, TEST_ACCOUNT.email, TEST_ACCOUNT.password)

    // Then: Should redirect to program page
    await page.waitForURL('**/program', { timeout: 15000 })

    // Navigate to workout page where StatusIndicators is displayed
    await page.goto('/workout')

    // And: StatusIndicators should be visible
    await waitForStatusIndicators(page)

    // And: Should show loading state or success state
    const statusText = await getStatusIndicatorsText(page)
    const hasValidState =
      statusText.includes('Loading exercises') ||
      statusText.includes('exercises ready') ||
      statusText.includes('Offline Mode')

    expect(hasValidState).toBeTruthy()

    // Verify the container is visible and has proper styling
    const container = await page.locator(
      '[data-testid="status-indicators-container"]'
    )
    await expect(container).toBeVisible()

    // Verify minimum height for touch targets (52px)
    const badges = await container.locator('[role="status"]').all()
    await Promise.all(
      badges.map(async (badge) => {
        const boundingBox = await badge.boundingBox()
        if (boundingBox) {
          expect(boundingBox.height).toBeGreaterThanOrEqual(52)
        }
      })
    )
  })

  test('should show prefetch progress percentages during loading', async ({
    page,
  }) => {
    // Mock slower API responses to see progress
    await page.route('**/api/SetLoader*', async (route) => {
      await page.waitForTimeout(500) // Add delay to see progress
      await route.continue()
    })

    await page.goto('/login')
    await login(page, TEST_ACCOUNT.email, TEST_ACCOUNT.password)
    await page.waitForURL('**/program', { timeout: 15000 })

    // Navigate to workout page
    await page.goto('/workout')

    // Wait for StatusIndicators
    await waitForStatusIndicators(page)

    // Check for loading state with percentage
    const statusText = await getStatusIndicatorsText(page)

    // Should show either loading with percentage or completed state
    const hasProgressOrComplete =
      statusText.match(/Loading exercises.*\d+%/) ||
      statusText.includes('exercises ready')

    expect(hasProgressOrComplete).toBeTruthy()
  })

  test('should maintain state on page reload during prefetch', async ({
    page,
  }) => {
    await page.goto('/login')
    await login(page, TEST_ACCOUNT.email, TEST_ACCOUNT.password)
    await page.waitForURL('**/program', { timeout: 15000 })

    // Navigate to workout page
    await page.goto('/workout')

    // Wait for initial StatusIndicators
    await waitForStatusIndicators(page)
    await getStatusIndicatorsText(page)

    // Reload the page
    await page.reload()

    // StatusIndicators should still be visible after reload
    await waitForStatusIndicators(page)
    const reloadedText = await getStatusIndicatorsText(page)

    // Should maintain some state (either loading or ready)
    expect(reloadedText).toBeTruthy()
    expect(reloadedText.length).toBeGreaterThan(0)
  })

  test('should handle network failure gracefully', async ({
    page,
    context,
  }) => {
    await page.goto('/login')
    await login(page, TEST_ACCOUNT.email, TEST_ACCOUNT.password)
    await page.waitForURL('**/program', { timeout: 15000 })

    // Wait for initial load
    await waitForStatusIndicators(page)

    // Simulate network failure
    await context.setOffline(true)

    // Wait a moment for the UI to update
    await page.waitForTimeout(1000)

    // Should show offline mode
    const offlineText = await getStatusIndicatorsText(page)
    expect(offlineText).toContain('Offline Mode')

    // Restore network
    await context.setOffline(false)

    // Wait for online state to restore
    await page.waitForTimeout(2000)

    // Should recover and show normal state
    const onlineText = await getStatusIndicatorsText(page)
    expect(onlineText).not.toContain('Offline Mode')
  })

  test('should show smart scheduling indicator when enabled', async ({
    page,
  }) => {
    await page.goto('/login')
    await login(page, TEST_ACCOUNT.email, TEST_ACCOUNT.password)
    await page.waitForURL('**/program', { timeout: 15000 })

    // Navigate to workout page
    await page.goto('/workout')

    // Wait for StatusIndicators
    await waitForStatusIndicators(page)

    // Give time for prefetch to complete
    await page.waitForTimeout(3000)

    // Check for smart scheduling indicator (⚡)
    const statusText = await getStatusIndicatorsText(page)

    // Smart scheduling may or may not be enabled based on account
    // Just verify the UI can handle both states
    const hasValidState =
      statusText.includes('⚡') ||
      statusText.includes('exercises ready') ||
      statusText.includes('Loading exercises')

    expect(hasValidState).toBeTruthy()
  })
})

test.describe('StatusIndicators Mobile Viewport', () => {
  test('should be visible and functional on iPhone SE viewport', async ({
    browser,
  }) => {
    const context = await browser.newContext({
      ...MOBILE_VIEWPORTS.iphoneSE,
      locale: 'en-US',
    })
    const page = await context.newPage()

    await page.goto('/login')
    await login(page, TEST_ACCOUNT.email, TEST_ACCOUNT.password)
    await page.waitForURL('**/program', { timeout: 15000 })

    // StatusIndicators should be visible on mobile
    await waitForStatusIndicators(page)

    // Verify touch target sizes
    const container = await page.locator(
      '[data-testid="status-indicators-container"]'
    )
    const badges = await container.locator('[role="status"]').all()

    await Promise.all(
      badges.map(async (badge) => {
        const boundingBox = await badge.boundingBox()
        if (boundingBox) {
          // Should maintain 52px minimum height on mobile
          expect(boundingBox.height).toBeGreaterThanOrEqual(52)
        }
      })
    )

    // Verify text is readable
    const statusText = await getStatusIndicatorsText(page)
    expect(statusText).toBeTruthy()

    await context.close()
  })

  test('should be visible on Android Pixel 5 viewport', async ({ browser }) => {
    const context = await browser.newContext({
      ...MOBILE_VIEWPORTS.pixel5,
      locale: 'en-US',
    })
    const page = await context.newPage()

    await page.goto('/login')
    await login(page, TEST_ACCOUNT.email, TEST_ACCOUNT.password)
    await page.waitForURL('**/program', { timeout: 15000 })

    // StatusIndicators should be visible
    await waitForStatusIndicators(page)

    // Verify container is visible
    const container = await page.locator(
      '[data-testid="status-indicators-container"]'
    )
    await expect(container).toBeVisible()

    // Verify proper styling for mobile
    const hasProperStyling = await container.evaluate((el) => {
      const styles = window.getComputedStyle(el)
      return styles.display !== 'none' && styles.visibility !== 'hidden'
    })
    expect(hasProperStyling).toBeTruthy()

    await context.close()
  })

  test('should handle scroll position on mobile', async ({ browser }) => {
    const context = await browser.newContext({
      ...MOBILE_VIEWPORTS.iphoneSE,
      locale: 'en-US',
    })
    const page = await context.newPage()

    await page.goto('/login')
    await login(page, TEST_ACCOUNT.email, TEST_ACCOUNT.password)
    await page.waitForURL('**/program', { timeout: 15000 })

    await waitForStatusIndicators(page)

    // Scroll down
    await page.evaluate(() => window.scrollTo(0, 500))
    await page.waitForTimeout(500)

    // StatusIndicators should still be accessible
    const container = await page.locator(
      '[data-testid="status-indicators-container"]'
    )

    // Container should either be in viewport or easily accessible
    expect(await container.count()).toBeGreaterThan(0)

    await context.close()
  })
})

test.describe('StatusIndicators Progress Tracking', () => {
  test('should show accurate exercise counts', async ({ page }) => {
    await page.goto('/login')
    await login(page, TEST_ACCOUNT.email, TEST_ACCOUNT.password)
    await page.waitForURL('**/program', { timeout: 15000 })

    await waitForStatusIndicators(page)

    // Wait for prefetch to complete or show progress
    await page.waitForTimeout(5000)

    const statusText = await getStatusIndicatorsText(page)

    // Should show exercise count in some form
    const hasExerciseInfo =
      statusText.match(/\d+\s+exercise/) || // "3 exercises ready"
      statusText.includes('Loading exercises') // Still loading

    expect(hasExerciseInfo).toBeTruthy()

    // If showing count, verify proper pluralization
    const match = statusText.match(/(\d+)\s+(exercise|exercises)/)
    if (match) {
      const count = parseInt(match[1])
      const plural = match[2]
      if (count === 1) {
        expect(plural).toBe('exercise')
      } else {
        expect(plural).toBe('exercises')
      }
    }
  })

  test('should update counts as exercises load', async ({ page }) => {
    // Add delay to API calls to observe count changes
    let callCount = 0
    await page.route('**/api/SetLoader*', async (route) => {
      callCount++
      if (callCount <= 3) {
        await page.waitForTimeout(1000) // Delay first few calls
      }
      await route.continue()
    })

    await page.goto('/login')
    await login(page, TEST_ACCOUNT.email, TEST_ACCOUNT.password)
    await page.waitForURL('**/program', { timeout: 15000 })

    await waitForStatusIndicators(page)

    // Capture initial state
    const initialText = await getStatusIndicatorsText(page)

    // Wait for updates
    await page.waitForTimeout(3000)

    // Capture updated state
    const updatedText = await getStatusIndicatorsText(page)

    // States should potentially be different as loading progresses
    // (unless already fully loaded)
    expect(initialText.length).toBeGreaterThan(0)
    expect(updatedText.length).toBeGreaterThan(0)
  })
})

test.describe('StatusIndicators Auth Flow', () => {
  test('should clear state on logout', async ({ page }) => {
    await page.goto('/login')
    await login(page, TEST_ACCOUNT.email, TEST_ACCOUNT.password)
    await page.waitForURL('**/program', { timeout: 15000 })

    await waitForStatusIndicators(page)

    // Navigate to settings and logout
    await page.goto('/settings')

    // Click logout button
    const logoutButton = page.locator('button:has-text("Log out")')
    await logoutButton.click()

    // Should redirect to login
    await page.waitForURL('**/login', { timeout: 10000 })

    // StatusIndicators should not be visible on login page
    const container = page.locator(
      '[data-testid="status-indicators-container"]'
    )
    await expect(container)
      .not.toBeVisible({ timeout: 1000 })
      .catch(() => {
        // Container might not exist at all, which is also valid
      })
  })

  test('should handle session expiry gracefully', async ({ page }) => {
    await page.goto('/login')
    await login(page, TEST_ACCOUNT.email, TEST_ACCOUNT.password)
    await page.waitForURL('**/program', { timeout: 15000 })

    await waitForStatusIndicators(page)

    // Simulate session expiry by clearing auth storage
    await page.evaluate(() => {
      localStorage.clear()
      sessionStorage.clear()
    })

    // Reload page
    await page.reload()

    // Should redirect to login
    await page.waitForURL('**/login', { timeout: 10000 })

    // StatusIndicators should not be visible
    const container = page.locator(
      '[data-testid="status-indicators-container"]'
    )
    await expect(container)
      .not.toBeVisible({ timeout: 1000 })
      .catch(() => {
        // Container might not exist, which is valid
      })
  })
})

test.describe('StatusIndicators Retry Mechanism', () => {
  test('should show retry count on failures', async ({ page }) => {
    // Simulate some API failures
    let failCount = 0
    await page.route('**/api/SetLoader*', async (route) => {
      failCount++
      if (failCount <= 2) {
        // Fail first 2 requests
        await route.fulfill({
          status: 500,
          body: JSON.stringify({ error: 'Server error' }),
        })
      } else {
        // Then succeed
        await route.continue()
      }
    })

    await page.goto('/login')
    await login(page, TEST_ACCOUNT.email, TEST_ACCOUNT.password)
    await page.waitForURL('**/program', { timeout: 15000 })

    await waitForStatusIndicators(page)

    // Wait for retries to happen
    await page.waitForTimeout(3000)

    const statusText = await getStatusIndicatorsText(page)

    // Should either show retry info or have recovered
    const hasValidState =
      statusText.includes('retry') ||
      statusText.includes('failed') ||
      statusText.includes('exercises ready')

    expect(hasValidState).toBeTruthy()
  })
})
