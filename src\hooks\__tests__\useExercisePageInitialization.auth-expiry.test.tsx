import { renderHook, waitFor } from '@testing-library/react'
import { vi } from 'vitest'
import { useExercisePageInitialization } from '../useExercisePageInitialization'
import { useWorkout } from '../useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useRouter } from 'next/navigation'

// Mock dependencies
vi.mock('../useWorkout')
vi.mock('@/stores/workoutStore')
vi.mock('next/navigation')
vi.mock('@/utils/debugLog', () => ({
  debugLog: Object.assign(vi.fn(), {
    log: vi.fn(),
    error: vi.fn(),
  }),
}))
vi.mock('@/utils/RecommendationLoadingCoordinator', () => ({
  RecommendationLoadingCoordinator: {
    getInstance: vi.fn(() => ({
      canStartLoading: vi.fn(() => true),
      startLoading: vi.fn(),
    })),
  },
}))

describe('useExercisePageInitialization - Auth Expiry', () => {
  const mockRouter = {
    replace: vi.fn(),
    push: vi.fn(),
  }

  const mockWorkout = {
    todaysWorkout: [
      {
        WorkoutTemplates: [{ id: 1, name: 'Test Workout' }],
      },
    ],
    isLoadingWorkout: false,
    startWorkout: vi.fn().mockResolvedValue({ success: true }),
    exercises: [
      { Id: 123, Label: 'Bench Press' },
      { Id: 456, Label: 'Squat' },
    ],
    workoutSession: { id: 'session-1' },
    loadRecommendation: vi.fn(),
    updateExerciseWorkSets: vi.fn(),
  }

  const mockWorkoutStore = {
    setCurrentExerciseById: vi.fn(),
    loadingStates: new Map(),
    getCachedExerciseRecommendation: vi.fn().mockReturnValue(null),
  }

  beforeEach(() => {
    vi.clearAllMocks()
    ;(useRouter as any).mockReturnValue(mockRouter)
    ;(useWorkout as any).mockReturnValue(mockWorkout)
    ;(useWorkoutStore as any).mockReturnValue(mockWorkoutStore)
  })

  describe('Auth Token Handling', () => {
    it('should NOT check auth status - handled by API client', async () => {
      // When: Exercise page initializes without auth token
      const { result } = renderHook(() => useExercisePageInitialization(123))

      // Then: Should proceed with initialization without redirect
      await waitFor(() => {
        expect(result.current.isInitializing).toBe(false)
      })

      // And: Should NOT redirect to login
      expect(mockRouter.replace).not.toHaveBeenCalledWith('/login')

      // And: Should set current exercise and load recommendations
      expect(mockWorkoutStore.setCurrentExerciseById).toHaveBeenCalledWith(123)
      expect(mockWorkout.loadRecommendation).toHaveBeenCalledWith(
        123,
        'Bench Press'
      )
    })

    it('should NOT redirect when API returns 401 - let API client handle token refresh', async () => {
      // Mock no workout session to trigger startWorkout
      const workoutWithNoSession = { ...mockWorkout, workoutSession: null }
      ;(useWorkout as any).mockReturnValue(workoutWithNoSession)

      // Mock startWorkout to simulate 401 error
      workoutWithNoSession.startWorkout.mockRejectedValueOnce({
        response: { status: 401 },
        message: 'Unauthorized',
      })

      // When: Exercise page initializes
      const { result } = renderHook(() => useExercisePageInitialization(123))

      // Then: Should attempt workout start and set error
      await waitFor(() => {
        expect(workoutWithNoSession.startWorkout).toHaveBeenCalled()
        expect(result.current.loadingError).toBeTruthy()
      })

      // And: Should NOT redirect (API client will handle)
      expect(mockRouter.replace).not.toHaveBeenCalledWith('/login')
    })

    it('should proceed with initialization when valid token is present', async () => {
      // When: Exercise page initializes
      const { result } = renderHook(() => useExercisePageInitialization(123))

      // Then: Should proceed with normal initialization
      await waitFor(() => {
        expect(result.current.isInitializing).toBe(false)
      })

      // And: Should set current exercise and load recommendations
      expect(mockWorkoutStore.setCurrentExerciseById).toHaveBeenCalledWith(123)
      expect(mockWorkout.loadRecommendation).toHaveBeenCalledWith(
        123,
        'Bench Press'
      )

      // And: Should NOT redirect
      expect(mockRouter.replace).not.toHaveBeenCalledWith('/login')
    })

    it('should NOT redirect when token expires during initialization', async () => {
      // Mock workout without session to trigger initialization
      const workoutNoSession = { ...mockWorkout, workoutSession: null }
      ;(useWorkout as any).mockReturnValue(workoutNoSession)

      // When: Exercise page starts initializing
      const { result } = renderHook(() => useExercisePageInitialization(123))

      // Wait for initial render
      await waitFor(() => {
        expect(workoutNoSession.startWorkout).toHaveBeenCalled()
      })

      // Clear mocks
      vi.clearAllMocks()

      // And: Token expires and next call fails with 401
      workoutNoSession.startWorkout.mockRejectedValueOnce({
        response: { status: 401 },
      })

      // Trigger retry
      await result.current.retryInitialization()

      // Then: Should NOT redirect (let API client handle)
      expect(mockRouter.replace).not.toHaveBeenCalledWith('/login')
      expect(result.current.loadingError).toBeTruthy()
    })

    it('should allow retry without auth validation', async () => {
      // Mock initial failure
      const workoutNoSession = { ...mockWorkout, workoutSession: null }
      ;(useWorkout as any).mockReturnValue(workoutNoSession)
      workoutNoSession.startWorkout.mockRejectedValueOnce(new Error('Failed'))

      // When: Exercise page initializes
      const { result } = renderHook(() => useExercisePageInitialization(123))

      await waitFor(() => {
        expect(result.current.loadingError).toBeTruthy()
      })

      // Reset mocks
      vi.clearAllMocks()
      workoutNoSession.startWorkout.mockResolvedValueOnce({ success: true })

      // When: Retry is attempted
      await result.current.retryInitialization()

      // Then: Should retry without auth checks
      expect(mockRouter.replace).not.toHaveBeenCalledWith('/login')
      expect(workoutNoSession.startWorkout).toHaveBeenCalled()
    })
  })

  describe('Error Handling', () => {
    it('should differentiate between auth errors and other errors', async () => {
      // Mock workout without session to trigger initialization
      const workoutNoSession = { ...mockWorkout, workoutSession: null }
      ;(useWorkout as any).mockReturnValue(workoutNoSession)

      // Mock network error (not auth error)
      workoutNoSession.startWorkout.mockRejectedValueOnce(
        new Error('Network unavailable')
      )

      // When: Exercise page initializes
      const { result } = renderHook(() => useExercisePageInitialization(123))

      // Then: Should set error but NOT redirect to login
      await waitFor(() => {
        expect(result.current.loadingError).toBeTruthy()
        expect(result.current.loadingError?.message).toContain('Network')
      })

      expect(mockRouter.replace).not.toHaveBeenCalledWith('/login')
    })
  })
})
