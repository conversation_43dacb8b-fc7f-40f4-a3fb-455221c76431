import { test, expect } from '@playwright/test'

test.describe('Session Persistence After Background', () => {
  test.beforeEach(async ({ page }) => {
    // Start at login page
    await page.goto('/login')
  })

  test('should allow users to access exercises after extended background period without re-login', async ({
    page,
  }) => {
    // Wait for login form to be ready
    await page.waitForSelector('#email', { timeout: 10000 })

    // Login first
    await page.locator('#email').fill('<EMAIL>')
    await page.locator('#password').fill('Dr123456')
    await page.getByRole('button', { name: 'Login' }).click()

    // Wait for successful login and redirect to program page
    await page.waitForURL('**/program', { timeout: 30000 })

    // Navigate to workout page
    await page.goto('/workout')
    await page.waitForURL('**/workout')

    // Wait for exercise cards to load
    await page.waitForSelector('[data-testid="exercise-card"]', {
      timeout: 10000,
    })

    // Navigate to first exercise
    const firstExerciseCard = page
      .locator('[data-testid="exercise-card"]')
      .first()
    await firstExerciseCard.click()

    // Wait for exercise page to load
    await page.waitForURL('**/workout/exercise/**')
    await expect(page.locator('h1')).toBeVisible()

    // Store the current URL
    const exerciseUrl = page.url()

    // Simulate token expiry by modifying the token in localStorage
    // This will make the next API call return 401
    await page.evaluate(() => {
      const tokenData = localStorage.getItem('auth-storage')
      if (tokenData) {
        const parsed = JSON.parse(tokenData)
        // Modify the access token to make it invalid
        if (parsed.state && parsed.state.token) {
          parsed.state.token = `expired-token-${Date.now()}`
          localStorage.setItem('auth-storage', JSON.stringify(parsed))
        }
      }
    })

    // Navigate back to workout page
    await page.goto('/workout')
    await page.waitForURL('**/workout')

    // Now navigate back to the exercise page
    // This should trigger a 401 error, but the token refresh should handle it
    await page.goto(exerciseUrl)

    // Verify we stay on the exercise page and are not redirected to login
    await expect(page).toHaveURL(exerciseUrl)
    await expect(page.locator('h1')).toBeVisible()

    // Verify we can still interact with the page
    // Look for any interactive element (e.g., rest timer button, set controls)
    const interactiveElements = page.locator(
      'button:visible, [role="button"]:visible'
    )
    await expect(interactiveElements.first()).toBeVisible()

    // Ensure we're NOT on the login page
    await expect(page).not.toHaveURL('**/login')
  })

  test('should handle token refresh transparently when accessing exercise pages', async ({
    page,
  }) => {
    // Wait for login form to be ready
    await page.waitForSelector('#email', { timeout: 10000 })

    // Login
    await page.locator('#email').fill('<EMAIL>')
    await page.locator('#password').fill('Dr123456')
    await page.getByRole('button', { name: 'Login' }).click()

    // Wait for redirect to program
    await page.waitForURL('**/program', { timeout: 30000 })

    // Navigate to workout page
    await page.goto('/workout')
    await page.waitForURL('**/workout')

    // Intercept API calls to simulate 401 response initially
    let interceptCount = 0
    await page.route('**/api/v2/**', async (route) => {
      interceptCount++
      // Simulate 401 on first request to trigger token refresh
      if (interceptCount === 1) {
        await route.fulfill({
          status: 401,
          json: { error: 'Unauthorized' },
        })
      } else {
        // Let subsequent requests through (after token refresh)
        await route.continue()
      }
    })

    // Try to navigate to an exercise
    const firstExerciseCard = page
      .locator('[data-testid="exercise-card"]')
      .first()
    await firstExerciseCard.click()

    // Should successfully navigate to exercise page despite initial 401
    await page.waitForURL('**/workout/exercise/**')
    await expect(page.locator('h1')).toBeVisible()

    // Verify we're still logged in and not redirected to login
    await expect(page).not.toHaveURL('**/login')
  })
})
