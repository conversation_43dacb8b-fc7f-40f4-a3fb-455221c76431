import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { act, renderHook } from '@testing-library/react'
import { useOfflineStore } from '../offlineStore'
import { createDefaultCacheManager } from '@/cache'

// Mock cache manager
const mockCacheManager = {
  set: vi.fn().mockResolvedValue(undefined),
  get: vi.fn().mockResolvedValue(null),
  has: vi.fn().mockResolvedValue(false),
  delete: vi.fn().mockResolvedValue(true),
  clear: vi.fn().mockResolvedValue(undefined),
  getStats: vi.fn().mockReturnValue({
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    size: 0,
    memoryUsage: 0,
  }),
}

// Mock the cache manager
vi.mock('@/cache', () => ({
  createDefaultCacheManager: vi.fn(() => mockCacheManager),
}))

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
})

describe('useOfflineStore', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    // Reset cache manager mocks
    mockCacheManager.set.mockClear()
    mockCacheManager.get.mockClear()
    mockCacheManager.has.mockClear()
    mockCacheManager.delete.mockClear()
    mockCacheManager.clear.mockClear()
    mockCacheManager.getStats.mockClear()

    // Reset localStorage mock
    mockLocalStorage.getItem.mockReturnValue(null)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Initial State', () => {
    it('should initialize with default state', () => {
      const { result } = renderHook(() => useOfflineStore())

      expect(result.current.isOfflineWorkoutLoaded).toBe(false)
      expect(result.current.loadedWorkoutId).toBeNull()
      expect(result.current.isLoadingOfflineWorkout).toBe(false)
      expect(result.current.syncStatus).toBe('idle')
      expect(result.current.lastSyncAttempt).toBeNull()
      expect(result.current.queuedRequestsCount).toBe(0)
    })

    it('should initialize cache manager on first access', () => {
      const { result } = renderHook(() => useOfflineStore())

      // Access the cache manager to trigger initialization
      const { cacheManager } = result.current

      expect(cacheManager).toBeDefined()
      expect(createDefaultCacheManager).toHaveBeenCalledTimes(1)
    })
  })

  describe('Workout Loading State', () => {
    it('should set loading state when starting workout load', () => {
      const { result } = renderHook(() => useOfflineStore())

      act(() => {
        result.current.setLoadingOfflineWorkout(true)
      })

      expect(result.current.isLoadingOfflineWorkout).toBe(true)
    })

    it('should clear loading state when workout load completes', () => {
      const { result } = renderHook(() => useOfflineStore())

      act(() => {
        result.current.setLoadingOfflineWorkout(true)
      })

      act(() => {
        result.current.setLoadingOfflineWorkout(false)
      })

      expect(result.current.isLoadingOfflineWorkout).toBe(false)
    })
  })

  describe('Offline Workout Management', () => {
    it('should mark workout as loaded with ID', () => {
      const { result } = renderHook(() => useOfflineStore())
      const workoutId = 'workout-123'

      act(() => {
        result.current.setOfflineWorkoutLoaded(workoutId)
      })

      expect(result.current.isOfflineWorkoutLoaded).toBe(true)
      expect(result.current.loadedWorkoutId).toBe(workoutId)
    })

    it('should clear loaded workout', () => {
      const { result } = renderHook(() => useOfflineStore())

      // First load a workout
      act(() => {
        result.current.setOfflineWorkoutLoaded('workout-123')
      })

      // Then clear it
      act(() => {
        result.current.clearOfflineWorkout()
      })

      expect(result.current.isOfflineWorkoutLoaded).toBe(false)
      expect(result.current.loadedWorkoutId).toBeNull()
    })
  })

  describe('Sync Status Management', () => {
    it('should update sync status', () => {
      const { result } = renderHook(() => useOfflineStore())

      act(() => {
        result.current.setSyncStatus('syncing')
      })

      expect(result.current.syncStatus).toBe('syncing')
    })

    it('should record sync attempt timestamp', () => {
      const { result } = renderHook(() => useOfflineStore())
      const beforeSync = Date.now()

      act(() => {
        result.current.recordSyncAttempt()
      })

      const afterSync = Date.now()
      expect(result.current.lastSyncAttempt).toBeGreaterThanOrEqual(beforeSync)
      expect(result.current.lastSyncAttempt).toBeLessThanOrEqual(afterSync)
    })
  })

  describe('Queue Management', () => {
    it('should update queued requests count', () => {
      const { result } = renderHook(() => useOfflineStore())

      act(() => {
        result.current.setQueuedRequestsCount(5)
      })

      expect(result.current.queuedRequestsCount).toBe(5)
    })
  })

  describe('Cache Integration', () => {
    it('should provide access to cache manager', () => {
      const { result } = renderHook(() => useOfflineStore())

      expect(result.current.cacheManager).toBe(mockCacheManager)
    })

    it('should cache workout data', async () => {
      const { result } = renderHook(() => useOfflineStore())
      const workoutData = { id: 'workout-123', exercises: [] }

      await act(async () => {
        await result.current.cacheWorkoutData('workout-123', workoutData)
      })

      expect(mockCacheManager.set).toHaveBeenCalledWith(
        'workout-123',
        workoutData,
        { namespace: 'offline-workouts', ttl: 24 * 60 * 60 * 1000 }
      )
    })

    it('should retrieve cached workout data', async () => {
      const { result } = renderHook(() => useOfflineStore())
      const workoutData = { id: 'workout-123', exercises: [] }

      mockCacheManager.get.mockResolvedValue(workoutData)

      let cachedData: unknown
      await act(async () => {
        cachedData = await result.current.getCachedWorkoutData('workout-123')
      })

      expect(mockCacheManager.get).toHaveBeenCalledWith(
        'workout-123',
        'offline-workouts'
      )
      expect(cachedData).toEqual(workoutData)
    })

    it('should clear cached workout data', async () => {
      const { result } = renderHook(() => useOfflineStore())

      await act(async () => {
        await result.current.clearCachedWorkoutData('workout-123')
      })

      expect(mockCacheManager.delete).toHaveBeenCalledWith(
        'workout-123',
        'offline-workouts'
      )
    })
  })

  describe('State Persistence', () => {
    it('should persist state to localStorage', () => {
      const { result } = renderHook(() => useOfflineStore())

      act(() => {
        result.current.setOfflineWorkoutLoaded('workout-123')
        result.current.setSyncStatus('syncing')
      })

      // Zustand persist middleware should handle localStorage calls
      // We can't easily test this without mocking the entire persist middleware
      // This would be better tested in integration tests
    })
  })

  describe('Error Handling', () => {
    it('should handle cache errors gracefully', async () => {
      const { result } = renderHook(() => useOfflineStore())
      const error = new Error('Cache error')

      mockCacheManager.set.mockRejectedValue(error)

      // Should not throw
      await act(async () => {
        await expect(
          result.current.cacheWorkoutData('workout-123', {})
        ).rejects.toThrow('Cache error')
      })
    })
  })
})
