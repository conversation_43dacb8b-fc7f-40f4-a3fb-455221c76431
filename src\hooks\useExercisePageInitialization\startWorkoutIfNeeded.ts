/**
 * Utility function to handle workout initialization
 * Extracted from useExercisePageInitialization to reduce file size
 */

import { WorkoutTemplateGroupModel } from '@/types'
import { WorkoutSession } from '@/types/workoutSession'
import { debugLog } from '@/utils/debugLog'
import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime'

interface StartWorkoutIfNeededParams {
  workoutSession: WorkoutSession | null
  todaysWorkout: WorkoutTemplateGroupModel[] | null
  isLoadingWorkout: boolean
  startWorkout: (
    workout: WorkoutTemplateGroupModel[]
  ) => Promise<{ success: boolean }>
  router: AppRouterInstance
  setLoadingError: (error: Error | null) => void
}

export async function startWorkoutIfNeeded({
  workoutSession,
  todaysWorkout,
  isLoadingWorkout,
  startWorkout,
  router,
  setLoadingError,
}: StartWorkoutIfNeededParams): Promise<void> {
  try {
    setLoadingError(null)

    // Guard: Skip workout start if session already exists
    if (workoutSession) {
      debugLog(
        '✅ [ExercisePageClient] Workout session already exists, skipping workout start',
        {
          sessionId: workoutSession.id,
        }
      )
      // Don't set isInitializing to false here - let the exercise setup effect handle it
      return
    }

    if (todaysWorkout && !isLoadingWorkout) {
      debugLog('🚀 [ExercisePageClient] Starting workout...', {
        todaysWorkout,
      })

      const workoutGroup = todaysWorkout[0]
      const workout = workoutGroup?.WorkoutTemplates?.[0]

      if (workout) {
        const result = await startWorkout(todaysWorkout)
        if (result.success) {
          debugLog('✅ [ExercisePageClient] Workout started successfully')
        } else {
          debugLog.error('Failed to start workout in initialization')
        }
      } else {
        debugLog.error(
          '❌ [ExercisePageClient] No workout template found, redirecting...'
        )
        router.replace('/workout')
      }
    }
  } catch (error: unknown) {
    debugLog.error('Failed to start workout:', error)
    // Auth errors are handled by API client interceptor
    setLoadingError(
      error instanceof Error ? error : new Error('Failed to start workout')
    )
  }
}
