import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook } from '@testing-library/react'
import { useProgressivePrefetch } from '../useProgressivePrefetch'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useExerciseSetsPrefetch } from '../useExerciseSetsPrefetch'

// Mock dependencies
vi.mock('@/stores/workoutStore')
vi.mock('../useExerciseSetsPrefetch')

describe('useProgressivePrefetch', () => {
  const mockPrefetchExerciseSets = vi.fn().mockResolvedValue(undefined)
  const mockExercises = [
    { Id: 1, Label: 'Bench Press' },
    { Id: 2, Label: 'Squats' },
    { Id: 3, Label: 'Deadlifts' },
    { Id: 4, Label: 'Rows' },
    { Id: 5, Label: 'Pull-ups' },
    { Id: 6, Label: 'Shoulder Press' },
  ]

  beforeEach(() => {
    vi.clearAllMocks()
    mockPrefetchExerciseSets.mockResolvedValue(undefined)

    // Mock useWorkoutStore
    vi.mocked(useWorkoutStore).mockReturnValue({
      exercises: mockExercises,
      currentExerciseIndex: 0,
      workoutSession: { id: 'session-1', startTime: new Date() },
      previewExerciseSkips: new Set(),
    } as any)

    // Mock useExerciseSetsPrefetch
    vi.mocked(useExerciseSetsPrefetch).mockReturnValue({
      prefetchExerciseSets: mockPrefetchExerciseSets,
      prefetchStatus: {},
      isPrefetching: false,
      prefetchedExerciseIds: [],
      getPrefetchProgress: vi.fn(),
    })
  })

  it('should prefetch next exercises when exercise index changes', async () => {
    const { rerender } = renderHook(() => useProgressivePrefetch())

    // Initial state - no prefetch yet
    expect(mockPrefetchExerciseSets).not.toHaveBeenCalled()

    // Move to exercise 1 (index 0 to 1)
    vi.mocked(useWorkoutStore).mockReturnValue({
      exercises: mockExercises,
      currentExerciseIndex: 1,
      workoutSession: { id: 'session-1', startTime: new Date() },
      previewExerciseSkips: new Set(),
    } as any)

    rerender()

    // Should prefetch exercises 4 and 5 (indices 3 and 4)
    expect(mockPrefetchExerciseSets).toHaveBeenCalledWith([4, 5])
  })

  it('should not prefetch if no workout session', () => {
    vi.mocked(useWorkoutStore).mockReturnValue({
      exercises: mockExercises,
      currentExerciseIndex: 1,
      workoutSession: null,
      previewExerciseSkips: new Set(),
    } as any)

    renderHook(() => useProgressivePrefetch())

    expect(mockPrefetchExerciseSets).not.toHaveBeenCalled()
  })

  it('should skip already prefetched exercises', () => {
    const { rerender } = renderHook(() => useProgressivePrefetch())

    // Initial state at index 0 - no prefetch yet
    expect(mockPrefetchExerciseSets).not.toHaveBeenCalled()

    // Update mock to have prefetched exercises
    vi.mocked(useExerciseSetsPrefetch).mockReturnValue({
      prefetchExerciseSets: mockPrefetchExerciseSets,
      prefetchStatus: {
        4: 'success',
        5: 'success',
      },
      isPrefetching: false,
      prefetchedExerciseIds: [4, 5],
      getPrefetchProgress: vi.fn(),
    })

    // Move to exercise 1
    vi.mocked(useWorkoutStore).mockReturnValue({
      exercises: mockExercises,
      currentExerciseIndex: 1,
      workoutSession: { id: 'session-1', startTime: new Date() },
      previewExerciseSkips: new Set(),
    } as any)

    rerender()

    // Should prefetch exercise 6 since 4 and 5 are already prefetched
    expect(mockPrefetchExerciseSets).toHaveBeenCalledWith([6])
  })

  it('should skip preview-skipped exercises', () => {
    const { rerender } = renderHook(() => useProgressivePrefetch())

    // Initial state at index 0 - no prefetch yet
    expect(mockPrefetchExerciseSets).not.toHaveBeenCalled()

    // Move to exercise 1 with skipped exercises
    vi.mocked(useWorkoutStore).mockReturnValue({
      exercises: mockExercises,
      currentExerciseIndex: 1,
      workoutSession: { id: 'session-1', startTime: new Date() },
      previewExerciseSkips: new Set([4]), // Exercise 4 is skipped
    } as any)

    rerender()

    // Should skip exercise 4 and prefetch 5 and 6
    expect(mockPrefetchExerciseSets).toHaveBeenCalledWith([5, 6])
  })

  it('should handle reaching end of workout', () => {
    vi.mocked(useWorkoutStore).mockReturnValue({
      exercises: mockExercises,
      currentExerciseIndex: 5, // Last exercise
      workoutSession: { id: 'session-1', startTime: new Date() },
      previewExerciseSkips: new Set(),
    } as any)

    renderHook(() => useProgressivePrefetch())

    // Should not prefetch anything
    expect(mockPrefetchExerciseSets).not.toHaveBeenCalled()
  })

  it('should not prefetch same exercises multiple times', () => {
    const { rerender } = renderHook(() => useProgressivePrefetch())

    // Move to exercise 1
    vi.mocked(useWorkoutStore).mockReturnValue({
      exercises: mockExercises,
      currentExerciseIndex: 1,
      workoutSession: { id: 'session-1', startTime: new Date() },
      previewExerciseSkips: new Set(),
    } as any)

    rerender()
    expect(mockPrefetchExerciseSets).toHaveBeenCalledTimes(1)

    // Rerender with same index
    rerender()

    // Should not prefetch again
    expect(mockPrefetchExerciseSets).toHaveBeenCalledTimes(1)
  })

  it('should handle prefetch errors gracefully', () => {
    mockPrefetchExerciseSets.mockRejectedValueOnce(new Error('Network error'))

    const { rerender } = renderHook(() => useProgressivePrefetch())

    // Move to exercise 1
    vi.mocked(useWorkoutStore).mockReturnValue({
      exercises: mockExercises,
      currentExerciseIndex: 1,
      workoutSession: { id: 'session-1', startTime: new Date() },
      previewExerciseSkips: new Set(),
    } as any)

    // Should not throw
    expect(() => rerender()).not.toThrow()
  })

  it('should adjust prefetch window based on exercise index', () => {
    const { rerender } = renderHook(() => useProgressivePrefetch())

    // At beginning - prefetch 2 ahead
    vi.mocked(useWorkoutStore).mockReturnValue({
      exercises: mockExercises,
      currentExerciseIndex: 0,
      workoutSession: { id: 'session-1', startTime: new Date() },
      previewExerciseSkips: new Set(),
    } as any)
    rerender()
    expect(mockPrefetchExerciseSets).not.toHaveBeenCalled() // Initial state, no change

    // Move to exercise 1 - prefetch 2 ahead
    vi.mocked(useWorkoutStore).mockReturnValue({
      exercises: mockExercises,
      currentExerciseIndex: 1,
      workoutSession: { id: 'session-1', startTime: new Date() },
      previewExerciseSkips: new Set(),
    } as any)
    rerender()
    expect(mockPrefetchExerciseSets).toHaveBeenCalledWith([4, 5])
  })
})
