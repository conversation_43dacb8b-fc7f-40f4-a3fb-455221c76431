import { test, expect } from '@playwright/test'

test.describe('Smoke Tests', () => {
  test('should load home page without critical errors', async ({ page }) => {
    // Track console errors
    const errors: string[] = []
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        // Filter out known CSP violations and other expected errors
        const text = msg.text()
        if (
          !text.includes('CSP') &&
          !text.includes('vercel-scripts') &&
          !text.includes('Content Security Policy') &&
          !text.includes('script.debug.js') &&
          !text.includes('Environment validation failed') &&
          !text.includes('NEXT_PUBLIC_API_URL') &&
          !text.includes('NEXT_PUBLIC_APPLE_SERVICES_ID') &&
          !text.includes('Apple OAuth') &&
          !text.includes('environment variable')
        ) {
          errors.push(text)
        }
      }
    })

    // Navigate to home page
    await page.goto('/')
    await page.waitForLoadState('domcontentloaded')

    // Check basic page structure
    const body = await page.locator('body')
    await expect(body).toBeVisible()

    // Check that page title is set
    const title = await page.title()
    expect(title).toBeTruthy()
    expect(title.length).toBeGreaterThan(0)

    // Verify no critical JavaScript errors (filter out non-critical ones)
    const criticalErrors = errors.filter((error) => {
      // Filter out non-critical errors that don't affect core functionality
      const nonCriticalPatterns = [
        'Failed to load resource: the server responded with a status of 404',
        '_vercel/insights/script.js',
        'vercel-scripts.com',
        'MIME type',
        'favicon.ico',
        'sw.js',
        'manifest.json',
        'A tree hydrated but some attributes',
        'hydration-mismatch',
        'CSP Violation',
      ]
      return !nonCriticalPatterns.some((pattern) => error.includes(pattern))
    })

    // Log filtered errors for debugging (only in CI)
    if (process.env.CI && criticalErrors.length > 0) {
      console.log('Critical errors found:', criticalErrors)
    }

    expect(criticalErrors).toHaveLength(0)
  })

  test('should be able to navigate to login page', async ({ page }) => {
    await page.goto('/login')
    await page.waitForLoadState('domcontentloaded')

    // Check that we're on the login page
    expect(page.url()).toContain('/login')

    // Check for basic page structure
    const body = await page.locator('body')
    await expect(body).toBeVisible()

    // Check that page has some content
    const bodyText = await body.textContent()
    expect(bodyText).toBeTruthy()
    expect(bodyText!.length).toBeGreaterThan(10)
  })

  test('should handle API endpoints gracefully', async ({ page }) => {
    // Mock a basic API response to test infrastructure
    await page.route('**/api/test', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ message: 'test successful' }),
      })
    })

    await page.goto('/')
    await page.waitForLoadState('domcontentloaded')

    // Test that API mocking works
    const response = await page.evaluate(async () => {
      try {
        const res = await fetch('/api/test')
        return await res.json()
      } catch (error) {
        return { error: error.message }
      }
    })

    expect(response.message).toBe('test successful')
  })

  test('should render responsive layout on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })

    await page.goto('/')
    await page.waitForLoadState('domcontentloaded')

    // Check that page adapts to mobile viewport
    const body = await page.locator('body')
    await expect(body).toBeVisible()

    // Check viewport meta tag for mobile responsiveness
    const viewportMeta = await page.locator('meta[name="viewport"]')
    const viewportContent = await viewportMeta.getAttribute('content')
    expect(viewportContent).toContain('width=device-width')
  })

  test('should load CSS and basic styling', async ({ page }) => {
    await page.goto('/')
    await page.waitForLoadState('domcontentloaded')

    // Check that CSS is loaded by verifying computed styles
    const body = await page.locator('body')
    const backgroundColor = await body.evaluate(
      (el) => window.getComputedStyle(el).backgroundColor
    )

    // Should have some background color set (not transparent)
    expect(backgroundColor).not.toBe('rgba(0, 0, 0, 0)')
    expect(backgroundColor).not.toBe('transparent')
  })
})
