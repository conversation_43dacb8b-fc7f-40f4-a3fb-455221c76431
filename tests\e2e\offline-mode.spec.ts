import { test, expect } from '@playwright/test'

test.describe('Offline Mode', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the test page
    await page.goto('/test-offline')

    // Wait for the page to load
    await page.waitForLoadState('networkidle')
  })

  test('should show online status initially', async ({ page }) => {
    // Check that we start online
    await expect(page.locator('text=🟢 Online')).toBeVisible()

    // Check that offline indicators are not shown when online and no workout loaded
    await expect(page.locator('[role="status"]')).not.toBeVisible()
  })

  test('should load offline workout when online', async ({ page }) => {
    // Click the Load Offline Workout button
    await page.click('button:has-text("Load Offline Workout")')

    // Wait for loading state
    await expect(page.locator('text=Loading...')).toBeVisible()

    // Wait for completion (with timeout)
    await page.waitForSelector('text=Offline Workout (Loaded)', {
      timeout: 10000,
    })

    // Verify workout is loaded
    await expect(page.locator('text=Workout Loaded: ✅')).toBeVisible()
    await expect(page.locator('text=Available: ✅')).toBeVisible()
  })

  test('should show offline indicators when workout is loaded', async ({
    page,
  }) => {
    // First load a workout
    await page.click('button:has-text("Load Offline Workout")')
    await page.waitForSelector('text=Offline Workout (Loaded)', {
      timeout: 10000,
    })

    // Check that offline ready indicator appears
    await expect(page.locator('text=Offline Ready')).toBeVisible()
    await expect(page.locator('text=💾')).toBeVisible()
  })

  test('should simulate offline mode', async ({ page }) => {
    // First load a workout while online
    await page.click('button:has-text("Load Offline Workout")')
    await page.waitForSelector('text=Offline Workout (Loaded)', {
      timeout: 10000,
    })

    // Simulate going offline
    await page.context().setOffline(true)

    // Wait a moment for the network status to update
    await page.waitForTimeout(1000)

    // Check offline status
    await expect(page.locator('text=🔴 Offline')).toBeVisible()
    await expect(page.locator('text=📴')).toBeVisible()

    // Check offline limitation message
    await expect(page.locator('text=You are offline')).toBeVisible()
  })

  test('should test cached recommendations when offline', async ({ page }) => {
    // Load workout while online
    await page.click('button:has-text("Load Offline Workout")')
    await page.waitForSelector('text=Offline Workout (Loaded)', {
      timeout: 10000,
    })

    // Go offline
    await page.context().setOffline(true)
    await page.waitForTimeout(1000)

    // Test cached recommendation
    await page.click('button:has-text("Test Recommendation")')

    // Check for success message in test results
    await expect(page.locator('text=✅ Got cached recommendation')).toBeVisible(
      { timeout: 5000 }
    )
  })

  test('should test offline set saving', async ({ page }) => {
    // Load workout while online
    await page.click('button:has-text("Load Offline Workout")')
    await page.waitForSelector('text=Offline Workout (Loaded)', {
      timeout: 10000,
    })

    // Go offline
    await page.context().setOffline(true)
    await page.waitForTimeout(1000)

    // Start workout session
    await page.click('button:has-text("Start Workout")')
    await expect(page.locator('text=✅ Workout session started')).toBeVisible()

    // Save a test set
    await page.click('button:has-text("Save Test Set")')
    await expect(page.locator('text=✅ Set saved successfully')).toBeVisible()

    // Check that workout progress is updated
    await expect(page.locator('text=Completed Sets: 1')).toBeVisible()
  })

  test('should test offline workout completion', async ({ page }) => {
    // Load workout while online
    await page.click('button:has-text("Load Offline Workout")')
    await page.waitForSelector('text=Offline Workout (Loaded)', {
      timeout: 10000,
    })

    // Go offline
    await page.context().setOffline(true)
    await page.waitForTimeout(1000)

    // Start workout and save a set
    await page.click('button:has-text("Start Workout")')
    await page.click('button:has-text("Save Test Set")')

    // Complete workout
    await page.click('button:has-text("Complete Workout")')
    await expect(
      page.locator('text=✅ Workout completed successfully')
    ).toBeVisible()
  })

  test('should test feature guards', async ({ page }) => {
    // Test online-only feature when online
    await expect(
      page.locator('button:has-text("Online Only Feature")')
    ).toBeEnabled()

    // Go offline
    await page.context().setOffline(true)
    await page.waitForTimeout(1000)

    // Test that online-only feature is disabled/blocked
    const onlineOnlyButton = page.locator(
      'button:has-text("Online Only Feature")'
    )
    await expect(onlineOnlyButton).toBeVisible()

    // Test that cached workout feature is disabled without workout
    const cachedWorkoutButton = page.locator(
      'button:has-text("Requires Cached Workout")'
    )
    await expect(cachedWorkoutButton).toBeVisible()
  })

  test('should test sync queue when going back online', async ({ page }) => {
    // Load workout while online
    await page.click('button:has-text("Load Offline Workout")')
    await page.waitForSelector('text=Offline Workout (Loaded)', {
      timeout: 10000,
    })

    // Go offline and perform actions
    await page.context().setOffline(true)
    await page.waitForTimeout(1000)

    await page.click('button:has-text("Start Workout")')
    await page.click('button:has-text("Save Test Set")')

    // Check that queued requests count increases
    await expect(page.locator('text=Queued Requests:')).toBeVisible()

    // Go back online
    await page.context().setOffline(false)
    await page.waitForTimeout(2000) // Wait for sync to potentially start

    // Check that we're back online
    await expect(page.locator('text=🟢 Online')).toBeVisible()
  })

  test('should clear offline workout', async ({ page }) => {
    // Load workout
    await page.click('button:has-text("Load Offline Workout")')
    await page.waitForSelector('text=Offline Workout (Loaded)', {
      timeout: 10000,
    })

    // Verify workout is loaded
    await expect(page.locator('text=Workout Loaded: ✅')).toBeVisible()

    // Clear workout
    await page.click('button:has-text("Clear Workout")')

    // Verify workout is cleared
    await expect(page.locator('text=Workout Loaded: ❌')).toBeVisible()
    await expect(page.locator('text=Load Offline Workout')).toBeVisible()
  })

  test('should check offline capability', async ({ page }) => {
    // Check capability without workout loaded
    await page.click('button:has-text("Check Capability")')
    await expect(page.locator('text=❌ Cannot execute offline')).toBeVisible()

    // Load workout
    await page.click('button:has-text("Load Offline Workout")')
    await page.waitForSelector('text=Offline Workout (Loaded)', {
      timeout: 10000,
    })

    // Check capability with workout loaded
    await expect(
      page.locator('text=✅ Workout can be executed offline')
    ).toBeVisible()
  })

  test('should persist offline state across page reloads', async ({ page }) => {
    // Load workout
    await page.click('button:has-text("Load Offline Workout")')
    await page.waitForSelector('text=Offline Workout (Loaded)', {
      timeout: 10000,
    })

    // Reload page
    await page.reload()
    await page.waitForLoadState('networkidle')

    // Check that workout is still loaded
    await expect(page.locator('text=Workout Loaded: ✅')).toBeVisible()
    await expect(page.locator('text=Offline Workout (Loaded)')).toBeVisible()
  })

  test('should handle network transitions gracefully', async ({ page }) => {
    // Start online
    await expect(page.locator('text=🟢 Online')).toBeVisible()

    // Go offline
    await page.context().setOffline(true)
    await page.waitForTimeout(1000)
    await expect(page.locator('text=🔴 Offline')).toBeVisible()

    // Go back online
    await page.context().setOffline(false)
    await page.waitForTimeout(1000)
    await expect(page.locator('text=🟢 Online')).toBeVisible()

    // Repeat to test stability
    await page.context().setOffline(true)
    await page.waitForTimeout(1000)
    await expect(page.locator('text=🔴 Offline')).toBeVisible()

    await page.context().setOffline(false)
    await page.waitForTimeout(1000)
    await expect(page.locator('text=🟢 Online')).toBeVisible()
  })
})
