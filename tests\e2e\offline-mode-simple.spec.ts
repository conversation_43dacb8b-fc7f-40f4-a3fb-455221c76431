import { test, expect } from '@playwright/test'

test.describe('Offline Mode - Simple Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the test page
    await page.goto('/test-offline')

    // Wait for the page to load
    await page.waitForLoadState('networkidle')
  })

  test('should show online status initially', async ({ page }) => {
    // Check that we start online
    await expect(page.locator('text=🟢 Online')).toBeVisible()

    // Check that the page loads without errors
    await expect(page.locator('h1')).toContainText('Offline Mode Test Page')
  })

  test('should show load offline workout button', async ({ page }) => {
    // Check that the load offline workout button is visible
    await expect(
      page.locator('button:has-text("Load Offline Workout")')
    ).toBeVisible()

    // Check that the button is enabled when online
    await expect(
      page.locator('button:has-text("Load Offline Workout")')
    ).toBeEnabled()
  })

  test('should show network status correctly', async ({ page }) => {
    // Check online status
    await expect(page.locator('text=🟢 Online')).toBeVisible()

    // Check that offline store status is shown
    await expect(page.locator('text=Workout Loaded:')).toBeVisible()
    await expect(page.locator('text=Sync Status:')).toBeVisible()
  })

  test('should simulate offline mode', async ({ page }) => {
    // Simulate going offline
    await page.context().setOffline(true)

    // Wait a moment for the network status to update
    await page.waitForTimeout(1000)

    // Check offline status
    await expect(page.locator('text=🔴 Offline')).toBeVisible()

    // Check that load button is disabled when offline
    await expect(
      page.locator('button:has-text("Load Offline Workout")')
    ).toBeDisabled()
  })

  test('should show test actions section', async ({ page }) => {
    // Check that test actions are visible
    await expect(page.locator('h3:has-text("Test Actions")')).toBeVisible()

    // Check that test buttons are present
    await expect(
      page.locator('button:has-text("Test Recommendation")')
    ).toBeVisible()
    await expect(page.locator('button:has-text("Start Workout")')).toBeVisible()
    await expect(page.locator('button:has-text("Save Test Set")')).toBeVisible()
    await expect(
      page.locator('button:has-text("Complete Workout")')
    ).toBeVisible()
  })

  test('should show feature guards demo', async ({ page }) => {
    // Check that feature guards section is visible
    await expect(
      page.locator('h3:has-text("Feature Guards Demo")')
    ).toBeVisible()

    // Check that feature guard buttons are present
    await expect(
      page.locator('button:has-text("Online Only Feature")')
    ).toBeVisible()
    await expect(
      page.locator('button:has-text("Requires Cached Workout")')
    ).toBeVisible()
  })

  test('should handle network transitions', async ({ page }) => {
    // Start online
    await expect(page.locator('text=🟢 Online')).toBeVisible()

    // Go offline
    await page.context().setOffline(true)
    await page.waitForTimeout(1000)
    await expect(page.locator('text=🔴 Offline')).toBeVisible()

    // Go back online
    await page.context().setOffline(false)
    await page.waitForTimeout(1000)
    await expect(page.locator('text=🟢 Online')).toBeVisible()
  })

  test('should show test results section', async ({ page }) => {
    // Check that test results section is visible
    await expect(page.locator('h3:has-text("Test Results")')).toBeVisible()

    // Check initial state
    await expect(page.locator('text=No test results yet')).toBeVisible()

    // Check clear results button
    await expect(page.locator('button:has-text("Clear Results")')).toBeVisible()
  })

  test('should show offline capability check', async ({ page }) => {
    // Click check capability button
    await page.click('button:has-text("Check Capability")')

    // Should show that workout cannot be executed offline without cached data
    await expect(page.locator('text=❌ Cannot execute offline')).toBeVisible()
  })

  test('should persist across page reloads', async ({ page }) => {
    // Check initial state
    await expect(page.locator('text=🟢 Online')).toBeVisible()

    // Reload page
    await page.reload()
    await page.waitForLoadState('networkidle')

    // Check that page still works
    await expect(page.locator('h1')).toContainText('Offline Mode Test Page')
    await expect(page.locator('text=🟢 Online')).toBeVisible()
  })
})
