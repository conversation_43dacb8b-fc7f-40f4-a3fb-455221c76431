import { describe, it, expect, vi, beforeEach } from 'vitest'
import { GET } from '@/app/api/auth/token/route'
import { authCookies } from '@/lib/cookies'
import { apiClient } from '@/api/client'

// Mock dependencies
vi.mock('@/lib/cookies')
vi.mock('@/api/client', () => ({
  apiClient: {
    post: vi.fn(),
  },
}))

describe('GET /api/auth/token', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should return token and user data when valid auth cookie exists', async () => {
    // Given: Valid token in cookie and user data from API
    const mockToken = 'valid-jwt-token'
    const mockUserData = {
      Email: '<EMAIL>',
      Firstname: 'Test',
      Lastname: 'User',
    }

    vi.mocked(authCookies.getAuthToken).mockResolvedValue(mockToken)
    vi.mocked(apiClient.post).mockResolvedValue({
      data: mockUserData,
    })

    // When: GET request is made
    const response = await GET()
    const data = await response.json()

    // Then: Should return token, authenticated status, and user data
    expect(response.status).toBe(200)
    expect(data).toEqual({
      token: mockToken,
      authenticated: true,
      user: {
        email: mockUserData.Email,
        name: 'Test User',
      },
    })

    // Should call GetUserInfoPyramid with the token
    expect(apiClient.post).toHaveBeenCalledWith(
      '/api/Account/GetUserInfoPyramid',
      {},
      {
        headers: {
          Authorization: `Bearer ${mockToken}`,
        },
      }
    )
  })

  it('should return unauthenticated when no token in cookie', async () => {
    // Given: No token in cookie
    vi.mocked(authCookies.getAuthToken).mockResolvedValue(undefined)

    // When: GET request is made
    const response = await GET()
    const data = await response.json()

    // Then: Should return unauthenticated
    expect(response.status).toBe(200)
    expect(data).toEqual({
      token: null,
      authenticated: false,
      user: null,
    })

    // Should not call API
    expect(apiClient.post).not.toHaveBeenCalled()
  })

  it('should handle API errors gracefully', async () => {
    // Given: Valid token but API fails
    const mockToken = 'valid-jwt-token'
    vi.mocked(authCookies.getAuthToken).mockResolvedValue(mockToken)
    vi.mocked(apiClient.post).mockRejectedValue(new Error('API Error'))

    // When: GET request is made
    const response = await GET()
    const data = await response.json()

    // Then: Should return token but no user data
    expect(response.status).toBe(200)
    expect(data).toEqual({
      token: mockToken,
      authenticated: true,
      user: null,
    })
  })

  it('should handle expired tokens', async () => {
    // Given: Expired token in cookie
    const expiredToken = 'expired-jwt-token'
    vi.mocked(authCookies.getAuthToken).mockResolvedValue(expiredToken)
    vi.mocked(apiClient.post).mockRejectedValue({
      response: { status: 401 },
    })

    // When: GET request is made
    const response = await GET()
    const data = await response.json()

    // Then: Should return unauthenticated
    expect(response.status).toBe(200)
    expect(data).toEqual({
      token: null,
      authenticated: false,
      user: null,
    })
  })

  it('should extract user email from token if API fails', async () => {
    // Given: Valid JWT token with email claim but API fails
    const mockEmail = '<EMAIL>'
    // Create a simple JWT-like token with 3 parts
    const mockToken = `header.${Buffer.from(JSON.stringify({ email: mockEmail })).toString('base64')}.signature`

    vi.mocked(authCookies.getAuthToken).mockResolvedValue(mockToken)
    vi.mocked(apiClient.post).mockRejectedValue(new Error('API Error'))

    // When: GET request is made
    const response = await GET()
    const data = await response.json()

    // Then: Should return token with email from JWT
    expect(response.status).toBe(200)
    expect(data).toEqual({
      token: mockToken,
      authenticated: true,
      user: {
        email: mockEmail,
      },
    })
  })

  it('should handle user data without lastname', async () => {
    // Given: User data without lastname
    const mockToken = 'valid-jwt-token'
    const mockUserData = {
      Email: '<EMAIL>',
      Firstname: 'Test',
      // No Lastname
    }

    vi.mocked(authCookies.getAuthToken).mockResolvedValue(mockToken)
    vi.mocked(apiClient.post).mockResolvedValue({
      data: mockUserData,
    })

    // When: GET request is made
    const response = await GET()
    const data = await response.json()

    // Then: Should return user with only firstname
    expect(data.user).toEqual({
      email: mockUserData.Email,
      name: 'Test',
    })
  })
})
