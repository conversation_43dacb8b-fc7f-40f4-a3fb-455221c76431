import { test, expect } from '@playwright/test'

test.describe('Exercise V2 Background Loading Fix', () => {
  test('should not get stuck in infinite loading when returning from background', async ({
    page,
  }) => {
    // Login first
    await page.goto('/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'Dr123456')
    await page.click('button[type="submit"]')

    // Wait for redirect to dashboard
    await page.waitForURL('**/dashboard', { timeout: 10000 })

    // Navigate to workout
    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Start workout if available
    const startButton = page.locator('button:has-text("Start workout")')
    if (await startButton.isVisible()) {
      await startButton.click()

      // Should navigate to exercise page
      await page.waitForURL('**/workout/exercise-v2/**', { timeout: 10000 })

      // Verify exercise loads initially
      await expect(page.locator('[data-testid="exercise-name"]')).toBeVisible({
        timeout: 10000,
      })

      // Simulate going to background by triggering visibility change
      await page.evaluate(() => {
        // Override to simulate background
        Object.defineProperty(document, 'visibilityState', {
          configurable: true,
          get: () => 'hidden',
        })
        Object.defineProperty(document, 'hidden', {
          configurable: true,
          get: () => true,
        })

        // Trigger the event
        document.dispatchEvent(new Event('visibilitychange'))

        // Wait a bit to simulate background time
        return new Promise((resolve) => {
          setTimeout(() => {
            // Restore to foreground
            Object.defineProperty(document, 'visibilityState', {
              configurable: true,
              get: () => 'visible',
            })
            Object.defineProperty(document, 'hidden', {
              configurable: true,
              get: () => false,
            })

            // Trigger return to foreground
            document.dispatchEvent(new Event('visibilitychange'))
            resolve(true)
          }, 2000) // 2 seconds in background
        })
      })

      // Verify exercise still loads after returning from background
      await expect(page.locator('[data-testid="exercise-name"]')).toBeVisible({
        timeout: 10000,
      })

      // Verify no infinite loading state
      await expect(
        page.locator('[data-testid="loading-skeleton"]')
      ).not.toBeVisible()

      // Verify exercise controls are interactive
      const saveButton = page.locator('button:has-text("Save")')
      await expect(saveButton).toBeEnabled({ timeout: 5000 })
    }
  })

  test('should recover from long background period', async ({ page }) => {
    // Login
    await page.goto('/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'Dr123456')
    await page.click('button[type="submit"]')

    await page.waitForURL('**/dashboard', { timeout: 10000 })

    // Navigate directly to an exercise (if we know the ID)
    await page.goto('/workout/exercise-v2/1')

    // Wait for initial load
    await page.waitForLoadState('networkidle')

    // Simulate long background period
    await page.evaluate(() => {
      // Go to background
      Object.defineProperty(document, 'visibilityState', {
        configurable: true,
        get: () => 'hidden',
      })
      document.dispatchEvent(new Event('visibilitychange'))

      // Simulate 5 minute background
      return new Promise((resolve) => {
        setTimeout(() => {
          // Return to foreground
          Object.defineProperty(document, 'visibilityState', {
            configurable: true,
            get: () => 'visible',
          })
          document.dispatchEvent(new Event('visibilitychange'))
          resolve(true)
        }, 1000) // Simulated as 1 second but represents 5 minutes
      })
    })

    // Should recover and not show infinite loading
    const errorState = page.locator('[data-testid="error-state"]')
    const loadingState = page.locator('[data-testid="loading-skeleton"]')
    const exerciseContent = page.locator('[data-testid="exercise-content"]')

    // Either show error with retry or show content, but not infinite loading
    await expect(async () => {
      const hasError = await errorState.isVisible()
      const hasLoading = await loadingState.isVisible()
      const hasContent = await exerciseContent.isVisible()

      // Should either show content or error, not stuck in loading
      expect(hasLoading).toBe(false)
      expect(hasError || hasContent).toBe(true)
    }).toPass({ timeout: 10000 })
  })
})
