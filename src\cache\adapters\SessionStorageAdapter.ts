/* eslint-disable no-restricted-syntax, @typescript-eslint/no-explicit-any */
/**
 * SessionStorage cache adapter
 *
 * This adapter uses the browser's sessionStorage API for session-scoped storage.
 * Features:
 * - Session-scoped storage (cleared when tab closes)
 * - Quota handling with automatic cleanup
 * - Shared logic with LocalStorageAdapter
 * - Lightweight implementation
 */

import { BaseStorageAdapter } from './BaseStorageAdapter'
import { CacheQuotaExceededError, CacheMetadata } from '../types'

/**
 * Configuration for SessionStorage adapter
 */
interface SessionStorageAdapterConfig {
  /** Key prefix for isolation */
  keyPrefix?: string
  /** Whether to enable compression for large values */
  enableCompression?: boolean
  /** Compression threshold in bytes */
  compressionThreshold?: number
  /** Maximum number of entries to keep */
  maxEntries?: number
  /** Whether to enable automatic cleanup on quota exceeded */
  enableAutoCleanup?: boolean
}

/**
 * SessionStorage cache adapter
 */
export class SessionStorageAdapter extends BaseStorageAdapter {
  private readonly adapterConfig: Required<SessionStorageAdapterConfig>

  constructor(config: SessionStorageAdapterConfig = {}) {
    // Check if sessionStorage is available
    if (typeof window === 'undefined' || !window.sessionStorage) {
      throw new Error('sessionStorage is not available')
    }

    super(window.sessionStorage, {
      keyPrefix: config.keyPrefix ?? 'drmuscle-session:',
      enableCompression: config.enableCompression ?? false, // Less compression for session storage
      compressionThreshold: config.compressionThreshold ?? 2048, // Higher threshold
    })

    this.adapterConfig = {
      keyPrefix: config.keyPrefix ?? 'drmuscle-session:',
      enableCompression: config.enableCompression ?? false,
      compressionThreshold: config.compressionThreshold ?? 2048,
      maxEntries: config.maxEntries ?? 500, // Lower limit for session storage
      enableAutoCleanup: config.enableAutoCleanup ?? true,
    }
  }

  /**
   * Enhanced quota handling (similar to LocalStorage but simpler)
   */
  protected override async handleQuotaExceeded<T>(
    key: string,
    value: T,
    metadata: CacheMetadata
  ): Promise<void> {
    if (!this.adapterConfig.enableAutoCleanup) {
      throw new CacheQuotaExceededError(
        `sessionStorage quota exceeded for key "${key}"`
      )
    }

    try {
      // Step 1: Clear expired entries
      const expiredCleared = await this.clearExpiredEntries()

      if (expiredCleared > 0) {
        // Try again after clearing expired entries
        await this.trySetAfterCleanup(key, value, metadata)
        return
      }

      // Step 2: Check if we're over the entry limit
      const currentKeys = await this.getAllKeys()
      if (currentKeys.length >= this.adapterConfig.maxEntries) {
        // Evict oldest entries (simpler than LRU for session storage)
        const evicted = await this.evictOldestEntries(
          Math.ceil(this.adapterConfig.maxEntries * 0.2)
        )

        if (evicted > 0) {
          await this.trySetAfterCleanup(key, value, metadata)
          return
        }
      }

      // If all else fails, throw quota exceeded error
      throw new CacheQuotaExceededError(
        `sessionStorage quota exceeded for key "${key}" after cleanup attempts`
      )
    } catch (error) {
      if (error instanceof CacheQuotaExceededError) {
        throw error
      }
      throw new CacheQuotaExceededError(
        `sessionStorage quota exceeded for key "${key}"`,
        error instanceof Error ? error : undefined
      )
    }
  }

  /**
   * Try to set value after cleanup
   */
  private async trySetAfterCleanup<T>(
    key: string,
    value: T,
    metadata: any
  ): Promise<void> {
    const fullKey = this.buildKey(key)
    const entry = { value, metadata }
    const serialized = this.serializeEntry(entry)

    this.storage.setItem(fullKey, serialized)
  }

  /**
   * Evict oldest entries (by creation time)
   */
  private async evictOldestEntries(count: number): Promise<number> {
    const entries: Array<{ key: string; created: number }> = []

    // Collect all entries with their creation times
    for (let i = 0; i < this.storage.length; i++) {
      const key = this.storage.key(i)
      if (key && key.startsWith(this.config.keyPrefix)) {
        try {
          const serialized = this.storage.getItem(key)
          if (serialized) {
            const entry = this.deserializeEntry(serialized)
            entries.push({
              key,
              created: entry.metadata.created || 0,
            })
          }
        } catch {
          // Remove invalid entries
          entries.push({ key, created: 0 })
        }
      }
    }

    // Sort by creation time (oldest first)
    entries.sort((a, b) => a.created - b.created)

    // Remove the oldest entries
    const toEvict = entries.slice(0, Math.min(count, entries.length))
    for (const { key } of toEvict) {
      this.storage.removeItem(key)
    }

    return toEvict.length
  }

  /**
   * Get storage statistics
   */
  async getStorageStats() {
    const stats = {
      totalEntries: 0,
      totalSize: 0,
      sessionId: this.getSessionId(),
    }

    // Count entries and calculate size
    for (let i = 0; i < this.storage.length; i++) {
      const key = this.storage.key(i)
      if (key && key.startsWith(this.config.keyPrefix)) {
        stats.totalEntries++
        const value = this.storage.getItem(key)
        if (value) {
          stats.totalSize += this.calculateSize(value)
        }
      }
    }

    return stats
  }

  /**
   * Get a unique session identifier
   */
  private getSessionId(): string {
    const sessionKey = `${this.config.keyPrefix}__session_id__`
    let sessionId = this.storage.getItem(sessionKey)

    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      try {
        this.storage.setItem(sessionKey, sessionId)
      } catch {
        // If we can't store the session ID, just return a temporary one
      }
    }

    return sessionId
  }

  /**
   * Check if sessionStorage is available and working
   */
  static isAvailable(): boolean {
    try {
      if (typeof window === 'undefined' || !window.sessionStorage) {
        return false
      }

      // Test if we can actually use sessionStorage
      const testKey = '__sessionStorage_test__'
      window.sessionStorage.setItem(testKey, 'test')
      window.sessionStorage.removeItem(testKey)
      return true
    } catch {
      return false
    }
  }

  /**
   * Clear all sessionStorage data for this app
   */
  static clearAll(keyPrefix = 'drmuscle-session:'): void {
    if (!SessionStorageAdapter.isAvailable()) {
      return
    }

    const keysToRemove: string[] = []

    for (let i = 0; i < window.sessionStorage.length; i++) {
      const key = window.sessionStorage.key(i)
      if (key && key.startsWith(keyPrefix)) {
        keysToRemove.push(key)
      }
    }

    for (const key of keysToRemove) {
      window.sessionStorage.removeItem(key)
    }
  }

  /**
   * Get all cache keys with their metadata
   */
  async getAllKeysWithMetadata() {
    const keys: Array<{ key: string; metadata: any }> = []

    for (let i = 0; i < this.storage.length; i++) {
      const fullKey = this.storage.key(i)
      if (fullKey && fullKey.startsWith(this.config.keyPrefix)) {
        try {
          const serialized = this.storage.getItem(fullKey)
          if (serialized) {
            const entry = this.deserializeEntry(serialized)
            keys.push({
              key: this.extractKey(fullKey),
              metadata: entry.metadata,
            })
          }
        } catch {
          // Skip invalid entries
        }
      }
    }

    return keys
  }

  /**
   * Cleanup expired entries and return count
   */
  async cleanup(): Promise<number> {
    return this.clearExpiredEntries()
  }

  /**
   * Check if this is a new session (no existing data)
   */
  isNewSession(): boolean {
    for (let i = 0; i < this.storage.length; i++) {
      const key = this.storage.key(i)
      if (key && key.startsWith(this.config.keyPrefix)) {
        return false
      }
    }
    return true
  }

  /**
   * Get session start time
   */
  getSessionStartTime(): number {
    const sessionKey = `${this.config.keyPrefix}__session_start__`
    let startTime = this.storage.getItem(sessionKey)

    if (!startTime) {
      startTime = Date.now().toString()
      try {
        this.storage.setItem(sessionKey, startTime)
      } catch {
        // If we can't store the start time, return current time
        return Date.now()
      }
    }

    return parseInt(startTime, 10)
  }
}
