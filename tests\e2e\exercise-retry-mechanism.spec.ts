import { test, expect } from '@playwright/test'

test.describe('Exercise V2 Retry Mechanism', () => {
  test.beforeEach(async ({ page }) => {
    // Login first
    await page.goto('/login')
    await page.getByTestId('email-input').fill('<EMAIL>')
    await page.getByTestId('password-input').fill('Dr123456')
    await page.getByTestId('login-button').click()

    // Wait for redirect to workout
    await page.waitForURL('/workout', { timeout: 10000 })
  })

  test('should automatically retry when recommendation loading fails', async ({
    page,
  }) => {
    // Intercept recommendation requests to simulate failures
    let requestCount = 0

    await page.route(
      '**/api/Workouts/GetActivePlanExerciseRecommendation**',
      async (route) => {
        requestCount++

        if (requestCount < 3) {
          // Fail the first 2 attempts
          await route.abort('failed')
        } else {
          // Let the 3rd attempt succeed
          await route.continue()
        }
      }
    )

    // Navigate to workout and start
    await page.getByRole('button', { name: 'Start Workout' }).click()

    // Should show loading state
    await expect(page.getByText(/loading exercise data/i)).toBeVisible()

    // After first retry (15s), should show retry message
    await page.waitForTimeout(15500)
    await expect(page.getByText(/having trouble loading/i)).toBeVisible()
    await expect(page.getByText(/retry 1 of 3/i)).toBeVisible()

    // After second retry (5s more), should show retry 2
    await page.waitForTimeout(5500)
    await expect(page.getByText(/retry 2 of 3/i)).toBeVisible()

    // Eventually should load successfully
    await expect(page.getByRole('button', { name: /save set/i })).toBeVisible({
      timeout: 20000,
    })
  })

  test('should show error state with retry option after all retries fail', async ({
    page,
  }) => {
    // Block all recommendation requests
    await page.route(
      '**/api/Workouts/GetActivePlanExerciseRecommendation**',
      async (route) => {
        await route.abort('failed')
      }
    )

    // Navigate to workout and start
    await page.getByRole('button', { name: 'Start Workout' }).click()

    // Wait for all retries to exhaust (15s + 5s + 10s + 15s final timeout = 45s)
    // But let's check the error state appears within 50s
    await expect(page.getByText(/unable to load exercise data/i)).toBeVisible({
      timeout: 50000,
    })
    await expect(
      page.getByText(/check your internet connection/i)
    ).toBeVisible()

    // Should have Try Again and Back to Workout buttons
    await expect(page.getByRole('button', { name: /try again/i })).toBeVisible()
    await expect(
      page.getByRole('button', { name: /back to workout/i })
    ).toBeVisible()

    // Test Try Again button
    await page.getByRole('button', { name: /try again/i }).click()
    await expect(page.getByText(/loading exercise data/i)).toBeVisible()
  })

  test('should retry when app returns from background', async ({ page }) => {
    // Navigate to workout and start
    await page.getByRole('button', { name: 'Start Workout' }).click()

    // Wait for exercise to load
    await expect(page.getByRole('button', { name: /save set/i })).toBeVisible({
      timeout: 20000,
    })

    // Navigate to a different exercise that will need loading
    await page.keyboard.press('ArrowRight')

    // Intercept next recommendation request
    let backgroundRetryTriggered = false
    await page.route(
      '**/api/Workouts/GetActivePlanExerciseRecommendation**',
      async (route, request) => {
        const url = request.url()
        if (url.includes('exerciseId=')) {
          backgroundRetryTriggered = true
          await route.continue()
        } else {
          await route.continue()
        }
      }
    )

    // Simulate app going to background and returning
    await page.evaluate(() => {
      // Simulate visibility change
      Object.defineProperty(document, 'visibilityState', {
        configurable: true,
        get: () => 'hidden',
      })
      document.dispatchEvent(new Event('visibilitychange'))

      // Wait a bit then return to visible
      setTimeout(() => {
        Object.defineProperty(document, 'visibilityState', {
          configurable: true,
          get: () => 'visible',
        })
        document.dispatchEvent(new Event('visibilitychange'))
      }, 100)
    })

    // Give it time to process
    await page.waitForTimeout(1000)

    // Should trigger a retry
    expect(backgroundRetryTriggered).toBe(true)
  })
})
