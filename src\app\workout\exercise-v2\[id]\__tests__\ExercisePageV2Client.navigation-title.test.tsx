import { render } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { ExercisePageV2Client } from '../ExercisePageV2Client'

// Mock all the hooks and stores
const mockSetTitle = vi.fn()
const mockUseNavigation = {
  setTitle: mockSetTitle,
}

const mockCurrentExercise = {
  Id: 1,
  Label: 'Bench Press',
  IsFinished: false,
}

const mockUseSetScreenLogic = {
  currentExercise: mockCurrentExercise,
  exercises: [mockCurrentExercise],
  currentSetIndex: 0,
  isSaving: false,
  saveError: null,
  showComplete: false,
  showExerciseComplete: false,
  recommendation: { Id: 1, WeightIncrement: 5 },
  isLoading: false,
  error: null,
  isLastExercise: false,
  isLastSet: false,
  isWarmup: false,
  isFirstWorkSet: true,
  completedSets: [],
  setData: { reps: '10', weight: '135' },
  setSetData: vi.fn(),
  setSaveError: vi.fn(),
  handleSaveSet: vi.fn(),
  refetchRecommendation: vi.fn(),
  showRIRPicker: false,
  handleRIRSelect: vi.fn(),
  handleRIRCancel: vi.fn(),
}

// Mock all dependencies
vi.mock('@/contexts/NavigationContext', () => ({
  useNavigation: () => mockUseNavigation,
}))

vi.mock('@/hooks/useSetScreenLogic', () => ({
  useSetScreenLogic: () => mockUseSetScreenLogic,
}))

vi.mock('@/hooks/useWorkout', () => ({
  useWorkout: () => ({
    isLoadingWorkout: false,
    workoutError: null,
    workoutSession: { Id: 1 },
  }),
}))

vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: () => ({
    loadingStates: new Map(),
    restTimerState: { isActive: false },
  }),
}))

vi.mock('@/stores/authStore', () => ({
  useAuthStore: () => ({
    getCachedUserInfo: () => ({ MassUnit: 'lbs' }),
  }),
}))

vi.mock('@/hooks/useExercisePageInitialization', () => ({
  useExercisePageInitialization: () => ({
    isInitializing: false,
    loadingError: null,
    retryInitialization: vi.fn(),
  }),
}))

vi.mock('@/hooks/useExerciseV2Actions', () => ({
  useExerciseV2Actions: () => ({
    handleCompleteSet: vi.fn(),
    handleSkipSet: vi.fn(),
  }),
}))

vi.mock('@/hooks/usePullToRefresh', () => ({
  usePullToRefresh: () => ({
    pullDistance: 0,
    isRefreshing: false,
    isPulling: false,
  }),
}))

vi.mock('next/navigation', () => ({
  useSearchParams: () => ({
    get: () => null,
  }),
}))

describe('ExercisePageV2Client Navigation Title', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should call setTitle with exercise name when currentExercise is available', () => {
    render(<ExercisePageV2Client exerciseId={1} />)

    // Verify setTitle was called with the exercise name
    expect(mockSetTitle).toHaveBeenCalledWith('Bench Press')
  })

  it('should not call setTitle when currentExercise is null', () => {
    // Mock currentExercise as null
    vi.mocked(mockUseSetScreenLogic).currentExercise = null

    render(<ExercisePageV2Client exerciseId={1} />)

    // Verify setTitle was not called
    expect(mockSetTitle).not.toHaveBeenCalled()
  })

  it('should clear title on unmount', () => {
    const { unmount } = render(<ExercisePageV2Client exerciseId={1} />)

    // Clear previous calls
    vi.clearAllMocks()

    // Unmount component
    unmount()

    // Verify setTitle was called with empty string to clear title
    expect(mockSetTitle).toHaveBeenCalledWith('')
  })
})
