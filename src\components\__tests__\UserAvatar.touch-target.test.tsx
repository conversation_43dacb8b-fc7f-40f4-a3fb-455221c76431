import React from 'react'
import { render, screen } from '@testing-library/react'
import { UserAvatar } from '../UserAvatar'

describe('UserAvatar Touch Target Compliance', () => {
  const mockUser = {
    email: '<EMAIL>',
    name: 'Test User',
  }

  describe('52px minimum touch target requirement', () => {
    it('should achieve 52px touch target when wrapped in button', () => {
      // UserAvatar is used in NavigationWrapper inside a button with p-2 (8px padding)
      // To achieve 52px total, the avatar itself needs to be at least 36px (52 - 16)
      // Current sizes: sm=32px, md=40px, lg=48px all need adjustment

      render(
        <button className="p-2">
          <UserAvatar user={mockUser} size="sm" />
        </button>
      )

      const avatar = screen.getByLabelText(/User avatar showing T/)

      // Current: w-8 h-8 (32px), with p-2 button = 48px total
      // Need: minimum 36px avatar for 52px total with p-2
      expect(avatar).toHaveClass('w-9') // 36px minimum
      expect(avatar).toHaveClass('h-9') // 36px minimum
    })

    it('should have proper sizes for all variants to achieve 52px with p-2 wrapper', () => {
      const { rerender } = render(
        <button className="p-2">
          <UserAvatar user={mockUser} size="sm" />
        </button>
      )

      // Small size should be at least 36px (9 * 4px)
      let avatar = screen.getByLabelText(/User avatar showing T/)
      expect(avatar).toHaveClass('w-9', 'h-9')

      // Medium size should be at least 36px (already 40px, so compliant)
      rerender(
        <button className="p-2">
          <UserAvatar user={mockUser} size="md" />
        </button>
      )
      avatar = screen.getByLabelText(/User avatar showing T/)
      expect(avatar).toHaveClass('w-10', 'h-10') // 40px is fine

      // Large size already 48px, which gives 64px total with p-2
      rerender(
        <button className="p-2">
          <UserAvatar user={mockUser} size="lg" />
        </button>
      )
      avatar = screen.getByLabelText(/User avatar showing T/)
      expect(avatar).toHaveClass('w-12', 'h-12') // 48px is fine
    })

    it('should have minimum touch target even without user data', () => {
      const { container } = render(
        <button className="p-2">
          <UserAvatar user={null} size="sm" />
        </button>
      )

      const avatarDiv = container.querySelector('div[aria-label="User avatar"]')
      expect(avatarDiv).toHaveClass('w-9', 'h-9')
    })
  })

  describe('Icon sizing should scale with avatar size', () => {
    it('should have appropriately sized icons for each avatar size', () => {
      const { container, rerender } = render(
        <UserAvatar user={null} size="sm" />
      )

      // Small avatar (36px) should have slightly larger icon
      let svg = container.querySelector('svg[aria-label="User avatar"]')
      expect(svg).toHaveClass('w-5', 'h-5') // Current size, might need adjustment

      rerender(<UserAvatar user={null} size="md" />)
      svg = container.querySelector('svg[aria-label="User avatar"]')
      expect(svg).toHaveClass('w-6', 'h-6')

      rerender(<UserAvatar user={null} size="lg" />)
      svg = container.querySelector('svg[aria-label="User avatar"]')
      expect(svg).toHaveClass('w-7', 'h-7')
    })
  })
})
