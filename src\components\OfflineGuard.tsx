'use client'

import { ReactNode } from 'react'
import { useNetworkStatus } from '@/hooks/useNetworkStatus'
import { useOfflineStore } from '@/stores/offlineStore'
import { cn } from '@/lib/utils'

interface OfflineGuardProps {
  children: ReactNode
  /**
   * Whether this feature requires an internet connection
   */
  requiresOnline?: boolean
  /**
   * Whether this feature requires cached workout data
   */
  requiresCachedWorkout?: boolean
  /**
   * Custom message to show when feature is blocked
   */
  blockedMessage?: string
  /**
   * Whether to show the blocked overlay or just disable
   */
  showOverlay?: boolean
  /**
   * Custom className for the wrapper
   */
  className?: string
  /**
   * Whether to completely hide the feature when blocked
   */
  hideWhenBlocked?: boolean
}

/**
 * OfflineGuard Component
 *
 * Guards features that should be disabled or blocked when offline
 * or when certain offline conditions aren't met.
 */
export function OfflineGuard({
  children,
  requiresOnline = false,
  requiresCachedWorkout = false,
  blockedMessage,
  showOverlay = true,
  className,
  hideWhenBlocked = false,
}: OfflineGuardProps) {
  const { isOffline } = useNetworkStatus()
  const { isOfflineWorkoutLoaded } = useOfflineStore()

  // Determine if feature should be blocked
  const isBlocked =
    (requiresOnline && isOffline) ||
    (requiresCachedWorkout && !isOfflineWorkoutLoaded)

  // Get appropriate blocked message
  const getBlockedMessage = () => {
    if (blockedMessage) return blockedMessage

    if (requiresOnline && isOffline) {
      return 'This feature requires an internet connection'
    }

    if (requiresCachedWorkout && !isOfflineWorkoutLoaded) {
      return 'This feature requires cached workout data. Please load a workout for offline use first.'
    }

    return 'This feature is currently unavailable'
  }

  // Hide completely if requested
  if (isBlocked && hideWhenBlocked) {
    return null
  }

  // If not blocked, render children normally
  if (!isBlocked) {
    return <div className={className}>{children}</div>
  }

  // Render blocked state
  return (
    <div className={cn('relative', className)}>
      {/* Render children but potentially disabled */}
      <div className={cn(isBlocked && 'pointer-events-none opacity-50')}>
        {children}
      </div>

      {/* Show overlay if requested */}
      {showOverlay && isBlocked && (
        <div className="absolute inset-0 flex items-center justify-center bg-white/80 backdrop-blur-sm rounded-lg">
          <div className="text-center p-4 max-w-xs">
            <div className="text-2xl mb-2">🚫</div>
            <p className="text-sm text-gray-600 font-medium">
              {getBlockedMessage()}
            </p>
          </div>
        </div>
      )}
    </div>
  )
}

/**
 * Hook for checking if features should be blocked
 */
export function useOfflineGuard() {
  const { isOnline, isOffline } = useNetworkStatus()
  const { isOfflineWorkoutLoaded } = useOfflineStore()

  return {
    isOnline,
    isOffline,
    isOfflineWorkoutLoaded,
    canUseOnlineFeatures: isOnline,
    canUseOfflineFeatures: isOfflineWorkoutLoaded,
    canUseWorkoutFeatures: isOnline || isOfflineWorkoutLoaded,

    /**
     * Check if a specific feature is available
     */
    isFeatureAvailable: (requirements: {
      requiresOnline?: boolean
      requiresCachedWorkout?: boolean
    }) => {
      if (requirements.requiresOnline && isOffline) return false
      if (requirements.requiresCachedWorkout && !isOfflineWorkoutLoaded)
        return false
      return true
    },

    /**
     * Get a message explaining why a feature is blocked
     */
    getBlockedReason: (requirements: {
      requiresOnline?: boolean
      requiresCachedWorkout?: boolean
    }) => {
      if (requirements.requiresOnline && isOffline) {
        return 'This feature requires an internet connection'
      }
      if (requirements.requiresCachedWorkout && !isOfflineWorkoutLoaded) {
        return 'This feature requires cached workout data'
      }
      return null
    },
  }
}

/**
 * Simple wrapper for disabling buttons when offline
 */
interface OfflineDisabledButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: ReactNode
  requiresOnline?: boolean
  requiresCachedWorkout?: boolean
  className?: string
}

export function OfflineDisabledButton({
  children,
  requiresOnline = true,
  requiresCachedWorkout = false,
  className,
  ...props
}: OfflineDisabledButtonProps) {
  const { isFeatureAvailable, getBlockedReason } = useOfflineGuard()

  const isAvailable = isFeatureAvailable({
    requiresOnline,
    requiresCachedWorkout,
  })
  const blockedReason = getBlockedReason({
    requiresOnline,
    requiresCachedWorkout,
  })

  return (
    <button
      {...props}
      disabled={!isAvailable || props.disabled}
      className={cn(className, !isAvailable && 'opacity-50 cursor-not-allowed')}
      title={blockedReason || props.title}
      aria-label={
        blockedReason
          ? `${props['aria-label'] || 'Button'} - ${blockedReason}`
          : props['aria-label']
      }
    >
      {children}
    </button>
  )
}

/**
 * Message component for explaining offline limitations
 */
export function OfflineLimitationMessage({
  type = 'info',
  className,
}: {
  type?: 'info' | 'warning'
  className?: string
}) {
  const { isOffline, isOfflineWorkoutLoaded } = useOfflineGuard()

  if (!isOffline) return null

  const message = isOfflineWorkoutLoaded
    ? 'You are offline. Some features are limited, but you can continue your workout.'
    : 'You are offline. Load a workout while online to use offline features.'

  const typeStyles = {
    info: 'bg-blue-50 text-blue-800 border-blue-200',
    warning: 'bg-yellow-50 text-yellow-800 border-yellow-200',
  }

  return (
    <div
      className={cn(
        'flex items-center gap-2 p-3 rounded-lg border text-sm',
        typeStyles[type],
        className
      )}
      role="status"
      aria-live="polite"
    >
      <span className="text-lg" role="img" aria-hidden="true">
        {type === 'warning' ? '⚠️' : 'ℹ️'}
      </span>
      <p>{message}</p>
    </div>
  )
}
