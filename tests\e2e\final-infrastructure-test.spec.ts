import { test, expect } from '@playwright/test'

test.describe('Final Infrastructure Test', () => {
  test('should load application and basic functionality works', async ({
    page,
  }) => {
    // Navigate to the home page
    await page.goto('/')

    // Wait for the page to load
    await page.waitForLoadState('networkidle')

    // Check that the page loaded successfully
    expect(page.url()).toContain('localhost:3000')

    // Check for basic page structure
    const body = await page.locator('body')
    await expect(body).toBeVisible()

    // Check that page title is set
    const title = await page.title()
    expect(title).toBeTruthy()
    expect(title.length).toBeGreaterThan(0)

    // Check that page has content
    const bodyText = await body.textContent()
    expect(bodyText).toBeTruthy()
    expect(bodyText!.length).toBeGreaterThan(10)
  })

  test('should handle navigation between pages', async ({ page }) => {
    // Start at home page
    await page.goto('/')
    await page.waitForLoadState('networkidle')

    // Navigate to login
    await page.goto('/login')
    await page.waitForLoadState('networkidle')
    expect(page.url()).toContain('/login')

    // Check that login page loads
    const body = await page.locator('body')
    await expect(body).toBeVisible()

    const bodyText = await body.textContent()
    expect(bodyText).toBeTruthy()
    expect(bodyText!.length).toBeGreaterThan(10)
  })

  test('should handle API mocking correctly', async ({ page }) => {
    // Mock a basic API response to test infrastructure
    await page.route('**/api/test-infrastructure', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          message: 'infrastructure test successful',
          timestamp: Date.now(),
        }),
      })
    })

    await page.goto('/')
    await page.waitForLoadState('networkidle')

    // Test that API mocking works
    const response = await page.evaluate(async () => {
      try {
        const res = await fetch('/api/test-infrastructure')
        return {
          status: res.status,
          data: await res.json(),
        }
      } catch (error) {
        return { error: error.message }
      }
    })

    expect(response.status).toBe(200)
    expect(response.data.message).toBe('infrastructure test successful')
  })

  test('should work on mobile viewport', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })

    await page.goto('/')
    await page.waitForLoadState('networkidle')

    // Check that page loads on mobile
    const body = await page.locator('body')
    await expect(body).toBeVisible()

    // Check that content is accessible
    const bodyText = await body.textContent()
    expect(bodyText).toBeTruthy()
    expect(bodyText!.length).toBeGreaterThan(10)

    // Check viewport meta tag for mobile responsiveness
    const viewportMeta = await page.locator('meta[name="viewport"]')
    const viewportContent = await viewportMeta.getAttribute('content')
    expect(viewportContent).toContain('width=device-width')
  })

  test('should handle CSS and styling', async ({ page }) => {
    await page.goto('/')
    await page.waitForLoadState('networkidle')

    // Check that CSS is loaded by verifying computed styles
    const body = await page.locator('body')
    const backgroundColor = await body.evaluate(
      (el) => window.getComputedStyle(el).backgroundColor
    )

    // Should have some background color set (not transparent)
    expect(backgroundColor).not.toBe('rgba(0, 0, 0, 0)')
    expect(backgroundColor).not.toBe('transparent')
    expect(backgroundColor).not.toBe('')
  })
})
