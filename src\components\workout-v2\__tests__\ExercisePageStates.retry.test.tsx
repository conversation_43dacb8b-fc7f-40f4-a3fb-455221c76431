import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest'
import { ExercisePageStates } from '../ExercisePageStates'
import type { ExerciseModel, RecommendationModel } from '@/types'

// Mock next/navigation
const mockRouter = {
  push: vi.fn(),
  back: vi.fn(),
}

vi.mock('next/navigation', () => ({
  useRouter: () => mockRouter,
}))

// Mock components
vi.mock('@/components/workout/SetScreenLoadingState', () => ({
  SetScreenLoadingState: ({
    exerciseName,
    retryAttempt,
    loadingMessage,
  }: any) => (
    <div data-testid="loading-state">
      <div>{loadingMessage || 'Loading exercise data...'}</div>
      {exerciseName && <div>{exerciseName}</div>}
      {retryAttempt && <div>Retry {retryAttempt} of 3</div>}
    </div>
  ),
}))

vi.mock('@/components/workout/SetScreenErrorState', () => ({
  SetScreenErrorState: ({ onRetry, errorMessage, showBackButton }: any) => (
    <div data-testid="error-state">
      <div>{errorMessage || 'Unable to load exercise'}</div>
      <button onClick={onRetry}>Try Again</button>
      {showBackButton && (
        <button onClick={() => mockRouter.back()}>Back to Workout</button>
      )}
    </div>
  ),
}))

describe('ExercisePageStates - Retry Mechanism', () => {
  const mockRetryInitialization = vi.fn()
  const mockRefetchRecommendation = vi.fn()
  const mockHandleSaveSet = vi.fn()
  const mockHandleNavigateToNext = vi.fn()

  const defaultProps = {
    loadingError: null,
    workoutError: null,
    retryInitialization: mockRetryInitialization,
    isInitializing: false,
    isLoadingWorkout: false,
    isLoadingRecommendation: false,
    isLoading: false,
    recommendation: null,
    currentExercise: null,
    workoutSession: {},
    error: null,
    refetchRecommendation: mockRefetchRecommendation,
    showComplete: false,
    showExerciseComplete: false,
    currentSet: null,
    isLastExercise: false,
    handleSaveSet: mockHandleSaveSet,
    handleNavigateToNext: mockHandleNavigateToNext,
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  describe('Automatic Retry Logic', () => {
    it('should automatically retry with exponential backoff', async () => {
      render(
        <ExercisePageStates
          {...defaultProps}
          isLoadingRecommendation
          currentExercise={{ Label: 'Test Exercise' } as ExerciseModel}
        />
      )

      // Initial loading state
      expect(screen.getByText(/loading exercise data/i)).toBeInTheDocument()

      // First retry after 15 seconds
      vi.advanceTimersByTime(15000)
      await waitFor(() => {
        expect(mockRefetchRecommendation).toHaveBeenCalledTimes(1)
      })

      // Second retry after 5 more seconds (exponential backoff)
      vi.advanceTimersByTime(5000)
      await waitFor(() => {
        expect(mockRefetchRecommendation).toHaveBeenCalledTimes(2)
      })

      // Third retry after 10 more seconds
      vi.advanceTimersByTime(10000)
      await waitFor(() => {
        expect(mockRefetchRecommendation).toHaveBeenCalledTimes(3)
      })

      // No more retries after the third attempt
      vi.advanceTimersByTime(20000)
      expect(mockRefetchRecommendation).toHaveBeenCalledTimes(3)
    })

    it('should show retry count during retry attempts', async () => {
      const { rerender } = render(
        <ExercisePageStates
          {...defaultProps}
          isLoadingRecommendation
          currentExercise={{ Label: 'Test Exercise' } as ExerciseModel}
        />
      )

      // Trigger first retry
      vi.advanceTimersByTime(15000)
      await waitFor(() => {
        expect(mockRefetchRecommendation).toHaveBeenCalledTimes(1)
      })

      // Force re-render to update UI
      rerender(
        <ExercisePageStates
          {...defaultProps}
          isLoadingRecommendation
          currentExercise={{ Label: 'Test Exercise' } as ExerciseModel}
        />
      )

      // Should show retry count
      expect(screen.getByText(/retry 1 of 3/i)).toBeInTheDocument()
    })

    it('should reset retry count when recommendation loads successfully', async () => {
      const { rerender } = render(
        <ExercisePageStates
          {...defaultProps}
          isLoadingRecommendation
          currentExercise={{ Label: 'Test Exercise' } as ExerciseModel}
        />
      )

      // Trigger first retry
      vi.advanceTimersByTime(15000)
      await waitFor(() => {
        expect(mockRefetchRecommendation).toHaveBeenCalledTimes(1)
      })

      // Recommendation loads successfully
      rerender(
        <ExercisePageStates
          {...defaultProps}
          isLoadingRecommendation={false}
          recommendation={{ id: 1 } as RecommendationModel}
          currentExercise={{ Label: 'Test Exercise' } as ExerciseModel}
          currentSet={{ Id: 1 } as any}
          workoutSession={{ id: 1 }}
        />
      )

      // Should not show loading state anymore
      expect(screen.queryByTestId('loading-state')).not.toBeInTheDocument()

      // Start loading again - retry count should be reset
      rerender(
        <ExercisePageStates
          {...defaultProps}
          isLoadingRecommendation
          currentExercise={{ Label: 'Test Exercise' } as ExerciseModel}
        />
      )

      vi.clearAllMocks()

      // Should wait full 15 seconds again (not shortened)
      vi.advanceTimersByTime(15000)
      await waitFor(() => {
        expect(mockRefetchRecommendation).toHaveBeenCalledTimes(1)
      })
    })
  })

  describe('Loading Messages', () => {
    it('should show progressive loading messages', async () => {
      const { rerender } = render(
        <ExercisePageStates
          {...defaultProps}
          isLoadingRecommendation
          currentExercise={{ Label: 'Bench Press' } as ExerciseModel}
        />
      )

      // Initial message
      expect(screen.getByText(/loading exercise data/i)).toBeInTheDocument()

      // First retry - should show trouble loading message
      vi.advanceTimersByTime(15000)
      await waitFor(() => {
        expect(mockRefetchRecommendation).toHaveBeenCalled()
      })

      rerender(
        <ExercisePageStates
          {...defaultProps}
          isLoadingRecommendation
          currentExercise={{ Label: 'Bench Press' } as ExerciseModel}
        />
      )

      expect(screen.getByText(/retry 1 of 3/i)).toBeInTheDocument()
    })
  })

  describe('Error State', () => {
    it('should show enhanced error state after all retries fail', async () => {
      const { rerender } = render(
        <ExercisePageStates
          {...defaultProps}
          isLoadingRecommendation
          currentExercise={{ Label: 'Test Exercise' } as ExerciseModel}
        />
      )

      // Exhaust all retries
      vi.advanceTimersByTime(15000) // First retry
      vi.advanceTimersByTime(5000) // Second retry
      vi.advanceTimersByTime(10000) // Third retry
      vi.advanceTimersByTime(15000) // Final timeout

      await waitFor(() => {
        expect(mockRefetchRecommendation).toHaveBeenCalledTimes(3)
      })

      // Update to show error state
      rerender(
        <ExercisePageStates
          {...defaultProps}
          isLoadingRecommendation={false}
          currentExercise={{ Label: 'Test Exercise' } as ExerciseModel}
        />
      )

      expect(screen.getByText(/unable to load exercise/i)).toBeInTheDocument()
      expect(
        screen.getByRole('button', { name: /try again/i })
      ).toBeInTheDocument()
      expect(
        screen.getByRole('button', { name: /back to workout/i })
      ).toBeInTheDocument()
    })

    it('should handle manual retry from error state', async () => {
      render(<ExercisePageStates {...defaultProps} error="Failed to load" />)

      const tryAgainButton = screen.getByRole('button', { name: /try again/i })
      await userEvent.click(tryAgainButton)

      expect(mockRefetchRecommendation).toHaveBeenCalledTimes(1)
    })
  })

  describe('Background Recovery', () => {
    it('should retry when app returns from background during loading', async () => {
      // Mock document visibility API
      let visibilityState = 'visible'
      Object.defineProperty(document, 'visibilityState', {
        configurable: true,
        get: () => visibilityState,
      })

      const visibilityListeners: Array<() => void> = []
      const addEventListenerSpy = vi
        .spyOn(document, 'addEventListener')
        .mockImplementation((event: string, handler: any) => {
          if (event === 'visibilitychange') {
            visibilityListeners.push(handler)
          }
        })

      render(
        <ExercisePageStates
          {...defaultProps}
          isLoadingRecommendation
          currentExercise={{ Label: 'Test Exercise' } as ExerciseModel}
        />
      )

      // Simulate app going to background
      visibilityState = 'hidden'
      visibilityListeners.forEach((handler) => handler())

      // Simulate app returning from background
      visibilityState = 'visible'
      visibilityListeners.forEach((handler) => handler())

      // Should trigger a retry
      await waitFor(() => {
        expect(mockRefetchRecommendation).toHaveBeenCalled()
      })

      addEventListenerSpy.mockRestore()
    })

    it('should not retry when app returns from background if not loading', async () => {
      // Mock document visibility API
      let visibilityState = 'visible'
      Object.defineProperty(document, 'visibilityState', {
        configurable: true,
        get: () => visibilityState,
      })

      const visibilityListeners: Array<() => void> = []
      vi.spyOn(document, 'addEventListener').mockImplementation(
        (event: string, handler: any) => {
          if (event === 'visibilitychange') {
            visibilityListeners.push(handler)
          }
        }
      )

      render(
        <ExercisePageStates
          {...defaultProps}
          isLoadingRecommendation={false}
          recommendation={{ id: 1 } as RecommendationModel}
          currentExercise={{ Label: 'Test Exercise' } as ExerciseModel}
          currentSet={{ Id: 1 } as any}
          workoutSession={{ id: 1 }}
        />
      )

      // Simulate app going to background and returning
      visibilityState = 'hidden'
      visibilityListeners.forEach((handler) => handler())
      visibilityState = 'visible'
      visibilityListeners.forEach((handler) => handler())

      // Should NOT trigger a retry since we have data
      expect(mockRefetchRecommendation).not.toHaveBeenCalled()
    })
  })
})
