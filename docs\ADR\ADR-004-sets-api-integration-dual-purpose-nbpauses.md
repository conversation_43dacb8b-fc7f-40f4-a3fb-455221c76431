# ADR-004: SET's API Integration - Dual-Purpose NbPauses Pattern

## Status

Accepted

## Context

We spent two weeks blocked on implementing SET's recommendation from the Dr. Muscle API. The core issue was that drop sets were incorrectly using the rest-pause API endpoint instead of the normal endpoint, resulting in incorrect set generation and display.

The investigation revealed that the API field `NbPauses` has a dual purpose that wasn't initially understood, and that bodyweight exercises require special handling that differs from weighted exercises.

## Problem Statement

1. Drop sets were showing as "Rest-pause" in the UI despite being configured as drop sets
2. The total number of sets displayed didn't match the mobile app (e.g., web showed 1 set vs mobile showing 4 sets)
3. Bodyweight exercises with drop/pyramid configurations were not being handled correctly
4. Endpoint selection logic was based on incorrect assumptions about exercise properties

## Decision

### 1. Dual-Purpose NbPauses Pattern

We will interpret the `NbPauses` field based on the `IsDropSet` flag:

- When `IsDropSet === true`: NbPauses creates DROP SETS with 10% progressive weight reductions
- When `IsDropSet === false` and `IsNormalSets === false`: <PERSON><PERSON><PERSON><PERSON><PERSON> creates REST-PAUSE micro-sets

Total sets are always calculated as: `Series + NbPauses`

### 2. Endpoint Selection Logic

We will use server-driven endpoint selection based on user preferences, not exercise properties:

```typescript
// Correct approach
const endpoint =
  userProfile.IsNormalSet === false
    ? '/api/Exercise/GetRecommendationRestPauseRIRForExerciseWithoutWarmupsNew'
    : '/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew'

// Special case: Bodyweight + Pyramid users
if (
  userProfile.IsNormalSet === true &&
  userProfile.IsPyramid === true &&
  exercise.IsBodyweight === true
) {
  // Use Rest-Pause endpoint (MAUI compatibility)
  endpoint =
    '/api/Exercise/GetRecommendationRestPauseRIRForExerciseWithoutWarmupsNew'
}
```

### 3. Bodyweight Exercise Normalization

We will implement client-side normalization for bodyweight exercises:

```typescript
if (isBodyweight && (rec.IsDropSet || rec.IsPyramid)) {
  return {
    ...rec,
    IsDropSet: false, // Clear drop set flag
    IsPyramid: false, // Clear pyramid flag
    IsNormalSets: false, // Enable rest-pause behavior
    // Keep NbPauses for rest-pause micro-sets
  }
}
```

### 4. Implementation Requirements

- Always pass `isBodyweight` parameter to the `generateAllSets` function
- Never use `exercise.IsDropSet` for endpoint selection
- Apply server response normalization when needed
- Follow the set type precedence: Drop > Pyramid > Reverse Pyramid > Rest-Pause > Back-off > Normal

## Consequences

### Positive

- Correct drop set display and functionality matching the mobile app
- Proper handling of bodyweight exercises
- Clear understanding of API contract and field semantics
- Reduced debugging time for future SET-related issues
- Better parity with MAUI mobile app behavior

### Negative

- Additional client-side normalization logic required
- More complex set generation logic to handle dual-purpose fields
- Dependency on understanding undocumented API behaviors

### Neutral

- Need to maintain compatibility with both old and new API response formats
- Documentation must be kept up-to-date as API evolves

## Implementation Details

### Files Modified

- `src/utils/generateAllSets.ts` - Added normalization logic and dual-purpose handling
- `src/services/endpointSelection.ts` - Corrected endpoint selection logic
- `src/components/workout/ExercisePageV2Client.tsx` - Pass isBodyweight parameter
- `src/components/workout/ExerciseSetsGrid.tsx` - Use proper set generation

### Testing

- Unit tests for dual-purpose NbPauses interpretation
- E2E tests for drop sets and rest-pause sets
- Specific test cases for bodyweight exercise normalization

## References

- [Comprehensive SET's Implementation Guide](../guides/comprehensive-sets-implementation-guide-2.md)
- [Patterns and Gotchas - SET's Implementation](../patterns-and-gotchas.md#sets-recommendation-implementation-critical-learning)
- Mobile app code: `DrMaxMuscle/Screens/User/MainAIPage.xaml.cs` lines 12760-12810
- API models: `DrMuscleWebApiSharedModel/RecommendationModel.cs`

## Date

2025-01-18

## Author

Terragon Labs / Claude

## Review

This ADR documents the resolution of a critical two-week blocker that affected the core workout functionality. The dual-purpose nature of the NbPauses field was not documented in the API and required extensive investigation including analysis of the mobile app source code to understand the correct behavior.
