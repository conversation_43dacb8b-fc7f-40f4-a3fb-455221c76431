import { render, screen, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { describe, it, expect, vi } from 'vitest'
import WeightIncrementInput from './WeightIncrementInput'

describe('WeightIncrementInput', () => {
  const mockOnChange = vi.fn()

  beforeEach(() => {
    mockOnChange.mockClear()
  })

  describe('Touch Target Compliance', () => {
    it('should have 52px minimum height for increment button', () => {
      render(
        <WeightIncrementInput
          value={2.5}
          unit="kg"
          onChange={mockOnChange}
          disabled={false}
        />
      )

      const incrementBtn = screen.getByLabelText('Increase weight increment')
      // Test will FAIL initially - current height is 40px (h-10)
      expect(incrementBtn).toHaveClass('min-h-[52px]')
    })

    it('should have 52px minimum height for decrement button', () => {
      render(
        <WeightIncrementInput
          value={2.5}
          unit="kg"
          onChange={mockOnChange}
          disabled={false}
        />
      )

      const decrementBtn = screen.getByLabelText('Decrease weight increment')
      // Test will FAIL initially - current height is 40px (h-10)
      expect(decrementBtn).toHaveClass('min-h-[52px]')
    })
  })

  describe('Clickable/Tapable Input Field', () => {
    it('should render center value as clickable input field, not span', () => {
      render(
        <WeightIncrementInput
          value={2.5}
          unit="kg"
          onChange={mockOnChange}
          disabled={false}
        />
      )

      // Test will FAIL initially - currently a span, should be input
      const input = screen.getByDisplayValue('2.5')
      expect(input.tagName).toBe('INPUT')
      expect(input).toHaveAttribute('type', 'number')
    })

    it('should make input field clickable/tapable for direct editing', () => {
      render(
        <WeightIncrementInput
          value={2.5}
          unit="kg"
          onChange={mockOnChange}
          disabled={false}
        />
      )

      const input = screen.getByDisplayValue('2.5')

      // Test focus and edit capability
      fireEvent.click(input)
      // Focus behavior tested via interaction, not direct focus assertion

      // Test direct value change
      fireEvent.change(input, { target: { value: '5.0' } })
      expect(mockOnChange).toHaveBeenCalledWith(5.0)
    })

    it('should have 52px minimum height for input field', () => {
      render(
        <WeightIncrementInput
          value={2.5}
          unit="kg"
          onChange={mockOnChange}
          disabled={false}
        />
      )

      const input = screen.getByDisplayValue('2.5')
      // Test will FAIL initially - needs proper touch target size
      expect(input).toHaveClass('min-h-[52px]')
    })
  })

  describe('Button Functionality', () => {
    it('should increment kg values by 0.5', () => {
      render(
        <WeightIncrementInput
          value={2.5}
          unit="kg"
          onChange={mockOnChange}
          disabled={false}
        />
      )

      const incrementBtn = screen.getByLabelText('Increase weight increment')
      fireEvent.click(incrementBtn)

      expect(mockOnChange).toHaveBeenCalledWith(3.0)
    })

    it('should decrement kg values by 0.5', () => {
      render(
        <WeightIncrementInput
          value={2.5}
          unit="kg"
          onChange={mockOnChange}
          disabled={false}
        />
      )

      const decrementBtn = screen.getByLabelText('Decrease weight increment')
      fireEvent.click(decrementBtn)

      expect(mockOnChange).toHaveBeenCalledWith(2.0)
    })

    it('should increment lbs values by 1.0', () => {
      render(
        <WeightIncrementInput
          value={5}
          unit="lbs"
          onChange={mockOnChange}
          disabled={false}
        />
      )

      const incrementBtn = screen.getByLabelText('Increase weight increment')
      fireEvent.click(incrementBtn)

      expect(mockOnChange).toHaveBeenCalledWith(6)
    })

    it('should decrement lbs values by 1.0', () => {
      render(
        <WeightIncrementInput
          value={5}
          unit="lbs"
          onChange={mockOnChange}
          disabled={false}
        />
      )

      const decrementBtn = screen.getByLabelText('Decrease weight increment')
      fireEvent.click(decrementBtn)

      expect(mockOnChange).toHaveBeenCalledWith(4)
    })
  })

  describe('Value Display and Unit', () => {
    it('should display kg values with 1 decimal place', () => {
      render(
        <WeightIncrementInput
          value={2.5}
          unit="kg"
          onChange={mockOnChange}
          disabled={false}
        />
      )

      expect(screen.getByDisplayValue('2.5')).toBeInTheDocument()
      expect(screen.getByText('kg')).toBeInTheDocument()
    })

    it('should display lbs values as whole numbers', () => {
      render(
        <WeightIncrementInput
          value={5}
          unit="lbs"
          onChange={mockOnChange}
          disabled={false}
        />
      )

      expect(screen.getByDisplayValue('5')).toBeInTheDocument()
      expect(screen.getByText('lbs')).toBeInTheDocument()
    })
  })

  describe('Boundary Constraints', () => {
    it('should not allow kg values below 0.5', () => {
      render(
        <WeightIncrementInput
          value={0.5}
          unit="kg"
          onChange={mockOnChange}
          disabled={false}
        />
      )

      const decrementBtn = screen.getByLabelText('Decrease weight increment')
      fireEvent.click(decrementBtn)

      expect(mockOnChange).not.toHaveBeenCalled()
    })

    it('should not allow lbs values below 1', () => {
      render(
        <WeightIncrementInput
          value={1}
          unit="lbs"
          onChange={mockOnChange}
          disabled={false}
        />
      )

      const decrementBtn = screen.getByLabelText('Decrease weight increment')
      fireEvent.click(decrementBtn)

      expect(mockOnChange).not.toHaveBeenCalled()
    })

    it('should not allow kg values above 10.0', () => {
      render(
        <WeightIncrementInput
          value={10.0}
          unit="kg"
          onChange={mockOnChange}
          disabled={false}
        />
      )

      const incrementBtn = screen.getByLabelText('Increase weight increment')
      fireEvent.click(incrementBtn)

      expect(mockOnChange).not.toHaveBeenCalled()
    })

    it('should not allow lbs values above 20', () => {
      render(
        <WeightIncrementInput
          value={20}
          unit="lbs"
          onChange={mockOnChange}
          disabled={false}
        />
      )

      const incrementBtn = screen.getByLabelText('Increase weight increment')
      fireEvent.click(incrementBtn)

      expect(mockOnChange).not.toHaveBeenCalled()
    })
  })

  describe('Input Validation', () => {
    it('should validate and constrain manual input values for kg', () => {
      render(
        <WeightIncrementInput
          value={2.5}
          unit="kg"
          onChange={mockOnChange}
          disabled={false}
        />
      )

      const input = screen.getByDisplayValue('2.5')

      // Test invalid high value
      fireEvent.change(input, { target: { value: '15' } })
      expect(mockOnChange).toHaveBeenCalledWith(10.0) // Clamped to max

      // Test invalid low value
      fireEvent.change(input, { target: { value: '0' } })
      expect(mockOnChange).toHaveBeenCalledWith(0.5) // Clamped to min
    })

    it('should handle non-numeric input gracefully', () => {
      render(
        <WeightIncrementInput
          value={2.5}
          unit="kg"
          onChange={mockOnChange}
          disabled={false}
        />
      )

      const input = screen.getByDisplayValue('2.5')

      // Test that invalid input doesn't crash the component
      expect(() => {
        fireEvent.focus(input)
        fireEvent.change(input, { target: { value: 'abc' } })
      }).not.toThrow()

      // Component should remain stable after invalid input
      expect(input).toBeInTheDocument()
    })

    it('should sanitize input to prevent XSS and invalid characters', () => {
      render(
        <WeightIncrementInput
          value={2.5}
          unit="kg"
          onChange={mockOnChange}
          disabled={false}
        />
      )

      const input = screen.getByDisplayValue('2.5')

      // Focus first to enable input tracking
      fireEvent.focus(input)

      // Test that XSS and invalid characters don't crash the component
      expect(() => {
        fireEvent.change(input, {
          target: { value: '<script>alert("xss")</script>5.5' },
        })
        fireEvent.change(input, { target: { value: '2@#$%.5' } })
        fireEvent.change(input, { target: { value: '3.0abc' } })
      }).not.toThrow()

      // Component should remain functional
      expect(input).toBeInTheDocument()
    })
  })

  describe('Disabled State', () => {
    it('should disable all controls when disabled prop is true', () => {
      render(
        <WeightIncrementInput
          value={2.5}
          unit="kg"
          onChange={mockOnChange}
          disabled
        />
      )

      const incrementBtn = screen.getByLabelText('Increase weight increment')
      const decrementBtn = screen.getByLabelText('Decrease weight increment')
      const input = screen.getByDisplayValue('2.5')

      expect(incrementBtn).toBeDisabled()
      expect(decrementBtn).toBeDisabled()
      expect(input).toBeDisabled()
    })

    it('should not call onChange when disabled and buttons clicked', () => {
      render(
        <WeightIncrementInput
          value={2.5}
          unit="kg"
          onChange={mockOnChange}
          disabled
        />
      )

      const incrementBtn = screen.getByLabelText('Increase weight increment')
      const decrementBtn = screen.getByLabelText('Decrease weight increment')

      fireEvent.click(incrementBtn)
      fireEvent.click(decrementBtn)

      expect(mockOnChange).not.toHaveBeenCalled()
    })
  })

  describe('Decimal Input Continuation - Critical Fix', () => {
    it('should allow typing decimal point without toFixed blocking', async () => {
      // This test will FAIL initially - targets the root cause on line 51
      // Root cause: value={value.toFixed()} prevents decimal continuation
      render(
        <WeightIncrementInput
          value={2.0}
          unit="kg"
          onChange={mockOnChange}
          disabled={false}
        />
      )

      const input = screen.getByDisplayValue('2')

      // Focus first to activate input tracking
      fireEvent.focus(input)

      // When: User types "2."
      fireEvent.change(input, { target: { value: '2.' } })

      // Then: Input should show "2." not "2" (proves toFixed isn't blocking)
      expect(input.value).toBe('2.')

      // When: User continues typing "5"
      fireEvent.change(input, { target: { value: '2.5' } })

      // Then: Input shows "2.5" and onChange fires with 2.5
      expect(input.value).toBe('2.5')
      expect(mockOnChange).toHaveBeenCalledWith(2.5)
    })

    it('should handle backspacing through decimal values', async () => {
      render(
        <WeightIncrementInput
          value={2.5}
          unit="kg"
          onChange={mockOnChange}
          disabled={false}
        />
      )

      const input = screen.getByDisplayValue('2.5')

      // Focus first to activate input tracking
      fireEvent.focus(input)

      // When: User backspaces to "2."
      fireEvent.change(input, { target: { value: '2.' } })

      // Then: Input should show "2." and allow continuation
      expect(input.value).toBe('2.')

      // When: User types "7"
      fireEvent.change(input, { target: { value: '2.7' } })

      // Then: Input shows "2.7" but onChange rounds to valid step (2.5)
      expect(mockOnChange).toHaveBeenCalledWith(2.5) // Rounds to nearest 0.5 step
    })

    it('should handle copy/paste decimal values', async () => {
      const user = userEvent.setup()
      render(
        <WeightIncrementInput
          value={2.0}
          unit="kg"
          onChange={mockOnChange}
          disabled={false}
        />
      )

      const input = screen.getByDisplayValue('2')
      await user.click(input)

      // Mock clipboard paste
      await user.clear(input)
      fireEvent.change(input, { target: { value: '3.5' } })

      expect(mockOnChange).toHaveBeenCalledWith(3.5)
    })

    it('should handle invalid decimal formats gracefully', async () => {
      const user = userEvent.setup()
      render(
        <WeightIncrementInput
          value={2.0}
          unit="kg"
          onChange={mockOnChange}
          disabled={false}
        />
      )

      const input = screen.getByDisplayValue('2')

      // Focus first
      await user.click(input)

      // Test invalid inputs
      await user.clear(input)
      fireEvent.change(input, { target: { value: '2.5.5' } })
      expect(mockOnChange).toHaveBeenCalledWith(2.5) // parseFloat handles this

      // Reset mock for next test
      mockOnChange.mockClear()

      await user.clear(input)
      fireEvent.change(input, { target: { value: '.' } })
      // For just ".", parseFloat returns NaN, so it should not call onChange
      expect(mockOnChange).not.toHaveBeenCalled()
    })

    it('should preserve decimal precision within boundaries', async () => {
      const user = userEvent.setup()
      render(
        <WeightIncrementInput
          value={2.0}
          unit="kg"
          onChange={mockOnChange}
          disabled={false}
        />
      )

      const input = screen.getByDisplayValue('2')

      // Focus first
      await user.click(input)

      // Test min boundary with decimal
      await user.clear(input)
      await user.type(input, '0.5')
      expect(mockOnChange).toHaveBeenCalledWith(0.5)

      // Test max boundary with decimal
      await user.clear(input)
      await user.type(input, '10.0')
      expect(mockOnChange).toHaveBeenCalledWith(10.0)

      // Test over max
      await user.clear(input)
      await user.type(input, '10.5')
      expect(mockOnChange).toHaveBeenCalledWith(10.0) // Clamped
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels for all interactive elements', () => {
      render(
        <WeightIncrementInput
          value={2.5}
          unit="kg"
          onChange={mockOnChange}
          disabled={false}
        />
      )

      expect(
        screen.getByLabelText('Increase weight increment')
      ).toBeInTheDocument()
      expect(
        screen.getByLabelText('Decrease weight increment')
      ).toBeInTheDocument()
      expect(
        screen.getByLabelText('Weight increment value')
      ).toBeInTheDocument()
    })

    it('should associate unit label with input field', () => {
      render(
        <WeightIncrementInput
          value={2.5}
          unit="kg"
          onChange={mockOnChange}
          disabled={false}
        />
      )

      const input = screen.getByLabelText('Weight increment value')
      screen.getByText('kg')

      // Unit should be properly associated via aria-describedby
      expect(input).toHaveAttribute(
        'aria-describedby',
        expect.stringContaining('unit')
      )
    })
  })
})
