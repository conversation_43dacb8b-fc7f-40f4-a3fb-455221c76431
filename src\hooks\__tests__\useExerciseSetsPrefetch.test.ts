import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useExerciseSetsPrefetch } from '../useExerciseSetsPrefetch'

// Mock SetLoader
const mockBatchLoadExerciseSets = vi.fn()
const mockLoadExerciseSets = vi.fn()

vi.mock('@/api/setLoader', () => {
  class MockSetLoader {
    batchLoadExerciseSets = mockBatchLoadExerciseSets

    loadExerciseSets = mockLoadExerciseSets
  }

  return {
    SetLoader: MockSetLoader,
  }
})

describe('useExerciseSetsPrefetch', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should initialize with empty prefetch state', () => {
    const { result } = renderHook(() => useExerciseSetsPrefetch())

    expect(result.current.prefetchStatus).toEqual({})
    expect(result.current.isPrefetching).toBe(false)
    expect(result.current.prefetchedExerciseIds).toEqual([])
  })

  it('should prefetch exercise sets for given exercise IDs', async () => {
    const exerciseIds = [1, 2, 3]
    const mockSets = {
      1: { success: true, sets: [{ Id: 1, Reps: 10 }], error: null },
      2: { success: true, sets: [{ Id: 2, Reps: 12 }], error: null },
      3: { success: true, sets: [{ Id: 3, Reps: 8 }], error: null },
    }

    mockBatchLoadExerciseSets.mockResolvedValue(mockSets)

    const { result } = renderHook(() => useExerciseSetsPrefetch())

    await act(async () => {
      await result.current.prefetchExerciseSets(exerciseIds)
    })

    expect(mockBatchLoadExerciseSets).toHaveBeenCalledWith(exerciseIds)
    expect(result.current.prefetchStatus).toEqual({
      1: 'success',
      2: 'success',
      3: 'success',
    })
    expect(result.current.prefetchedExerciseIds).toEqual([1, 2, 3])
    expect(result.current.isPrefetching).toBe(false)
  })

  it('should handle prefetch errors gracefully', async () => {
    const exerciseIds = [1, 2]
    const mockSets = {
      1: { success: true, sets: [{ Id: 1, Reps: 10 }], error: null },
      2: { success: false, sets: [], error: 'Network error' },
    }

    mockBatchLoadExerciseSets.mockResolvedValue(mockSets)

    const { result } = renderHook(() => useExerciseSetsPrefetch())

    await act(async () => {
      await result.current.prefetchExerciseSets(exerciseIds)
    })

    expect(result.current.prefetchStatus).toEqual({
      1: 'success',
      2: 'error',
    })
    expect(result.current.prefetchedExerciseIds).toEqual([1])
  })

  it('should prevent duplicate prefetch requests', async () => {
    const exerciseIds = [1, 2]
    mockBatchLoadExerciseSets.mockImplementation(
      () => new Promise((resolve) => setTimeout(() => resolve({}), 100))
    )

    const { result } = renderHook(() => useExerciseSetsPrefetch())

    // Start first prefetch
    act(() => {
      result.current.prefetchExerciseSets(exerciseIds)
    })

    expect(result.current.isPrefetching).toBe(true)

    // Try to start second prefetch while first is in progress
    act(() => {
      result.current.prefetchExerciseSets(exerciseIds)
    })

    // Should only be called once
    expect(mockBatchLoadExerciseSets).toHaveBeenCalledTimes(1)
  })

  it('should track prefetch progress', async () => {
    const exerciseIds = [1, 2, 3]
    const progressUpdates: number[] = []

    const { result } = renderHook(() => useExerciseSetsPrefetch())

    // Mock implementation that resolves exercises one by one
    mockBatchLoadExerciseSets.mockImplementation(async (ids) => {
      const results: any = {}
      // eslint-disable-next-line no-restricted-syntax
      for (const id of ids) {
        // eslint-disable-next-line no-await-in-loop
        await new Promise((resolve) => setTimeout(resolve, 10))
        results[id] = { success: true, sets: [], error: null }
        // Simulate progress callback
        result.current.onProgress?.(Object.keys(results).length / ids.length)
      }
      return results
    })

    result.current.onProgress = (progress) => {
      progressUpdates.push(progress)
    }

    await act(async () => {
      await result.current.prefetchExerciseSets(exerciseIds)
    })

    expect(progressUpdates.length).toBeGreaterThan(0)
    expect(progressUpdates[progressUpdates.length - 1]).toBe(1)
  })

  it('should clear prefetch cache when requested', async () => {
    const { result } = renderHook(() => useExerciseSetsPrefetch())

    // Prefetch some exercises first
    const mockSets = {
      1: { success: true, sets: [{ Id: 1, Reps: 10 }], error: null },
      2: { success: false, sets: [], error: 'Network error' },
    }
    mockBatchLoadExerciseSets.mockResolvedValue(mockSets)

    await act(async () => {
      await result.current.prefetchExerciseSets([1, 2])
    })

    // Verify state is set
    expect(result.current.prefetchStatus).toEqual({ 1: 'success', 2: 'error' })
    expect(result.current.prefetchedExerciseIds).toEqual([1])

    // Clear cache
    act(() => {
      result.current.clearPrefetchCache()
    })

    expect(result.current.prefetchStatus).toEqual({})
    expect(result.current.prefetchedExerciseIds).toEqual([])
  })

  it('should check if exercise is prefetched', async () => {
    const { result } = renderHook(() => useExerciseSetsPrefetch())

    // Prefetch some exercises with mixed results
    const mockSets = {
      1: { success: true, sets: [{ Id: 1, Reps: 10 }], error: null },
      2: { success: false, sets: [], error: 'Network error' },
    }
    mockBatchLoadExerciseSets.mockResolvedValue(mockSets)

    await act(async () => {
      await result.current.prefetchExerciseSets([1, 2])
    })

    expect(result.current.isExercisePrefetched(1)).toBe(true)
    expect(result.current.isExercisePrefetched(2)).toBe(false)
    expect(result.current.isExercisePrefetched(3)).toBe(false)
    expect(result.current.isExercisePrefetched(4)).toBe(false)
  })
})
