/**
 * Tailwind Configuration 52px Touch Target Tests
 *
 * Purpose: Validate Tailwind config meets 52px minimum touch target requirement
 * Context: Tests written to fail initially, enforcing TDD implementation
 */

import tailwindConfig from '../../tailwind.config.js'

describe('Tailwind Config - 52px Touch Target Compliance', () => {
  it('should define touch spacing as 52px minimum', () => {
    // @ts-expect-error - accessing JS config in TS test
    const touchSpacing = tailwindConfig.theme?.extend?.spacing?.touch

    // This test SHOULD FAIL initially as current value is '44px'
    expect(touchSpacing).toBe('52px')
  })

  it('should define touch minHeight as 52px minimum', () => {
    // @ts-expect-error - accessing JS config in TS test
    const touchMinHeight = tailwindConfig.theme?.extend?.minHeight?.touch

    // This test SHOULD FAIL initially as current value is '44px'
    expect(touchMinHeight).toBe('52px')
  })

  it('should have consistent touch values across spacing and minHeight', () => {
    // @ts-expect-error - accessing JS config in TS test
    const touchSpacing = tailwindConfig.theme?.extend?.spacing?.touch
    // @ts-expect-error - accessing JS config in TS test
    const touchMinHeight = tailwindConfig.theme?.extend?.minHeight?.touch

    // Both should be the same value
    expect(touchSpacing).toBe(touchMinHeight)
    expect(touchSpacing).toBe('52px')
  })
})

/**
 * Test Rationale:
 *
 * 1. These tests validate that Tailwind configuration defines 52px touch targets
 * 2. Currently fails because config has '44px' values
 * 3. Tests ensure consistency between spacing and minHeight definitions
 * 4. Simple, focused tests that validate configuration changes
 */
