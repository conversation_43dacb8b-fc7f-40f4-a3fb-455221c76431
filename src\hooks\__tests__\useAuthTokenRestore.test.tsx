import { renderHook, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useAuthTokenRestore } from '../useAuthTokenRestore'
import { restoreAuthToken } from '@/utils/auth/tokenRestore'

// Mock the token restore utility
vi.mock('@/utils/auth/tokenRestore', () => ({
  restoreAuthToken: vi.fn(),
}))

describe('useAuthTokenRestore', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should restore token on mount and update state', async () => {
    const mockResponse = {
      success: true,
      token: 'restored-token',
      user: { email: '<EMAIL>' },
    }

    vi.mocked(restoreAuthToken).mockResolvedValue(mockResponse)

    const { result } = renderHook(() => useAuthTokenRestore())

    // Initially restoring
    expect(result.current.isRestoringToken).toBe(true)
    expect(result.current.tokenRestored).toBe(false)

    // Wait for restoration to complete
    await waitFor(() => {
      expect(result.current.isRestoringToken).toBe(false)
    })

    // Should be restored
    expect(result.current.tokenRestored).toBe(true)
    expect(restoreAuthToken).toHaveBeenCalledTimes(1)
  })

  it('should handle restoration failure', async () => {
    const mockResponse = {
      success: false,
      token: null,
      user: null,
    }

    vi.mocked(restoreAuthToken).mockResolvedValue(mockResponse)

    const { result } = renderHook(() => useAuthTokenRestore())

    await waitFor(() => {
      expect(result.current.isRestoringToken).toBe(false)
    })

    expect(result.current.tokenRestored).toBe(false)
  })

  it('should handle restoration errors gracefully', async () => {
    vi.mocked(restoreAuthToken).mockRejectedValue(new Error('Network error'))

    const { result } = renderHook(() => useAuthTokenRestore())

    await waitFor(() => {
      expect(result.current.isRestoringToken).toBe(false)
    })

    // Should continue and mark as not restored
    expect(result.current.tokenRestored).toBe(false)
  })

  it('should only call restoreAuthToken once', async () => {
    vi.mocked(restoreAuthToken).mockResolvedValue({
      success: true,
      token: 'token',
      user: null,
    })

    const { rerender } = renderHook(() => useAuthTokenRestore())

    await waitFor(() => {
      expect(restoreAuthToken).toHaveBeenCalledTimes(1)
    })

    // Re-render should not trigger another restoration
    rerender()

    await waitFor(() => {
      expect(restoreAuthToken).toHaveBeenCalledTimes(1)
    })
  })

  it('should clean up properly on unmount', async () => {
    vi.mocked(restoreAuthToken).mockImplementation(
      () =>
        new Promise((resolve) => {
          setTimeout(
            () => resolve({ success: true, token: 'token', user: null }),
            100
          )
        })
    )

    const { result, unmount } = renderHook(() => useAuthTokenRestore())

    expect(result.current.isRestoringToken).toBe(true)

    // Unmount before restoration completes
    unmount()

    // Should not throw or cause issues
    await new Promise((resolve) => setTimeout(resolve, 150))
  })
})
