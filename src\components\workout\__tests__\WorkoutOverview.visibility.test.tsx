/**
 * Test Rationale:
 * - Verify WorkoutOverview integrates with visibility change detection
 * - Ensure workout store visibility actions are called when app backgrounds/foregrounds
 * - Test debouncing of rapid visibility changes
 * - Verify cleanup on unmount to prevent memory leaks
 *
 * This test should FAIL initially because WorkoutOverview doesn't use useVisibilityChange yet
 */

import { render, waitFor } from '@testing-library/react'
import { useVisibilityChange } from '@/hooks/useVisibilityChange'
import { useWorkout } from '@/hooks/useWorkout'
import { useProgressivePrefetch } from '@/hooks/useProgressivePrefetch'
import { useExerciseSetsPrefetch } from '@/hooks/useExerciseSetsPrefetch'
import { usePullToRefresh } from '@/hooks/usePullToRefresh'
import { useWorkoutRetry } from '@/hooks/useWorkoutRetry'
import { useWorkoutPreview } from '@/hooks/useWorkoutPreview'
import { useNavigation } from '@/contexts/NavigationContext'
import { WorkoutOverview } from '../WorkoutOverview'
import { useWorkoutStore } from '@/stores/workoutStore'
import {
  mockWorkoutTemplate,
  mockExercise,
  mockNextExercise,
} from '@/test-utils/workout-mocks'
import { useWorkoutActions } from '../WorkoutOverviewActions'
import type { WorkoutTemplateGroupModel, ExerciseWorkSetsModel } from '@/types'

const mockWorkout: WorkoutTemplateGroupModel[] = [
  {
    Id: 1,
    WorkoutTemplateModels: [mockWorkoutTemplate],
  },
]

const mockExercises: ExerciseWorkSetsModel[] = [
  { ...mockExercise, Id: 1, Sets: [] },
  { ...mockNextExercise, Id: 2, Sets: [] },
]

// Mock all the hooks used by WorkoutOverview
vi.mock('@/hooks/useWorkout')
vi.mock('@/hooks/useProgressivePrefetch')
vi.mock('@/hooks/useExerciseSetsPrefetch')
vi.mock('@/hooks/usePullToRefresh')
vi.mock('@/hooks/useWorkoutRetry')
vi.mock('@/hooks/useWorkoutPreview')
vi.mock('@/contexts/NavigationContext')
vi.mock('@/stores/workoutStore')
vi.mock('../WorkoutOverviewActions')
vi.mock('../TestV2UIBanner', () => ({
  TestV2UIBanner: () => null,
}))

// Mock visibility change hook to track if it's being used
vi.mock('@/hooks/useVisibilityChange', () => ({
  useVisibilityChange: vi.fn(),
}))

describe('WorkoutOverview - Visibility Change Integration', () => {
  const mockHandleAppBackground = vi.fn()
  const mockHandleAppForeground = vi.fn().mockResolvedValue(undefined)

  beforeEach(() => {
    vi.clearAllMocks()

    // Mock workout store
    vi.mocked(useWorkoutStore).mockReturnValue({
      handleAppBackground: mockHandleAppBackground,
      handleAppForeground: mockHandleAppForeground,
    } as any)

    // Setup default mocks for other hooks
    vi.mocked(useWorkout).mockReturnValue({
      todaysWorkout: mockWorkout,
      isLoadingWorkout: false,
      workoutError: null,
      exercises: mockExercises,
      workoutSession: { startTime: new Date() },
      prefetchedExerciseIds: [1, 2, 3],
      isPrefetching: false,
      prefetchStatus: {},
      workoutStorePrefetchedExerciseIds: [1, 2, 3], // Add missing property
      workoutStorePrefetchStatus: {},
      startWorkout: vi.fn(),
      userProgramInfo: {},
      exerciseWorkSetsModels: mockExercises,
      expectedExerciseCount: 2,
      hasInitialData: true,
      isOffline: false,
      refreshWorkout: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
      finishWorkout: vi.fn(),
      isLoading: false,
      loadExerciseRecommendation: vi.fn(),
    } as any)

    vi.mocked(useProgressivePrefetch).mockReturnValue(undefined)

    vi.mocked(useExerciseSetsPrefetch).mockReturnValue({
      prefetchStatus: {},
      isPrefetching: false,
      prefetchedExerciseIds: [],
      retryCount: 0,
    } as any)

    vi.mocked(usePullToRefresh).mockReturnValue({
      isRefreshing: false,
      pullToRefreshRef: { current: null },
    } as any)

    vi.mocked(useWorkoutRetry).mockReturnValue({
      retryCount: 0,
      isRetrying: false,
      handleManualRetry: vi.fn(),
    } as any)

    vi.mocked(useWorkoutPreview).mockReturnValue({
      previewWorkout: null,
      isLoadingPreview: false,
    } as any)

    vi.mocked(useNavigation).mockReturnValue({
      setTitle: vi.fn(),
    } as any)

    vi.mocked(useWorkoutActions).mockReturnValue({
      isStartingWorkout: false,
      handleStartWorkout: vi.fn(),
      handleFinishWorkout: vi.fn(),
      handleExerciseClick: vi.fn(),
      handleRetryExercise: vi.fn(),
      hasCompletedSets: false,
      getButtonLabel: vi.fn().mockReturnValue('Start Workout'),
      getButtonAriaLabel: vi.fn().mockReturnValue('Start your workout'),
    } as any)
  })

  it('should use visibility change hook and call store actions on visibility changes', async () => {
    let visibilityCallback: ((state: 'visible' | 'hidden') => void) | null =
      null

    // Capture the callback passed to useVisibilityChange
    vi.mocked(useVisibilityChange).mockImplementation((callback) => {
      visibilityCallback = callback
    })

    render(<WorkoutOverview />)

    // Verify the visibility hook is being used
    expect(useVisibilityChange).toHaveBeenCalledTimes(1)
    expect(visibilityCallback).toBeTruthy()

    // Simulate app going to background
    visibilityCallback!('hidden')

    await waitFor(() => {
      expect(mockHandleAppBackground).toHaveBeenCalledTimes(1)
    })

    // Simulate app returning to foreground
    visibilityCallback!('visible')

    await waitFor(() => {
      expect(mockHandleAppForeground).toHaveBeenCalledTimes(1)
    })
  })

  it('should debounce rapid visibility changes', async () => {
    let visibilityCallback: ((state: 'visible' | 'hidden') => void) | null =
      null

    vi.mocked(useVisibilityChange).mockImplementation((callback) => {
      visibilityCallback = callback
    })

    render(<WorkoutOverview />)

    // Simulate rapid visibility changes
    visibilityCallback!('hidden')
    visibilityCallback!('visible')
    visibilityCallback!('hidden')
    visibilityCallback!('visible')
    visibilityCallback!('hidden')

    // Wait for potential debounce
    await waitFor(() => {
      // Should not call handlers multiple times rapidly
      expect(mockHandleAppBackground.mock.calls.length).toBeLessThanOrEqual(3)
      expect(mockHandleAppForeground.mock.calls.length).toBeLessThanOrEqual(2)
    })
  })

  it('should not call visibility actions when no workout session exists', async () => {
    // Mock no workout session
    vi.mocked(useWorkout).mockReturnValue({
      todaysWorkout: null,
      workoutSession: null,
      exercises: [],
      workoutStorePrefetchedExerciseIds: [],
      prefetchedExerciseIds: [],
      isLoadingWorkout: false,
      workoutError: null,
      isPrefetching: false,
      prefetchStatus: {},
      workoutStorePrefetchStatus: {},
      startWorkout: vi.fn(),
      userProgramInfo: {},
      exerciseWorkSetsModels: [],
      expectedExerciseCount: 0,
      hasInitialData: false,
      isOffline: false,
      refreshWorkout: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
      finishWorkout: vi.fn(),
      isLoading: false,
      loadExerciseRecommendation: vi.fn(),
    } as any)

    let visibilityCallback: ((state: 'visible' | 'hidden') => void) | null =
      null

    vi.mocked(useVisibilityChange).mockImplementation((callback) => {
      visibilityCallback = callback
    })

    render(<WorkoutOverview />)

    // Simulate visibility changes
    visibilityCallback!('hidden')
    visibilityCallback!('visible')

    await waitFor(() => {
      // Should still call the actions - the store will handle the no-session case
      expect(mockHandleAppBackground).toHaveBeenCalledTimes(1)
      expect(mockHandleAppForeground).toHaveBeenCalledTimes(1)
    })
  })

  it('should clean up visibility listener on unmount', () => {
    const mockCleanup = vi.fn()

    vi.mocked(useVisibilityChange).mockImplementation(() => {
      // React will call the cleanup function returned by useEffect
      return mockCleanup
    })

    const { unmount } = render(<WorkoutOverview />)

    // Unmount the component
    unmount()

    // Verify visibility change hook was used (cleanup happens internally)
    expect(useVisibilityChange).toHaveBeenCalled()
  })

  it('should handle errors in visibility callbacks gracefully', async () => {
    // Make handleAppForeground throw an error
    mockHandleAppForeground.mockRejectedValueOnce(new Error('Network error'))

    let visibilityCallback: ((state: 'visible' | 'hidden') => void) | null =
      null

    vi.mocked(useVisibilityChange).mockImplementation((callback) => {
      visibilityCallback = callback
    })

    render(<WorkoutOverview />)

    // Should not throw when visibility changes despite error
    expect(() => {
      visibilityCallback!('visible')
    }).not.toThrow()

    await waitFor(() => {
      expect(mockHandleAppForeground).toHaveBeenCalledTimes(1)
    })
  })
})
