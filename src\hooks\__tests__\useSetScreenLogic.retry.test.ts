import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { useSetScreenLogic } from '../useSetScreenLogic'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useAuthStore } from '@/stores/authStore'
import { useRIR } from '@/hooks/useRIR'

// Mock dependencies
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
  }),
}))

vi.mock('@/hooks/useWorkout')
vi.mock('@/stores/workoutStore')
vi.mock('@/stores/authStore')
vi.mock('@/hooks/useRIR')

describe('useSetScreenLogic retry logic', () => {
  let mockGetRecommendation: ReturnType<typeof vi.fn>
  let mockGetCachedExerciseRecommendation: ReturnType<typeof vi.fn>

  beforeEach(() => {
    vi.clearAllMocks()
    vi.useFakeTimers({ shouldAdvanceTime: true })

    mockGetRecommendation = vi.fn()
    mockGetCachedExerciseRecommendation = vi.fn()

    // Setup mocks
    vi.mocked(useWorkout).mockReturnValue({
      saveSet: vi.fn(),
      isLoading: false,
      error: null,
      getRecommendation: mockGetRecommendation,
    } as any)

    const mockWorkoutStoreReturn = {
      exercises: [{ Id: 1, Label: 'Bench Press' }],
      currentExerciseIndex: 0,
      currentSetIndex: 0,
      workoutSession: {
        exercises: [
          {
            exerciseId: 1,
            sets: [],
          },
        ],
      },
      nextSet: vi.fn(),
      setCurrentSetIndex: vi.fn(),
      nextExercise: vi.fn(),
      setCurrentExerciseById: vi.fn(),
      getCachedExerciseRecommendation: mockGetCachedExerciseRecommendation,
      updateCurrentSet: vi.fn(),
    } as any

    vi.mocked(useWorkoutStore).mockReturnValue(mockWorkoutStoreReturn)

    // Also mock the static getState method
    ;(useWorkoutStore as any).getState = vi.fn(() => mockWorkoutStoreReturn)

    // Mock getState for useAuthStore
    vi.mocked(useAuthStore).mockImplementation(
      () =>
        ({
          getCachedUserInfo: () => ({ MassUnit: 'lb' }),
        }) as any
    )

    // Also mock the static getState method
    ;(useAuthStore as any).getState = vi.fn(() => ({
      getCachedUserInfo: () => ({ MassUnit: 'lb' }),
    }))

    vi.mocked(useRIR).mockReturnValue({
      mapRIRValueToNumber: vi.fn(),
      saveRIR: vi.fn(),
    } as any)
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it('should retry loading recommendation when it returns null', async () => {
    // First two calls return null, third returns recommendation
    mockGetRecommendation
      .mockResolvedValueOnce(null)
      .mockResolvedValueOnce(null)
      .mockResolvedValueOnce({
        Reps: 10,
        Weight: { Lb: 135, Kg: 61.2 },
        Series: 3,
        WarmupsCount: 2,
      })

    mockGetCachedExerciseRecommendation.mockReturnValue(null)

    const { result } = renderHook(() => useSetScreenLogic(1))

    // Should be loading initially
    expect(result.current.isLoading).toBe(true)
    expect(result.current.recommendation).toBe(null)

    // Wait for first attempt
    await waitFor(() => {
      expect(mockGetRecommendation).toHaveBeenCalledTimes(1)
    })

    // Still loading after first null
    expect(result.current.isLoading).toBe(true)

    // Advance timer for retry
    vi.advanceTimersByTime(2000)

    // Wait for second attempt
    await waitFor(() => {
      expect(mockGetRecommendation).toHaveBeenCalledTimes(2)
    })

    // Still loading after second null
    expect(result.current.isLoading).toBe(true)

    // Advance timer for second retry
    vi.advanceTimersByTime(2000)

    // Wait for third attempt
    await waitFor(() => {
      expect(mockGetRecommendation).toHaveBeenCalledTimes(3)
    })

    // Should have recommendation now
    expect(result.current.isLoading).toBe(false)
    expect(result.current.recommendation).toEqual({
      Reps: 10,
      Weight: { Lb: 135, Kg: 61.2 },
      Series: 3,
      WarmupsCount: 2,
    })
  })

  it('should retry on errors and call refetchRecommendation after max retries', async () => {
    // All calls throw error
    mockGetRecommendation.mockRejectedValue(new Error('Network error'))
    mockGetCachedExerciseRecommendation.mockReturnValue(null)

    const { result } = renderHook(() => useSetScreenLogic(1))

    // Should be loading initially
    expect(result.current.isLoading).toBe(true)

    // Wait for first attempt
    await waitFor(() => {
      expect(mockGetRecommendation).toHaveBeenCalledTimes(1)
    })

    // Advance timers for all retries
    vi.advanceTimersByTime(2000)
    await waitFor(() => {
      expect(mockGetRecommendation).toHaveBeenCalledTimes(2)
    })

    vi.advanceTimersByTime(2000)
    await waitFor(() => {
      expect(mockGetRecommendation).toHaveBeenCalledTimes(3)
    })

    vi.advanceTimersByTime(2000)
    await waitFor(() => {
      expect(mockGetRecommendation).toHaveBeenCalledTimes(4)
    })

    // After max retries, it should have called getRecommendation one more time
    // through refetchRecommendation
    await waitFor(() => {
      expect(mockGetRecommendation).toHaveBeenCalledTimes(5)
    })
  })

  it('should stop loading immediately when cached recommendation exists', async () => {
    const cachedRecommendation = {
      Reps: 8,
      Weight: { Lb: 100, Kg: 45 },
      Series: 3,
      WarmupsCount: 1,
    }

    mockGetCachedExerciseRecommendation.mockReturnValue(cachedRecommendation)

    const { result } = renderHook(() => useSetScreenLogic(1))

    // Should not be loading and have recommendation immediately
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
      expect(result.current.recommendation).toEqual(cachedRecommendation)
    })

    // Should not call API
    expect(mockGetRecommendation).not.toHaveBeenCalled()
  })
})
