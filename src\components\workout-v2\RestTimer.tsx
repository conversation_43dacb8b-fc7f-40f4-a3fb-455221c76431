'use client'

import { useState, useEffect, useRef } from 'react'
import { AnimatePresence } from 'framer-motion'
import { useWorkoutStore } from '@/stores/workoutStore'
import { vibrate } from '@/utils/haptics'
import { DurationPicker } from './DurationPicker'
import { RestTimerDisplay } from './RestTimerDisplay'

export function RestTimer() {
  const { restTimerState, setRestTimerState } = useWorkoutStore()
  const [timeRemaining, setTimeRemaining] = useState(0)
  const [showDurationPicker, setShowDurationPicker] = useState(false)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    if (restTimerState.isActive && restTimerState.duration > 0) {
      setTimeRemaining(restTimerState.duration)

      // Clear any existing interval
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }

      // Start countdown
      intervalRef.current = setInterval(() => {
        setTimeRemaining((prev) => {
          if (prev <= 1) {
            // Timer complete
            vibrate('success')
            setRestTimerState({ isActive: false, duration: 0 })

            // Show notification if available
            if (
              'Notification' in window &&
              Notification.permission === 'granted'
            ) {
              try {
                const notification = new Notification('Rest Complete!', {
                  body: 'Time for your next set',
                  icon: '/icon-192x192.png',
                  tag: 'rest-timer',
                })
                // Close notification after 5 seconds
                setTimeout(() => notification.close(), 5000)
              } catch (error) {
                // Handle notification constructor errors gracefully
                console.warn('Failed to show rest timer notification:', error)
              }
            }

            return 0
          }

          // Vibrate at 10 seconds
          if (prev === 11) {
            vibrate('warning')
          }

          return prev - 1
        })
      }, 1000)

      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current)
        }
      }
    }
    return undefined
  }, [restTimerState, setRestTimerState])

  const skipRest = () => {
    vibrate('light')
    setRestTimerState({ isActive: false, duration: 0 })
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const handleDurationSelect = (duration: number) => {
    // Save to localStorage for future use
    localStorage.setItem('restDuration', duration.toString())
    // Dispatch storage event for same-tab sync
    window.dispatchEvent(new Event('storage'))

    // Restart the current timer with the new duration
    setRestTimerState({
      isActive: true,
      duration,
      nextSetInfo: restTimerState.nextSetInfo,
    })

    // Reset the countdown immediately
    setTimeRemaining(duration)

    // Provide haptic feedback
    vibrate('light')
  }

  if (!restTimerState.isActive) return null

  return (
    <>
      <AnimatePresence>
        {restTimerState.isActive && (
          <RestTimerDisplay
            timeRemaining={timeRemaining}
            duration={restTimerState.duration}
            onDurationClick={() => setShowDurationPicker(true)}
            onSkip={skipRest}
            formatTime={formatTime}
          />
        )}
      </AnimatePresence>

      {/* Duration Picker */}
      {showDurationPicker && (
        <DurationPicker
          currentDuration={restTimerState.duration}
          onSelect={handleDurationSelect}
          onClose={() => setShowDurationPicker(false)}
        />
      )}
    </>
  )
}

// Helper hook to start rest timer
export function useRestTimer() {
  const { setRestTimerState } = useWorkoutStore()

  const startRestTimer = (
    duration: number,
    nextSetInfo?: { reps: number; weight: number; unit: 'kg' | 'lbs' }
  ) => {
    // Request notification permission if not granted
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission()
    }

    setRestTimerState({ isActive: true, duration, nextSetInfo })
  }

  return { startRestTimer }
}
