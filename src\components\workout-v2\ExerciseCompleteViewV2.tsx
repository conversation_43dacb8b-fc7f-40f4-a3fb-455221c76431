'use client'

import { motion } from 'framer-motion'
import { ArrowRight } from 'lucide-react'
import type {
  ExerciseModel,
  RecommendationModel,
  WorkoutLogSerieModel,
} from '@/types'
import { vibrate } from '@/utils/haptics'
import { SuccessIcon } from '@/components/workout/SuccessIcon'
import { computeOneRM } from '@/utils/oneRmCalculator'

// Enhanced confetti component with conditional rendering
function Confetti({ show }: { show: boolean }) {
  if (!show) return null

  // Use a stable array to avoid recreating on each render
  const confettiPieces = Array.from({ length: 20 }, (_, i) => i)

  return (
    <div className="fixed inset-0 pointer-events-none">
      {confettiPieces.map((piece) => (
        <motion.div
          key={`confetti-particle-${piece}`}
          data-testid={`confetti-particle-${piece}`}
          className="absolute w-2 h-2 bg-brand-primary rounded-full"
          initial={{
            x: '50vw',
            y: '50vh',
            scale: 0,
          }}
          animate={{
            x: `${50 + (Math.random() - 0.5) * 80}vw`,
            y: `${50 + (Math.random() - 0.5) * 80}vh`,
            scale: [0, 1, 1, 0],
          }}
          transition={{
            duration: 1.5,
            delay: piece * 0.05,
            ease: 'easeOut',
          }}
        />
      ))}
    </div>
  )
}

interface ExerciseCompleteViewV2Props {
  exercise: ExerciseModel | null
  isLastExercise: boolean
  onContinue: () => void
  recommendation?: RecommendationModel | null
  completedSets?: WorkoutLogSerieModel[]
}

export function ExerciseCompleteViewV2({
  exercise,
  isLastExercise,
  onContinue,
  recommendation,
  completedSets = [],
}: ExerciseCompleteViewV2Props) {
  const handleContinue = () => {
    vibrate('success')
    onContinue()
  }

  // Calculate OneRM progress by comparing best sets
  const calculateActualProgress = () => {
    // Filter out warmup sets to get work sets only
    const workSets = completedSets.filter(
      (set) => !set.IsWarmups && set.Reps && set.Reps > 0
    )

    if (workSets.length === 0) return 0

    // Find the best set (highest 1RM) from current workout
    let bestCurrentOneRM = 0

    workSets.forEach((set) => {
      const weight = set.Weight?.Kg || set.Weight?.Lb || 0
      if (weight > 0 && set.Reps) {
        const oneRM = computeOneRM(weight, set.Reps)
        if (oneRM > bestCurrentOneRM) {
          bestCurrentOneRM = oneRM
        }
      }
    })

    // Get the reference set from last workout (FirstWorkSet1RM contains the best 1RM from last time)
    const lastBestOneRM =
      recommendation?.FirstWorkSet1RM?.Kg ||
      recommendation?.FirstWorkSet1RM?.Lb ||
      0

    if (lastBestOneRM === 0 || bestCurrentOneRM === 0) return 0

    // Calculate percentage change
    const progress = ((bestCurrentOneRM - lastBestOneRM) * 100) / lastBestOneRM
    return Math.round(progress * 10) / 10 // Round to 1 decimal place
  }

  const oneRMProgress = calculateActualProgress()
  const showPercentage = oneRMProgress !== 0
  const showConfetti = oneRMProgress > 0

  return (
    <div className="flex flex-col items-center justify-center min-h-screen px-4 bg-surface-primary">
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.3 }}
        className="text-center max-w-sm"
      >
        {/* Golden Success icon with animation */}
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
          className="mb-6"
        >
          <SuccessIcon size={120} className="animate-scale-bounce" autoPlay />
        </motion.div>

        {/* Exercise name with fade-in animation */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.3 }}
          className="animate-fade-in"
        >
          <h2 className="text-2xl font-bold text-text-primary mb-2">
            {exercise?.Label || 'Exercise'} Complete!
          </h2>
        </motion.div>

        {/* Percentage increase display */}
        {showPercentage && (
          <motion.p
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.5, duration: 0.3 }}
            className={`text-xl font-bold mb-2 animate-fade-in ${
              oneRMProgress > 0 ? 'text-yellow-500' : 'text-text-secondary'
            }`}
          >
            {oneRMProgress > 0
              ? `+${oneRMProgress}% stronger!`
              : `${oneRMProgress}% today`}
          </motion.p>
        )}

        {/* Motivational message with fade-in */}
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6, duration: 0.3 }}
          className="text-text-secondary mb-8"
        >
          {isLastExercise
            ? "Amazing work! You've crushed your workout!"
            : 'Great job! Ready for the next one?'}
        </motion.p>

        {/* Continue button with gold gradient */}
        <motion.button
          onClick={handleContinue}
          className="w-full py-4 px-6 bg-gradient-to-r from-brand-gold-start to-brand-gold-end 
                     text-text-inverse rounded-full font-medium text-lg flex items-center 
                     justify-center gap-2 hover:opacity-90 
                     transition-all shadow-lg"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7, duration: 0.3 }}
        >
          {isLastExercise ? 'Finish Workout' : 'Next Exercise'}
          <ArrowRight className="w-5 h-5" />
        </motion.button>
      </motion.div>

      {/* Confetti animation only for positive progress */}
      <Confetti show={showConfetti} />
    </div>
  )
}
