/**
 * Workout-related mocks for testing
 */

import { vi } from 'vitest'
import type { ExerciseModel, WorkoutTemplateModel } from '@/types'
import type { useWorkout } from '@/hooks/useWorkout'

// Mock exercise data
export const mockExercise: ExerciseModel = {
  Id: 1,
  Label: 'Bench Press',
  BodyPartId: 2,
  EquipmentId: 1,
  IsBodyweight: false,
  VideoUrl: 'https://example.com/video',
  IsSystemExercise: true,
  IsSwapTarget: false,
  IsFinished: false,
  IsUnilateral: false,
  IsTimeBased: false,
  IsEasy: false,
  IsMedium: true,
  IsNextExercise: false,
  IsPlate: false,
  IsWeighted: true,
  IsPyramid: false,
  IsNormalSets: true,
  IsBodypartPriority: false,
  IsFlexibility: false,
  IsOneHanded: false,
  LocalVideo: '',
  IsAssisted: false,
}

export const mockNextExercise: ExerciseModel = {
  Id: 2,
  Label: 'Squats',
  BodyPartId: 3,
  EquipmentId: 1,
  IsBodyweight: false,
  VideoUrl: 'https://example.com/video2',
  IsSystemExercise: true,
  IsSwapTarget: false,
  IsFinished: false,
  IsUnilateral: false,
  IsTimeBased: false,
  IsEasy: false,
  IsMedium: true,
  IsNextExercise: true,
  IsPlate: false,
  IsWeighted: true,
  IsPyramid: false,
  IsNormalSets: true,
  IsBodypartPriority: false,
  IsFlexibility: false,
  IsOneHanded: false,
  LocalVideo: '',
  IsAssisted: false,
}

// Mock workout template
export const mockWorkoutTemplate: WorkoutTemplateModel = {
  Id: 12345,
  UserId: 'user123',
  Label: 'Push Day',
  Exercises: [mockExercise, mockNextExercise],
  IsSystemExercise: true,
  WorkoutSettingsModel: {},
}

// Default useWorkout mock return value
export const createWorkoutMock = (
  overrides: Partial<ReturnType<typeof useWorkout>> = {}
) => ({
  // State
  currentWorkout: mockWorkoutTemplate,
  currentExercise: mockExercise,
  currentExerciseIndex: 0,
  currentSetIndex: 0,
  exercises: [mockExercise, mockNextExercise],
  workoutSession: { id: 'session-1', startTime: new Date() },
  isLoading: false,
  error: null,
  isOffline: false,
  cachedWorkout: null,
  recommendation: null,

  // Actions
  startWorkout: vi.fn(),
  nextSet: vi.fn(),
  nextExercise: vi.fn(),
  completeWorkout: vi.fn(),
  isCompletingWorkout: false,
  saveSet: vi.fn(),
  finishWorkout: vi.fn(),
  goToNextExercise: vi.fn(),
  resetWorkout: vi.fn(),

  // Queries
  todaysWorkout: [mockWorkoutTemplate],
  userProgramInfo: {
    RecommendedProgram: { Id: 1, Label: 'Test Program' },
    NextWorkoutTemplate: mockWorkoutTemplate,
  },
  userWorkouts: [mockWorkoutTemplate],
  isLoadingWorkout: false,
  workoutError: null,
  exerciseWorkSetsModels: [],
  loadingStates: new Map(),
  expectedExerciseCount: 2,
  hasInitialData: true,
  isLoadingFresh: false,
  refreshWorkout: vi.fn(),
  updateExerciseWorkSets: vi.fn(),
  updateSetRIR: vi.fn(),

  // Utility functions
  getCurrentExercise: vi.fn(() => mockExercise),
  getNextExercise: vi.fn(() => mockNextExercise),
  getRestDuration: vi.fn(() => 90),
  getCacheStats: vi.fn(() => ({ hits: 0, misses: 0 })),
  getExerciseProgress: vi.fn(() => ({ completed: 0, total: 3 })),

  // Derived properties
  totalSets: 3,
  isLastSet: false,
  isLastExercise: false,

  ...overrides,
})

/**
 * Mock the useWorkout hook with default values
 */
export function mockUseWorkout(
  overrides: Partial<ReturnType<typeof useWorkout>> = {}
) {
  const mockReturn = createWorkoutMock(overrides)

  // Store the mock return value globally so it's accessible in the hoisted mock
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  ;(globalThis as any).__mockUseWorkoutReturn = mockReturn

  vi.mock('@/hooks/useWorkout', () => ({
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    useWorkout: vi.fn(() => (globalThis as any).__mockUseWorkoutReturn),
  }))

  return mockReturn
}

/**
 * Mock workout store functions
 */
export const mockWorkoutStore = {
  // State
  exercises: [mockExercise, mockNextExercise],
  currentExerciseIndex: 0,
  currentSetIndex: 0,
  currentWorkout: mockWorkoutTemplate,
  workoutSession: { id: 'session-1', startTime: new Date() },
  isLoading: false,
  error: null,
  currentSetData: {},

  // Actions
  setWorkout: vi.fn(),
  startWorkout: vi.fn(),
  nextSet: vi.fn(),
  nextExercise: vi.fn(),
  completeWorkout: vi.fn(),
  saveSet: vi.fn(),
  resetWorkout: vi.fn(),

  // Cache functions
  getCachedUserProgramInfo: vi.fn(),
  getCachedUserWorkouts: vi.fn(),
  getCachedTodaysWorkout: vi.fn(),
  getCurrentExercise: vi.fn(() => mockExercise),
  getNextExercise: vi.fn(() => mockNextExercise),
  getRestDuration: vi.fn(() => 90),
  getCacheStats: vi.fn(() => ({ hits: 0, misses: 0 })),
  getExerciseProgress: vi.fn(() => ({ completed: 0, total: 3 })),
  loadAllExerciseRecommendations: vi.fn(),
  loadingStates: new Map(),
  updateSetRIR: vi.fn(),
  getCachedExerciseRecommendation: vi.fn(),
}

/**
 * Mock the useWorkoutStore hook
 */
export function mockUseWorkoutStore(
  overrides: Partial<typeof mockWorkoutStore> = {}
) {
  const mockReturn = { ...mockWorkoutStore, ...overrides }

  // Store the mock return value globally so it's accessible in the hoisted mock
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  ;(globalThis as any).__mockUseWorkoutStoreReturn = mockReturn

  vi.mock('@/stores/workoutStore', () => ({
    useWorkoutStore: vi.fn(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      () => (globalThis as any).__mockUseWorkoutStoreReturn
    ),
  }))

  return mockReturn
}

/**
 * Mock router for navigation tests
 * Note: next/navigation is now mocked globally in setup.ts
 * This function is kept for backward compatibility
 */
export function mockUseRouter() {
  // Use vi.fn() directly since global mocks are handled in setup.ts
  const mockPush = vi.fn()
  const mockReplace = vi.fn()
  const mockBack = vi.fn()
  const mockRefresh = vi.fn()

  return { mockPush, mockBack, mockReplace, mockRefresh }
}

/**
 * Reset all workout mocks
 */
export function resetWorkoutMocks() {
  vi.clearAllMocks()
}

// Type helper for useWorkout hook (this would normally be imported from the actual hook)
// type UseWorkoutType = () => ReturnType<typeof createWorkoutMock>
