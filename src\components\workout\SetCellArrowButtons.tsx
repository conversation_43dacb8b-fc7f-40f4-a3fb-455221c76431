'use client'

import React from 'react'

interface ArrowButtonProps {
  direction: 'up' | 'down'
  onClick: () => void
  label: string
}

function ArrowButton({ direction, onClick, label }: ArrowButtonProps) {
  return (
    <button
      onClick={onClick}
      className="text-brand-primary hover:text-brand-primary/80 transition-colors min-h-[52px] min-w-[52px] flex items-center justify-center"
      aria-label={label}
    >
      <svg
        className="w-12 h-12"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d={direction === 'up' ? 'M5 15l7-7 7 7' : 'M19 9l-7 7-7-7'}
        />
      </svg>
    </button>
  )
}

interface SetCellArrowButtonsProps {
  isActive: boolean
  onRepsUp: () => void
  onRepsDown: () => void
  onWeightUp: () => void
  onWeightDown: () => void
  position: 'above' | 'below'
}

export function SetCellArrowButtons({
  isActive,
  onRepsUp,
  onRepsDown,
  onWeightUp,
  onWeightDown,
  position,
}: SetCellArrowButtonsProps) {
  if (!isActive) return null

  const direction = position === 'above' ? 'up' : 'down'
  const repsClick = position === 'above' ? onRepsUp : onRepsDown
  const weightClick = position === 'above' ? onWeightUp : onWeightDown

  return (
    <div className="grid grid-cols-[25px_60px_1fr_25px_1fr] gap-0 items-center pb-1">
      <div />
      <div />
      <div className="px-2 flex justify-center">
        <ArrowButton
          direction={direction}
          onClick={repsClick}
          label={`${direction === 'up' ? 'Increase' : 'Decrease'} reps`}
        />
      </div>
      <div />
      <div className="px-2 flex justify-center">
        <ArrowButton
          direction={direction}
          onClick={weightClick}
          label={`${direction === 'up' ? 'Increase' : 'Decrease'} weight`}
        />
      </div>
    </div>
  )
}
