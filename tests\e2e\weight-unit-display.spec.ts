import { test, expect } from '@playwright/test'

test.describe('Weight Unit Display', () => {
  test.beforeEach(async ({ page }) => {
    // Mock basic authentication
    await page.route('**/api/Account/Login', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          access_token: 'test-token',
          token_type: 'Bearer',
          expires_in: 86400,
          userName: '<EMAIL>',
          issued: new Date().toISOString(),
          expires: new Date(Date.now() + ********).toISOString(),
        }),
      })
    })
  })

  test('should display total weight lifted in kg when user mass unit is kg', async ({
    page,
  }) => {
    // Mock user info with kg preference
    await page.route('**/api/Account/GetUserInfoPyramid', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          StatusCode: 200,
          Result: {
            Email: '<EMAIL>',
            FirstName: 'Test',
            LastName: 'User',
            MassUnit: 'kg',
          },
        }),
      })
    })

    // Mock user stats with both kg and lbs values
    await page.route(
      '**/api/WorkoutLog/GetLogAverageWithSetsV2',
      async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            StatusCode: 200,
            Result: {
              HistoryExerciseModel: {
                TotalWorkoutCompleted: 20,
                ConsecutiveWeeks: 5,
                TotalWeight: {
                  Lb: 443000, // 443k lbs
                  Kg: 201000, // 201k kg
                },
              },
            },
          }),
        })
      }
    )

    // Navigate to program page
    await page.goto('/program')
    await page.waitForLoadState('networkidle')

    // Wait for stats to load
    await page.waitForSelector('[data-testid="swipeable-stat-card"]')

    // Navigate to weight stat (third stat)
    const indicators = page.locator('[data-testid="stat-indicator"]')
    await indicators.nth(2).click()

    // Should display kg value with correct label
    await expect(page.locator('[data-testid="stat-value"]')).toHaveText(
      '201,000'
    )
    await expect(page.locator('text=Kg Lifted')).toBeVisible()
  })

  test('should display total weight lifted in lbs when user mass unit is lbs', async ({
    page,
  }) => {
    // Mock user info with lbs preference
    await page.route('**/api/Account/GetUserInfoPyramid', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          StatusCode: 200,
          Result: {
            Email: '<EMAIL>',
            FirstName: 'Test',
            LastName: 'User',
            MassUnit: 'lbs',
          },
        }),
      })
    })

    // Mock user stats
    await page.route(
      '**/api/WorkoutLog/GetLogAverageWithSetsV2',
      async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            StatusCode: 200,
            Result: {
              HistoryExerciseModel: {
                TotalWorkoutCompleted: 20,
                ConsecutiveWeeks: 5,
                TotalWeight: {
                  Lb: 443000,
                  Kg: 201000,
                },
              },
            },
          }),
        })
      }
    )

    // Navigate to program page
    await page.goto('/program')
    await page.waitForLoadState('networkidle')

    // Wait for stats to load
    await page.waitForSelector('[data-testid="swipeable-stat-card"]')

    // Navigate to weight stat (third stat)
    const indicators = page.locator('[data-testid="stat-indicator"]')
    await indicators.nth(2).click()

    // Should display lbs value with correct label
    await expect(page.locator('[data-testid="stat-value"]')).toHaveText(
      '443,000'
    )
    await expect(page.locator('text=Lbs Lifted')).toBeVisible()
  })

  test('should default to lbs when user has no mass unit preference', async ({
    page,
  }) => {
    // Mock user info without mass unit preference
    await page.route('**/api/Account/GetUserInfoPyramid', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          StatusCode: 200,
          Result: {
            Email: '<EMAIL>',
            FirstName: 'Test',
            LastName: 'User',
            // No MassUnit field
          },
        }),
      })
    })

    // Mock user stats
    await page.route(
      '**/api/WorkoutLog/GetLogAverageWithSetsV2',
      async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            StatusCode: 200,
            Result: {
              HistoryExerciseModel: {
                TotalWorkoutCompleted: 20,
                ConsecutiveWeeks: 5,
                TotalWeight: {
                  Lb: 443000,
                  Kg: 201000,
                },
              },
            },
          }),
        })
      }
    )

    // Navigate to program page
    await page.goto('/program')
    await page.waitForLoadState('networkidle')

    // Wait for stats to load
    await page.waitForSelector('[data-testid="swipeable-stat-card"]')

    // Navigate to weight stat (third stat)
    const indicators = page.locator('[data-testid="stat-indicator"]')
    await indicators.nth(2).click()

    // Should default to lbs
    await expect(page.locator('[data-testid="stat-value"]')).toHaveText(
      '443,000'
    )
    await expect(page.locator('text=Lbs Lifted')).toBeVisible()
  })

  test('should handle API returning only lbs value gracefully', async ({
    page,
  }) => {
    // Mock user info with kg preference
    await page.route('**/api/Account/GetUserInfoPyramid', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          StatusCode: 200,
          Result: {
            Email: '<EMAIL>',
            FirstName: 'Test',
            LastName: 'User',
            MassUnit: 'kg',
          },
        }),
      })
    })

    // Mock user stats with only Lb value (legacy API response)
    await page.route(
      '**/api/WorkoutLog/GetLogAverageWithSetsV2',
      async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            StatusCode: 200,
            Result: {
              HistoryExerciseModel: {
                TotalWorkoutCompleted: 20,
                ConsecutiveWeeks: 5,
                TotalWeight: {
                  Lb: 443000,
                  // No Kg field
                },
              },
            },
          }),
        })
      }
    )

    // Navigate to program page
    await page.goto('/program')
    await page.waitForLoadState('networkidle')

    // Wait for stats to load
    await page.waitForSelector('[data-testid="swipeable-stat-card"]')

    // Navigate to weight stat (third stat)
    const indicators = page.locator('[data-testid="stat-indicator"]')
    await indicators.nth(2).click()

    // Should convert lbs to kg (443000 lbs = 201031 kg)
    await expect(page.locator('[data-testid="stat-value"]')).toHaveText(
      '201,031'
    )
    await expect(page.locator('text=Kg Lifted')).toBeVisible()
  })
})
