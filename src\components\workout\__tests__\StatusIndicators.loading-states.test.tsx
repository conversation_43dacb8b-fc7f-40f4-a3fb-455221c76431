import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import { StatusIndicators } from '../WorkoutOverviewStates'

describe('StatusIndicators Loading States', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Exercise Count Display', () => {
    it('should show "1 exercise loaded" when first exercise is prefetched', () => {
      render(
        <StatusIndicators
          isOffline={false}
          isRefreshing={false}
          isPrefetching={false}
          prefetchedCount={1}
          errorCount={0}
        />
      )

      expect(screen.getByText('1 exercise loaded')).toBeInTheDocument()
    })

    it('should show "X exercises loaded" (plural) for multiple exercises', () => {
      render(
        <StatusIndicators
          isOffline={false}
          isRefreshing={false}
          isPrefetching={false}
          prefetchedCount={5}
          errorCount={0}
        />
      )

      expect(screen.getByText('5 exercises loaded')).toBeInTheDocument()
    })

    it('should show intermediate progress when still loading', () => {
      render(
        <StatusIndicators
          isOffline={false}
          isRefreshing={false}
          isPrefetching
          prefetchedCount={3}
          errorCount={0}
        />
      )

      expect(
        screen.getByText('Loading exercises... (3 exercises loaded)')
      ).toBeInTheDocument()
    })

    it('should show singular "exercise" for 1 exercise in intermediate state', () => {
      render(
        <StatusIndicators
          isOffline={false}
          isRefreshing={false}
          isPrefetching
          prefetchedCount={1}
          errorCount={0}
        />
      )

      expect(
        screen.getByText('Loading exercises... (1 exercise loaded)')
      ).toBeInTheDocument()
    })

    it('should show percentage progress when provided', () => {
      render(
        <StatusIndicators
          isOffline={false}
          isRefreshing={false}
          isPrefetching
          prefetchedCount={0}
          errorCount={0}
          prefetchProgress={75}
        />
      )

      expect(screen.getByText('Loading exercises... 75%')).toBeInTheDocument()
    })

    it('should prioritize showing exercise count over percentage', () => {
      render(
        <StatusIndicators
          isOffline={false}
          isRefreshing={false}
          isPrefetching
          prefetchedCount={2}
          errorCount={0}
          prefetchProgress={50}
        />
      )

      // Should show count, not percentage when both are available
      expect(
        screen.getByText('Loading exercises... (2 exercises loaded)')
      ).toBeInTheDocument()
      expect(
        screen.queryByText('Loading exercises... 50%')
      ).not.toBeInTheDocument()
    })
  })

  describe('Loading Text Messages', () => {
    it('should show "Loading exercises..." not "Loading stats..."', () => {
      render(
        <StatusIndicators
          isOffline={false}
          isRefreshing={false}
          isPrefetching
          prefetchedCount={0}
          errorCount={0}
        />
      )

      expect(screen.getByText('Loading exercises...')).toBeInTheDocument()
      expect(screen.queryByText('Loading stats...')).not.toBeInTheDocument()
    })

    it('should show "Refreshing workout..." during refresh', () => {
      render(
        <StatusIndicators
          isOffline={false}
          isRefreshing
          isPrefetching={false}
          prefetchedCount={0}
          errorCount={0}
        />
      )

      expect(screen.getByText('Refreshing workout...')).toBeInTheDocument()
    })

    it('should show "Offline Mode" when offline', () => {
      render(
        <StatusIndicators
          isOffline
          isRefreshing={false}
          isPrefetching={false}
          prefetchedCount={0}
          errorCount={0}
        />
      )

      expect(screen.getByText('Offline Mode')).toBeInTheDocument()
    })
  })

  describe('State Persistence', () => {
    it('should maintain exercise count when transitioning from loading to loaded', () => {
      const { rerender } = render(
        <StatusIndicators
          isOffline={false}
          isRefreshing={false}
          isPrefetching
          prefetchedCount={3}
          errorCount={0}
        />
      )

      // Initially loading with 3 exercises
      expect(
        screen.getByText('Loading exercises... (3 exercises loaded)')
      ).toBeInTheDocument()

      // Transition to loaded state
      rerender(
        <StatusIndicators
          isOffline={false}
          isRefreshing={false}
          isPrefetching={false}
          prefetchedCount={3}
          errorCount={0}
        />
      )

      // Should show final count
      expect(screen.getByText('3 exercises loaded')).toBeInTheDocument()
    })

    it('should handle zero exercises loaded state', () => {
      render(
        <StatusIndicators
          isOffline={false}
          isRefreshing={false}
          isPrefetching={false}
          prefetchedCount={0}
          errorCount={0}
        />
      )

      // Should not display anything when no exercises are loaded and not loading
      expect(screen.queryByText(/exercise/)).not.toBeInTheDocument()
    })

    it('should show forceShow state when no prefetch activity detected', () => {
      render(
        <StatusIndicators
          isOffline={false}
          isRefreshing={false}
          isPrefetching={false}
          prefetchedCount={0}
          errorCount={0}
          forceShow
        />
      )

      expect(screen.getByText('Loading exercises...')).toBeInTheDocument()
    })
  })

  describe('Error States', () => {
    it('should show error count along with success count', () => {
      render(
        <StatusIndicators
          isOffline={false}
          isRefreshing={false}
          isPrefetching={false}
          prefetchedCount={3}
          errorCount={2}
        />
      )

      // When both success and error, shows combined message
      expect(
        screen.getByText('3 exercises loaded, 2 failed')
      ).toBeInTheDocument()
    })
  })

  describe('Design Token Compliance', () => {
    it('should use correct CSS classes for theme tokens', () => {
      const { container } = render(
        <StatusIndicators
          isOffline={false}
          isRefreshing={false}
          isPrefetching
          prefetchedCount={1}
          errorCount={0}
        />
      )

      const indicator = container.querySelector('[role="status"]')
      expect(indicator).toHaveClass('bg-bg-secondary/50')
      expect(indicator).toHaveClass('rounded-xl') // Should be rounded-theme per design system
      expect(indicator).toHaveClass('min-h-[52px]') // 52px touch target
    })
  })
})
