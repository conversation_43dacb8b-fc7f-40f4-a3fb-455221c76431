import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { useSettingsData } from '../useSettingsData'

// Mock the userInfoCache service
const mockGetServerUserInfoCached = vi.fn()
vi.mock('@/services/userInfoCache', () => ({
  getServerUserInfoCached: () => mockGetServerUserInfoCached(),
}))

// Mock useAuthStore
const mockGetCachedUserInfo = vi.fn()
vi.mock('@/stores/authStore', () => ({
  useAuthStore: () => ({
    getCachedUserInfo: mockGetCachedUserInfo,
  }),
}))

describe('useSettingsData', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('fetches user settings from cache', async () => {
    const mockUserData = {
      Email: '<EMAIL>',
      MassUnit: 'kg',
      IsNormalSet: true,
      RepsMinimum: 6,
      RepsMaximum: 12,
      WeightIncrement: 2.5,
      WarmupSets: 3,
      IsQuickMode: false,
      IsDropSet: false,
      IsPyramid: false,
      IsRestPause: false,
      IsReversePyramid: false,
    }

    mockGetServerUserInfoCached.mockResolvedValue(mockUserData)
    mockGetCachedUserInfo.mockReturnValue(mockUserData)

    const { result } = renderHook(() => useSettingsData())

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })

    expect(result.current.data).toBeDefined()
    expect(result.current.data?.email).toBe('<EMAIL>')
    expect(result.current.data?.weightUnit).toBe('kg')
    expect(result.current.data?.setStyle).toBe('Normal')
  })

  it('maps UserInfoModel to UI format correctly', async () => {
    const mockUserData = {
      Email: '<EMAIL>',
      MassUnit: 'lbs',
      IsNormalSet: false,
      IsRestPause: true,
      RepsMinimum: 8,
      RepsMaximum: 15,
      WeightIncrement: 5,
      WarmupSets: 2,
      IsQuickMode: true,
      IsDropSet: false,
      IsPyramid: false,
      IsReversePyramid: false,
    }

    mockGetServerUserInfoCached.mockResolvedValue(mockUserData)
    mockGetCachedUserInfo.mockReturnValue(mockUserData)

    const { result } = renderHook(() => useSettingsData())

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })

    const { data } = result.current
    expect(data).toEqual({
      email: '<EMAIL>',
      weightUnit: 'lbs',
      setStyle: 'Rest-Pause',
      quickMode: true,
      repRange: { min: 8, max: 15 },
      weightIncrement: 5,
      warmupSets: 2,
    })
  })

  it('handles different set styles correctly', async () => {
    const testCases = [
      {
        IsNormalSet: true,
        IsDropSet: false,
        IsPyramid: false,
        IsRestPause: false,
        IsReversePyramid: false,
        expected: 'Normal',
      },
      {
        IsNormalSet: false,
        IsDropSet: true,
        IsPyramid: false,
        IsRestPause: false,
        IsReversePyramid: false,
        expected: 'Drop',
      },
      {
        IsNormalSet: false,
        IsDropSet: false,
        IsPyramid: true,
        IsRestPause: false,
        IsReversePyramid: false,
        expected: 'Pyramid',
      },
      {
        IsNormalSet: false,
        IsDropSet: false,
        IsPyramid: false,
        IsRestPause: true,
        IsReversePyramid: false,
        expected: 'Rest-Pause',
      },
      {
        IsNormalSet: false,
        IsDropSet: false,
        IsPyramid: false,
        IsRestPause: false,
        IsReversePyramid: true,
        expected: 'Reverse Pyramid',
      },
    ]

    // eslint-disable-next-line no-restricted-syntax
    for (const testCase of testCases) {
      const mockUserData = {
        Email: '<EMAIL>',
        MassUnit: 'kg',
        ...testCase,
        RepsMinimum: 6,
        RepsMaximum: 12,
        WeightIncrement: 2.5,
        WarmupSets: 3,
        IsQuickMode: false,
      }

      mockGetServerUserInfoCached.mockResolvedValue(mockUserData)
      mockGetCachedUserInfo.mockReturnValue(mockUserData)

      const { result } = renderHook(() => useSettingsData())

      // eslint-disable-next-line no-await-in-loop
      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })

      expect(result.current.data?.setStyle).toBe(testCase.expected)
    }
  })

  it('handles loading state', () => {
    mockGetServerUserInfoCached.mockReturnValue(new Promise(() => {})) // Never resolves

    const { result } = renderHook(() => useSettingsData())

    expect(result.current.isLoading).toBe(true)
    expect(result.current.data).toBeUndefined()
    expect(result.current.error).toBeNull()
  })

  it('handles errors gracefully', async () => {
    const error = new Error('Failed to fetch user info')
    mockGetServerUserInfoCached.mockRejectedValue(error)

    const { result } = renderHook(() => useSettingsData())

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })

    expect(result.current.error).toBeDefined()
    expect(result.current.data).toBeUndefined()
  })

  it('handles null/undefined gracefully', async () => {
    mockGetServerUserInfoCached.mockResolvedValue(null)
    mockGetCachedUserInfo.mockReturnValue(null)

    const { result } = renderHook(() => useSettingsData())

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })

    // Should return default values
    expect(result.current.data).toEqual({
      email: '',
      weightUnit: 'lbs',
      setStyle: 'Normal',
      quickMode: false,
      repRange: { min: 6, max: 12 },
      weightIncrement: 5,
      warmupSets: 0,
    })
  })

  it('handles partial data with defaults', async () => {
    const mockUserData = {
      Email: '<EMAIL>',
      MassUnit: 'kg',
      // Missing other fields
    }

    mockGetServerUserInfoCached.mockResolvedValue(mockUserData)
    mockGetCachedUserInfo.mockReturnValue(mockUserData)

    const { result } = renderHook(() => useSettingsData())

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })

    const { data } = result.current
    expect(data?.email).toBe('<EMAIL>')
    expect(data?.weightUnit).toBe('kg')
    expect(data?.setStyle).toBe('Normal') // Default
    expect(data?.quickMode).toBe(false) // Default
    expect(data?.repRange).toEqual({ min: 6, max: 12 }) // Default
    expect(data?.warmupSets).toBe(0) // Default
  })
})
