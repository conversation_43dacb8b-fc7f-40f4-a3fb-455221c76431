import { test, expect } from '@playwright/test'

test.describe('Critical User Flows', () => {
  test.beforeEach(async ({ page }) => {
    // Set up basic API mocking for all tests
    await page.route('**/api/Account/GetUserInfo*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Email: '<EMAIL>',
          FirstName: 'Test',
          LastName: 'User',
          MassUnit: 'lbs',
        }),
      })
    })

    await page.route('**/api/Workout/GetUserProgramInfo*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          GetUserProgramInfoResponseModel: {
            RecommendedProgram: {
              Id: 1,
              Label: 'Test Program',
              RemainingToLevelUp: 3,
            },
            NextWorkoutTemplate: {
              Id: 101,
              Label: 'Workout A',
              IsSystemExercise: false,
            },
          },
          TotalWorkoutCompleted: 10,
          ConsecutiveWeeks: 3,
        }),
      })
    })
  })

  test('should load login page and show login form', async ({ page }) => {
    await page.goto('/login')
    await page.waitForLoadState('networkidle')

    // Check that we're on the login page
    expect(page.url()).toContain('/login')

    // Check for basic page structure
    const body = await page.locator('body')
    await expect(body).toBeVisible()

    // Check that page has login-related content
    const bodyText = await body.textContent()
    expect(bodyText).toBeTruthy()
    expect(bodyText!.toLowerCase()).toMatch(/login|sign|email|password/)
  })

  test('should handle navigation between pages', async ({ page }) => {
    // Start at home page
    await page.goto('/')
    await page.waitForLoadState('networkidle')

    // Navigate to login
    await page.goto('/login')
    await page.waitForLoadState('networkidle')
    expect(page.url()).toContain('/login')

    // Navigate back to home
    await page.goto('/')
    await page.waitForLoadState('networkidle')
    expect(page.url()).not.toContain('/login')
  })

  test('should handle workout page with mocked data', async ({ page }) => {
    // Mock workout data
    await page.route(
      '**/api/Workout/GetUserWorkoutTemplateGroup*',
      async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([
            {
              Id: 101,
              Label: 'Test Workout A',
              IsSystemExercise: false,
              WorkoutTemplateExercises: [
                {
                  Id: 1001,
                  ExerciseId: 1,
                  ExerciseName: 'Bench Press',
                  Sets: 3,
                  Reps: 10,
                  RestTime: 120,
                  IsWarmupExercise: false,
                },
              ],
            },
          ]),
        })
      }
    )

    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Check that workout page loads
    const body = await page.locator('body')
    await expect(body).toBeVisible()

    // Check for workout-related content
    const bodyText = await body.textContent()
    expect(bodyText).toBeTruthy()
  })

  test('should handle API errors gracefully', async ({ page }) => {
    // Mock API error
    await page.route('**/api/Workout/GetUserProgramInfo*', async (route) => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal server error' }),
      })
    })

    await page.goto('/')
    await page.waitForLoadState('networkidle')

    // Page should still load even with API errors
    const body = await page.locator('body')
    await expect(body).toBeVisible()

    // Check that page doesn't crash
    const bodyText = await body.textContent()
    expect(bodyText).toBeTruthy()
    expect(bodyText!.length).toBeGreaterThan(10)
  })

  test('should work on mobile viewport', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })

    await page.goto('/')
    await page.waitForLoadState('networkidle')

    // Check that page loads on mobile
    const body = await page.locator('body')
    await expect(body).toBeVisible()

    // Check that content is accessible
    const bodyText = await body.textContent()
    expect(bodyText).toBeTruthy()
    expect(bodyText!.length).toBeGreaterThan(10)
  })
})
