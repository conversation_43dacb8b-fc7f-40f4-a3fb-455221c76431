import { test, expect } from '@playwright/test'

test.describe('Exercise Sets Loading State', () => {
  test.beforeEach(async ({ page }) => {
    // Set up authentication before navigation
    await page.addInitScript(() => {
      window.localStorage.setItem(
        'dr-muscle-auth',
        JSON.stringify({
          user: {
            Email: '<EMAIL>',
            Token: 'test-token',
            Id: 'test-user-id',
            MassUnit: 'lb',
          },
          token: 'test-token',
        })
      )

      // Also set workout session
      window.localStorage.setItem(
        'dr-muscle-workout',
        JSON.stringify({
          exercises: [
            {
              Id: 1,
              Label: 'Bench Press',
              IsBodyweight: false,
              IsTimeBased: false,
            },
          ],
          currentExerciseIndex: 0,
          currentSetIndex: 0,
          workoutSession: {
            exercises: [],
          },
        })
      )
    })
  })

  test('should show loading skeleton instead of "No sets" message when loading @critical', async ({
    page,
  }) => {
    // Set up API mock to delay recommendation response
    await page.route('**/api/GetRecommendation*', async (route) => {
      // Delay for 1 second to simulate loading
      await new Promise((resolve) => setTimeout(resolve, 1000))
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Reps: 10,
          Weight: { Lb: 135, Kg: 61.2 },
          Series: 3,
          WarmupsCount: 2,
        }),
      })
    })

    // Navigate directly to exercise page
    await page.goto('/workout/exercise/1')

    // Immediately check for loading skeleton while API is delayed
    const loadingSkeleton = page.locator(
      '[data-testid="sets-loading-skeleton"]'
    )
    const noSetsMessage = page.locator('text="No sets for this exercise"')

    // Should show skeleton, not "No sets" message
    await expect(loadingSkeleton).toBeVisible({ timeout: 5000 })
    await expect(noSetsMessage).not.toBeVisible()

    // Wait for recommendation to load
    await page.waitForFunction(
      () => {
        const skeleton = document.querySelector(
          '[data-testid="sets-loading-skeleton"]'
        )
        return !skeleton
      },
      { timeout: 10000 }
    )

    // Verify sets are now shown
    await expect(
      page.locator('[data-testid="exercise-sets-grid"]')
    ).toBeVisible()
  })

  test('should show "No sets" message when not loading and no recommendation', async ({
    page,
  }) => {
    // Mock recommendation to return null immediately
    await page.route('**/api/GetRecommendation*', (route) =>
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: 'null',
      })
    )

    // Navigate directly to exercise page
    await page.goto('/workout/exercise/1')

    // Wait for page to load
    await page.waitForLoadState('networkidle')

    // Verify "No sets for this exercise" IS shown when not loading
    const noSetsMessage = page.locator('text="No sets for this exercise"')
    await expect(noSetsMessage).toBeVisible({ timeout: 5000 })

    // Verify loading skeleton is NOT shown
    const loadingSkeleton = page.locator(
      '[data-testid="sets-loading-skeleton"]'
    )
    await expect(loadingSkeleton).not.toBeVisible()
  })
})
