import { render } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import ExerciseV2Loading from '../loading'

describe('ExerciseV2Loading', () => {
  it('should render skeleton loading elements', () => {
    const { container } = render(<ExerciseV2Loading />)

    // Check for multiple skeleton elements
    const skeletons = container.querySelectorAll('.animate-pulse')
    expect(skeletons.length).toBeGreaterThan(5)
  })

  it('should have theme-aware background color', () => {
    const { container } = render(<ExerciseV2Loading />)

    // Check for main container with theme background
    const mainContainer = container.querySelector('.bg-bg-primary')
    expect(mainContainer).toBeInTheDocument()
  })

  it('should render header skeleton', () => {
    const { container } = render(<ExerciseV2Loading />)

    // Check for header skeleton elements
    const headerSection = container.querySelector('.bg-bg-secondary.border-b')
    expect(headerSection).toBeInTheDocument()
  })

  it('should render current set card skeleton', () => {
    const { container } = render(<ExerciseV2Loading />)

    // Check for card with rounded corners
    const card = container.querySelector('.bg-bg-secondary.rounded-xl')
    expect(card).toBeInTheDocument()
  })

  it('should render bottom action button skeleton', () => {
    const { container } = render(<ExerciseV2Loading />)

    // Check for fixed bottom element
    const bottomSection = container.querySelector('.fixed.bottom-0')
    expect(bottomSection).toBeInTheDocument()
  })
})
