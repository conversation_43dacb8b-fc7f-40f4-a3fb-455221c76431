/**
 * Cross-Browser E2E Tests for Comprehensive Sets Implementation
 * Tests actual browser behavior with real user scenarios
 */

import { test, expect, devices, type BrowserContext } from '@playwright/test'

// Test data for potential future use
// const testUser = {
//   email: '<EMAIL>',
//   password: 'Dr123456',
// }

// Utility function for future use
// async function loginUser(page: Page) {
//   await page.goto('/')
//   await page.getByTestId('login-email').fill(testUser.email)
//   await page.getByTestId('login-password').fill(testUser.password)
//   await page.getByTestId('login-button').click()
//
//   // Wait for successful login
//   await expect(page.getByTestId('dashboard')).toBeVisible({ timeout: 10000 })
// }

async function simulateMemoryPressure(context: BrowserContext) {
  // Add memory pressure simulation for mobile browsers
  await context.addInitScript(() => {
    // Simulate low memory by limiting cache sizes
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      Object.defineProperty(navigator.storage, 'estimate', {
        value: () =>
          Promise.resolve({
            quota: 50 * 1024 * 1024, // 50MB
            usage: 45 * 1024 * 1024, // 45MB used (90%)
          }),
      })
    }
  })
}

// Utility function for future use
// async function simulateSlowNetwork(context: BrowserContext) {
//   // Simulate slow 3G network
//   const page = context.pages()[0] || (await context.newPage())
//   const client = await page.context().newCDPSession(page)
//   await client.send('Network.emulateNetworkConditions', {
//     offline: false,
//     downloadThroughput: (1.6 * 1024 * 1024) / 8, // 1.6 Mbps
//     uploadThroughput: (750 * 1024) / 8, // 750 Kbps
//     latency: 150,
//   })
// }

// Chrome Mobile Android Tests
test.describe('Cross-Browser: Chrome Mobile Android', () => {
  test.use({
    ...devices['Pixel 5'],
    permissions: ['geolocation'],
  })

  test('should handle localStorage operations without errors', async ({
    page,
    context,
  }) => {
    await simulateMemoryPressure(context)

    // Monitor console for localStorage errors
    const consoleErrors: string[] = []
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text())
      }
    })

    await page.goto('/')

    // Test localStorage compatibility by triggering user settings
    await page.evaluate(() => {
      try {
        localStorage.setItem('test-key', 'test-value')
        const value = localStorage.getItem('test-key')
        localStorage.removeItem('test-key')
        return value === 'test-value'
      } catch (error) {
        throw new Error(`localStorage test failed: ${error.message}`)
      }
    })

    // Check that no localStorage errors occurred
    const localStorageErrors = consoleErrors.filter(
      (error) =>
        error.includes('localStorage') ||
        error.includes('QuotaExceededError') ||
        error.includes('storage')
    )

    expect(localStorageErrors).toHaveLength(0)
  })

  test('should handle memory cache pressure gracefully', async ({
    page,
    context,
  }) => {
    await simulateMemoryPressure(context)

    await page.goto('/')

    // Simulate memory-intensive operations
    const memoryTestResult = await page.evaluate(async () => {
      // Create a large array to simulate memory pressure
      const largeArray = new Array(1000).fill(0).map((_, i) => ({
        id: i,
        data: new Array(100).fill('memory-test-data'),
        timestamp: Date.now(),
      }))

      // Test that the app still functions under memory pressure
      try {
        // Simulate cache operations
        const cache = new Map()
        for (let i = 0; i < 100; i++) {
          cache.set(`key-${i}`, largeArray[i])
        }

        // Clear some cache to simulate memory management
        for (let i = 0; i < 50; i++) {
          cache.delete(`key-${i}`)
        }

        return {
          success: true,
          finalSize: cache.size,
          memoryUsed: largeArray.length,
        }
      } catch (error) {
        return {
          success: false,
          error: error.message,
        }
      }
    })

    expect(memoryTestResult.success).toBe(true)
    expect(memoryTestResult.finalSize).toBe(50)
  })

  test('should handle viewport changes during keyboard interactions', async ({
    page,
  }) => {
    await page.goto('/')

    // Get initial viewport
    const initialViewport = await page.evaluate(() => ({
      width: window.innerWidth,
      height: window.innerHeight,
      devicePixelRatio: window.devicePixelRatio,
    }))

    // Simulate input focus that might trigger virtual keyboard
    await page.evaluate(() => {
      const input = document.createElement('input')
      input.type = 'text'
      input.style.position = 'fixed'
      input.style.bottom = '10px'
      input.setAttribute('data-testid', 'test-input')
      document.body.appendChild(input)
      input.focus()
    })

    await page.waitForTimeout(1000) // Wait for potential viewport change

    // Check that app handles viewport change gracefully
    const viewportAfterFocus = await page.evaluate(() => ({
      width: window.innerWidth,
      height: window.innerHeight,
      hasInput: !!document.querySelector('[data-testid="test-input"]'),
    }))

    expect(viewportAfterFocus.hasInput).toBe(true)
    expect(viewportAfterFocus.width).toBe(initialViewport.width)
    // Height might change on mobile with virtual keyboard, but app should still work
  })

  test('should handle network interruptions gracefully', async ({
    page,
    context,
  }) => {
    await page.goto('/')

    // Test with normal network first (commented out for simplicity)
    // const initialNetworkTest = await page.evaluate(() => {
    //   return fetch('/api/test', { method: 'HEAD' })
    //     .then(() => ({ connected: true }))
    //     .catch(() => ({ connected: false }))
    // })

    // Simulate network going offline
    await context.setOffline(true)

    // Test offline behavior
    const offlineTest = await page.evaluate(() => {
      return fetch('/api/test', { method: 'HEAD' })
        .then(() => ({ offline: false }))
        .catch(() => ({ offline: true }))
    })

    expect(offlineTest.offline).toBe(true)

    // Restore network
    await context.setOffline(false)

    // Verify network is restored
    const restoredNetworkTest = await page.evaluate(() => {
      return fetch('/api/test', { method: 'HEAD' })
        .then(() => ({ restored: true }))
        .catch(() => ({ restored: false }))
    })

    expect(restoredNetworkTest.restored).toBe(true)
  })
})

// Safari Mobile iOS Tests
test.describe('Cross-Browser: Safari Mobile iOS', () => {
  test.use({
    ...devices['iPhone 13'],
    permissions: ['geolocation'],
  })

  test('should handle localStorage in private browsing simulation', async ({
    page,
    context,
  }) => {
    // Simulate Safari private browsing localStorage restrictions
    await context.addInitScript(() => {
      const originalSetItem = localStorage.setItem
      const originalGetItem = localStorage.getItem

      // Simulate intermittent localStorage failures like Safari private mode
      localStorage.setItem = function (key: string, value: string) {
        if (Math.random() > 0.7) {
          // 30% failure rate
          throw new DOMException(
            'QuotaExceededError',
            'The quota has been exceeded.'
          )
        }
        return originalSetItem.call(this, key, value)
      }

      localStorage.getItem = function (key: string) {
        if (Math.random() > 0.9) {
          // 10% failure rate
          throw new DOMException('SecurityError', 'Access denied.')
        }
        return originalGetItem.call(this, key)
      }
    })

    const consoleErrors: string[] = []
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text())
      }
    })

    await page.goto('/')

    // Test that the app gracefully handles localStorage failures
    const localStorageTest = await page.evaluate(() => {
      const results = {
        setSuccess: 0,
        setFailures: 0,
        getSuccess: 0,
        getFailures: 0,
      }

      // Test multiple localStorage operations
      for (let i = 0; i < 10; i++) {
        try {
          localStorage.setItem(`test-${i}`, `value-${i}`)
          results.setSuccess++
        } catch (error) {
          results.setFailures++
        }

        try {
          localStorage.getItem(`test-${i}`)
          results.getSuccess++
        } catch (error) {
          results.getFailures++
        }
      }

      return results
    })

    // App should handle both successes and failures gracefully
    expect(localStorageTest.setSuccess + localStorageTest.setFailures).toBe(10)
    expect(localStorageTest.getSuccess + localStorageTest.getFailures).toBe(10)

    // Check that localStorage failures don't cause app crashes
    const criticalErrors = consoleErrors.filter(
      (error) =>
        error.includes('localStorage') &&
        !error.includes('QuotaExceededError') &&
        !error.includes('SecurityError')
    )

    expect(criticalErrors).toHaveLength(0)
  })

  test('should handle iOS viewport and safe area properly', async ({
    page,
  }) => {
    await page.goto('/')

    // Test safe area detection
    const safeAreaTest = await page.evaluate(() => {
      const testElement = document.createElement('div')
      testElement.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
      `
      document.body.appendChild(testElement)

      const computed = getComputedStyle(testElement)
      const { paddingTop } = computed
      const { paddingBottom } = computed

      document.body.removeChild(testElement)

      return {
        paddingTop,
        paddingBottom,
        hasNotch: parseInt(paddingTop) > 20 || parseInt(paddingBottom) > 20,
      }
    })

    // On iPhone 13, we expect some safe area insets
    expect(parseInt(safeAreaTest.paddingTop) >= 0).toBe(true)
    expect(parseInt(safeAreaTest.paddingBottom) >= 0).toBe(true)
  })

  test('should handle iOS date parsing restrictions', async ({ page }) => {
    await page.goto('/')

    // Test various date parsing scenarios that might fail in Safari
    const dateTest = await page.evaluate(() => {
      const testDates = [
        '2024-01-15T10:30:00Z', // ISO format (should work)
        '2024-01-15T10:30:00.000Z', // ISO with milliseconds
        '2024/01/15 10:30:00', // US format (might fail in Safari)
        '15/01/2024 10:30:00', // European format (might fail in Safari)
        'January 15, 2024 10:30:00', // Text format (might fail in Safari)
      ]

      const results = testDates.map((dateStr) => {
        try {
          const date = new Date(dateStr)
          return {
            input: dateStr,
            valid: !Number.isNaN(date.getTime()),
            timestamp: date.getTime(),
          }
        } catch (error) {
          return {
            input: dateStr,
            valid: false,
            error: error.message,
          }
        }
      })

      return results
    })

    // At least ISO format should work
    const isoResults = dateTest.filter(
      (result) => result.input.includes('T') && result.input.includes('Z')
    )
    expect(isoResults.every((result) => result.valid)).toBe(true)
  })
})

// Firefox Mobile Tests
test.describe('Cross-Browser: Firefox Mobile', () => {
  test.use({
    userAgent: 'Mozilla/5.0 (Mobile; rv:100.0) Gecko/100.0 Firefox/100.0',
    viewport: { width: 375, height: 667 },
    hasTouch: true,
    isMobile: true,
  })

  test('should handle service worker lifecycle properly', async ({ page }) => {
    // Monitor service worker events
    const swEvents: string[] = []
    page.on('console', (msg) => {
      if (msg.text().includes('service worker') || msg.text().includes('SW')) {
        swEvents.push(msg.text())
      }
    })

    await page.goto('/')

    // Test service worker registration
    const swTest = await page.evaluate(async () => {
      if ('serviceWorker' in navigator) {
        try {
          // Test service worker availability
          const registration = await navigator.serviceWorker.getRegistration()
          return {
            supported: true,
            hasRegistration: !!registration,
            hasController: !!navigator.serviceWorker.controller,
          }
        } catch (error) {
          return {
            supported: true,
            error: error.message,
          }
        }
      } else {
        return {
          supported: false,
        }
      }
    })

    expect(swTest.supported).toBe(true)
  })

  test('should handle Firefox-specific promise behavior', async ({ page }) => {
    await page.goto('/')

    // Test Promise.race behavior which can differ in Firefox
    const promiseTest = await page.evaluate(async () => {
      const timeoutPromise = new Promise((resolve) =>
        setTimeout(() => resolve('timeout'), 100)
      )
      const immediatePromise = Promise.resolve('immediate')

      try {
        const result = await Promise.race([immediatePromise, timeoutPromise])
        return {
          success: true,
          result,
          timing: 'immediate',
        }
      } catch (error) {
        return {
          success: false,
          error: error.message,
        }
      }
    })

    expect(promiseTest.success).toBe(true)
    expect(promiseTest.result).toBe('immediate')
  })
})

// Cross-Browser Memory and Performance Tests
test.describe('Cross-Browser: Memory and Performance', () => {
  test('should handle cache limits across browsers @memory', async ({
    page,
    context,
  }) => {
    await simulateMemoryPressure(context)

    await page.goto('/')

    // Test in-memory cache behavior under pressure
    const cacheTest = await page.evaluate(() => {
      const cache = new Map()
      const testData = []

      try {
        // Create test data
        for (let i = 0; i < 1000; i++) {
          const data = {
            id: i,
            timestamp: Date.now(),
            data: new Array(100).fill(`test-data-${i}`),
          }
          testData.push(data)
          cache.set(i, data)
        }

        // Simulate cache cleanup under memory pressure
        if (cache.size > 500) {
          const keysToDelete = Array.from(cache.keys()).slice(0, 250)
          keysToDelete.forEach((key) => cache.delete(key))
        }

        return {
          success: true,
          initialSize: testData.length,
          finalCacheSize: cache.size,
          memoryManaged: cache.size < testData.length,
        }
      } catch (error) {
        return {
          success: false,
          error: error.message,
        }
      }
    })

    expect(cacheTest.success).toBe(true)
    expect(cacheTest.memoryManaged).toBe(true)
  })

  test('should handle concurrent operations across browsers', async ({
    page,
  }) => {
    await page.goto('/')

    // Test concurrent promise handling
    const concurrencyTest = await page.evaluate(async () => {
      const promises = []

      // Create multiple concurrent operations
      for (let i = 0; i < 10; i++) {
        promises.push(
          new Promise((resolve) => {
            setTimeout(() => resolve(`result-${i}`), Math.random() * 100)
          })
        )
      }

      try {
        const results = await Promise.allSettled(promises)
        const successful = results.filter((r) => r.status === 'fulfilled')
        const failed = results.filter((r) => r.status === 'rejected')

        return {
          success: true,
          total: results.length,
          successful: successful.length,
          failed: failed.length,
        }
      } catch (error) {
        return {
          success: false,
          error: error.message,
        }
      }
    })

    expect(concurrencyTest.success).toBe(true)
    expect(concurrencyTest.successful).toBe(10)
    expect(concurrencyTest.failed).toBe(0)
  })
})
