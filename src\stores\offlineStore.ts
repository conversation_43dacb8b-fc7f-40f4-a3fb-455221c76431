import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import { createDefaultCacheManager } from '@/cache'
import type { CacheManager } from '@/cache/CacheManager'

/**
 * Sync status for offline operations
 */
export type SyncStatus = 'idle' | 'syncing' | 'success' | 'error'

/**
 * Offline store state interface
 */
export interface OfflineState {
  // Workout loading state
  isOfflineWorkoutLoaded: boolean
  loadedWorkoutId: string | null
  isLoadingOfflineWorkout: boolean

  // Sync state
  syncStatus: SyncStatus
  lastSyncAttempt: number | null
  syncError: string | null

  // Queue state
  queuedRequestsCount: number

  // Cache manager instance (not persisted)
  cacheManager: CacheManager
}

/**
 * Offline store actions interface
 */
export interface OfflineActions {
  // Workout loading actions
  setLoadingOfflineWorkout: (loading: boolean) => void
  setOfflineWorkoutLoaded: (workoutId: string) => void
  clearOfflineWorkout: () => void

  // Sync actions
  setSyncStatus: (status: SyncStatus) => void
  setSyncError: (error: string | null) => void
  recordSyncAttempt: () => void

  // Queue actions
  setQueuedRequestsCount: (count: number) => void

  // Cache actions
  cacheWorkoutData: (workoutId: string, data: unknown) => Promise<void>
  getCachedWorkoutData: (workoutId: string) => Promise<unknown>
  clearCachedWorkoutData: (workoutId: string) => Promise<void>
  cacheExerciseRecommendations: (
    exerciseId: string,
    data: unknown
  ) => Promise<void>
  getCachedExerciseRecommendations: (exerciseId: string) => Promise<unknown>
  clearAllCachedData: () => Promise<void>
}

/**
 * Combined offline store type
 */
export type OfflineStore = OfflineState & OfflineActions

/**
 * Initial state for the offline store
 */
const initialState: Omit<OfflineState, 'cacheManager'> = {
  isOfflineWorkoutLoaded: false,
  loadedWorkoutId: null,
  isLoadingOfflineWorkout: false,
  syncStatus: 'idle',
  lastSyncAttempt: null,
  syncError: null,
  queuedRequestsCount: 0,
}

/**
 * Cache configuration constants
 */
const CACHE_CONFIG = {
  WORKOUT_TTL: 24 * 60 * 60 * 1000, // 24 hours
  RECOMMENDATIONS_TTL: 2 * 60 * 60 * 1000, // 2 hours
  WORKOUT_NAMESPACE: 'offline-workouts',
  RECOMMENDATIONS_NAMESPACE: 'offline-recommendations',
} as const

/**
 * Create cache manager instance
 */
let cacheManagerInstance: CacheManager | null = null

const getCacheManager = (): CacheManager => {
  if (!cacheManagerInstance) {
    cacheManagerInstance = createDefaultCacheManager()
  }
  return cacheManagerInstance
}

/**
 * Offline store with Zustand persistence and CacheManager integration
 */
export const useOfflineStore = create<OfflineStore>()(
  persist(
    immer((set, get) => {
      return {
        ...initialState,
        // Cache manager getter (not persisted)
        get cacheManager() {
          return getCacheManager()
        },

        // Workout loading actions
        setLoadingOfflineWorkout: (loading: boolean) =>
          set((state) => {
            state.isLoadingOfflineWorkout = loading
          }),

        setOfflineWorkoutLoaded: (workoutId: string) =>
          set((state) => {
            state.isOfflineWorkoutLoaded = true
            state.loadedWorkoutId = workoutId
            state.isLoadingOfflineWorkout = false
          }),

        clearOfflineWorkout: () =>
          set((state) => {
            state.isOfflineWorkoutLoaded = false
            state.loadedWorkoutId = null
            state.isLoadingOfflineWorkout = false
          }),

        // Sync actions
        setSyncStatus: (status: SyncStatus) =>
          set((state) => {
            state.syncStatus = status
            if (status === 'success') {
              state.syncError = null
            }
          }),

        setSyncError: (error: string | null) =>
          set((state) => {
            state.syncError = error
            if (error) {
              state.syncStatus = 'error'
            }
          }),

        recordSyncAttempt: () =>
          set((state) => {
            state.lastSyncAttempt = Date.now()
          }),

        // Queue actions
        setQueuedRequestsCount: (count: number) =>
          set((state) => {
            state.queuedRequestsCount = count
          }),

        // Cache actions
        cacheWorkoutData: async (workoutId: string, data: unknown) => {
          const { cacheManager } = get()
          await cacheManager.set(workoutId, data, {
            ttl: CACHE_CONFIG.WORKOUT_TTL,
            namespace: CACHE_CONFIG.WORKOUT_NAMESPACE,
          })
        },

        getCachedWorkoutData: async (workoutId: string) => {
          const { cacheManager } = get()
          return await cacheManager.get(
            workoutId,
            CACHE_CONFIG.WORKOUT_NAMESPACE
          )
        },

        clearCachedWorkoutData: async (workoutId: string) => {
          const { cacheManager } = get()
          await cacheManager.delete(workoutId, CACHE_CONFIG.WORKOUT_NAMESPACE)
        },

        cacheExerciseRecommendations: async (
          exerciseId: string,
          data: unknown
        ) => {
          const { cacheManager } = get()
          await cacheManager.set(exerciseId, data, {
            ttl: CACHE_CONFIG.RECOMMENDATIONS_TTL,
            namespace: CACHE_CONFIG.RECOMMENDATIONS_NAMESPACE,
          })
        },

        getCachedExerciseRecommendations: async (exerciseId: string) => {
          const { cacheManager } = get()
          return await cacheManager.get(
            exerciseId,
            CACHE_CONFIG.RECOMMENDATIONS_NAMESPACE
          )
        },

        clearAllCachedData: async () => {
          const { cacheManager } = get()
          await Promise.all([
            cacheManager.clear(CACHE_CONFIG.WORKOUT_NAMESPACE),
            cacheManager.clear(CACHE_CONFIG.RECOMMENDATIONS_NAMESPACE),
          ])
        },
      }
    }),
    {
      name: 'offline-store',
      storage: createJSONStorage(() => localStorage),
      // Only persist the state, not the cache manager
      partialize: (state) => ({
        isOfflineWorkoutLoaded: state.isOfflineWorkoutLoaded,
        loadedWorkoutId: state.loadedWorkoutId,
        isLoadingOfflineWorkout: state.isLoadingOfflineWorkout,
        syncStatus: state.syncStatus,
        lastSyncAttempt: state.lastSyncAttempt,
        syncError: state.syncError,
        queuedRequestsCount: state.queuedRequestsCount,
      }),
      // No need to restore cache manager as it's a getter
    }
  )
)

/**
 * Selectors for common offline store queries
 */
export const offlineSelectors = {
  isWorkoutCached: (state: OfflineStore) => state.isOfflineWorkoutLoaded,
  isSyncing: (state: OfflineStore) => state.syncStatus === 'syncing',
  hasQueuedRequests: (state: OfflineStore) => state.queuedRequestsCount > 0,
  canLoadOfflineWorkout: (state: OfflineStore) =>
    !state.isLoadingOfflineWorkout && !state.isOfflineWorkoutLoaded,
  needsSync: (state: OfflineStore) =>
    state.queuedRequestsCount > 0 && state.syncStatus !== 'syncing',
}
