import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { updateUserSettings } from '../updateUserSettings'
import type { LocalSettings } from '../../types/settings'

// Import after mocking
import { apiClient } from '@/api/client'

// Mock API client - define inline in factory to avoid hoisting issues
vi.mock('@/api/client', () => ({
  apiClient: {
    patch: vi.fn(),
    defaults: {
      headers: {
        common: {} as Record<string, string>,
      },
    },
  },
}))

// Mock auth store
const mockAuthStore = {
  getAccessToken: vi.fn(),
  getCachedUserInfo: vi.fn(),
  updateUserInfo: vi.fn(),
}

vi.mock('@/stores/authStore', () => ({
  useAuthStore: () => mockAuthStore,
}))

describe('updateUserSettings', () => {
  const mockToken = 'mock-jwt-token'
  const mockLocalSettings: LocalSettings = {
    quickMode: true,
    weightUnit: 'kg',
    setStyle: 'Rest-Pause',
    repsMin: 8,
    repsMax: 12,
    weightIncrement: 2.5,
    warmupSets: 2,
  }

  beforeEach(() => {
    vi.clearAllMocks()
    // Set up auth token in API client headers
    ;(apiClient.defaults.headers.common as any)['Authorization'] =
      `Bearer ${mockToken}`
    mockAuthStore.getCachedUserInfo.mockReturnValue({
      Email: '<EMAIL>',
      MassUnit: 'lbs',
      IsQuickMode: false,
    })
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  /**
   * Test rationale: Core functionality test - verify the service can successfully
   * call the API with proper payload structure and authentication headers.
   * This is the primary happy path that must work for the feature to be functional.
   */
  it('should successfully update user settings with correct API payload', async () => {
    // Arrange: Mock successful API response
    const mockApiResponse = {
      status: 200,
      data: { message: 'Settings updated successfully', updated: true },
    }
    ;(apiClient.patch as any).mockResolvedValue(mockApiResponse)

    // Act: Call the service function
    const result = await updateUserSettings(mockLocalSettings)

    // Assert: Verify API call structure and success
    expect(apiClient.patch).toHaveBeenCalledWith(
      '/api/User/UpdateSettings', // Should call correct endpoint
      {
        // Expected payload structure based on Dr. Muscle API patterns
        IsQuickMode: true,
        MassUnit: 'kg',
        IsNormalSet: false, // Rest-Pause maps to IsNormalSet: false
        RepsMinimum: 8,
        RepsMaximum: 12,
        WeightIncrement: 2.5,
        WarmupSets: 2,
      },
      {
        headers: {
          Authorization: `Bearer ${mockToken}`,
          'Content-Type': 'application/json',
        },
      }
    )
    expect(result.success).toBe(true)
    expect(result.data).toEqual(mockApiResponse.data)
  })

  /**
   * Test rationale: Authentication failure handling is critical for security
   * and user experience. 401 errors should trigger token refresh or redirect to login.
   */
  it('should handle authentication errors (401) with proper error structure', async () => {
    // Arrange: Mock 401 authentication error
    const mockAuthError = {
      response: {
        status: 401,
        data: { message: 'Token expired' },
      },
    }
    ;(apiClient.patch as any).mockRejectedValue(mockAuthError)

    // Act & Assert: Should throw authentication error
    await expect(updateUserSettings(mockLocalSettings)).rejects.toThrow(
      'Authentication failed. Please log in again.'
    )

    expect(apiClient.patch).toHaveBeenCalled()
  })

  /**
   * Test rationale: Validation errors (400) need specific handling to show
   * field-level errors to users. This is essential for good UX.
   */
  it('should handle validation errors (400) with field-specific messages', async () => {
    // Arrange: Mock validation error response
    const mockValidationError = {
      response: {
        status: 400,
        data: {
          message: 'Validation failed',
          errors: {
            RepsMinimum: 'Must be between 1 and 50',
            WeightIncrement: 'Must be positive number',
          },
        },
      },
    }
    ;(apiClient.patch as any).mockRejectedValue(mockValidationError)

    // Act & Assert: Should preserve validation error details
    await expect(updateUserSettings(mockLocalSettings)).rejects.toThrow(
      'Validation failed'
    )

    // The error should contain field-specific details for UI consumption
    try {
      await updateUserSettings(mockLocalSettings)
    } catch (error: any) {
      expect(error.validationErrors).toEqual({
        RepsMinimum: 'Must be between 1 and 50',
        WeightIncrement: 'Must be positive number',
      })
    }
  })

  /**
   * Test rationale: Network failures are common in mobile environments.
   * The service must handle them gracefully with user-friendly messages.
   */
  it('should handle network errors with retry-friendly error messages', async () => {
    // Arrange: Mock network error (no response)
    const mockNetworkError = {
      request: {},
      message: 'Network Error',
    }
    ;(apiClient.patch as any).mockRejectedValue(mockNetworkError)

    // Act & Assert: Should provide user-friendly network error
    await expect(updateUserSettings(mockLocalSettings)).rejects.toThrow(
      'Network error. Please check your connection and try again.'
    )
  })

  /**
   * Test rationale: Input validation on the client side prevents unnecessary
   * API calls and provides immediate feedback. Security-first approach.
   */
  it('should validate input settings before making API call', async () => {
    // Arrange: Invalid settings with boundary violations
    const invalidSettings: LocalSettings = {
      quickMode: true,
      weightUnit: 'kg',
      setStyle: 'Normal',
      repsMin: 15, // Invalid: greater than max
      repsMax: 10,
      weightIncrement: -5, // Invalid: negative
      warmupSets: 10, // Invalid: too many
    }

    // Act & Assert: Should validate before API call
    await expect(updateUserSettings(invalidSettings)).rejects.toThrow(
      'Minimum reps must be less than maximum reps'
    )

    // Should not make API call with invalid data
    expect(apiClient.patch).not.toHaveBeenCalled()
  })

  /**
   * Test rationale: The service should handle missing authentication gracefully
   * rather than making unauthenticated requests that will fail.
   */
  it('should handle missing authentication token', async () => {
    // Arrange: No auth token available
    ;(apiClient.defaults.headers.common as any)['Authorization'] = undefined

    // Act & Assert: Should fail early without API call
    await expect(updateUserSettings(mockLocalSettings)).rejects.toThrow(
      'No authentication token available'
    )

    expect(apiClient.patch).not.toHaveBeenCalled()
  })

  /**
   * Test rationale: Set style mapping is complex business logic that needs
   * proper testing to ensure UI selections map to correct API values.
   */
  it('should correctly map set styles to API values', async () => {
    const mockApiResponse = {
      status: 200,
      data: { updated: true },
    }
    ;(apiClient.patch as any).mockResolvedValue(mockApiResponse)

    // Test each set style mapping
    const setStyleMappings = [
      { ui: 'Normal', api: { IsNormalSet: true } },
      { ui: 'Rest-Pause', api: { IsNormalSet: false } },
      { ui: 'Drop', api: { IsNormalSet: true, IsDropSet: true } },
      { ui: 'Pyramid', api: { IsNormalSet: false, IsPyramid: true } },
      {
        ui: 'Reverse Pyramid',
        api: { IsNormalSet: false, IsReversePyramid: true },
      },
    ]

    // Test each mapping individually to avoid loops
    const testMapping = async (mapping: (typeof setStyleMappings)[0]) => {
      const settingsWithStyle = {
        ...mockLocalSettings,
        setStyle: mapping.ui as LocalSettings['setStyle'],
      }

      await updateUserSettings(settingsWithStyle)

      expect(apiClient.patch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining(mapping.api),
        expect.any(Object)
      )

      vi.clearAllMocks()
      ;(apiClient.patch as any).mockResolvedValue(mockApiResponse)
    }

    // Test each mapping sequentially
    await testMapping(setStyleMappings[0]) // Normal
    await testMapping(setStyleMappings[1]) // Rest-Pause
    await testMapping(setStyleMappings[2]) // Drop
    await testMapping(setStyleMappings[3]) // Pyramid
    await testMapping(setStyleMappings[4]) // Reverse Pyramid
  })

  /**
   * Test rationale: Unit conversion between lbs and kg is critical functionality
   * that affects all weight-related calculations. Must be precise.
   */
  it('should handle weight unit conversions correctly', async () => {
    const mockApiResponse = {
      status: 200,
      data: { updated: true },
    }
    ;(apiClient.patch as any).mockResolvedValue(mockApiResponse)

    // Test lbs to kg conversion (5 lbs ≈ 2.27 kg)
    const lbsSettings = {
      ...mockLocalSettings,
      weightUnit: 'lbs' as const,
      weightIncrement: 5,
    }

    await updateUserSettings(lbsSettings)

    expect(apiClient.patch).toHaveBeenCalledWith(
      expect.any(String),
      expect.objectContaining({
        MassUnit: 'lbs',
        WeightIncrement: 5,
      }),
      expect.any(Object)
    )

    // Test kg settings (should be passed through)
    vi.clearAllMocks()
    ;(apiClient.patch as any).mockResolvedValue(mockApiResponse)

    const kgSettings = {
      ...mockLocalSettings,
      weightUnit: 'kg' as const,
      weightIncrement: 2.5,
    }

    await updateUserSettings(kgSettings)

    expect(apiClient.patch).toHaveBeenCalledWith(
      expect.any(String),
      expect.objectContaining({
        MassUnit: 'kg',
        WeightIncrement: 2.5,
      }),
      expect.any(Object)
    )
  })

  /**
   * Test rationale: Server errors (500, 503) should be handled differently
   * from client errors, with appropriate retry guidance.
   */
  it('should handle server errors (5xx) with appropriate messaging', async () => {
    // Test 500 Internal Server Error
    const mockServerError = {
      response: {
        status: 500,
        data: { message: 'Internal server error' },
      },
    }
    ;(apiClient.patch as any).mockRejectedValue(mockServerError)

    await expect(updateUserSettings(mockLocalSettings)).rejects.toThrow(
      'Server error. Please try again later.'
    )

    // Test 503 Service Unavailable
    vi.clearAllMocks()
    const mockServiceError = {
      response: {
        status: 503,
        data: { message: 'Service temporarily unavailable' },
      },
    }
    ;(apiClient.patch as any).mockRejectedValue(mockServiceError)

    await expect(updateUserSettings(mockLocalSettings)).rejects.toThrow(
      'Service temporarily unavailable. Please try again later.'
    )
  })
})

/**
 * Test Coverage Analysis:
 *
 * This test suite covers:
 * ✅ Happy path: successful API calls with correct payload
 * ✅ Authentication errors (401)
 * ✅ Validation errors (400) with field details
 * ✅ Network connectivity issues
 * ✅ Input validation before API calls
 * ✅ Missing authentication handling
 * ✅ Complex business logic (set style mapping)
 * ✅ Unit conversions (lbs ↔ kg)
 * ✅ Server errors (5xx) with appropriate messaging
 *
 * Expected coverage: 95%+
 * Test-first approach: All tests written before implementation
 *
 * These tests will FAIL initially because updateUserSettings doesn't exist yet.
 * This is the correct TDD approach - write failing tests that define the API,
 * then implement the minimal code to make them pass.
 */
