import { render, screen, fireEvent } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import CardLayout from './CardLayout'
import type { LocalSettings } from '@/types/settings'

// Mock the UI components
vi.mock('@/components/ui/Card', () => ({
  Card: ({ children, className }: any) => (
    <div className={className}>{children}</div>
  ),
}))

vi.mock('../controls/QuickModeToggle', () => ({
  default: ({ onChange, disabled }: any) => (
    <button
      onClick={() => onChange(!disabled)}
      disabled={disabled}
      data-testid="quick-mode-toggle"
    >
      Quick Mode
    </button>
  ),
}))

vi.mock('../controls/SetStyleDropdown', () => ({
  default: ({ onChange, disabled }: any) => (
    <select
      onChange={(e) => onChange(e.target.value)}
      disabled={disabled}
      data-testid="set-style-dropdown"
    >
      <option value="Normal">Normal</option>
      <option value="Rest-pause">Rest-pause</option>
    </select>
  ),
}))

vi.mock('../controls/WeightUnitToggle', () => ({
  default: ({ onChange, disabled }: any) => (
    <button
      onClick={() => onChange('kg')}
      disabled={disabled}
      data-testid="weight-unit-toggle"
    >
      Weight Unit
    </button>
  ),
}))

vi.mock('../controls/WeightIncrementInput', () => ({
  default: ({ onChange, disabled }: any) => (
    <input
      type="number"
      onChange={(e) => onChange(Number(e.target.value))}
      disabled={disabled}
      data-testid="weight-increment-input"
    />
  ),
}))

vi.mock('../controls/WarmupSetsSelector', () => ({
  default: ({ onChange, disabled }: any) => (
    <input
      type="number"
      onChange={(e) => onChange(Number(e.target.value))}
      disabled={disabled}
      data-testid="warmup-sets-selector"
    />
  ),
}))

// Mock RepRangeInput - this is the critical component for our test
vi.mock('../controls/RepRangeInput', () => ({
  default: ({ minReps, maxReps, onChange, disabled }: any) => (
    <div data-testid="rep-range-input">
      <button
        onClick={() => onChange({ minReps: minReps + 1, maxReps })}
        disabled={disabled}
        data-testid="min-reps-increment"
        aria-label="Increase minimum reps"
      >
        Min +
      </button>
      <input
        type="number"
        value={minReps}
        onChange={(e) => onChange({ minReps: Number(e.target.value), maxReps })}
        disabled={disabled}
        data-testid="min-reps-input"
        aria-label="Minimum reps"
      />
      <button
        onClick={() => onChange({ minReps, maxReps: maxReps + 1 })}
        disabled={disabled}
        data-testid="max-reps-increment"
        aria-label="Increase maximum reps"
      >
        Max +
      </button>
      <input
        type="number"
        value={maxReps}
        onChange={(e) => onChange({ minReps, maxReps: Number(e.target.value) })}
        disabled={disabled}
        data-testid="max-reps-input"
        aria-label="Maximum reps"
      />
    </div>
  ),
}))

describe('CardLayout RepRange Issue', () => {
  const mockUpdateSetting = vi.fn()

  const defaultSettings: LocalSettings = {
    quickMode: false,
    weightUnit: 'lbs' as const,
    setStyle: 'Normal' as const,
    repsMin: 6,
    repsMax: 12,
    weightIncrement: 5,
    warmupSets: 0,
  }

  beforeEach(() => {
    mockUpdateSetting.mockClear()
  })

  /**
   * TEST: Reproduces the unresponsive min reps issue
   *
   * This test demonstrates the problem where RepRangeInput.onChange
   * triggers two sequential updateSetting calls, potentially causing
   * race conditions and validation errors that block UI responsiveness.
   */
  it('should handle RepRangeInput onChange with sequential updateSetting calls', () => {
    render(
      <CardLayout
        settings={defaultSettings}
        updateSetting={mockUpdateSetting}
        isSaving={false}
      />
    )

    // Simulate user clicking min reps increment button
    const minIncrement = screen.getByTestId('min-reps-increment')
    fireEvent.click(minIncrement)

    // CRITICAL: This is where the issue occurs
    // RepRangeInput calls onChange with { minReps: 7, maxReps: 12 }
    // CardLayout then calls:
    // 1. updateSetting('repsMin', 7)  <- May trigger validation error
    // 2. updateSetting('repsMax', 12) <- Happens after potential error

    expect(mockUpdateSetting).toHaveBeenCalledTimes(2)
    expect(mockUpdateSetting).toHaveBeenNthCalledWith(1, 'repsMin', 7)
    expect(mockUpdateSetting).toHaveBeenNthCalledWith(2, 'repsMax', 12)
  })

  /**
   * TEST: Verifies rapid sequential calls don't break functionality
   */
  it('should handle rapid RepRangeInput changes without blocking UI', () => {
    render(
      <CardLayout
        settings={defaultSettings}
        updateSetting={mockUpdateSetting}
        isSaving={false}
      />
    )

    // Simulate rapid button clicks
    const minIncrement = screen.getByTestId('min-reps-increment')
    fireEvent.click(minIncrement) // 6 -> 7
    fireEvent.click(minIncrement) // 7 -> 8
    fireEvent.click(minIncrement) // 8 -> 9

    // Each click should result in 2 updateSetting calls
    expect(mockUpdateSetting).toHaveBeenCalledTimes(6)

    // Final calls should have correct values
    expect(mockUpdateSetting).toHaveBeenLastCalledWith('repsMax', 12)
  })

  /**
   * TEST: Edge case where min approaches max boundary
   */
  it('should handle boundary validation without freezing UI', () => {
    const boundarySettings = { ...defaultSettings, repsMin: 11, repsMax: 12 }

    render(
      <CardLayout
        settings={boundarySettings}
        updateSetting={mockUpdateSetting}
        isSaving={false}
      />
    )

    // Try to increment min reps to equal max (invalid)
    const minIncrement = screen.getByTestId('min-reps-increment')
    fireEvent.click(minIncrement)

    // Should still call updateSetting even if validation will fail
    expect(mockUpdateSetting).toHaveBeenCalledTimes(2)
    expect(mockUpdateSetting).toHaveBeenNthCalledWith(1, 'repsMin', 12)
    expect(mockUpdateSetting).toHaveBeenNthCalledWith(2, 'repsMax', 12)
  })

  /**
   * TEST: Disabled state should prevent interactions
   */
  it('should disable RepRangeInput when isSaving is true', () => {
    render(
      <CardLayout
        settings={defaultSettings}
        updateSetting={mockUpdateSetting}
        isSaving // This should disable all controls
      />
    )

    const minIncrement = screen.getByTestId('min-reps-increment')
    expect(minIncrement).toBeDisabled()

    // Click should not work when disabled
    fireEvent.click(minIncrement)
    expect(mockUpdateSetting).not.toHaveBeenCalled()
  })

  /**
   * TEST: Direct input field changes
   */
  it('should handle direct input field changes in RepRangeInput', () => {
    render(
      <CardLayout
        settings={defaultSettings}
        updateSetting={mockUpdateSetting}
        isSaving={false}
      />
    )

    const minInput = screen.getByTestId('min-reps-input')
    fireEvent.change(minInput, { target: { value: '8' } })

    // Direct input change should also trigger dual updateSetting calls
    expect(mockUpdateSetting).toHaveBeenCalledTimes(2)
    expect(mockUpdateSetting).toHaveBeenNthCalledWith(1, 'repsMin', 8)
    expect(mockUpdateSetting).toHaveBeenNthCalledWith(2, 'repsMax', 12)
  })
})
