import { test, expect } from '@playwright/test'
import { login } from './helpers/auth'

test.describe('Indefinite Session Support', () => {
  test.beforeEach(async ({ page }) => {
    // Login once before the test
    await login(page)
  })

  test('should maintain session on exercise v2 page', async ({ page }) => {
    // Navigate to workout overview
    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Look for Start workout button to verify we're logged in
    const startButton = page.getByRole('button', { name: /start workout/i })
    await expect(startButton).toBeVisible()

    // Navigate directly to exercise v2 page
    await page.goto('/workout/exercise-v2/1')
    await page.waitForLoadState('networkidle')

    // Wait a moment to ensure any session validation would have run
    await page.waitForTimeout(1000)

    // Should still be on the exercise page, not redirected to login
    await expect(page).toHaveURL(/\/workout\/exercise-v2\/1/)

    // Navigate back to workout overview to verify session is still active
    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Should see Start workout button again, confirming we're still logged in
    await expect(startButton).toBeVisible()
  })

  test('should not log out after extended time on exercise v2 page', async ({
    page,
  }) => {
    // Navigate to workout overview
    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Navigate to exercise v2 page
    await page.goto('/workout/exercise-v2/1')
    await page.waitForLoadState('networkidle')

    // Simulate extended time on page (5 seconds to test, in reality would be hours)
    await page.waitForTimeout(5000)

    // Should still be on the exercise page
    await expect(page).toHaveURL(/\/workout\/exercise-v2\/1/)

    // Verify we can still interact with the page (not logged out)
    const pageContainer = page.getByTestId('exercise-page-container')
    await expect(pageContainer).toBeVisible()
  })
})
