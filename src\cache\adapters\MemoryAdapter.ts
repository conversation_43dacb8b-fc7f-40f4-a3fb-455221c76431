/* eslint-disable no-restricted-syntax, no-await-in-loop, class-methods-use-this */
/**
 * In-memory cache adapter implementation
 *
 * This adapter stores cache entries in memory using a Map. It provides:
 * - Automatic TTL expiration checking
 * - Size tracking
 * - Periodic cleanup of expired entries
 * - Batch operations support
 */

import type { CacheAdapter, CacheEntry, CacheMetadata } from '../types'
import { CacheAdapterError, CacheSerializationError } from '../types'

/**
 * Configuration for the memory adapter
 */
interface MemoryAdapterConfig {
  /** Maximum number of entries to store */
  maxEntries?: number
  /** Cleanup interval in milliseconds */
  cleanupInterval?: number
  /** Whether to enable automatic cleanup */
  enableAutoCleanup?: boolean
}

/**
 * In-memory cache adapter using Map for storage
 */
export class MemoryAdapter implements CacheAdapter {
  private readonly storage = new Map<string, CacheEntry>()

  private readonly config: Required<MemoryAdapterConfig>

  private cleanupTimer?: NodeJS.Timeout

  private totalSize = 0

  constructor(config: MemoryAdapterConfig = {}) {
    this.config = {
      maxEntries: config.maxEntries ?? 1000,
      cleanupInterval: config.cleanupInterval ?? 5 * 60 * 1000, // 5 minutes
      enableAutoCleanup: config.enableAutoCleanup ?? true,
    }

    if (this.config.enableAutoCleanup) {
      this.startCleanup()
    }
  }

  /**
   * Get a value from the cache
   */
  async get<T>(key: string): Promise<CacheEntry<T> | null> {
    const entry = this.storage.get(key) as CacheEntry<T> | undefined

    if (!entry) {
      return null
    }

    // Check if expired
    if (this.isExpired(entry.metadata)) {
      await this.delete(key)
      return null
    }

    // Update access time
    entry.metadata.accessed = Date.now()

    return entry
  }

  /**
   * Set a value in the cache
   */
  async set<T>(key: string, value: T, metadata: CacheMetadata): Promise<void> {
    try {
      // Calculate size of the serialized value
      const serializedValue = this.serialize(value)
      const entrySize = this.calculateSize(key, serializedValue)

      // Check if we need to make room
      if (this.storage.size >= this.config.maxEntries) {
        await this.evictLRU()
      }

      // Remove old entry if it exists (to update size tracking)
      const existingEntry = this.storage.get(key)
      if (existingEntry) {
        this.totalSize -= existingEntry.metadata.size
      }

      // Create new entry
      const entry: CacheEntry<T> = {
        value: this.deserialize(serializedValue), // Store deserialized for consistency
        metadata: {
          ...metadata,
          size: entrySize,
          created: metadata.created || Date.now(),
          accessed: Date.now(),
        },
      }

      // Store the entry
      this.storage.set(key, entry)
      this.totalSize += entrySize
    } catch (error) {
      throw new CacheAdapterError(
        `Failed to set cache entry for key "${key}"`,
        error instanceof Error ? error : undefined
      )
    }
  }

  /**
   * Delete a value from the cache
   */
  async delete(key: string): Promise<void> {
    const entry = this.storage.get(key)
    if (entry) {
      this.totalSize -= entry.metadata.size
      this.storage.delete(key)
    }
  }

  /**
   * Clear all entries from the cache
   */
  async clear(): Promise<void> {
    this.storage.clear()
    this.totalSize = 0
  }

  /**
   * Get all keys in the cache
   */
  async getAllKeys(): Promise<string[]> {
    // Filter out expired keys
    const validKeys: string[] = []
    const now = Date.now()

    for (const [key, entry] of this.storage.entries()) {
      if (!this.isExpired(entry.metadata, now)) {
        validKeys.push(key)
      }
    }

    return validKeys
  }

  /**
   * Get the total size of the cache in bytes
   */
  async getSize(): Promise<number> {
    return this.totalSize
  }

  /**
   * Check if the adapter supports batch operations
   */
  supportsBatch(): boolean {
    return true
  }

  /**
   * Batch get operation
   */
  async getMany<T>(keys: string[]): Promise<Map<string, CacheEntry<T>>> {
    const results = new Map<string, CacheEntry<T>>()

    for (const key of keys) {
      const entry = await this.get<T>(key)
      if (entry) {
        results.set(key, entry)
      }
    }

    return results
  }

  /**
   * Batch set operation
   */
  async setMany<T>(
    entries: Map<string, { value: T; metadata: CacheMetadata }>
  ): Promise<void> {
    for (const [key, { value, metadata }] of entries) {
      await this.set(key, value, metadata)
    }
  }

  /**
   * Batch delete operation
   */
  async deleteMany(keys: string[]): Promise<void> {
    for (const key of keys) {
      await this.delete(key)
    }
  }

  /**
   * Manually trigger cleanup of expired entries
   */
  async cleanup(): Promise<number> {
    const now = Date.now()
    const expiredKeys: string[] = []

    for (const [key, entry] of this.storage.entries()) {
      if (this.isExpired(entry.metadata, now)) {
        expiredKeys.push(key)
      }
    }

    for (const key of expiredKeys) {
      await this.delete(key)
    }

    return expiredKeys.length
  }

  /**
   * Get statistics about the cache
   */
  getStats() {
    return {
      entryCount: this.storage.size,
      totalSize: this.totalSize,
      maxEntries: this.config.maxEntries,
    }
  }

  /**
   * Destroy the adapter and clean up resources
   */
  destroy(): void {
    this.stopCleanup()
    this.storage.clear()
    this.totalSize = 0
  }

  /**
   * Start automatic cleanup process
   */
  private startCleanup(): void {
    if (this.cleanupTimer) {
      return
    }

    this.cleanupTimer = setInterval(() => {
      this.cleanup().catch((error) => {
        console.warn('Cache cleanup failed:', error)
      })
    }, this.config.cleanupInterval)

    // Don't keep the process alive for cleanup
    if (typeof this.cleanupTimer === 'object' && 'unref' in this.cleanupTimer) {
      this.cleanupTimer.unref()
    }
  }

  /**
   * Stop automatic cleanup process
   */
  private stopCleanup(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = undefined
    }
  }

  /**
   * Check if an entry is expired
   */
  private isExpired(metadata: CacheMetadata, now = Date.now()): boolean {
    return metadata.expires > 0 && now > metadata.expires
  }

  /**
   * Evict the least recently used entry
   */
  private async evictLRU(): Promise<void> {
    let oldestKey: string | null = null
    let oldestAccess = Infinity

    for (const [key, entry] of this.storage.entries()) {
      if (entry.metadata.accessed < oldestAccess) {
        oldestAccess = entry.metadata.accessed
        oldestKey = key
      }
    }

    if (oldestKey) {
      await this.delete(oldestKey)
    }
  }

  /**
   * Serialize a value for size calculation
   */
  private serialize(value: unknown): string {
    try {
      return JSON.stringify(value)
    } catch (error) {
      throw new CacheSerializationError(
        'Failed to serialize cache value',
        error instanceof Error ? error : undefined
      )
    }
  }

  /**
   * Deserialize a value
   */
  private deserialize<T>(serialized: string): T {
    try {
      return JSON.parse(serialized)
    } catch (error) {
      throw new CacheSerializationError(
        'Failed to deserialize cache value',
        error instanceof Error ? error : undefined
      )
    }
  }

  /**
   * Calculate the approximate size of a cache entry
   */
  private calculateSize(key: string, serializedValue: string): number {
    // Approximate size calculation:
    // - Key size (UTF-16, so 2 bytes per character)
    // - Serialized value size
    // - Metadata overhead (approximate)
    const keySize = key.length * 2
    const valueSize = serializedValue.length * 2
    const metadataSize = 200 // Approximate metadata overhead

    return keySize + valueSize + metadataSize
  }
}
