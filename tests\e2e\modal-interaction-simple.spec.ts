import { test, expect } from '@playwright/test'
import { ExercisePage } from './pages/ExercisePage'
import { WorkoutPage } from './pages/WorkoutPage'
import { setupAuthenticatedUser } from './helpers/auth-helper'

test.describe('Modal Interaction Simple Test', () => {
  test.beforeEach(async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 })
    await setupAuthenticatedUser(page)
  })

  test('should test custom duration modal interaction @critical', async ({
    page,
  }) => {
    // Navigate to workout and start (like the working test)
    await page.goto('/workout')
    const workoutPage = new WorkoutPage(page)
    await workoutPage.waitForPageLoad()
    await workoutPage.startWorkout()

    // Navigate to exercise page
    await page.goto('/workout/exercise-v2/1')
    const exercisePage = new ExercisePage(page)
    await exercisePage.waitForPageLoad()

    // Save a set to trigger rest timer
    await page.fill('input[placeholder="Reps"]', '10')
    await page.fill('input[placeholder*="Weight"]', '50')
    await page.click('button:has-text("Save set")')

    // Wait for rest timer to appear
    await expect(page.getByTestId('rest-timer-container')).toBeVisible()

    // Open duration picker
    await page.getByTestId('duration-setting-button').click()
    await expect(page.getByTestId('duration-picker')).toBeVisible()

    // Click custom button
    await page.getByTestId('duration-option-custom').click()

    // Custom duration modal should appear
    const modal = page.getByTestId('custom-duration-modal')
    await expect(modal).toBeVisible()

    // Test 1: Click on modal content should NOT close it
    await modal.click({ position: { x: 200, y: 100 } })
    await expect(modal).toBeVisible()
    console.log('✓ Modal stays open when clicking on content')

    // Test 2: Input should be functional
    const input = page.getByLabel('Enter duration in seconds')
    await input.fill('120')
    await expect(input).toHaveValue('120')
    console.log('✓ Input is functional')

    // Test 3: Confirm button should work
    await page.click('button:has-text("Confirm")')
    await expect(modal).not.toBeVisible()
    console.log('✓ Confirm button works')

    // Test 4: Duration should be updated
    await expect(page.getByTestId('duration-setting')).toContainText('2:00')
    console.log('✓ Duration updated correctly')

    console.log('All modal interaction tests passed!')
  })
})
