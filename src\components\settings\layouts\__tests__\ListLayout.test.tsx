import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import ListLayout from '../ListLayout'

const mockSettings = {
  email: '<EMAIL>',
  weightUnit: 'lbs' as const,
  setStyle: 'Normal' as const,
  quickMode: false,
  repRange: { min: 6, max: 12 },
  weightIncrement: 5,
  warmupSets: 3,
}

describe('ListLayout', () => {
  it('renders all settings sections', () => {
    render(<ListLayout settings={mockSettings} onSettingChange={vi.fn()} />)

    // Check for section headers
    expect(screen.getByText('Workout Style')).toBeInTheDocument()
    expect(screen.getByText('Weights & Reps')).toBeInTheDocument()
    expect(screen.getByText('Warmups')).toBeInTheDocument()
  })

  it('displays settings in vertical list format', () => {
    const { container } = render(
      <ListLayout settings={mockSettings} onSettingChange={vi.fn()} />
    )

    // Check for vertical spacing classes
    const sections = container.querySelectorAll(
      '[data-testid="settings-section"]'
    )
    expect(sections.length).toBeGreaterThanOrEqual(3)

    // Check for mobile-optimized spacing
    expect(container.querySelector('.space-y-4')).toBeInTheDocument()
  })

  it('displays current settings values', () => {
    render(<ListLayout settings={mockSettings} onSettingChange={vi.fn()} />)

    // Check that current values are displayed
    expect(screen.getByText('Normal')).toBeInTheDocument()
    expect(screen.getByText('lbs')).toBeInTheDocument()
    expect(screen.getByText('6-12')).toBeInTheDocument()
  })

  it('uses Card component for sections', () => {
    const { container } = render(
      <ListLayout settings={mockSettings} onSettingChange={vi.fn()} />
    )

    // Check for Card styling (Card uses bg-bg-secondary)
    const cards = container.querySelectorAll('.bg-bg-secondary')
    expect(cards.length).toBeGreaterThan(0)

    // Check for rounded corners (Card uses rounded-theme)
    const roundedCards = container.querySelectorAll('.rounded-theme')
    expect(roundedCards.length).toBeGreaterThan(0)
  })

  it('has mobile-optimized touch targets', () => {
    const { container } = render(
      <ListLayout settings={mockSettings} onSettingChange={vi.fn()} />
    )

    // Check that interactive elements have proper sizing
    const controls = container.querySelectorAll(
      '[data-testid="setting-control"]'
    )
    controls.forEach((control) => {
      // Should have min-height of at least 52px for touch targets
      expect(control.classList.toString()).toMatch(
        /h-\[52px\]|min-h-\[52px\]|p-[3-4]/
      )
    })
  })

  it('groups settings by category', () => {
    render(<ListLayout settings={mockSettings} onSettingChange={vi.fn()} />)

    // Workout Style section should contain set style and quick mode
    const workoutStyleSection = screen.getByTestId('workout-style-section')
    expect(workoutStyleSection).toBeInTheDocument()

    // Weights & Reps section should contain unit, range, and increments
    const weightsSection = screen.getByTestId('weights-reps-section')
    expect(weightsSection).toBeInTheDocument()

    // Warmups section
    const warmupsSection = screen.getByTestId('warmups-section')
    expect(warmupsSection).toBeInTheDocument()
  })

  it('handles onSettingChange callback', () => {
    const onSettingChange = vi.fn()
    render(
      <ListLayout settings={mockSettings} onSettingChange={onSettingChange} />
    )

    // The callback should be available to child controls
    // (We'll test actual interaction when controls are implemented)
    expect(onSettingChange).toBeDefined()
  })

  it('is scrollable on mobile viewport', () => {
    const { container } = render(
      <ListLayout settings={mockSettings} onSettingChange={vi.fn()} />
    )

    // Should have scrollable container
    const scrollContainer = container.querySelector(
      '[data-testid="list-layout-container"]'
    )
    expect(scrollContainer).toBeInTheDocument()
    expect(scrollContainer).toHaveClass('overflow-y-auto')
  })
})
