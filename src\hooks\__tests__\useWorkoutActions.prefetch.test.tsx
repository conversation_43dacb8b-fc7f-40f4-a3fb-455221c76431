import { describe, it, expect, vi, beforeEach } from 'vitest'
import React from 'react'
import { renderHook, act } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useWorkoutActions } from '../useWorkoutActions'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useExerciseSetsPrefetch } from '../useExerciseSetsPrefetch'
import { logger } from '@/utils/logger'

// Mock dependencies
vi.mock('@/stores/workoutStore')
vi.mock('../useExerciseSetsPrefetch')
vi.mock('@/utils/logger')
vi.mock('@/utils/workoutCache', () => ({
  WorkoutCache: {
    set: vi.fn(),
  },
}))

describe('useWorkoutActions with prefetch', () => {
  const mockSetWorkout = vi.fn()
  const mockStartWorkoutStore = vi.fn()
  const mockLoadAllExerciseRecommendations = vi
    .fn()
    .mockResolvedValue(undefined)
  const mockApplyExerciseSwaps = vi.fn((workout) => workout)
  const mockPrefetchExerciseSets = vi.fn().mockResolvedValue(undefined)

  const mockWorkout = [
    {
      WorkoutTemplates: [
        {
          Id: 1,
          Label: 'Test Workout',
          Exercises: [
            { Id: 1, Label: 'Bench Press' },
            { Id: 2, Label: 'Squats' },
            { Id: 3, Label: 'Deadlifts' },
            { Id: 4, Label: 'Rows' },
          ],
        },
      ],
    },
  ]

  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )

  beforeEach(() => {
    vi.clearAllMocks()

    // Mock useWorkoutStore
    vi.mocked(useWorkoutStore).mockReturnValue({
      setWorkout: mockSetWorkout,
      startWorkout: mockStartWorkoutStore,
      loadAllExerciseRecommendations: mockLoadAllExerciseRecommendations,
      applyExerciseSwaps: mockApplyExerciseSwaps,
      exercises: mockWorkout[0].WorkoutTemplates[0].Exercises,
      previewExerciseSkips: new Set(),
    } as any)

    // Mock useExerciseSetsPrefetch
    vi.mocked(useExerciseSetsPrefetch).mockReturnValue({
      prefetchExerciseSets: mockPrefetchExerciseSets,
      prefetchStatus: {},
      isPrefetching: false,
      prefetchedExerciseIds: [],
      getPrefetchProgress: vi.fn(),
    })

    // Mock logger
    vi.mocked(logger.log).mockImplementation(() => {})
    vi.mocked(logger.error).mockImplementation(() => {})
  })

  it('should prefetch first 3 exercises when starting workout', async () => {
    const { result } = renderHook(() => useWorkoutActions(), { wrapper })

    await act(async () => {
      const response = await result.current.startWorkout(mockWorkout)
      expect(response.success).toBe(true)
    })

    // Should set workout and start it
    expect(mockSetWorkout).toHaveBeenCalledWith(
      mockWorkout[0].WorkoutTemplates[0]
    )
    expect(mockStartWorkoutStore).toHaveBeenCalled()

    // Should prefetch first 3 exercises
    expect(mockPrefetchExerciseSets).toHaveBeenCalledWith([1, 2, 3])
  })

  it('should skip preview-skipped exercises when prefetching', async () => {
    // Mock with preview skips
    vi.mocked(useWorkoutStore).mockReturnValue({
      setWorkout: mockSetWorkout,
      startWorkout: mockStartWorkoutStore,
      loadAllExerciseRecommendations: mockLoadAllExerciseRecommendations,
      applyExerciseSwaps: mockApplyExerciseSwaps,
      exercises: mockWorkout[0].WorkoutTemplates[0].Exercises,
      previewExerciseSkips: new Set([1, 2]), // Skip first two exercises
    } as any)

    const { result } = renderHook(() => useWorkoutActions(), { wrapper })

    await act(async () => {
      await result.current.startWorkout(mockWorkout)
    })

    // Should skip exercises 1 and 2, prefetch 3 and 4
    expect(mockPrefetchExerciseSets).toHaveBeenCalledWith([3, 4])
  })

  it('should handle workouts with fewer than 3 exercises', async () => {
    const smallWorkout = [
      {
        WorkoutTemplates: [
          {
            Id: 1,
            Label: 'Small Workout',
            Exercises: [
              { Id: 1, Label: 'Bench Press' },
              { Id: 2, Label: 'Squats' },
            ],
          },
        ],
      },
    ]

    vi.mocked(useWorkoutStore).mockReturnValue({
      setWorkout: mockSetWorkout,
      startWorkout: mockStartWorkoutStore,
      loadAllExerciseRecommendations: mockLoadAllExerciseRecommendations,
      applyExerciseSwaps: mockApplyExerciseSwaps,
      exercises: smallWorkout[0].WorkoutTemplates[0].Exercises,
      previewExerciseSkips: new Set(),
    } as any)

    const { result } = renderHook(() => useWorkoutActions(), { wrapper })

    await act(async () => {
      await result.current.startWorkout(smallWorkout)
    })

    // Should prefetch only available exercises
    expect(mockPrefetchExerciseSets).toHaveBeenCalledWith([1, 2])
  })

  it('should not fail workout start if prefetch fails', async () => {
    mockPrefetchExerciseSets.mockRejectedValueOnce(new Error('Network error'))

    const { result } = renderHook(() => useWorkoutActions(), { wrapper })

    await act(async () => {
      const response = await result.current.startWorkout(mockWorkout)
      expect(response.success).toBe(true)
      expect(response.firstExerciseId).toBe(1)
    })

    // Should still complete workout start
    expect(mockSetWorkout).toHaveBeenCalled()
    expect(mockStartWorkoutStore).toHaveBeenCalled()
    expect(mockLoadAllExerciseRecommendations).toHaveBeenCalled()
  })

  it('should not prefetch if no exercises are available', async () => {
    const emptyWorkout = [
      {
        WorkoutTemplates: [
          {
            Id: 1,
            Label: 'Empty Workout',
            Exercises: [],
          },
        ],
      },
    ]

    vi.mocked(useWorkoutStore).mockReturnValue({
      setWorkout: mockSetWorkout,
      startWorkout: mockStartWorkoutStore,
      loadAllExerciseRecommendations: mockLoadAllExerciseRecommendations,
      applyExerciseSwaps: mockApplyExerciseSwaps,
      exercises: [],
      previewExerciseSkips: new Set(),
    } as any)

    const { result } = renderHook(() => useWorkoutActions(), { wrapper })

    await act(async () => {
      await result.current.startWorkout(emptyWorkout)
    })

    // Should not call prefetch
    expect(mockPrefetchExerciseSets).not.toHaveBeenCalled()
  })
})
