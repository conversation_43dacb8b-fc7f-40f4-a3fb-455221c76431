import { test, expect } from '@playwright/test'

test.describe('Safari Google Sign-in Fix', () => {
  test.beforeEach(async ({ page }) => {
    // Mock Firebase getRedirectResult to simulate redirect return
    await page.addInitScript(() => {
      // Override Firebase functions for testing
      ;(window as any).mockFirebaseAuth = true
    })
  })

  test('should always check for redirect result on Safari @safari', async ({
    page,
    browserName,
  }) => {
    test.skip(browserName !== 'webkit', 'Safari-specific test')

    // Navigate to login page
    await page.goto('/login')

    // Should show loading state initially while checking redirect
    await expect(
      page.locator('h1.text-gradient-gold:has-text("Dr. Muscle X")')
    ).toBeVisible()

    // Should transition to login form after no redirect found
    await expect(page.locator('[data-testid="login-form"]')).toBeVisible({
      timeout: 6000,
    })

    // Verify Google Sign-in button is available
    await expect(
      page.locator('button:has-text("Sign in with Google")')
    ).toBeVisible()
  })

  test('should handle redirect flow without URL parameters @safari', async ({
    page,
    browserName,
  }) => {
    test.skip(browserName !== 'webkit', 'Safari-specific test')

    // Simulate returning from OAuth redirect without 'code' or 'state' params
    await page.goto('/login?someOtherParam=value')

    // Should still check for redirect result
    await expect(
      page.locator('h1.text-gradient-gold:has-text("Dr. Muscle X")')
    ).toBeVisible()

    // Should show login form after checking
    await expect(page.locator('[data-testid="login-form"]')).toBeVisible({
      timeout: 6000,
    })
  })

  test('should handle timeout gracefully @safari', async ({
    page,
    browserName,
  }) => {
    test.skip(browserName !== 'webkit', 'Safari-specific test')

    // Add script to make Firebase hang
    await page.addInitScript(() => {
      ;(window as any).mockFirebaseHang = true
    })

    await page.goto('/login')

    // Should show loading initially
    await expect(
      page.locator('h1.text-gradient-gold:has-text("Dr. Muscle X")')
    ).toBeVisible()

    // Should timeout and show login form after 5 seconds
    await expect(page.locator('[data-testid="login-form"]')).toBeVisible({
      timeout: 7000,
    })
  })

  test('should maintain mobile viewport and touch targets @safari @mobile', async ({
    page,
    browserName,
  }) => {
    test.skip(browserName !== 'webkit', 'Safari-specific test')

    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 }) // iPhone SE

    await page.goto('/login')

    // Wait for login form
    await page.locator('[data-testid="login-form"]').waitFor()

    // Check Google button has proper size
    const googleButton = page.locator('button:has-text("Sign in with Google")')
    await expect(googleButton).toBeVisible()

    // Get button dimensions
    const box = await googleButton.boundingBox()
    expect(box).toBeTruthy()
    expect(box!.height).toBeGreaterThanOrEqual(48) // Minimum 48px height

    // Check safe areas are present
    await expect(page.locator('.safe-area-top')).toBeVisible()
    await expect(page.locator('.safe-area-bottom')).toBeVisible()
  })

  test('should work on non-Safari browsers with popup flow @chrome', async ({
    page,
    browserName,
  }) => {
    test.skip(browserName === 'webkit', 'Non-Safari test')

    await page.goto('/login')

    // Should show login form quickly (no long redirect check)
    await expect(page.locator('[data-testid="login-form"]')).toBeVisible({
      timeout: 2000,
    })

    // Google button should be available
    await expect(
      page.locator('button:has-text("Sign in with Google")')
    ).toBeVisible()
  })
})
