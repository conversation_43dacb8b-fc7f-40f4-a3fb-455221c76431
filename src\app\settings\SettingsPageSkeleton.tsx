export default function SettingsPageSkeleton() {
  return (
    <div
      className="min-h-screen bg-background"
      role="status"
      aria-busy="true"
      aria-label="Loading settings"
    >
      {/* Header skeleton */}
      <div
        data-testid="header-skeleton"
        className="sticky top-0 z-40 bg-surface-primary/95 backdrop-blur-md border-b border-surface-tertiary"
      >
        <div className="flex items-center justify-between h-14 px-4">
          <div className="w-8 h-8 bg-surface-secondary rounded-full animate-pulse" />
          <div className="w-20 h-6 bg-surface-secondary rounded animate-pulse" />
          <div className="w-8 h-8 bg-surface-secondary rounded-full animate-pulse" />
        </div>
      </div>

      {/* Content skeleton */}
      <div className="px-4 py-6 space-y-6">
        {/* User info skeleton */}
        <div className="space-y-2">
          <div className="w-32 h-4 bg-surface-secondary rounded animate-pulse" />
          <div className="w-48 h-6 bg-surface-secondary rounded animate-pulse" />
        </div>

        {/* Settings sections skeleton */}
        {[1, 2, 3].map((section) => (
          <div
            key={section}
            data-testid="section-skeleton"
            className="bg-surface-primary rounded-xl p-4 space-y-4"
          >
            {/* Section title */}
            <div className="w-40 h-5 bg-surface-secondary rounded animate-pulse" />

            {/* Setting items */}
            <div className="space-y-3">
              {[1, 2].map((item) => (
                <div key={item} className="flex items-center justify-between">
                  <div className="space-y-1 flex-1">
                    <div className="w-32 h-4 bg-surface-secondary rounded animate-pulse" />
                    <div className="w-24 h-3 bg-surface-secondary/50 rounded animate-pulse" />
                  </div>
                  <div className="w-16 h-8 bg-surface-secondary rounded-full animate-pulse" />
                </div>
              ))}
            </div>
          </div>
        ))}

        {/* Additional skeleton for scrollable content */}
        <div className="bg-surface-primary rounded-xl p-4 space-y-4">
          <div className="w-36 h-5 bg-surface-secondary rounded animate-pulse" />
          <div className="grid grid-cols-2 gap-3">
            {[1, 2, 3, 4].map((item) => (
              <div
                key={item}
                className="h-12 bg-surface-secondary rounded-lg animate-pulse"
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
