import { test, expect } from '@playwright/test'

test('WelcomeHeader cross-browser basic functionality', async ({
  page,
  browserName,
}) => {
  // This is a simplified test to verify basic functionality
  await page.goto('/')

  // Wait for the component to potentially load
  await page.waitForTimeout(5000)

  // Check if the page loads without major errors
  const errors = []
  page.on('pageerror', (error) => errors.push(error.message))

  // Try to find common elements that should exist
  const hasWelcomeText = await page.locator('text=Welcome back').count()
  const hasButtons = await page.locator('button').count()
  const hasHeading = await page.locator('h1,h2,h3').count()

  console.log(`Browser: ${browserName}`)
  console.log(`Welcome text found: ${hasWelcomeText > 0}`)
  console.log(`Buttons found: ${hasButtons}`)
  console.log(`Headings found: ${hasHeading}`)
  console.log(`Page errors: ${errors.length}`)

  // Basic assertion that the page loaded
  expect(errors.length).toBe(0)
})
