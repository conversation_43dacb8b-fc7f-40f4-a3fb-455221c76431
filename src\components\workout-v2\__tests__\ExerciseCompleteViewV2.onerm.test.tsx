import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { ExerciseCompleteViewV2 } from '../ExerciseCompleteViewV2'
import type {
  ExerciseModel,
  RecommendationModel,
  WorkoutLogSerieModel,
} from '@/types'

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => (
      <button {...props}>{children}</button>
    ),
    p: ({ children, ...props }: any) => <p {...props}>{children}</p>,
  },
  AnimatePresence: ({ children }: any) => children,
}))

// Mock haptics
vi.mock('@/utils/haptics', () => ({
  vibrate: vi.fn(),
}))

// Mock SuccessIcon
vi.mock('@/components/workout/SuccessIcon', () => ({
  SuccessIcon: () => <div data-testid="success-icon">✓</div>,
}))

describe('ExerciseCompleteViewV2 - OneRM Calculation', () => {
  const mockExercise: ExerciseModel = {
    Id: 1,
    Label: 'Bench Press',
    IsFinished: true,
  } as ExerciseModel

  it('should calculate progress correctly based on best sets', () => {
    // Last workout best 1RM was 100kg
    const mockRecommendation: RecommendationModel = {
      Id: 1,
      FirstWorkSet1RM: { Kg: 100, Lb: 220 },
    } as RecommendationModel

    // Current workout sets: 80kg x 10 = 111.37 1RM, 85kg x 8 = 111.67 1RM
    const completedSets: WorkoutLogSerieModel[] = [
      {
        Id: 1,
        Reps: 10,
        Weight: { Kg: 80, Lb: 176 },
        IsWarmups: false,
      } as WorkoutLogSerieModel,
      {
        Id: 2,
        Reps: 8,
        Weight: { Kg: 85, Lb: 187 },
        IsWarmups: false,
      } as WorkoutLogSerieModel,
    ]

    render(
      <ExerciseCompleteViewV2
        exercise={mockExercise}
        isLastExercise={false}
        onContinue={vi.fn()}
        recommendation={mockRecommendation}
        completedSets={completedSets}
      />
    )

    // Best 1RM from current workout: 85kg x 8 = 85 + (0.03921568 * 8 * 85) = 111.67kg
    // Progress: ((111.67 - 100) / 100) * 100 = 11.67% → 11.7%
    expect(screen.getByText('+11.7% stronger!')).toBeInTheDocument()
  })

  it('should handle no progress case', () => {
    const mockRecommendation: RecommendationModel = {
      Id: 1,
      FirstWorkSet1RM: { Kg: 100, Lb: 220 },
    } as RecommendationModel

    // Current workout: 100kg x 1 = 100 1RM (no progress)
    const completedSets: WorkoutLogSerieModel[] = [
      {
        Id: 1,
        Reps: 1,
        Weight: { Kg: 100, Lb: 220 },
        IsWarmups: false,
      } as WorkoutLogSerieModel,
    ]

    render(
      <ExerciseCompleteViewV2
        exercise={mockExercise}
        isLastExercise={false}
        onContinue={vi.fn()}
        recommendation={mockRecommendation}
        completedSets={completedSets}
      />
    )

    // No percentage should be shown when progress is 0
    expect(screen.queryByText(/stronger!/)).not.toBeInTheDocument()
    expect(screen.queryByText(/today/)).not.toBeInTheDocument()
  })

  it('should show negative progress correctly', () => {
    const mockRecommendation: RecommendationModel = {
      Id: 1,
      FirstWorkSet1RM: { Kg: 100, Lb: 220 },
    } as RecommendationModel

    // Current workout: 90kg x 1 = 90 1RM (-10% progress)
    const completedSets: WorkoutLogSerieModel[] = [
      {
        Id: 1,
        Reps: 1,
        Weight: { Kg: 90, Lb: 198 },
        IsWarmups: false,
      } as WorkoutLogSerieModel,
    ]

    render(
      <ExerciseCompleteViewV2
        exercise={mockExercise}
        isLastExercise={false}
        onContinue={vi.fn()}
        recommendation={mockRecommendation}
        completedSets={completedSets}
      />
    )

    expect(screen.getByText('-10% today')).toBeInTheDocument()
  })

  it('should ignore warmup sets in calculation', () => {
    const mockRecommendation: RecommendationModel = {
      Id: 1,
      FirstWorkSet1RM: { Kg: 100, Lb: 220 },
    } as RecommendationModel

    const completedSets: WorkoutLogSerieModel[] = [
      {
        Id: 1,
        Reps: 10,
        Weight: { Kg: 50, Lb: 110 },
        IsWarmups: true, // This should be ignored
      } as WorkoutLogSerieModel,
      {
        Id: 2,
        Reps: 5,
        Weight: { Kg: 110, Lb: 242 },
        IsWarmups: false,
      } as WorkoutLogSerieModel,
    ]

    render(
      <ExerciseCompleteViewV2
        exercise={mockExercise}
        isLastExercise={false}
        onContinue={vi.fn()}
        recommendation={mockRecommendation}
        completedSets={completedSets}
      />
    )

    // 110kg x 5 = 110 + (0.03921568 * 5 * 110) = 131.57kg
    // Progress: ((131.57 - 100) / 100) * 100 = 31.57% → 31.6%
    expect(screen.getByText('+31.6% stronger!')).toBeInTheDocument()
  })

  it('should handle no completed sets gracefully', () => {
    const mockRecommendation: RecommendationModel = {
      Id: 1,
      FirstWorkSet1RM: { Kg: 100, Lb: 220 },
    } as RecommendationModel

    render(
      <ExerciseCompleteViewV2
        exercise={mockExercise}
        isLastExercise={false}
        onContinue={vi.fn()}
        recommendation={mockRecommendation}
        completedSets={[]}
      />
    )

    // No percentage should be shown when there are no sets
    expect(screen.queryByText(/stronger!/)).not.toBeInTheDocument()
    expect(screen.queryByText(/today/)).not.toBeInTheDocument()
  })

  it('should handle missing previous workout data', () => {
    const mockRecommendation: RecommendationModel = {
      Id: 1,
      // No FirstWorkSet1RM
    } as RecommendationModel

    const completedSets: WorkoutLogSerieModel[] = [
      {
        Id: 1,
        Reps: 10,
        Weight: { Kg: 80, Lb: 176 },
        IsWarmups: false,
      } as WorkoutLogSerieModel,
    ]

    render(
      <ExerciseCompleteViewV2
        exercise={mockExercise}
        isLastExercise={false}
        onContinue={vi.fn()}
        recommendation={mockRecommendation}
        completedSets={completedSets}
      />
    )

    // No percentage shown when no previous data exists
    expect(screen.queryByText(/stronger!/)).not.toBeInTheDocument()
    expect(screen.queryByText(/today/)).not.toBeInTheDocument()
  })
})
