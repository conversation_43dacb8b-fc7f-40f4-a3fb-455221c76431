import { renderHook, act, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { useSetScreenLogic } from '../useSetScreenLogic'
import { useVisibilityChange } from '../useVisibilityChange'
import { useWorkoutStore } from '@/stores/workoutStore'
import { SetLoader } from '@/api/setLoader'

// Mock dependencies
vi.mock('../useVisibilityChange')
vi.mock('@/stores/workoutStore')
vi.mock('@/api/setLoader')
vi.mock('../useRecommendationRetry')
vi.mock('@/utils/analytics')
vi.mock('next/navigation')
vi.mock('@/hooks/useWorkout')
vi.mock('@/stores/authStore')
vi.mock('@/hooks/useRIR')

// Mock setLoader instance
const mockSetLoader = {
  loadExerciseSets: vi.fn().mockResolvedValue([]),
  cancelPendingRequests: vi.fn(),
  clearStuckStates: vi.fn(),
  retryFailed: vi.fn(),
  clearCache: vi.fn(),
  getPendingRequestCount: vi.fn().mockReturnValue(0),
  markAsLoading: vi.fn(),
  markAsFailed: vi.fn(),
}

// Create mock constructor
vi.mocked(SetLoader).mockImplementation(() => mockSetLoader as any)

// Mock router
const mockRouter = {
  replace: vi.fn(),
}
vi.mock('next/navigation', () => ({
  useRouter: () => mockRouter,
}))

// Mock useWorkout
const mockGetRecommendation = vi.fn().mockResolvedValue({
  Reps: 10,
  Weight: { Kg: 50, Lb: 110 },
  WarmupsCount: 2,
  Series: 3,
  IsNormalSets: true,
  NbPauses: 0,
})
const mockSaveSet = vi.fn()

vi.mock('@/hooks/useWorkout', () => ({
  useWorkout: () => ({
    saveSet: mockSaveSet,
    isLoading: false,
    error: null,
    getRecommendation: mockGetRecommendation,
  }),
}))

// Mock authStore
vi.mock('@/stores/authStore', () => ({
  useAuthStore: {
    getState: () => ({
      getCachedUserInfo: () => ({ MassUnit: 'lbs' }),
    }),
  },
}))

// Mock useRIR
vi.mock('@/hooks/useRIR', () => ({
  useRIR: () => ({
    mapRIRValueToNumber: vi.fn(),
    saveRIR: vi.fn(),
  }),
}))

describe('useSetScreenLogic - Visibility Handling', () => {
  const mockLoadExerciseRecommendation = vi.fn()
  const mockClearLoadingState = vi.fn()
  const mockSetCurrentExerciseById = vi.fn()
  const mockGetCachedExerciseRecommendation = vi.fn()
  const mockStore = {
    loadExerciseRecommendation: mockLoadExerciseRecommendation,
    clearLoadingState: mockClearLoadingState,
    setCurrentExerciseById: mockSetCurrentExerciseById,
    getCachedExerciseRecommendation: mockGetCachedExerciseRecommendation,
    exercises: [
      {
        Id: 123,
        Name: 'Bench Press',
        Equipment: 'barbell',
        IncrementValue: 2.5,
      },
    ],
    currentExerciseIndex: 0,
    currentSetIndex: 0,
    workoutSession: null,
    nextSet: vi.fn(),
    setCurrentSetIndex: vi.fn(),
  }

  let visibilityCallback: ((state: 'visible' | 'hidden') => void) | null = null

  beforeEach(() => {
    vi.clearAllMocks()
    visibilityCallback = null

    // Mock useWorkoutStore
    vi.mocked(useWorkoutStore).mockReturnValue(mockStore as any)

    // Mock useWorkoutStore.getState for visibility handler
    vi.mocked(useWorkoutStore).getState = vi.fn().mockReturnValue({
      clearLoadingState: mockClearLoadingState,
    })

    // Mock useVisibilityChange to capture callback
    vi.mocked(useVisibilityChange).mockImplementation((callback) => {
      visibilityCallback = callback
    })

    // Mock default recommendation response
    mockGetCachedExerciseRecommendation.mockReturnValue(null)
    mockGetRecommendation.mockResolvedValue({
      Reps: 10,
      Weight: { Kg: 50, Lb: 110 },
      WarmupsCount: 2,
      Series: 3,
      IsNormalSets: true,
      NbPauses: 0,
    })
  })

  afterEach(() => {
    visibilityCallback = null
  })

  describe('Background/Foreground Transitions', () => {
    it('should clear loading states when app goes to background while loading recommendation', async () => {
      // Setup loading state - make recommendation take time
      mockGetRecommendation.mockReturnValue(
        new Promise((resolve) =>
          setTimeout(
            () =>
              resolve({
                Reps: 10,
                Weight: { Kg: 50, Lb: 110 },
                WarmupsCount: 2,
                Series: 3,
                IsNormalSets: true,
                NbPauses: 0,
              }),
            1000
          )
        )
      )

      const { result } = renderHook(() => useSetScreenLogic(123, false))

      // Verify hook is loading
      expect(result.current.isLoading).toBe(true)

      // Simulate app going to background while loading
      act(() => {
        visibilityCallback?.('hidden')
      })

      // Should clear loading state
      expect(mockClearLoadingState).toHaveBeenCalledWith(123)

      // Should cancel pending requests
      expect(mockSetLoader.cancelPendingRequests).toHaveBeenCalled()
    })

    it('should reload data when app returns from background on exercise page', async () => {
      const { result } = renderHook(() => useSetScreenLogic(123, false))

      // Wait for initial load
      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })

      // Verify we have a current exercise
      expect(result.current.currentExercise).toBeTruthy()
      expect(result.current.currentExercise?.Id).toBe(123)

      // Clear mocks before testing visibility change
      mockSetLoader.clearStuckStates.mockClear()
      mockSetLoader.loadExerciseSets.mockClear()
      mockGetRecommendation.mockClear()

      // Simulate app returning from background
      act(() => {
        visibilityCallback?.('visible')
      })

      // Should clear stuck states immediately
      expect(mockSetLoader.clearStuckStates).toHaveBeenCalled()

      // Should load exercise sets
      expect(mockSetLoader.loadExerciseSets).toHaveBeenCalledWith(123, true)

      // Should get recommendation
      await waitFor(() => {
        expect(mockGetRecommendation).toHaveBeenCalledWith(123)
      })
    })

    it('should handle multiple loading operations cleanup when visibility changes', async () => {
      const { result } = renderHook(() => useSetScreenLogic(123, false))

      // Wait for initial load
      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })

      // Clear mocks
      mockSetLoader.cancelPendingRequests.mockClear()
      mockSetLoader.clearStuckStates.mockClear()

      // Simulate rapid visibility changes
      act(() => {
        visibilityCallback?.('hidden')
      })

      expect(mockSetLoader.cancelPendingRequests).toHaveBeenCalledTimes(1)

      act(() => {
        visibilityCallback?.('visible')
      })

      expect(mockSetLoader.clearStuckStates).toHaveBeenCalledTimes(1)

      act(() => {
        visibilityCallback?.('hidden')
      })

      // Should handle cleanup gracefully
      expect(mockSetLoader.cancelPendingRequests).toHaveBeenCalledTimes(2)
    })

    it('should handle save operation in progress when backgrounded', async () => {
      const { result } = renderHook(() => useSetScreenLogic(123, false))

      // Start save operation
      act(() => {
        result.current.setData = { reps: '10', weight: '100' }
      })

      const savePromise = act(async () => {
        await result.current.handleSaveSet()
      })

      // Simulate backgrounding during save
      act(() => {
        visibilityCallback?.('hidden')
      })

      // Save should still complete or retry
      await expect(savePromise).resolves.not.toThrow()
    })
  })

  describe('Edge Cases', () => {
    it('should handle background during initial component mount', () => {
      // Start in background state
      visibilityCallback = vi.fn()

      const { unmount } = renderHook(() => useSetScreenLogic(123, false))

      // Should register visibility handler
      expect(useVisibilityChange).toHaveBeenCalled()

      // Unmount should not cause issues
      expect(() => unmount()).not.toThrow()
    })

    it('should handle rapid background/foreground cycles', async () => {
      renderHook(() => useSetScreenLogic(123, false))

      // Rapid cycles
      const cycles = Array.from({ length: 10 }, (_, i) => i)
      cycles.forEach(() => {
        act(() => {
          visibilityCallback?.('hidden')
          visibilityCallback?.('visible')
        })
      })

      // Should not cause memory leaks or excessive calls
      expect(
        mockSetLoader.cancelPendingRequests.mock.calls.length
      ).toBeLessThanOrEqual(20)
    })

    it('should handle component unmount while backgrounded', () => {
      const { unmount } = renderHook(() => useSetScreenLogic(123, false))

      // Background the app
      act(() => {
        visibilityCallback?.('hidden')
      })

      // Unmount should cleanup properly
      expect(() => unmount()).not.toThrow()
    })
  })
})
