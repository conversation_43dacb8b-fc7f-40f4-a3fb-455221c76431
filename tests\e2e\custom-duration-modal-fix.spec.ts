import { test, expect } from '@playwright/test'
import { WorkoutPage } from './pages/WorkoutPage'
import { ExercisePage } from './pages/ExercisePage'
import { setupAuthenticatedUser } from './helpers/auth-helper'

test.describe('Custom Duration Modal Fix', () => {
  test.beforeEach(async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 })
    await setupAuthenticatedUser(page)

    // Navigate to workout and start
    await page.goto('/workout')
    const workoutPage = new WorkoutPage(page)
    await workoutPage.waitForPageLoad()
    await workoutPage.startWorkout()

    // Navigate to exercise page
    await page.goto('/workout/exercise-v2/1')
    const exercisePage = new ExercisePage(page)
    await exercisePage.waitForPageLoad()

    // Save a set to trigger rest timer
    await page.fill('input[placeholder="Reps"]', '10')
    await page.fill('input[placeholder*="Weight"]', '100')
    await page.click('button:has-text("Save set")')

    // Wait for rest timer to appear
    await expect(page.getByTestId('rest-timer-container')).toBeVisible()
  })

  test('should fix modal interaction issues @critical', async ({ page }) => {
    // Open duration picker
    await page.click('[data-testid="duration-setting-button"]')
    await expect(page.getByTestId('duration-picker')).toBeVisible()

    // Click custom duration
    await page.click('[data-testid="duration-option-custom"]')

    // Check modal is visible and not grayed out
    const modal = page.getByTestId('custom-duration-modal')
    await expect(modal).toBeVisible()

    // Verify modal has proper z-index (should be higher than backdrop)
    const modalZIndex = await modal.evaluate((el) => {
      return window.getComputedStyle(el).zIndex
    })
    expect(parseInt(modalZIndex)).toBeGreaterThan(70)

    // Click on the modal content itself - should NOT close the modal
    await modal.click({ position: { x: 200, y: 100 } })
    await expect(modal).toBeVisible()

    // Input should be focused and functional
    const input = page.locator('#duration-input')
    await expect(input).toBeFocused()

    // Clear and type new value
    await input.selectAll()
    await input.type('120')
    await expect(input).toHaveValue('120')

    // Click confirm button - should work
    const confirmButton = page.locator('button:has-text("Confirm")')
    await expect(confirmButton).toBeVisible()
    await expect(confirmButton).toBeEnabled()

    await confirmButton.click()

    // Modal should close
    await expect(modal).not.toBeVisible()

    // Duration should be updated
    await expect(page.getByTestId('duration-setting')).toContainText('2:00')
  })

  test('should handle keyboard interaction properly', async ({ page }) => {
    // Open duration picker
    await page.click('[data-testid="duration-setting-button"]')
    await expect(page.getByTestId('duration-picker')).toBeVisible()

    // Click custom duration
    await page.click('[data-testid="duration-option-custom"]')

    // Check modal is visible
    const modal = page.getByTestId('custom-duration-modal')
    await expect(modal).toBeVisible()

    // Input should be focused
    const input = page.locator('#duration-input')
    await expect(input).toBeFocused()

    // Type new value and press Enter
    await input.selectAll()
    await input.type('150')
    await page.keyboard.press('Enter')

    // Modal should close
    await expect(modal).not.toBeVisible()

    // Duration should be updated
    await expect(page.getByTestId('duration-setting')).toContainText('2:30')
  })

  test('should close when clicking backdrop only', async ({ page }) => {
    // Open duration picker
    await page.click('[data-testid="duration-setting-button"]')
    await expect(page.getByTestId('duration-picker')).toBeVisible()

    // Click custom duration
    await page.click('[data-testid="duration-option-custom"]')

    // Check modal is visible
    const modal = page.getByTestId('custom-duration-modal')
    await expect(modal).toBeVisible()

    // Click on backdrop (outside modal) - should close
    await page.click('body', { position: { x: 10, y: 10 } })
    await expect(modal).not.toBeVisible()
  })
})
