import { useEffect, useState, useRef, useCallback } from 'react'

interface UseRecommendationRetryProps {
  isLoadingRecommendation: boolean
  recommendation: unknown
  refetchRecommendation: () => void | Promise<void>
}

interface UseRecommendationRetryReturn {
  retryCount: number
  hasExhaustedRetries: boolean
  handleManualRetry: () => void
  loadingMessage: string
}

export function useRecommendationRetry({
  isLoadingRecommendation,
  recommendation,
  refetchRecommendation,
}: UseRecommendationRetryProps): UseRecommendationRetryReturn {
  const [retryCount, setRetryCount] = useState(0)
  const [retryTimer, setRetryTimer] = useState<NodeJS.Timeout | null>(null)
  const [hasExhaustedRetries, setHasExhaustedRetries] = useState(false)

  const maxRetries = 3
  const loadingStartTime = useRef<number | null>(null)
  const lastVisibilityState = useRef(document.visibilityState)

  // Clear retry timer on cleanup
  useEffect(() => {
    return () => {
      if (retryTimer) {
        clearTimeout(retryTimer)
      }
    }
  }, [retryTimer])

  // Handle automatic retry with exponential backoff
  useEffect(() => {
    const retryTimeouts = [15000, 5000, 10000] // 15s, then 5s, then 10s

    // Reset retry state when recommendation loads successfully
    if (recommendation && retryCount > 0) {
      setRetryCount(0)
      setHasExhaustedRetries(false)
      loadingStartTime.current = null
    }

    // Start tracking loading time
    if (
      isLoadingRecommendation &&
      !recommendation &&
      !loadingStartTime.current
    ) {
      loadingStartTime.current = Date.now()
    }

    // Set up automatic retry
    if (
      isLoadingRecommendation &&
      !recommendation &&
      retryCount < maxRetries &&
      !retryTimer
    ) {
      const timeout = retryTimeouts[retryCount] || 15000

      const timer = setTimeout(() => {
        setRetryCount((prev) => prev + 1)
        refetchRecommendation()
        setRetryTimer(null)
      }, timeout)

      setRetryTimer(timer)

      return () => {
        clearTimeout(timer)
        setRetryTimer(null)
      }
    }

    // Mark as exhausted after all retries
    if (retryCount >= maxRetries && !recommendation && !hasExhaustedRetries) {
      setTimeout(() => {
        setHasExhaustedRetries(true)
      }, 15000) // Give final attempt 15s before showing error
    }

    return undefined
  }, [
    isLoadingRecommendation,
    recommendation,
    retryCount,
    refetchRecommendation,
    retryTimer,
    hasExhaustedRetries,
  ])

  // Handle visibility change (app returning from background)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (
        document.visibilityState === 'visible' &&
        lastVisibilityState.current === 'hidden' &&
        isLoadingRecommendation &&
        !recommendation
      ) {
        // App returned from background while loading - trigger retry
        refetchRecommendation()
      }
      lastVisibilityState.current = document.visibilityState
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [isLoadingRecommendation, recommendation, refetchRecommendation])

  // Reset retry state when manually retrying
  const handleManualRetry = useCallback(() => {
    setRetryCount(0)
    setHasExhaustedRetries(false)
    loadingStartTime.current = Date.now()
    refetchRecommendation()
  }, [refetchRecommendation])

  const loadingMessage =
    retryCount > 0
      ? 'Having trouble loading. Retrying...'
      : 'Loading exercise data...'

  return {
    retryCount,
    hasExhaustedRetries,
    handleManualRetry,
    loadingMessage,
  }
}
