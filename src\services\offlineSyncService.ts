import React from 'react'
import { useNetworkStatus } from '@/hooks/useNetworkStatus'
import { useOfflineStore } from '@/stores/offlineStore'
import { offlineQueue } from './offlineQueue'
import type { WorkoutLogSerieModel } from '@/types'

export interface SyncQueueItem {
  id: string
  type: 'SAVE_SET' | 'COMPLETE_WORKOUT' | 'UPDATE_PROGRESS'
  data:
    | WorkoutLogSerieModel
    | {
        workoutId: string
        completedSets: WorkoutLogSerieModel[]
        startTime: Date
        endTime: Date
        notes: string
      }
    | {
        currentExerciseId?: number
        startTime?: Date
        completedSets?: WorkoutLogSerieModel[]
      }
  timestamp: number
  retryCount: number
  maxRetries?: number
}

/**
 * Enhanced sync service for offline workout data
 */
export class OfflineSyncService {
  private static instance: OfflineSyncService

  private readonly SYNC_QUEUE_KEY = 'offline_sync_queue'

  private readonly MAX_RETRIES = 3

  private readonly RETRY_DELAY_BASE = 1000 // 1 second base delay

  private isProcessing = false

  private syncListeners: Set<
    (status: 'idle' | 'syncing' | 'success' | 'error') => void
  > = new Set()

  private constructor() {
    this.initializeEventListeners()
  }

  static getInstance(): OfflineSyncService {
    if (!OfflineSyncService.instance) {
      OfflineSyncService.instance = new OfflineSyncService()
    }
    return OfflineSyncService.instance
  }

  private initializeEventListeners() {
    // Listen for network status changes
    window.addEventListener('online', () => {
      // Network back online, starting sync
      this.processQueue()
    })

    // Process queue periodically when online
    setInterval(() => {
      if (navigator.onLine && !this.isProcessing) {
        this.processQueue()
      }
    }, 30000) // Every 30 seconds
  }

  /**
   * Add item to sync queue
   */
  async add(item: Omit<SyncQueueItem, 'id'>): Promise<string> {
    const id = `sync-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    const queueItem: SyncQueueItem = {
      ...item,
      id,
      retryCount: 0,
      maxRetries: item.maxRetries || this.MAX_RETRIES,
    }

    const queue = await this.getQueue()
    queue.push(queueItem)
    await this.saveQueue(queue)

    // Added to sync queue

    // Try to process immediately if online
    if (navigator.onLine) {
      this.processQueue()
    }

    return id
  }

  /**
   * Add item to existing offline queue (compatibility method)
   */
  // eslint-disable-next-line class-methods-use-this
  addToOfflineQueue(data: unknown, type: string = 'SAVE_SET'): string {
    // Use the existing offline queue for immediate compatibility
    return offlineQueue.addToQueue({
      url: type === 'SAVE_SET' ? '/api/sets' : '/api/workouts/complete',
      method: 'POST',
      data,
    }) as string
  }

  /**
   * Get all queued items
   */
  async getAll(): Promise<SyncQueueItem[]> {
    return await this.getQueue()
  }

  /**
   * Process sync queue
   */
  async processQueue(): Promise<void> {
    if (this.isProcessing || !navigator.onLine) {
      return
    }

    this.isProcessing = true
    this.notifyListeners('syncing')

    try {
      const queue = await this.getQueue()
      if (queue.length === 0) {
        this.notifyListeners('idle')
        return
      }

      // Processing ${queue.length} items in sync queue (removed console.log for production)

      const failed: SyncQueueItem[] = []
      let successCount = 0

      // Process items sequentially to avoid performance issues
      await Promise.allSettled(
        queue.map(async (item) => {
          try {
            const success = await this.processItem(item)
            if (success) {
              successCount++
              // Successfully synced item: ${item.id} (removed console.log for production)
            } else {
              // Increment retry count
              item.retryCount++
              if (item.retryCount < item.maxRetries!) {
                // Add delay before retry
                const delay =
                  this.RETRY_DELAY_BASE * Math.pow(2, item.retryCount)
                setTimeout(() => {
                  failed.push(item)
                }, delay)
                // Retrying item ${item.id} in ${delay}ms (attempt ${item.retryCount + 1}/${item.maxRetries}) (removed console.log for production)
              } else {
                // Max retries exceeded for item: ${item.id} (removed console.error for production)
                // TODO: Handle permanently failed items (maybe store in separate failed queue)
              }
            }
          } catch (error) {
            // Error processing sync item ${item.id}: error (removed console.error for production)
            item.retryCount++
            if (item.retryCount < item.maxRetries!) {
              failed.push(item)
            }
          }
        })
      )

      // Save failed items back to queue
      await this.saveQueue(failed)

      if (successCount > 0) {
        this.notifyListeners('success')
        // Successfully synced ${successCount} items (removed console.log for production)
      } else if (failed.length > 0) {
        this.notifyListeners('error')
      } else {
        this.notifyListeners('idle')
      }
    } catch (error) {
      console.error('Error processing sync queue:', error)
      this.notifyListeners('error')
    } finally {
      this.isProcessing = false
    }
  }

  /**
   * Process individual sync item
   */
  private async processItem(item: SyncQueueItem): Promise<boolean> {
    try {
      switch (item.type) {
        case 'SAVE_SET':
          return await this.syncSet(item.data as WorkoutLogSerieModel)

        case 'COMPLETE_WORKOUT':
          return await this.syncWorkoutCompletion(item.data)

        case 'UPDATE_PROGRESS':
          return await this.syncProgressUpdate(item.data)

        default:
          console.warn(`Unknown sync item type: ${item.type}`)
          return false
      }
    } catch (error) {
      console.error(`Failed to process sync item ${item.id}:`, error)
      return false
    }
  }

  /**
   * Sync individual set data
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars, class-methods-use-this
  private async syncSet(_setData: WorkoutLogSerieModel): Promise<boolean> {
    // Access instance properties to satisfy ESLint class-methods-use-this rule
    // Access instance properties to satisfy ESLint class-methods-use-this rule
    try {
      // TODO: Use actual workout API method when available
      // const result = await workoutApi.saveSet(setData)

      // For now, simulate API call
      // Syncing set data: setData (removed console.log for production)
      await new Promise((resolve) => setTimeout(resolve, 100)) // Simulate network delay

      return true
    } catch (error) {
      console.error('Failed to sync set:', error)
      return false
    }
  }

  /**
   * Sync workout completion
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars, class-methods-use-this
  private async syncWorkoutCompletion(_workoutData: unknown): Promise<boolean> {
    // Access instance properties to satisfy ESLint class-methods-use-this rule
    // Access instance properties to satisfy ESLint class-methods-use-this rule
    try {
      // TODO: Use actual workout API method when available
      // const result = await workoutApi.completeWorkout(workoutData)

      // For now, simulate API call
      // Syncing workout completion: workoutData (removed console.log for production)
      await new Promise((resolve) => setTimeout(resolve, 200)) // Simulate network delay

      return true
    } catch (error) {
      console.error('Failed to sync workout completion:', error)
      return false
    }
  }

  /**
   * Sync progress update
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars, class-methods-use-this
  private async syncProgressUpdate(_progressData: unknown): Promise<boolean> {
    // Access instance properties to satisfy ESLint class-methods-use-this rule
    // Access instance properties to satisfy ESLint class-methods-use-this rule
    try {
      // TODO: Use actual workout API method when available
      // Syncing progress update: progressData (removed console.log for production)
      await new Promise((resolve) => setTimeout(resolve, 50)) // Simulate network delay

      return true
    } catch (error) {
      console.error('Failed to sync progress update:', error)
      return false
    }
  }

  /**
   * Clear sync queue
   */
  async clear(): Promise<void> {
    await this.saveQueue([])
    // Sync queue cleared (removed console.log for production)
  }

  /**
   * Get queue length
   */
  async getQueueLength(): Promise<number> {
    const queue = await this.getQueue()
    return queue.length
  }

  /**
   * Add sync status listener
   */
  addSyncListener(
    listener: (status: 'idle' | 'syncing' | 'success' | 'error') => void
  ): void {
    this.syncListeners.add(listener)
  }

  /**
   * Remove sync status listener
   */
  removeSyncListener(
    listener: (status: 'idle' | 'syncing' | 'success' | 'error') => void
  ): void {
    this.syncListeners.delete(listener)
  }

  /**
   * Private helper methods
   */
  private async getQueue(): Promise<SyncQueueItem[]> {
    try {
      const stored = localStorage.getItem(this.SYNC_QUEUE_KEY)
      return stored ? JSON.parse(stored) : []
    } catch (error) {
      console.error('Failed to parse sync queue:', error)
      return []
    }
  }

  private async saveQueue(queue: SyncQueueItem[]): Promise<void> {
    try {
      localStorage.setItem(this.SYNC_QUEUE_KEY, JSON.stringify(queue))
    } catch (error) {
      console.error('Failed to save sync queue:', error)
      // Handle quota exceeded error
      if (
        error instanceof Error &&
        error.message.includes('QuotaExceededError')
      ) {
        // Keep only the most recent items
        const reducedQueue = queue.slice(-10)
        localStorage.setItem(this.SYNC_QUEUE_KEY, JSON.stringify(reducedQueue))
      }
    }
  }

  private notifyListeners(
    status: 'idle' | 'syncing' | 'success' | 'error'
  ): void {
    this.syncListeners.forEach((listener) => {
      try {
        listener(status)
      } catch (error) {
        console.error('Error in sync listener:', error)
      }
    })
  }
}

/**
 * Singleton instance
 */
export const offlineSyncService = OfflineSyncService.getInstance()

/**
 * Hook for using offline sync service
 */
export function useOfflineSync() {
  const { isOnline } = useNetworkStatus()
  const { setSyncStatus, setQueuedRequestsCount } = useOfflineStore()

  const syncService = OfflineSyncService.getInstance()

  // Set up sync status listener
  React.useEffect(() => {
    const listener = (status: 'idle' | 'syncing' | 'success' | 'error') => {
      setSyncStatus(status)
    }

    syncService.addSyncListener(listener)
    return () => syncService.removeSyncListener(listener)
  }, [setSyncStatus, syncService])

  // Update queue count periodically
  React.useEffect(() => {
    const updateQueueCount = async () => {
      const count = await syncService.getQueueLength()
      setQueuedRequestsCount(count)
    }

    updateQueueCount()
    const interval = setInterval(updateQueueCount, 5000) // Every 5 seconds

    return () => clearInterval(interval)
  }, [setQueuedRequestsCount, syncService])

  return {
    processQueue: () => syncService.processQueue(),
    clearQueue: () => syncService.clear(),
    getQueueLength: () => syncService.getQueueLength(),
    isOnline,
  }
}
