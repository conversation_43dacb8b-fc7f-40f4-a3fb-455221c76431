import { test, expect } from '@playwright/test'

test.describe('Rest Timer Layout - Mobile', () => {
  test.beforeEach(async ({ page }) => {
    // Mock login endpoint
    await page.route('**/api/Account/Login', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          LoginModel: {
            AccessToken: 'mock-token',
            RefreshToken: 'mock-refresh',
            UserId: 1,
          },
        }),
      })
    })

    // Mock user info
    await page.route('**/api/Account/GetUserInfo', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          UserInfo: {
            FirstName: 'Test',
            LastName: 'User',
            Email: '<EMAIL>',
            MassUnit: 'lbs',
          },
        }),
      })
    })

    // Mock program info
    await page.route('**/api/workout/GetUserProgramInfo', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          WorkoutProgram: {
            Name: 'Test Program',
            TotalWorkouts: 10,
            CompletedWorkouts: 5,
          },
        }),
      })
    })

    // Mock workout template
    await page.route(
      '**/api/workout/GetUserWorkoutTemplateGroup',
      async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            WorkoutTemplateGroups: [
              {
                WorkoutTemplates: [
                  {
                    Id: 1,
                    Name: 'Test Workout',
                    Exercises: [
                      {
                        Id: 1,
                        Name: 'Bench Press',
                        Equipment: { Name: 'Barbell' },
                      },
                    ],
                  },
                ],
              },
            ],
          }),
        })
      }
    )

    // Mock start workout
    await page.route(
      '**/api/WorkoutSession/StartWorkoutSession',
      async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            WorkoutSession: {
              Id: 1,
              WorkoutTemplateId: 1,
            },
          }),
        })
      }
    )

    // Login first
    await page.goto('/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')
    await page.waitForURL('**/workout')

    // Mock exercise endpoints for navigation
    await page.route('**/api/Exercise/GetExerciseById*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Exercise: {
            Id: 1,
            Name: 'Bench Press',
            Equipment: { Name: 'Barbell' },
          },
        }),
      })
    })

    // Mock recommendations
    await page.route('**/api/Exercise/GetRecommendation*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Recommendation: {
            Reps: 10,
            Weight: { Kg: 60, Lb: 135 },
            WeightIncrement: 5,
            RepsIncrement: 1,
          },
        }),
      })
    })

    // Mock save set
    await page.route('**/api/WorkoutLog/CreateOrUpdateSet', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          SetLog: {
            Id: 1,
            Reps: 10,
            Weight: { Kg: 60, Lb: 135 },
          },
        }),
      })
    })

    // Navigate to exercise page
    await page.goto('/workout/exercise-v2/1')
    await page.waitForSelector('[data-testid="current-set-card"]')
  })

  test('should display compact rest timer layout with inline "Rest" text', async ({
    page,
  }) => {
    // Save a set to trigger rest timer
    await page.fill('input[placeholder="Reps"]', '10')
    await page.fill('input[placeholder*="Weight"]', '135')
    await page.click('button:has-text("Save set")')

    // Wait for rest timer to appear
    await page.waitForSelector('[data-testid="rest-timer-container"]')

    // Verify timer row has both time and "Rest" text inline
    const timerRow = await page.locator('[data-testid="timer-row"]')
    await expect(timerRow).toBeVisible()

    // Both elements should be in the same row
    const timeDisplay = await timerRow.locator('[data-testid="time-display"]')
    const restText = await timerRow.locator('text=Rest')

    await expect(timeDisplay).toBeVisible()
    await expect(restText).toBeVisible()

    // Verify they're on the same horizontal level
    const timeBox = await timeDisplay.boundingBox()
    const restBox = await restText.boundingBox()

    expect(timeBox).toBeTruthy()
    expect(restBox).toBeTruthy()

    // Y positions should be similar (within 10px tolerance for alignment)
    if (timeBox && restBox) {
      expect(Math.abs(timeBox.y - restBox.y)).toBeLessThan(10)
    }

    // Verify compact layout - only 2 rows
    const rows = await page.locator('[data-testid$="-row"]').all()
    expect(rows).toHaveLength(2)
  })

  test('should have wider buttons for better touch targets', async ({
    page,
  }) => {
    // Save a set to trigger rest timer
    await page.fill('input[placeholder="Reps"]', '10')
    await page.fill('input[placeholder*="Weight"]', '135')
    await page.click('button:has-text("Save set")')

    // Wait for rest timer
    await page.waitForSelector('[data-testid="rest-timer-container"]')

    // Check button widths
    const durationButton = await page.locator(
      '[data-testid="duration-setting-button"]'
    )
    const hideButton = await page.locator('button:has-text("Hide")')

    const durationBox = await durationButton.boundingBox()
    const hideBox = await hideButton.boundingBox()

    // Buttons should be wider (at least 120px for good touch targets)
    expect(durationBox?.width).toBeGreaterThan(120)
    expect(hideBox?.width).toBeGreaterThan(100)
  })

  test('should have appropriate bottom padding when timer is active', async ({
    page,
  }) => {
    // Initially, no extra padding
    const contentArea = await page.locator('.flex-1.flex.flex-col.px-4')
    await expect(contentArea).not.toHaveClass(/pb-28/)

    // Save a set to trigger rest timer
    await page.fill('input[placeholder="Reps"]', '10')
    await page.fill('input[placeholder*="Weight"]', '135')
    await page.click('button:has-text("Save set")')

    // Wait for rest timer
    await page.waitForSelector('[data-testid="rest-timer-container"]')

    // Now should have padding
    await expect(contentArea).toHaveClass(/pb-28/)

    // Verify user can scroll to see all content
    const lastElement = await page.locator('[data-testid="todays-sets"]')
    await lastElement.scrollIntoViewIfNeeded()

    // The last element should be fully visible
    await expect(lastElement).toBeInViewport({ ratio: 0.9 })
  })
})
