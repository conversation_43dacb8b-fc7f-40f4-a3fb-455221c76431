/* eslint-disable no-restricted-syntax, no-await-in-loop, class-methods-use-this, @typescript-eslint/no-explicit-any */
/**
 * Base storage adapter for browser storage APIs (localStorage, sessionStorage)
 *
 * This abstract class provides common functionality for browser storage adapters:
 * - Serialization/deserialization
 * - Quo<PERSON> exceeded error handling
 * - Key prefixing for isolation
 * - Metadata storage strategy
 */

import type { CacheAdapter, CacheEntry, CacheMetadata } from '../types'
import {
  CacheAdapterError,
  CacheQuotaExceededError,
  CacheSerializationError,
} from '../types'

/**
 * Configuration for base storage adapter
 */
interface BaseStorageAdapterConfig {
  /** Key prefix for isolation */
  keyPrefix?: string
  /** Whether to compress large values */
  enableCompression?: boolean
  /** Compression threshold in bytes */
  compressionThreshold?: number
}

/**
 * Abstract base class for browser storage adapters
 */
export abstract class BaseStorageAdapter implements CacheAdapter {
  protected readonly storage: Storage

  protected readonly config: Required<BaseStorageAdapterConfig>

  constructor(storage: Storage, config: BaseStorageAdapterConfig = {}) {
    this.storage = storage
    this.config = {
      keyPrefix: config.keyPrefix ?? 'drmuscle-cache:',
      enableCompression: config.enableCompression ?? false,
      compressionThreshold: config.compressionThreshold ?? 1024,
    }
  }

  /**
   * Get a value from storage
   */
  async get<T>(key: string): Promise<CacheEntry<T> | null> {
    try {
      const fullKey = this.buildKey(key)
      const serialized = this.storage.getItem(fullKey)

      if (!serialized) {
        return null
      }

      const entry = this.deserializeEntry<T>(serialized)

      // Check if expired
      if (this.isExpired(entry.metadata)) {
        await this.delete(key)
        return null
      }

      // Update access time
      entry.metadata.accessed = Date.now()
      await this.set(key, entry.value, entry.metadata)

      return entry
    } catch (error) {
      if (error instanceof CacheSerializationError) {
        throw error
      }
      throw new CacheAdapterError(
        `Failed to get entry for key "${key}"`,
        error instanceof Error ? error : undefined
      )
    }
  }

  /**
   * Set a value in storage
   */
  async set<T>(key: string, value: T, metadata: CacheMetadata): Promise<void> {
    try {
      const fullKey = this.buildKey(key)
      const entry: CacheEntry<T> = { value, metadata }

      // Calculate size before compression
      const serialized = this.serializeEntry(entry)
      const size = this.calculateSize(serialized)

      // Update metadata with actual size
      entry.metadata = { ...metadata, size }

      // Compress if needed
      const finalSerialized = this.shouldCompress(serialized)
        ? this.compressValue(serialized)
        : serialized

      this.storage.setItem(fullKey, finalSerialized)
    } catch (error) {
      if (this.isQuotaExceededError(error)) {
        await this.handleQuotaExceeded(key, value, metadata)
      } else if (error instanceof CacheSerializationError) {
        throw error
      } else {
        throw new CacheAdapterError(
          `Failed to set entry for key "${key}"`,
          error instanceof Error ? error : undefined
        )
      }
    }
  }

  /**
   * Delete a value from storage
   */
  async delete(key: string): Promise<void> {
    try {
      const fullKey = this.buildKey(key)
      this.storage.removeItem(fullKey)
    } catch (error) {
      throw new CacheAdapterError(
        `Failed to delete entry for key "${key}"`,
        error instanceof Error ? error : undefined
      )
    }
  }

  /**
   * Clear all entries from storage
   */
  async clear(): Promise<void> {
    try {
      const keysToRemove: string[] = []

      for (let i = 0; i < this.storage.length; i++) {
        const key = this.storage.key(i)
        if (key && key.startsWith(this.config.keyPrefix)) {
          keysToRemove.push(key)
        }
      }

      for (const key of keysToRemove) {
        this.storage.removeItem(key)
      }
    } catch (error) {
      throw new CacheAdapterError(
        'Failed to clear storage',
        error instanceof Error ? error : undefined
      )
    }
  }

  /**
   * Get all keys in storage
   */
  async getAllKeys(): Promise<string[]> {
    try {
      const keys: string[] = []

      for (let i = 0; i < this.storage.length; i++) {
        const key = this.storage.key(i)
        if (key && key.startsWith(this.config.keyPrefix)) {
          const originalKey = this.extractKey(key)

          // Check if entry is expired
          try {
            const entry = await this.get(originalKey)
            if (entry) {
              keys.push(originalKey)
            }
          } catch {
            // Skip invalid entries
          }
        }
      }

      return keys
    } catch (error) {
      throw new CacheAdapterError(
        'Failed to get all keys',
        error instanceof Error ? error : undefined
      )
    }
  }

  /**
   * Get total size of storage
   */
  async getSize(): Promise<number> {
    try {
      let totalSize = 0

      for (let i = 0; i < this.storage.length; i++) {
        const key = this.storage.key(i)
        if (key && key.startsWith(this.config.keyPrefix)) {
          const value = this.storage.getItem(key)
          if (value) {
            totalSize += this.calculateSize(value)
          }
        }
      }

      return totalSize
    } catch (error) {
      throw new CacheAdapterError(
        'Failed to calculate storage size',
        error instanceof Error ? error : undefined
      )
    }
  }

  /**
   * Check if adapter supports batch operations
   */
  supportsBatch(): boolean {
    return false // Browser storage doesn't support native batch operations
  }

  /**
   * Build a prefixed key
   */
  protected buildKey(key: string): string {
    return `${this.config.keyPrefix}${key}`
  }

  /**
   * Extract original key from prefixed key
   */
  protected extractKey(fullKey: string): string {
    return fullKey.slice(this.config.keyPrefix.length)
  }

  /**
   * Serialize a cache entry
   */
  protected serializeEntry<T>(entry: CacheEntry<T>): string {
    try {
      return JSON.stringify(entry)
    } catch (error) {
      throw new CacheSerializationError(
        'Failed to serialize cache entry',
        error instanceof Error ? error : undefined
      )
    }
  }

  /**
   * Deserialize a cache entry
   */
  protected deserializeEntry<T>(serialized: string): CacheEntry<T> {
    try {
      // Handle compressed data
      const decompressed = this.isCompressed(serialized)
        ? this.decompressValue(serialized)
        : serialized

      return JSON.parse(decompressed)
    } catch (error) {
      throw new CacheSerializationError(
        'Failed to deserialize cache entry',
        error instanceof Error ? error : undefined
      )
    }
  }

  /**
   * Calculate size of serialized data
   */
  protected calculateSize(serialized: string): number {
    // UTF-16 encoding, so 2 bytes per character
    return serialized.length * 2
  }

  /**
   * Check if an entry is expired
   */
  protected isExpired(metadata: CacheMetadata): boolean {
    return metadata.expires > 0 && Date.now() > metadata.expires
  }

  /**
   * Check if error is quota exceeded
   */
  protected isQuotaExceededError(error: any): boolean {
    return (
      error instanceof DOMException &&
      (error.code === 22 || // QUOTA_EXCEEDED_ERR
        error.code === 1014 || // NS_ERROR_DOM_QUOTA_REACHED (Firefox)
        error.name === 'QuotaExceededError')
    )
  }

  /**
   * Handle quota exceeded error
   */
  protected async handleQuotaExceeded<T>(
    key: string,
    value: T,
    metadata: CacheMetadata
  ): Promise<void> {
    try {
      // Try to free up space by clearing expired entries
      await this.clearExpiredEntries()

      // Try again
      const fullKey = this.buildKey(key)
      const entry: CacheEntry<T> = { value, metadata }
      const serialized = this.serializeEntry(entry)

      this.storage.setItem(fullKey, serialized)
    } catch (error) {
      throw new CacheQuotaExceededError(
        `Storage quota exceeded for key "${key}"`,
        error instanceof Error ? error : undefined
      )
    }
  }

  /**
   * Clear expired entries to free up space
   */
  protected async clearExpiredEntries(): Promise<number> {
    const expiredKeys: string[] = []

    for (let i = 0; i < this.storage.length; i++) {
      const key = this.storage.key(i)
      if (key && key.startsWith(this.config.keyPrefix)) {
        try {
          const serialized = this.storage.getItem(key)
          if (serialized) {
            const entry = this.deserializeEntry(serialized)
            if (this.isExpired(entry.metadata)) {
              expiredKeys.push(key)
            }
          }
        } catch {
          // Remove invalid entries too
          expiredKeys.push(key)
        }
      }
    }

    for (const key of expiredKeys) {
      this.storage.removeItem(key)
    }

    return expiredKeys.length
  }

  /**
   * Check if value should be compressed
   */
  protected shouldCompress(serialized: string): boolean {
    return (
      this.config.enableCompression &&
      serialized.length > this.config.compressionThreshold
    )
  }

  /**
   * Compress a value (placeholder - would use actual compression library)
   */
  protected compressValue(value: string): string {
    // In a real implementation, this would use a compression library like pako
    // For now, just add a compression marker
    return `__COMPRESSED__${value}`
  }

  /**
   * Check if value is compressed
   */
  protected isCompressed(value: string): boolean {
    return value.startsWith('__COMPRESSED__')
  }

  /**
   * Decompress a value (placeholder - would use actual compression library)
   */
  protected decompressValue(value: string): string {
    // In a real implementation, this would use a compression library like pako
    // For now, just remove the compression marker
    return value.slice('__COMPRESSED__'.length)
  }

  /**
   * Get available storage space (if supported)
   */
  protected async getAvailableSpace(): Promise<number | null> {
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      try {
        const estimate = await navigator.storage.estimate()
        if (estimate.quota && estimate.usage) {
          return estimate.quota - estimate.usage
        }
      } catch {
        // Ignore errors
      }
    }
    return null
  }
}
