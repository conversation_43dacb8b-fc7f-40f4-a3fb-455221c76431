import { test, expect, devices } from '@playwright/test'
import type { BrowserContext, Page } from '@playwright/test'

test.describe('StatusIndicators Cross-Browser Compatibility', () => {
  const createTestPage = async (context: BrowserContext): Promise<Page> => {
    const page = await context.newPage()

    await page.setContent(`
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
        <title>StatusIndicators Test</title>
        <style>
          .mb-4 { margin-bottom: 1rem; }
          .text-center { text-align: center; }
          .space-y-2 > * + * { margin-top: 0.5rem; }
          .flex { display: flex; }
          .flex-wrap { flex-wrap: wrap; }
          .justify-center { justify-content: center; }
          .gap-2 { gap: 0.5rem; }
          .inline-flex { display: inline-flex; }
          .items-center { align-items: center; }
          .px-4 { padding-left: 1rem; padding-right: 1rem; }
          .py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
          .min-h-52 { min-height: 52px; }
          .bg-bg-secondary { background-color: #f3f4f6; }
          .rounded-lg { border-radius: 0.5rem; }
          .text-text-secondary { color: #6b7280; }
          .text-sm { font-size: 0.875rem; }
          * { touch-action: manipulation; }
        </style>
      </head>
      <body>
        <div id="app">
          <div data-testid="status-indicators-container" class="mb-4 text-center space-y-2">
            <div class="flex flex-wrap justify-center gap-2">
              <div role="status" class="inline-flex items-center gap-2 px-4 py-3 min-h-52 bg-bg-secondary rounded-lg" aria-label="Loading exercise data">
                <span class="text-text-secondary text-sm">Loading exercises... 75%</span>
              </div>
            </div>
          </div>
        </div>
      </body>
      </html>
    `)

    return page
  }

  test('Chrome Mobile - Touch targets and rendering', async ({ browser }) => {
    const context = await browser.newContext({
      ...devices['Pixel 5'],
      locale: 'en-US',
    })
    const page = await createTestPage(context)

    const container = page.locator(
      '[data-testid="status-indicators-container"]'
    )
    await expect(container).toBeVisible()

    const statusBadge = page.locator('[role="status"]')
    await expect(statusBadge).toBeVisible()

    const boundingBox = await statusBadge.boundingBox()
    expect(boundingBox?.height).toBeGreaterThanOrEqual(52)

    await statusBadge.tap()
    await expect(statusBadge.locator('span')).toContainText('Loading exercises')

    await context.close()
  })

  test('Safari Mobile - Viewport and CSS compatibility', async ({
    browser,
  }) => {
    const context = await browser.newContext({
      ...devices['iPhone 13'],
      locale: 'en-US',
    })
    const page = await createTestPage(context)

    const container = page.locator(
      '[data-testid="status-indicators-container"]'
    )
    await expect(container).toBeVisible()

    const statusBadge = page.locator('[role="status"]')
    await expect(statusBadge).toBeVisible()

    const styles = await container.evaluate((el) => {
      const computed = window.getComputedStyle(el)
      return {
        display: computed.display,
        textAlign: computed.textAlign,
        marginBottom: computed.marginBottom,
      }
    })

    expect(styles.display).not.toBe('none')
    expect(styles.textAlign).toBe('center')

    await context.close()
  })
})
