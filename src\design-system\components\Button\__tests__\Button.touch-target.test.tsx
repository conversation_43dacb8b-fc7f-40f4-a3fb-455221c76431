import React from 'react'
import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { Button } from '../Button'
import { ThemeProvider } from '../../../theme'

describe('Button Touch Target Compliance', () => {
  const renderWithTheme = (ui: React.ReactElement) => {
    return render(<ThemeProvider>{ui}</ThemeProvider>)
  }

  describe('52px minimum touch target requirement', () => {
    it('should have minimum 52px height for small size', () => {
      renderWithTheme(<Button size="sm">Small Button</Button>)
      const button = screen.getByRole('button')

      // Test rationale: CLAUDE.md requires 52px minimum touch targets
      // Verify h-13 class is applied (52px = 13 * 4px)
      expect(button).toHaveClass('h-13')
    })

    it('should have minimum 52px width for all sizes', () => {
      const sizes = ['sm', 'md', 'lg'] as const

      sizes.forEach((size) => {
        const { container } = renderWithTheme(<Button size={size}>X</Button>)
        const button = container.querySelector('button')

        // Now updated to min-w-[52px] for compliance
        expect(button).toHaveClass('min-w-[52px]')
      })
    })

    it('should maintain 52px touch target in disabled state', () => {
      renderWithTheme(
        <Button size="sm" disabled>
          Disabled
        </Button>
      )
      const button = screen.getByRole('button')

      // Verify h-13 class is maintained even when disabled
      expect(button).toHaveClass('h-13')
      expect(button).toBeDisabled()
    })

    it('should have h-13 class for small size (52px)', () => {
      renderWithTheme(<Button size="sm">Small</Button>)
      const button = screen.getByRole('button')

      // Current: h-12 (48px), Expected: h-13 (52px)
      expect(button).toHaveClass('h-13')
    })

    it('should maintain proper sizes for md and lg variants', () => {
      // md should remain h-14 (56px) - already compliant
      const { rerender } = renderWithTheme(<Button size="md">Medium</Button>)
      let button = screen.getByRole('button')
      expect(button).toHaveClass('h-14')

      // lg should remain h-16 (64px) - already compliant
      rerender(
        <ThemeProvider>
          <Button size="lg">Large</Button>
        </ThemeProvider>
      )
      button = screen.getByRole('button')
      expect(button).toHaveClass('h-16')
    })
  })

  describe('Touch target with different content', () => {
    it('should maintain 52px minimum with single character', () => {
      renderWithTheme(<Button size="sm">X</Button>)
      const button = screen.getByRole('button')

      expect(button).toHaveClass('min-w-[52px]')
      expect(button).toHaveClass('h-13')
    })

    it('should expand beyond 52px with long content', () => {
      renderWithTheme(
        <Button size="sm">This is a very long button text</Button>
      )
      const button = screen.getByRole('button')

      // Should still have minimum classes applied
      expect(button).toHaveClass('min-w-[52px]')
      expect(button).toHaveClass('h-13')
    })
  })

  describe('Touch target class verification', () => {
    it('should update sizeClasses object to use h-13 for sm', () => {
      const button = renderWithTheme(<Button size="sm">Test</Button>)
      const element = button.container.querySelector('button')

      // Verify the class string includes h-13 instead of h-12
      const classes = element?.className || ''
      expect(classes).toContain('h-13')
      expect(classes).not.toContain('h-12')
    })
  })
})
