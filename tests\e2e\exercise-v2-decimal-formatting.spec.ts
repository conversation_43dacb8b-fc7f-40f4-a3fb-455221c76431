import { test, expect } from '@playwright/test'
import { setupAuthenticatedUser } from './helpers/auth-helper'

test.describe('Exercise V2 - Weight Decimal Formatting', () => {
  test.beforeEach(async ({ page }) => {
    await setupAuthenticatedUser(page)
  })

  test("should format weight values with proper decimal places in Today's Sets", async ({
    page,
  }) => {
    // Mock API responses with excessive decimal places
    await page.route(
      '**/api/workout/get-active-workout-mobile',
      async (route) => {
        await route.fulfill({
          status: 200,
          json: {
            Id: 1,
            Label: 'Test Workout',
            Date: new Date().toISOString(),
            WorkoutExercises: [
              {
                Id: 1,
                ExerciseId: 100,
                Label: 'Bench Press',
                IsFinished: false,
                Order: 1,
              },
            ],
          },
        })
      }
    )

    await page.route(
      '**/api/workout/exercise/100/recommendation',
      async (route) => {
        await route.fulfill({
          status: 200,
          json: {
            ExerciseId: 100,
            FirstWorkSetWeight: { Kg: 61.123456789, Lb: 135.123456789 },
            FirstWorkSetReps: 10,
            Sets: {
              work: [
                {
                  Id: 1,
                  Reps: 10,
                  Weight: { Kg: 61.123456789, Lb: 135.123456789 },
                  IsWarmups: false,
                },
                {
                  Id: 2,
                  Reps: 8,
                  Weight: { Kg: 63.987654321, Lb: 141.987654321 },
                  IsWarmups: false,
                },
              ],
              warmup: [
                {
                  Id: -1001,
                  WarmUpReps: 5,
                  WarmUpWeightSet: { Kg: 30.567891234, Lb: 67.456789123 },
                  Weight: { Kg: 0, Lb: 0 },
                  IsWarmups: true,
                },
              ],
            },
            NbWarmupSets: 1,
            NbSets: 2,
          },
        })
      }
    )

    // Navigate to exercise page
    await page.goto('/workout/exercise-v2/100?exerciseName=Bench%20Press')

    // Wait for the page to load
    await page.waitForSelector("text=Today's sets", { timeout: 10000 })

    // Check warmup set formatting (should show max 2 decimal places)
    const warmupSet = page
      .locator('[data-testid="set-row"]')
      .filter({ hasText: 'W1' })
    await expect(warmupSet).toContainText('5 reps')
    await expect(warmupSet).toContainText('67.46 lbs') // Should be formatted to 2 decimals

    // Check work set formatting
    const workSet1 = page
      .locator('[data-testid="set-row"]')
      .filter({ hasText: 'Set 1' })
    await expect(workSet1).toContainText('10 reps')
    await expect(workSet1).toContainText('135.12 lbs') // Should be formatted to 2 decimals

    const workSet2 = page
      .locator('[data-testid="set-row"]')
      .filter({ hasText: 'Set 2' })
    await expect(workSet2).toContainText('8 reps')
    await expect(workSet2).toContainText('141.99 lbs') // Should be formatted to 2 decimals

    // Verify no excessive decimals are displayed
    await expect(page.locator('text=/\\d+\\.\\d{3,}/')).toHaveCount(0)
  })

  test('should format weights correctly in kg units', async ({ page }) => {
    // Mock user with kg preference
    await page.evaluate(() => {
      const authStore = JSON.parse(localStorage.getItem('auth-store') || '{}')
      authStore.state.user.MassUnit = 'kg'
      localStorage.setItem('auth-store', JSON.stringify(authStore))
    })

    await page.route(
      '**/api/workout/get-active-workout-mobile',
      async (route) => {
        await route.fulfill({
          status: 200,
          json: {
            Id: 1,
            Label: 'Test Workout',
            Date: new Date().toISOString(),
            WorkoutExercises: [
              {
                Id: 1,
                ExerciseId: 100,
                Label: 'Bench Press',
                IsFinished: false,
                Order: 1,
              },
            ],
          },
        })
      }
    )

    await page.route(
      '**/api/workout/exercise/100/recommendation',
      async (route) => {
        await route.fulfill({
          status: 200,
          json: {
            ExerciseId: 100,
            FirstWorkSetWeight: { Kg: 60.0, Lb: 132.28 },
            FirstWorkSetReps: 10,
            Sets: {
              work: [
                {
                  Id: 1,
                  Reps: 10,
                  Weight: { Kg: 60.0, Lb: 132.28 },
                  IsWarmups: false,
                },
              ],
              warmup: [],
            },
            NbWarmupSets: 0,
            NbSets: 1,
          },
        })
      }
    )

    await page.goto('/workout/exercise-v2/100?exerciseName=Bench%20Press')
    await page.waitForSelector("text=Today's sets")

    // Check that trailing zeros are removed
    const workSet = page
      .locator('[data-testid="set-row"]')
      .filter({ hasText: 'Set 1' })
    await expect(workSet).toContainText('60 kg') // Not 60.00 kg
  })
})
