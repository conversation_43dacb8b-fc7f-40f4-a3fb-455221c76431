import { test, expect, Page } from '@playwright/test'

test.describe('Background Refresh Tests', () => {
  let page: Page
  const TEST_USER = {
    email: '<EMAIL>',
    password: 'Dr123456',
  }

  test.beforeEach(async ({ page: p }) => {
    page = p

    // Login first
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')
  })

  test('should refresh exercise recommendations when returning from background', async () => {
    // Navigate to workout
    await page.click('button:has-text("Continue to workout")')
    await page.waitForURL('/workout')

    // Wait for initial prefetch
    await page.waitForTimeout(2000)

    // Verify initial status indicators
    const initialPrefetchCount = await page
      .locator('[data-testid="prefetched-count"]')
      .textContent()
    expect(Number(initialPrefetchCount)).toBeGreaterThan(0)

    // Mock API response to verify refresh happens
    let refreshCalled = false
    await page.route('**/GetWorkoutRecommendation*', (route) => {
      refreshCalled = true
      route.continue()
    })

    // Simulate app going to background and returning
    // Note: Page Visibility API can't be directly triggered in Playwright
    // but we can test the hook is connected by calling the store methods directly
    await page.evaluate(() => {
      // Trigger visibility change by dispatching event
      const event = new Event('visibilitychange')
      Object.defineProperty(document, 'visibilityState', {
        value: 'hidden',
        configurable: true,
      })
      document.dispatchEvent(event)

      // Simulate returning to foreground after 1 second
      setTimeout(() => {
        Object.defineProperty(document, 'visibilityState', {
          value: 'visible',
          configurable: true,
        })
        document.dispatchEvent(new Event('visibilitychange'))
      }, 1000)
    })

    // Wait for refresh to complete
    await page.waitForTimeout(3000)

    // Verify refresh was triggered
    expect(refreshCalled).toBe(true)

    // Verify status indicators show refresh activity
    const smartSchedulingActive = await page
      .locator('[data-testid="smart-scheduling-active"]')
      .isVisible()
    expect(smartSchedulingActive).toBe(true)
  })

  test('should not refresh when no workout session exists', async () => {
    // Stay on program page (no workout session)
    await page.waitForURL('/program')

    // Mock API to verify no refresh happens
    let refreshCalled = false
    await page.route('**/GetWorkoutRecommendation*', (route) => {
      refreshCalled = true
      route.continue()
    })

    // Simulate visibility change
    await page.evaluate(() => {
      const event = new Event('visibilitychange')
      Object.defineProperty(document, 'visibilityState', {
        value: 'hidden',
        configurable: true,
      })
      document.dispatchEvent(event)

      setTimeout(() => {
        Object.defineProperty(document, 'visibilityState', {
          value: 'visible',
          configurable: true,
        })
        document.dispatchEvent(new Event('visibilitychange'))
      }, 1000)
    })

    // Wait to ensure no refresh happens
    await page.waitForTimeout(2000)

    // Verify no refresh was triggered
    expect(refreshCalled).toBe(false)
  })

  test('should handle refresh errors gracefully', async () => {
    // Navigate to workout
    await page.click('button:has-text("Continue to workout")')
    await page.waitForURL('/workout')

    // Mock API to return error
    await page.route('**/GetWorkoutRecommendation*', (route) => {
      route.fulfill({ status: 500 })
    })

    // Simulate visibility change
    await page.evaluate(() => {
      const event = new Event('visibilitychange')
      Object.defineProperty(document, 'visibilityState', {
        value: 'hidden',
        configurable: true,
      })
      document.dispatchEvent(event)

      setTimeout(() => {
        Object.defineProperty(document, 'visibilityState', {
          value: 'visible',
          configurable: true,
        })
        document.dispatchEvent(new Event('visibilitychange'))
      }, 1000)
    })

    // Wait for refresh attempt
    await page.waitForTimeout(3000)

    // App should continue working despite refresh error
    // Verify no crash and UI is still responsive
    const exerciseList = await page.locator('[data-testid="exercise-list"]')
    await expect(exerciseList).toBeVisible()

    // Verify error count increases in status indicators
    const errorCount = await page
      .locator('[data-testid="error-count"]')
      .textContent()
    expect(Number(errorCount)).toBeGreaterThan(0)
  })
})
