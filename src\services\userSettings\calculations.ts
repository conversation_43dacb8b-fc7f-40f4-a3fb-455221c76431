/**
 * User Settings Calculations
 * Helper functions for calculating user settings based on program data
 */

/**
 * Safe localStorage access that handles Safari private mode and other restrictions
 */
function safeLocalStorageGetItem(key: string): string | null {
  try {
    return localStorage.getItem(key)
  } catch (error) {
    // Safari private mode throws QuotaExceededError
    // Other browsers may throw SecurityError or other errors
    console.warn(`localStorage access failed for key "${key}":`, error)
    return null
  }
}

function safeLocalStorageSetItem(key: string, value: string): boolean {
  try {
    localStorage.setItem(key, value)
    return true
  } catch (error) {
    // Safari private mode throws QuotaExceededError
    console.warn(`localStorage write failed for key "${key}":`, error)
    return false
  }
}

/**
 * Calculate strength phase status based on mobile app logic
 */
export function calculateStrengthPhase(): {
  isStrengthPhase: boolean
  isFirstStrengthPhase: boolean
} {
  try {
    // Get program data from localStorage with safe access
    const programData = safeLocalStorageGetItem('selectedProgram')
    if (!programData) {
      return { isStrengthPhase: false, isFirstStrengthPhase: false }
    }

    const program = JSON.parse(programData)
    const programName = program.name || ''
    const userAge = program.userAge || 30
    const remainingWorkouts = program.remainingWorkouts || 50
    const totalWorkouts = program.totalWorkouts || 100
    // Skip if bodyweight/bands program or strength phase disabled
    if (
      programName.toLowerCase().includes('bodyweight') ||
      programName.toLowerCase().includes('bands')
    ) {
      return { isStrengthPhase: false, isFirstStrengthPhase: false }
    }

    // Calculate days based on program type (following mobile app logic)
    let xDays = 3 // default
    if (
      programName.includes('Split') ||
      programName.includes('Upper') ||
      programName.includes('Lower')
    ) {
      if (userAge < 30) {
        xDays = 5
      } else if (userAge < 50) {
        xDays = 4
      } else {
        xDays = 3
      }
    } else if (
      programName.includes('full-body') ||
      programName.includes('PL')
    ) {
      if (userAge < 30) {
        xDays = 4
      } else if (userAge < 50) {
        xDays = 3
      } else {
        xDays = 2
      }
    } else if (programName.includes('PPL')) {
      xDays = 6
    }

    const requiredWorkoutsForStrength = xDays * 3
    const completedWorkouts = totalWorkouts - remainingWorkouts
    const strengthPhaseThreshold = totalWorkouts - requiredWorkoutsForStrength

    return {
      isStrengthPhase: completedWorkouts >= strengthPhaseThreshold,
      isFirstStrengthPhase: completedWorkouts === strengthPhaseThreshold,
    }
  } catch (error) {
    // Handle JSON parse errors or other issues gracefully
    console.warn('Error calculating strength phase:', error)
    return { isStrengthPhase: false, isFirstStrengthPhase: false }
  }
}

/**
 * Check if user is on free plan (following mobile app logic)
 */
export function calculateIsFreePlan(): boolean {
  try {
    // Check for DailyReset in localStorage (mobile app pattern)
    const dailyReset = safeLocalStorageGetItem('DailyReset')
    // Check if it's 'true' string or a number > 0
    if (dailyReset === 'true') return true
    if (dailyReset && !Number.isNaN(parseInt(dailyReset))) {
      return parseInt(dailyReset) > 0
    }
    return false
  } catch (error) {
    console.warn('Error calculating free plan status:', error)
    return false // Default to not free plan if localStorage unavailable
  }
}

/**
 * Calculate light session days based on last workout date
 */
export function calculateLightSessionDays(): number | null {
  try {
    // Try to get last workout date from localStorage with safe access
    const lastWorkoutDate = safeLocalStorageGetItem('lastWorkoutCompletedDate')
    if (!lastWorkoutDate) return null

    // Use safer date parsing to handle Safari date restrictions
    const lastWorkoutTime = new Date(lastWorkoutDate).getTime()
    if (Number.isNaN(lastWorkoutTime)) {
      console.warn(
        'Invalid date format in lastWorkoutCompletedDate:',
        lastWorkoutDate
      )
      return null
    }

    const daysSince = Math.floor(
      (Date.now() - lastWorkoutTime) / (1000 * 60 * 60 * 24)
    )

    // Only set light session if > 9 days (mobile app logic)
    return daysSince > 9 ? Math.min(daysSince, 50) : null
  } catch (error) {
    console.warn('Error calculating light session days:', error)
    return null
  }
}

/**
 * Get user preferences from localStorage with browser compatibility safeguards
 */
export function getUserPreferencesFromLocalStorage(): {
  isQuickMode: boolean | null
  setStyle: 'Normal' | 'RestPause' | 'Pyramid' | undefined
  isPyramid: boolean
} {
  try {
    // Get user preferences from localStorage or defaults with safe access
    const quickModePreference = safeLocalStorageGetItem('IsQuickMode')
    let isQuickMode: boolean | null = null
    if (quickModePreference === 'true') {
      isQuickMode = true
    } else if (quickModePreference === 'false') {
      isQuickMode = false
    } else {
      isQuickMode = null
    }

    // Get SetStyle preference with safe access
    const setStylePreference = safeLocalStorageGetItem('SetStyle')
    let setStyle: 'Normal' | 'RestPause' | 'Pyramid' | undefined
    if (setStylePreference === 'RestPause') {
      setStyle = 'RestPause'
    } else if (setStylePreference === 'Pyramid') {
      setStyle = 'Pyramid'
    } else if (setStylePreference === 'Normal') {
      setStyle = 'Normal'
    } else {
      setStyle = undefined // Let the system decide based on exercise
    }

    // Get pyramid preference with safe access
    const isPyramid = safeLocalStorageGetItem('IsPyramid') === 'true'

    return { isQuickMode, setStyle, isPyramid }
  } catch (error) {
    // Fallback for complete localStorage failure
    console.warn('Error getting user preferences from localStorage:', error)
    return {
      isQuickMode: null,
      setStyle: undefined,
      isPyramid: false,
    }
  }
}

/**
 * Set user preference in localStorage with browser compatibility safeguards
 */
export function setUserPreferenceInLocalStorage(
  key: string,
  value: string | boolean
): boolean {
  try {
    const stringValue = typeof value === 'boolean' ? value.toString() : value
    return safeLocalStorageSetItem(key, stringValue)
  } catch (error) {
    console.warn(`Error setting user preference ${key}:`, error)
    return false
  }
}
