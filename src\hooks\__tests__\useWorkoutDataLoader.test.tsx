import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import React, { type ReactNode } from 'react'
import { extractExercisesFromWorkoutGroups } from '../useWorkoutDataLoader.helpers'
import type { WorkoutTemplateGroupModel, ExerciseModel } from '@/types'

// Mock dependencies
vi.mock('@/stores/authStore')
vi.mock('@/stores/workoutStore')
vi.mock('@/services/api/workout')
vi.mock('@/utils/RecommendationLoadingCoordinator')

describe('useWorkoutDataLoader', () => {
  let queryClient: QueryClient
  let wrapper: ({ children }: { children: ReactNode }) => ReactNode

  beforeEach(async () => {
    vi.clearAllMocks()

    // Clear module cache to ensure fresh imports
    vi.resetModules()

    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
      },
    })
    wrapper = ({ children }: { children: ReactNode }) => (
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    )

    // Reset mocks
    const authStore = await import('@/stores/authStore')
    const workoutStore = await import('@/stores/workoutStore')
    const workoutApi = await import('@/services/api/workout')

    // Default mock implementations - use mockImplementation for consistency
    vi.mocked(authStore.useAuthStore).mockImplementation(
      () =>
        ({
          isAuthenticated: true,
          user: { id: 1 },
        }) as any
    )

    // Mock the Zustand store - it needs to handle both direct calls and selector calls
    const mockStoreState = {
      hasHydrated: true,
      setCachedUserProgramInfo: vi.fn(),
      setCachedUserWorkouts: vi.fn(),
      setCachedTodaysWorkout: vi.fn(),
      workoutSession: null,
    }

    vi.mocked(workoutStore.useWorkoutStore).mockImplementation(((
      selector?: any
    ) => {
      if (selector) {
        return selector(mockStoreState)
      }
      return mockStoreState
    }) as any)

    // Mock API responses
    vi.mocked(workoutApi.getUserWorkoutProgramInfo).mockResolvedValue({
      HasActiveWorkout: true,
    })
    vi.mocked(workoutApi.getUserWorkout).mockResolvedValue([])
    vi.mocked(workoutApi.getTodaysWorkout).mockResolvedValue([
      {
        Id: 1,
        Label: 'Day 1',
        WorkoutTemplates: [
          {
            Id: 1,
            Label: 'Workout 1',
            Exercises: [{ Id: 1, Label: 'Bench Press' } as ExerciseModel],
          },
        ],
      },
    ])
  })

  describe('recommendation loading blocking', () => {
    it('should load workout data even when recommendations are loading', async () => {
      // Re-import and use the already mocked hook
      const { useWorkoutDataLoader } = await import('../useWorkoutDataLoader')

      const { result } = renderHook(() => useWorkoutDataLoader(), { wrapper })

      // Wait for queries to complete
      await waitFor(
        () => {
          const query = result.current.todaysWorkoutQuery
          return query.isSuccess || query.isError
        },
        { timeout: 5000 }
      )

      // Verify that workout data was loaded
      expect(result.current.todaysWorkoutQuery.data).toBeDefined()
      expect(result.current.todaysWorkoutQuery.data).toHaveLength(1)
      expect(result.current.optimisticData.todaysWorkout).toBeDefined()

      // Verify API was called
      const workoutApi = await import('@/services/api/workout')
      expect(vi.mocked(workoutApi.getTodaysWorkout)).toHaveBeenCalled()
    })

    it('should not block workout loading when multiple recommendations are in progress', async () => {
      const { useWorkoutDataLoader } = await import('../useWorkoutDataLoader')
      const { result } = renderHook(() => useWorkoutDataLoader(), { wrapper })

      // Wait for queries to complete
      await waitFor(
        () => {
          expect(result.current.todaysWorkoutQuery.isSuccess).toBe(true)
        },
        { timeout: 3000 }
      )

      // All queries should have executed successfully
      expect(result.current.userProgramInfoQuery.data).toBeDefined()
      expect(result.current.userWorkoutsQuery.data).toBeDefined()
      expect(result.current.todaysWorkoutQuery.data).toBeDefined()
    })
  })
})

describe('extractExercisesFromWorkoutGroups', () => {
  it('should return empty array when no groups', () => {
    expect(extractExercisesFromWorkoutGroups(null)).toEqual([])
    expect(extractExercisesFromWorkoutGroups([])).toEqual([])
  })

  it('should extract exercises from English field name (Exercises)', () => {
    const mockGroups: WorkoutTemplateGroupModel[] = [
      {
        Id: 1,
        Label: 'Day 1',
        WorkoutTemplates: [
          {
            Id: 1,
            Label: 'Workout 1',
            Exercises: [
              { Id: 1, Label: 'Bench Press' } as ExerciseModel,
              { Id: 2, Label: 'Squat' } as ExerciseModel,
            ],
          },
        ],
      },
    ]

    const result = extractExercisesFromWorkoutGroups(mockGroups)
    expect(result).toHaveLength(2)
    expect(result[0].Label).toBe('Bench Press')
    expect(result[1].Label).toBe('Squat')
  })

  it('should extract exercises from French field name (Exercices)', () => {
    const mockGroups: any = [
      {
        Id: 1,
        Label: 'Day 1',
        WorkoutTemplates: [
          {
            Id: 1,
            Label: 'Workout 1',
            Exercices: [
              { Id: 1, Label: 'Bench Press' },
              { Id: 2, Label: 'Squat' },
            ],
          },
        ],
      },
    ]

    const result = extractExercisesFromWorkoutGroups(mockGroups)
    expect(result).toHaveLength(2)
    expect(result[0].Label).toBe('Bench Press')
    expect(result[1].Label).toBe('Squat')
  })

  it('should return empty array when no exercises in template', () => {
    const mockGroups: WorkoutTemplateGroupModel[] = [
      {
        Id: 1,
        Label: 'Day 1',
        WorkoutTemplates: [
          {
            Id: 1,
            Label: 'Workout 1',
            Exercises: [],
          },
        ],
      },
    ]

    const result = extractExercisesFromWorkoutGroups(mockGroups)
    expect(result).toEqual([])
  })

  it('should return empty array when no workout templates', () => {
    const mockGroups: WorkoutTemplateGroupModel[] = [
      {
        Id: 1,
        Label: 'Day 1',
        WorkoutTemplates: [],
      },
    ]

    const result = extractExercisesFromWorkoutGroups(mockGroups)
    expect(result).toEqual([])
  })

  it('should prioritize Exercises over Exercices if both exist', () => {
    const mockGroups: any = [
      {
        Id: 1,
        Label: 'Day 1',
        WorkoutTemplates: [
          {
            Id: 1,
            Label: 'Workout 1',
            Exercises: [{ Id: 1, Label: 'From Exercises' }],
            Exercices: [{ Id: 2, Label: 'From Exercices' }],
          },
        ],
      },
    ]

    const result = extractExercisesFromWorkoutGroups(mockGroups)
    expect(result).toHaveLength(1)
    expect(result[0].Label).toBe('From Exercises')
  })
})
