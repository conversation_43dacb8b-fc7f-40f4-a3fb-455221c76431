# Dr. Muscle Sets Implementation Guide (Web App ↔ MAUI Parity)

This guide unifies how sets are selected, requested, and rendered across the Dr. Muscle web app, mirroring production behavior in the MAUI mobile app where appropriate. It consolidates learnings from recent investigations and defines the plan we will implement in the web app.

## Scope & Audience

- Web app engineers integrating exercise recommendations and rendering sets.
- Product/QA verifying parity with MAUI for Normal, Drop, Rest-Pause, Pyramid, Reverse-Pyramid, and Back-off.

## Single Source of Truth

- Per-exercise defaults (e.g., DropSet, Pyramid, SetCount) are stored server-side and applied by the recommendation engine based on (User, Exercise).
- Global preference (Normal vs Rest-Pause; Pyramid flag) is stored server-side at the user level and used to choose the recommendation route.
- The recommendation response returns definitive flags that the client must honor when building the UI.

## Endpoints & Transport

- We use the “without warmups” endpoints. In the web app these are called under the `/api/Exercise/*WithoutWarmupsNew` paths.
  - Normal RIR: `/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew`
  - Rest-Pause RIR: `/api/Exercise/GetRecommendationRestPauseRIRForExerciseWithoutWarmupsNew`
- HTTP method: In the web app, we POST a JSON body (GetRecommendationForExerciseModel). We generate warm-ups client-side and use only the work-set data returned by these endpoints.

## Request Model (Web App)

We POST a minimal body aligned with MAUI shape. Key fields used today:

- Username: string (user email as stored; no normalization)
- ExerciseId: number
- WorkoutId: number
- IsQuickMode?: boolean | null
- LightSessionDays?: number | null
- SwapedExId?: number
- IsStrengthPhashe?: boolean
- IsFreePlan?: boolean
- IsFirstWorkoutOfStrengthPhase?: boolean
- VersionNo?: number

Notes:

- Do not send SetStyle. The API determines set style from server-side settings.
- We do not use WorkoutExerciseId.

## Endpoint Selection Logic

Choose the endpoint using these rules (MAUI parity):

1. Fetch user profile from `/api/Account/GetUserInfoPyramid`.
2. If `user.IsNormalSet === false` → call Rest-Pause:
   `/api/Exercise/GetRecommendationRestPauseRIRForExerciseWithoutWarmupsNew`
3. Else if (`user.IsPyramid === true` and `exercise.IsBodyweight === true`) → call Rest-Pause (MAUI special case for bodyweight + pyramid).
4. Else → call Normal:
   `/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew`

Notes:

- Do not use local storage to decide the endpoint.
- Per-exercise settings (e.g., DropSet, Pyramid, SetCount) are applied server-side and reflected in the response; do not override in the client.

## Bodyweight Exercise Special Handling (MAUI Team Clarification)

**Critical Rule**: Bodyweight exercises NEVER support Drop Sets or Pyramid Sets. They are ALWAYS handled as Rest-Pause sets.

### MAUI Implementation Details

1. **Endpoint Choice**: For users set to Normal, if the exercise is bodyweight and the user's Pyramid flag is true, MAUI deliberately switches to the Rest-Pause endpoint.
   - Code location: `DrMaxMuscle/Screens/User/MainAIPage.xaml.cs`
   - Condition: `SetStyle == "Normal" && IsPyramid == true && m.IsBodyweight`
   - Action: Calls `GetRecommendationRestPauseRIRForExerciseWithoutWarmupsNew`

2. **Settings UI Guardrails**: The Exercise Settings screen only enables Drop Set/Pyramid styles if the exercise is NOT bodyweight.
   - Code location: `DrMaxMuscle/Screens/Exercises/ExerciseSettingsPage.xaml.cs`
   - Logic: Only applies DropStyle/Pyramid when `CurrentLog.Instance.WorkoutTemplateCurrentExercise.IsBodyweight == false`
   - Otherwise falls back to Rest-Pause style

3. **Rendering Behavior**: For bodyweight exercises, extra effort is represented via Rest-Pause micro-sets (`NbPauses`), not weight "drops" (since you can't lower bodyweight).
   - UI labels show "Rest-pause set" not "Drop set"
   - App computes assisted/weighted handling where applicable (e.g., assisted machines or added load)
   - Does NOT treat them as Drop Set progressions

4. **Rationale**: Drop Sets are defined by successive reductions in load. For unweighted bodyweight exercises, there's no load to "drop", so we use Rest-Pause to achieve a similar training stimulus without pretending to decrease weight.

### Web App Implementation

The `generateAllSets` function MUST receive the `isBodyweight` parameter to properly normalize bodyweight exercises:

```typescript
// CRITICAL: Always pass isBodyweight parameter
const allSets = generateAllSets({
  recommendation,
  completedSets,
  currentSetIndex,
  setData,
  unit,
  isBodyweight: currentExercise?.IsBodyweight || false, // REQUIRED
})
```

When `isBodyweight` is true and the recommendation contains drop/pyramid flags, the normalization will:

- Set `IsDropSet: false`
- Set `IsPyramid: false`
- Set `IsReversePyramid: false`
- Set `IsNormalSets: false` (enables rest-pause behavior)
- Keep `NbPauses` for rest-pause micro-sets

## Response Model (Key Fields)

RecommendationModel contains the flags and values to construct sets:

- Style flags: `IsDropSet`, `IsPyramid`, `IsReversePyramid`, `IsBackOffSet`, `IsNormalSets`
- Rest-Pause fields: `NbPauses`, `NbRepsPauses`
- Counts: `Series` (work-set count)
- Targets: `Reps`, `Weight`
- Warmups: Not returned by these endpoints; client generates.

Truth about Series (updated):

- `Series` counts full work sets. Total sets MUST be computed as `Series + NbPauses`.
- Dual-purpose `NbPauses`:
  - If `IsDropSet === true`: NbPauses creates DROP SETS (with client-side 10% weight reductions)
  - If `IsDropSet === false` and `IsNormalSets === false`: NbPauses creates REST-PAUSE micro-sets
- Do not assume NbPauses is only for rest-pause; it is interpreted by `IsDropSet`.

## Server Decision Priority

Server sets only one effective style in production; if any conflict occurred, the client should honor the following priority when mapping flags to builders:

1. DropSet
2. Pyramid
3. ReversePyramid
4. Rest-Pause (NbPauses > 0)
5. Back-off
6. Normal

## Set Type Behavior (Rendering Rules)

The web app trusts the response flags and values. We intentionally avoid extra client-side rules that override server intent.

- Normal Sets
  - Build `Series` sets using the same `Weight`/`Reps`.
  - All work sets have the same reps and weight

- Drop Sets (MAUI Parity)
  - The presence of `IsDropSet` indicates the series involves drops.
  - Render TOTAL sets as `Series + NbPauses`. Series creates the initial work sets; NbPauses adds DROP SETS when `IsDropSet === true`.
  - Label precedence: If `IsDropSet === true`, label ALL sets as "Drop set".
  - **MAUI Parity Rules (must implement for full parity)**:
    - Enforce minimum of 3 total sets (if less than 3, add additional drop sets)
    - Weight reduction: Apply cumulative 10% reduction per drop set
      - Set 1: base `Weight` from the response
      - Drop sets: apply a 10% reduction from the previous set weight (cumulative)
      - Example: 100kg → 90kg → 81kg
    - **Reps reduction (MAUI one-third rule)**: Drop sets use one-third of the base reps
      - Work set: Use full `Reps` from API response
      - Drop sets: Use `Math.max(1, Math.floor(Reps / 3))` reps
      - Example: 12 reps → drop sets use 4 reps each

- Rest-Pause Sets
  - `Series` = number of full work sets.
  - After each work set, append `NbPauses` micro-sets using the same weight and `NbRepsPauses` reps.
  - Warm-ups are prepended separately by the client.
  - Rest-pause style: The first work set has different reps than the other work sets
  - 60 seconds rest between work sets

- Pyramid Sets
  - Render up/down progression per server signals; use target reps/weights from the response.
  - Not applied to bodyweight exercises
  - No back-off set calculations
  - Weight increases while reps decrease from bottom to top
  - For normal exercises: Increase weight, decrease reps
  - For assisted exercises: Decrease displayed weight to users, decrease reps
  - Reps calculation:
    - Last work set reps = 50% of min rep for user (minimum 5 reps)
    - Each previous set: increase reps by 40% (rounding up)
    - Example: 5 reps → 8 reps → 12 reps → 17 reps → 24 reps → 34 reps
  - Weight calculation:
    - Start with target weight for last set
    - Each previous set: decrease weight by 20%
    - If user changes weight: compute new weights by adding 20% for each next set
    - If user changes reps: compute new reps by removing 40% for next sets
  - Only applies if user has 3+ work sets

- Reverse Pyramid Sets
  - Render heavy-to-light progression; respect rep scheme from the response.
  - Not applied to bodyweight exercises
  - No back-off set calculations
  - Weight decreases while reps increase (~20% reps increase, ~10% weight decrease + rounding)
  - For normal exercises: Decrease weights, increase reps
  - For assisted exercises: Increase displayed weights, increase reps

- Back-off Sets
  - If `IsBackOffSet` is true, include back-off logic using any explicit back-off fields provided by the response (e.g., `BackOffSetWeight`).
  - Only shown when at least 3 work sets (not applied in 30-min mode with 2 work sets)
  - Not applied to bodyweight exercises or Pyramid/Reverse pyramid styles
  - Weight reduction calculation:
    - If 30% weight decrease possible: increase reps by 70%
    - If weight cannot be decreased: increase reps by 20%
    - If in between: scale proportionally (20-70% increase)
  - For assisted bodyweight exercises: back-off resistance weight increases instead of decreases

Edge cases

- Body-weight exercises: If `TargetLoad`/`Weight` is null, show “Body weight”.
- Assisted (negative-weight) exercises: Display magnitudes as returned.
- Time-based sets: Timed exercises are handled as normal sets (`IsDropSet=false`) and do not mix with drops.

## Warm-Ups

- Not returned by these endpoints; generated locally by the client (WarmupCalculator/WarmupGenerator) and prepended to the work sets.

## MAUI Reference Behavior (Full Parity Required)

- MAUI enforces UI-specific drop-set rules that MUST be implemented in the web app for full parity:
  - Minimum 3 total sets (add drop sets if needed)
  - Cumulative 10% weight drops
  - One-third reps for drop sets
- MAUI chooses Normal vs Rest-Pause endpoint based on the user’s global preference stored on the server.
- MAUI does not pass `SetStyle` or `WorkoutExerciseId` in recommendation requests.

## Current Web App State (Findings)

- Uses the “WithoutWarmupsNew” endpoints and generates warm-ups locally.
- Historically sent `SetStyle` in the POST body and used local storage to pick the endpoint. This will be changed to read global style from the server and to omit `SetStyle` from the body.
- Does not pass `WorkoutExerciseId` (and we will continue not to).
- Rendering already consumes `IsDropSet`, `IsPyramid`, `IsReversePyramid`, `IsBackOffSet`, `NbPauses`, and `Series` to build sets.

## Implementation Plan (Web App - MAUI Parity)

We will implement full MAUI parity including all client-side drop-set rules to ensure consistent behavior across platforms.

1. Stop sending `SetStyle` in recommendation requests

- Update the request body builder in `src/services/api/workout.ts` to omit `SetStyle`.

2. Fetch global style from server and choose the route

- Replace localStorage-driven endpoint choice with a server-backed user settings fetch via `/api/Account/GetUserInfoPyramid`, mapping to Normal vs Rest-Pause selection using `user.IsNormalSet`.
- For parity, handle the MAUI special case: if `user.IsNormalSet` is true but (`exercise.IsBodyweight` and `user.IsPyramid`), choose the Rest-Pause route.
- Cache in memory during session; refresh on relevant events (e.g., login, settings change).

3. Trust server-stored per-exercise defaults

- Do not add UI for editing per-exercise defaults in the web app at this stage; rely on settings configured via mobile.
- Expect the API to return correct `IsDropSet`, `Series`, and other flags.

4. Keep warm-ups client-side

- Continue generating warm-ups on the client and prepend them to the returned work sets.

5. Implement MAUI's drop-set UI rules for full parity

- Enforce minimum 3 total sets rule (add drop sets if `Series + NbPauses < 3`)
- Apply one-third reps rule for drop sets: `Math.max(1, Math.floor(baseReps / 3))`
- Apply 10% cumulative weight reductions across drop sets
- The API provides `Series`, `NbPauses`, and base `Weight`/`Reps`; client computes total sets and applies MAUI rules

6. Observability & validation

- Add logging/metrics to confirm response contains `IsDropSet: true` and `NbPauses > 0` for known user/exercise combinations configured as drop sets (Total sets = `Series + NbPauses`).
- Provide a small debug page/flag to inspect the raw recommendation response for E2E verification.

## Acceptance Criteria

- Endpoint selection: Web app calls Normal vs Rest-Pause endpoints based on server-stored global style (not local storage).
- Request body: No `SetStyle` is sent; body matches the expected `GetRecommendationForExerciseModel` fields used in MAUI.
- Rendering: For a user with per-exercise DropSet configured, the web app displays the correct total number of sets as `Series + NbPauses`, and applies labels/style flags from the response (`IsDropSet` controls NbPauses meaning).
- Bodyweight+pyramid parity: When a user prefers Normal but `user.IsPyramid` is true and the exercise is bodyweight, the Rest-Pause endpoint is used (matching MAUI).
- Rest-Pause: Each work set is followed by `NbPauses` mini-sets with `NbRepsPauses` reps, same weight.
- Warm-ups: Generated locally and prepended consistently.

## Validation Steps

1. Pick a known user/exercise with DropSet configured (via mobile app).
2. Load the exercise in the web app and capture the request and response.
3. Confirm endpoint selection matches the user’s global style (Normal vs Rest-Pause).
4. Verify response contains `IsDropSet: true` and `NbPauses > 0` (Total sets = `Series + NbPauses`).
5. Confirm the UI renders total sets as `Series + NbPauses` with client-side 10% weight drops applied to the NbPauses-created drop sets (when `IsDropSet` is true).
6. Validate parity for the bodyweight+pyramid case per Acceptance Criteria.
7. Repeat with Rest-Pause, Pyramid, Reverse-Pyramid, and Back-off examples.

## FAQ

- Why not send `SetStyle`?
  - The API determines behavior from server-stored settings; sending `SetStyle` is unnecessary and can be misleading. Our investigation shows the recommendation model does not rely on this field.
- Why not use `WorkoutExerciseId`?
  - The MAUI flow does not use it for recommendations in production; our web app will follow suit.
- Why enforce min 3 drop sets and one-third reps client-side like MAUI?
  - For full platform parity, the web app must implement the same business rules as MAUI to ensure users have a consistent experience across all platforms.

## Appendix: Pseudocode Snippets

Endpoint selection (server-backed, MAUI parity):

```ts
// Fetch profile from server to mirror MAUI:
const user = await accountApi.getUserInfoPyramid() // /api/Account/GetUserInfoPyramid
const prefersRestPause = user.IsNormalSet === false
const pyramidEnabled = !!user.IsPyramid
const isBodyweight = exercise.IsBodyweight === true

const endpoint = prefersRestPause
  ? '/api/Exercise/GetRecommendationRestPauseRIRForExerciseWithoutWarmupsNew'
  : '/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew'

// MAUI special case for bodyweight + pyramid while user prefers Normal
const finalEndpoint =
  !prefersRestPause && pyramidEnabled && isBodyweight
    ? '/api/Exercise/GetRecommendationRestPauseRIRForExerciseWithoutWarmupsNew'
    : endpoint
```

Request body (omit SetStyle):

```ts
const body = {
  Username: userEmail, // exact value as stored (email), no normalization
  ExerciseId,
  WorkoutId,
  IsQuickMode: isQuickMode ?? null,
  LightSessionDays: lightSessionDays ?? null,
  SwapedExId: swapId ?? null,
  IsStrengthPhashe: isStrengthPhase,
  IsFreePlan: isFreePlan,
  IsFirstWorkoutOfStrengthPhase: isFirstOfPhase,
  VersionNo: versionNo,
}
```

Set expansion based on NbPauses dual-purpose pattern:

```ts
// Build Series sets first (initial work sets)
for (let s = 0; s < dto.Series; s++) {
  addWorkSet(dto.Weight, dto.Reps, s === 0 ? '1st work set' : 'work set')
}

// Calculate total sets and enforce minimum 3 for drop sets
let totalSets = dto.Series + dto.NbPauses
let additionalDrops = 0
if (dto.IsDropSet && totalSets < 3) {
  additionalDrops = 3 - totalSets
  totalSets = 3
}

// Build NbPauses sets based on IsDropSet flag
const dropsToCreate = dto.NbPauses + additionalDrops
for (let p = 0; p < dropsToCreate; p++) {
  if (dto.IsDropSet) {
    // NbPauses creates DROP SETS when IsDropSet is true
    const dropMultiplier = Math.pow(0.9, p + 1) // 10% reduction per drop
    const dropWeight = {
      Kg: dto.Weight.Kg * dropMultiplier,
      Lb: dto.Weight.Lb * dropMultiplier,
    }
    // MAUI one-third reps rule for drop sets
    const dropReps = Math.max(1, Math.floor(dto.Reps / 3))
    addDropSet(dropWeight, dropReps, 'Drop set')
  } else if (
    p < dto.NbPauses &&
    !dto.IsNormalSets &&
    !dto.IsPyramid &&
    !dto.IsReversePyramid
  ) {
    // NbPauses creates REST-PAUSE sets when IsDropSet is false
    addPauseSet(dto.Weight, dto.NbRepsPauses || dto.Reps, 'Rest-pause set')
  }
}
```

Warmups (client-side):

```ts
const warmups = WarmupCalculator.calculate(recommendation, exercise)
return [...warmups, ...workSets]
```

---

Branch: `comprehensive-sets-guide-GPT-2` contains this guide. Proposed follow-up PR will implement the plan above.

## Addendum: Server-Driven Sets Parity (Web ↔ MAUI)

**Purpose**: Close remaining gaps where the web app diverges from MAUI, especially Drop Sets vs Rest-Pause and "MAUI shows 4 sets vs web shows 1".
**Basis**: Verified against the MAUI repo you have (file paths referenced), not assumptions.

### Truth Sources

- **Per-exercise defaults**: Stored and read server-side
  - Save: `POST /api/Exercise/AddUpdateUserDefaultExerciseSettingsPyramid`
  - Read: `POST /api/Exercise/GetExerciseSettingsPyramid`
  - Code: `DrMaxMuscle/DrMuscleRestClient.cs` (AddUpdateUserDefaultExerciseSettings → posts to AddUpdateUserDefaultExerciseSettingsPyramid); `DrMaxMuscle/Screens/Exercises/ExerciseSettingsPage.xaml.cs` builds ExerciseSettingsModel (e.g., IsDropSet, IsNormalSets, IsPyramid, SetCount).

- **Global user prefs**: Stored and read server-side
  - Save: `POST /api/Account/SetUserSetStyleWithPyramid`, `POST /api/Account/SetUserPyramidSetStyle`, `POST /api/Account/SetUserSetCount`
  - Read: `POST /api/Account/GetUserInfoPyramid`
  - Code: `DrMaxMuscle/DrMuscleRestClient.cs` lines ~521 (SetUserSetCount), ~576 (SetUserSetStyleWithPyramid), ~579 (SetUserPyramidSetStyle), ~393–403 (GetUserInfoPyramid)

### Endpoints

- Normal RIR: `/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew`
- Rest-Pause RIR: `/api/Exercise/GetRecommendationRestPauseRIRForExerciseWithoutWarmupsNew`
- Profile: `/api/Account/GetUserInfoPyramid`
- Exercise settings: `/api/Exercise/GetExerciseSettingsPyramid`

### Recommendation Request Model (Parity with MAUI)

**Model**: GetRecommendationForExerciseModel (`DrMuscleWebApiSharedModel/.../GetRecommendationForExerciseModel.cs`)

**Fields MAUI uses**:

- Username (email as stored; do not normalize)
- ExerciseId, WorkoutId
- IsQuickMode, LightSessionDays (only when > 9, else null)
- SwapedExId (when swapping), IsStrengthPhashe, IsFreePlan, IsFirstWorkoutOfStrengthPhase, VersionNo

**Not sent**: SetStyle, WorkoutExerciseId, SetCount, IsFlexibility, ExerciseIsBodyweight

### How MAUI Chooses the Endpoint (what to mirror)

**Code reference**: `DrMaxMuscle/Screens/User/MainAIPage.xaml.cs` around 12760–12810

**Rules in production**:

- If user style is "Normal" → default to Normal endpoint
- Special case: if user style is "Normal" AND IsPyramid == true AND the exercise is bodyweight (and not flexibility) → use Rest-Pause endpoint
- Else (user prefers Rest-Pause) → Rest-Pause endpoint

**Notes**:

- MAUI does not consult per-exercise IsDropSet/SetCount to pick the route
- There are a couple of legacy exceptions (e.g., magic exercise id/bodypart/flexibility) that you should not replicate; use the general rules above

### Response Model (What to read)

**Model**: RecommendationModel (`DrMuscleWebApiSharedModel/.../RecommendationModel.cs`)

**Relevant fields**:

- Styles: IsDropSet, IsPyramid, IsReversePyramid, IsBackOffSet, IsNormalSets
- Counts: Series (full work sets), NbPauses and NbRepsPauses (Rest-Pause micro-sets)
- Targets: Weight, Reps (one target per recommendation; clients expand sets)

**Not present**: RecommendationType, per-set titles

### Set Type Precedence (badges/labels)

Use this order consistently (matches MAUI intent):

1. Drop Set
2. Pyramid
3. Reverse Pyramid
4. Rest-Pause (NbPauses > 0)
5. Back-off
6. Normal

If IsDropSet === true, label as "Drop set" (do not let NbPauses override the label)

### Contract: Drop Sets vs Rest-Pause

**Actual server contract (from mobile app investigation)**:

- Drop set → IsDropSet: true, Series = 1, NbPauses > 0 (NbPauses creates drop sets)
- Rest-Pause → IsDropSet: false, Series ≥ 1, NbPauses > 0 (NbPauses creates rest-pause sets)

The `IsDropSet` flag determines how `NbPauses` is interpreted - either as drop sets or rest-pause sets

### Warm-ups

- Not returned by these endpoints; generated locally and prepended
- Do not use warm-up logic to infer styles

### "MAUI shows 4 sets vs web shows 1" — Root causes and fixes

**Common root causes**:

1. Username mismatch (web normalized email; MAUI passes exact stored string) → server fails to find stored per-exercise defaults; returns defaults (Series = 1)
2. Wrong endpoint (calling Rest-Pause for a drop-set exercise) → shows 1 work set + pauses instead of multiple Series
3. Missing context in body (e.g., WorkoutId, SwapedExId, LightSessionDays logic) → server code path differs; defaults applied
4. Different environment or ExerciseId (web vs MAUI) → different stored settings

**Practical checklist**:

- Fetch and display GetExerciseSettingsPyramid for the current exercise; confirm IsDropSet and SetCount
- Fetch GetUserInfoPyramid; confirm IsNormalSet, IsPyramid, user-level SetCount
- Build the request body exactly like MAUI (fields above), and choose the route using the rules above
- With Normal endpoint and correct context, Drop Set should yield Series = SetCount and NbPauses = 0

### NbPauses Dual-Purpose Pattern

**Key Discovery**: NbPauses has TWO different meanings based on the IsDropSet flag:

- When `IsDropSet === true`: NbPauses indicates the number of DROP SETS to create
- When `IsDropSet === false`: NbPauses indicates the number of REST-PAUSE sets to create

**Implementation**:

```typescript
// Build Series sets first
for (let i = 0; i < recommendation.Series; i++) {
  addWorkSet(recommendation.Weight, recommendation.Reps)
}

// Calculate drops needed (enforce minimum 3 total sets for drop sets)
let dropsToCreate = recommendation.NbPauses
if (recommendation.IsDropSet) {
  const totalSets = recommendation.Series + recommendation.NbPauses
  if (totalSets < 3) {
    dropsToCreate = 3 - recommendation.Series
  }
}

// Build drop/pause sets based on IsDropSet
for (let i = 0; i < dropsToCreate; i++) {
  if (recommendation.IsDropSet) {
    // Drop set with 10% weight reduction and one-third reps
    const dropWeight = recommendation.Weight.Kg * Math.pow(0.9, i + 1)
    const dropReps = Math.max(1, Math.floor(recommendation.Reps / 3))
    addDropSet(dropWeight, dropReps)
  } else if (
    i < recommendation.NbPauses &&
    !recommendation.IsNormalSets &&
    !recommendation.IsPyramid &&
    !recommendation.IsReversePyramid
  ) {
    // Rest-pause set
    addRestPauseSet(recommendation.Weight, recommendation.NbRepsPauses)
  }
}
```

### Diagnostics and Observability

- Log exact method + URL and the request body (avoid logging only a label)
- Log key response flags: IsDropSet, Series, NbPauses, IsPyramid, IsReversePyramid, IsNormalSets
- Add a debug panel to fetch and display GetExerciseSettingsPyramid alongside the recommendation
- Log the interpretation: "NbPauses creating [drop/rest-pause] sets based on IsDropSet=[true/false]"

### When to escalate to backend

- MAUI and web (same env, same inputs) return different Series/NbPauses
- The API returns unexpected combinations that don't match the dual-purpose pattern

### Q&A (Precise, Unblockers)

**Endpoint selection: Exactly which fields and order?**

Primary inputs (in order):

1. User style: IsNormalSet (from `/api/Account/GetUserInfoPyramid`)
2. Bodyweight + pyramid special case: if IsNormalSet === true and IsPyramid === true and the exercise is bodyweight → choose Rest-Pause
3. Else if IsNormalSet === false → choose Rest-Pause
4. Else → choose Normal

MAUI does not read per-exercise Drop Set/SetCount to pick the route.
Code reference: `DrMaxMuscle/Screens/User/MainAIPage.xaml.cs` around 12760–12810

**Do you consult exercise-level setting (IsDropSet/SetCount) before selecting the endpoint?**

No. Route is chosen from user-level style and the bodyweight+pyramid exception. Per-exercise settings affect how the server computes the recommendation, not which route is called.

**Response contract for Drop sets: confirm server shape; provide example?**

Actual production contract (verified in mobile app):

- IsDropSet: true, Series = 1, NbPauses > 0 (where NbPauses creates the drop sets)

Example response:

```json
{
  "IsDropSet": true,
  "Series": 1, // Initial work set
  "NbPauses": 2, // Two additional drop sets
  "Reps": 12,
  "Weight": { "Kg": 50, "Lb": 110 }
}
```

This creates 3 total sets: 1 work set + 2 drop sets.

**Request body parity: Does MAUI send SetStyle or IsFlexibility? Any extra hints (e.g., ExerciseIsBodyweight)?**

No SetStyle; no IsFlexibility; no "ExerciseIsBodyweight" field.
Body fields MAUI sends are those in GetRecommendationForExerciseModel only (see Request Model above).
Bodyweight is used only in endpoint selection (client-side), not sent in the body.

**Backend behavior: Are there conditions where Normal returns IsDropSet=true, Series=1, NbPauses>0?**

Yes, this is the standard pattern for drop sets. The mobile app uses this pattern in production:

- IsDropSet=true with Series=1 and NbPauses>0 means: 1 work set + NbPauses drop sets
- The IsDropSet flag tells the client to interpret NbPauses as drop sets rather than rest-pause sets

**Caching/versioning: Could cached settings cause outdated Series/NbPauses on web but not MAUI? Any headers/version numbers that influence response shape?**

Most common mismatch is identity/context, not headers:

- Username normalization differences (web lowercased, MAUI not) lead to missing server settings → defaults (Series = 1)
- Missing WorkoutId/SwapedExId/LightSessionDays/IsFirstWorkoutOfStrengthPhase → different server path
- MAUI does not send special headers to influence recommendation shape. VersionNo exists on the model but is not used as a response-shape switch in the code we examined

**Special cases: For bodyweight + pyramid, confirm Rest-Pause is intended regardless of exercise-level Drop Set**

Yes. MAUI's route choice applies the bodyweight+pyramid exception even when a given exercise may have Drop Set defaults. The exception is at the routing stage; Drop Set is not consulted to choose the route.

This addendum, together with the previously shared guide, should let the web app achieve MAUI parity, keep UX correct via a guarded fallback where needed, and provide the backend with precise signals to resolve any mixed-flag responses.

### Testing

**Test User**: <EMAIL> / lobla911

**Test Exercise**: Exercise ID 27474 (Cable Curl) in Workout ID 27792 is confirmed to have drop sets and 4-5 sets in MAUI mobile app. After implementing changes, should be able to replicate.

## Drop Sets Implementation Clarifications (Web ↔ Mobile Team Discussion)

### Issue Summary

Drop sets displaying as "Rest-pause" in UI despite following the implementation guide, particularly when user's preference is rest-pause style.

### What's Correct in Current Implementation

1. **Endpoint Selection**: Drop sets correctly use Normal endpoint (with exception for bodyweight + pyramid → Rest-Pause route per MAUI special case)
2. **Weight Reduction**: 10% cumulative reduction matches MAUI logic (100kg → 90kg → 81kg)
   - Reference: `KenkoChooseYourWorkoutExercisePage.xaml.cs` lines 13960–14550

### Required Adjustments

#### 1. Endpoint Selection Logic

**Current Issue**: Deciding route from `exercise.IsDropSet` alone

**Correct Approach**:

```typescript
// Don't use exercise.IsDropSet for endpoint selection
// Use user profile + bodyweight/pyramid exception
const endpoint = determineEndpoint(userProfile, exercise)
// NOT: if (exercise.IsDropSet) return NormalEndpoint
```

Reference: `MainAIPage.xaml.cs` lines 12760–12810

#### 2. Set Type Priority (Badges/Labels)

**Enforce this precedence everywhere**:

1. DropSet (highest)
2. Pyramid
3. ReversePyramid
4. Rest-Pause (NbPauses > 0)
5. Back-off
6. Normal (default)

**Key Rule**: If `IsDropSet === true`, display "Drop set" regardless of `NbPauses`

#### 3. Per-Set Titles (Client-Derived)

Reference: `KenkoChooseYourWorkoutExercisePage.xaml.cs` lines 14440–14465

```typescript
// Client sets the title, not server
if (IsDropSet) {
  SetTitle = 'Drop set'
} else if (!IsNormalSets) {
  SetTitle = 'Rest-pause set'
}
// Server does NOT send per-set titles
```

#### 4. NbPauses Dual-Purpose Implementation

**When receiving**: `IsDropSet: true` AND `NbPauses > 0`

**This is the normal drop set pattern**:

- NbPauses creates drop sets (not rest-pause sets)
- Total sets = Series + NbPauses
- Apply 10% weight reduction per drop

```typescript
function processRecommendation(rec: RecommendationModel) {
  const sets = []

  // Add Series work sets
  for (let i = 0; i < rec.Series; i++) {
    sets.push({ type: 'work', weight: rec.Weight, reps: rec.Reps })
  }

  // Add NbPauses sets based on IsDropSet
  for (let i = 0; i < rec.NbPauses; i++) {
    if (rec.IsDropSet) {
      // Drop set with weight reduction
      const dropWeight = rec.Weight.Kg * Math.pow(0.9, i + 1)
      sets.push({ type: 'drop', weight: dropWeight, reps: rec.Reps })
    } else {
      // Rest-pause set
      sets.push({
        type: 'rest-pause',
        weight: rec.Weight,
        reps: rec.NbRepsPauses,
      })
    }
  }

  return sets
}
```

### API Contract Clarifications

#### Expected Response for Drop Sets

```json
{
  "IsDropSet": true,
  "Series": 1, // Initial work set
  "NbPauses": 2, // Two drop sets (NbPauses creates drop sets when IsDropSet=true)
  "IsNormalSets": false,
  "Reps": 12,
  "Weight": { "Kg": 50, "Lb": 110 }
}
```

**Total sets created**: 3 (1 work set + 2 drop sets)

#### What NOT to Expect

- `RecommendationType`: Not part of API model, don't use
- Per-set `SetTitle`: Client-generated, not from server
- `IsDropSet` on individual sets: Check recommendation level

### Answers to Specific Questions

1. **"Should IsNormalSets be true for drop sets?"**
   - No, not required. Use `IsDropSet` as primary signal

2. **"Should drop sets have NbPauses > 0?"**
   - Yes, this is the standard pattern. NbPauses indicates the number of drop sets
   - The IsDropSet flag determines that NbPauses creates drop sets (not rest-pause sets)

3. **"What properties identify individual drop sets?"**
   - Server doesn't send per-set flags
   - Client sets `SetTitle` based on recommendation-level `IsDropSet`

### Code References for Parity

- **Endpoint methods**: `DrMaxMuscle/DrMuscleRestClient.cs` lines 270–330
- **Endpoint selection**: `MainAIPage.xaml.cs` lines 12760–12810
- **Set building**: `KenkoChooseYourWorkoutExercisePage.xaml.cs` lines 13960–14550
- **NbPauses dual-purpose**: `KenkoChooseYourWorkoutExercisePage.xaml.cs` lines 14451-14459
- **Title precedence**: Same file, lines 14440–14465
- **Model fields**: `DrMuscleWebApiSharedModel/.../RecommendationModel.cs`

## Summary: Key Implementation Pattern

The critical discovery from mobile app investigation is that **NbPauses has a dual purpose**:

1. **When `IsDropSet === true`**: NbPauses creates DROP SETS
2. **When `IsDropSet === false`**: NbPauses creates REST-PAUSE sets

**Always calculate total sets as**: `Series + NbPauses`

This pattern explains why drop sets typically have `Series = 1` and `NbPauses > 0` - the Series creates the initial work set, and NbPauses creates the additional drop sets with progressive weight reduction.

### Implementation Checklist

- [ ] Remove `exercise.IsDropSet` from endpoint selection logic
- [ ] Enforce set type priority in all display locations
- [ ] Generate `SetTitle` client-side based on recommendation flags
- [ ] Handle mixed flags with feature-flagged normalization
- [ ] Ignore `RecommendationType` field if present
- [ ] Test with user preferring rest-pause + drop set exercise configuration

## Working Examples for Each Set Style

### Normal Style Example

**Quick Summary**: All work sets have identical reps and weight.

**Scenario**: Bench Press, 3 work sets, 10 reps, 100 kg

**API Response**:

```json
{
  "IsNormalSets": true,
  "Series": 3,
  "NbPauses": 0,
  "Reps": 10,
  "Weight": { "Kg": 100, "Lb": 220 }
}
```

**Generated Sets**:

```
Warmup 1: 5 reps × 50 kg
Warmup 2: 3 reps × 70 kg
Work Set 1: 10 reps × 100 kg
Work Set 2: 10 reps × 100 kg
Work Set 3: 10 reps × 100 kg
```

### Rest-Pause Style Example

**Quick Summary**: First work set differs from rest-pause mini-sets.

**Scenario**: Bicep Curls with rest-pause, 1 work set, 2 pauses

**API Response**:

```json
{
  "IsNormalSets": false,
  "IsDropSet": false,
  "Series": 1,
  "NbPauses": 2,
  "Reps": 12,
  "NbRepsPauses": 6,
  "Weight": { "Kg": 30, "Lb": 66 }
}
```

**Generated Sets**:

```
Warmup 1: 6 reps × 15 kg
Warmup 2: 4 reps × 21 kg
Work Set 1: 12 reps × 30 kg
Rest-Pause 1: 6 reps × 30 kg (15-30s rest)
Rest-Pause 2: 6 reps × 30 kg (15-30s rest)
```

**Total**: 1 work sets + 2 rest-pause mini-sets = 3 total sets

### Drop Set Style Example

**Quick Summary**: Progressive 10% weight reduction per drop.

**Scenario**: Cable Flyes with drop sets

**API Response**:

```json
{
  "IsDropSet": true,
  "Series": 1,
  "NbPauses": 2,
  "Reps": 12,
  "Weight": { "Kg": 50, "Lb": 110 }
}
```

**Generated Sets**:

```
Warmup 1: 6 reps × 25 kg
Warmup 2: 4 reps × 35 kg
Work Set 1: 12 reps × 50 kg (label: "Drop set")
Drop Set 1: 4 reps × 45 kg (label: "Drop set", 10% reduction, one-third reps)
Drop Set 2: 4 reps × 40.5 kg (label: "Drop set", 10% reduction from previous, one-third reps)
```

**Calculation Details**:

- Weight: Drop 1: 50 kg × 0.9 = 45 kg, Drop 2: 45 kg × 0.9 = 40.5 kg
- Reps: Drop sets use Math.floor(12 / 3) = 4 reps (MAUI one-third rule)
- All sets labeled "Drop set" when IsDropSet=true

### Pyramid Style Example

**Quick Summary**: Increasing weight, decreasing reps from bottom to top.

**Scenario**: Squats with pyramid style, 6 work sets

**API Response**:

```json
{
  "IsPyramid": true,
  "Series": 6,
  "Weight": { "Kg": 120, "Lb": 264 },
  "Reps": 5
}
```

**Generated Sets** (bottom to top):

```
Warmup 1: 14 reps × 30 kg
Warmup 2: 10 reps × 60 kg
Work Set 1: 28 reps × 61 kg
Work Set 2: 20 reps × 73 kg
Work Set 3: 14 reps × 88 kg
Work Set 4: 10 reps × 96 kg
Work Set 5: 7 reps × 108 kg
Work Set 6: 5 reps × 120 kg (target from API)
```

**Calculation Details**:

- Reps: Start at 5 (min), multiply by 1.4 and round up for each previous set
  - Set 6: 5 reps
  - Set 5: 5 × 1.4 = 7 reps
  - Set 4: 7 × 1.4 = 9.8 → 10 reps
  - Set 3: 10 × 1.4 = 14 reps
  - Set 2: 14 × 1.4 = 19.6 → 20 reps
  - Set 1: 20 × 1.4 = 28 reps
- Weight: Start at 120 kg, reduce by 20% for each previous set
  - Set 6: 120 kg
  - Set 5: 120 × 0.833 = 100 kg (adjusted for equipment)
  - Set 4: 100 × 0.833 = 83 kg
  - And so on...

### Reverse Pyramid Style Example

**Quick Summary**: Decreasing weight, increasing reps.

**Scenario**: Overhead Press with reverse pyramid

**API Response**:

```json
{
  "IsReversePyramid": true,
  "Series": 4,
  "Weight": { "Kg": 60, "Lb": 132 },
  "Reps": 6
}
```

**Generated Sets**:

```
Warmup 1: 5 reps × 30 kg
Warmup 2: 3 reps × 42 kg
Work Set 1: 6 reps × 60 kg
Work Set 2: 7 reps × 54 kg (10% weight decrease, ~20% rep increase)
Work Set 3: 8 reps × 49 kg
Work Set 4: 10 reps × 44 kg
```

**Calculation Details**:

- Weight reduction: ~10% per set
- Rep increase: ~20% per set (rounded)

### Back-off Set Example (with Weighted Exercise)

**Quick Summary**: Main sets followed by lighter set with more reps.

**Scenario**: Deadlifts with back-off set, user has 3 work sets configured

**Last completed set**: 2 reps × 150 kg (plus 85 kg bodyweight)

**Back-off Calculation**:

```
Total load: 150 kg (weight) + 85 kg (bodyweight) = 235 kg
Target reduction: 30% of 235 kg = 70.5 kg
New total: 235 - 70.5 = 164.5 kg
New weight: 164.5 - 85 = 79.5 kg

Actual reduction: (150 - 79.5) / 235 = 30%
Rep increase: 30% × (70/30) = 70%
New reps: 2 × 1.7 = 3.4 → 3 reps
```

**Generated Back-off Set**: 3 reps × 79.5 kg

### Back-off Set Example (Bodyweight/Assisted)

**Quick Summary**: For assisted exercises, assistance decreases (weight goes up).

**Scenario**: Assisted Pull-ups with back-off

**Last completed set**: 2 reps × -30 kg assistance (185 lbs bodyweight)

**Calculation in lbs**:

```
Total load: 185 lbs (bodyweight) - 30 lbs (assistance) = 155 lbs effective
Target: 30% reduction = 46.5 lbs
New effective: 155 - 46.5 = 108.5 lbs

Since we can't reduce assistance below 0:
Actual reduction: 30 lbs → 0 lbs (13.95% reduction)
Rep increase: 13.95 × (70/30) = 32.55%
New reps: 2 × 1.33 = 2.66 → 3 reps
```

**Generated Back-off Set**: 3 reps × 0 kg assistance (bodyweight only)

### Special Cases

#### Bodyweight Exercise with Pyramid

- Automatically uses Rest-Pause endpoint (not Normal)
- No pyramid calculations applied
- No back-off sets

#### Time-Based Exercise

- Treated as normal sets
- No drop sets applied
- Duration in seconds instead of reps

#### 30-Minute Quick Mode

- Maximum 2 work sets
- 1 warmup set
- No back-off sets (requires 3+ work sets)

## Key Implementation Notes

1. **Total Sets Formula**: Always `Series + NbPauses` regardless of set type
2. **NbPauses Dual Purpose**:
   - When `IsDropSet=true`: Creates drop sets
   - When `IsDropSet=false`: Creates rest-pause sets
3. **Weight Adjustments**: All calculated client-side based on recommendation
4. **Equipment Rounding**: Adjust calculated weights to available plates/dumbbells
5. **Label Priority**: Drop set > Pyramid > Reverse pyramid > Rest-pause > Back-off > Normal
