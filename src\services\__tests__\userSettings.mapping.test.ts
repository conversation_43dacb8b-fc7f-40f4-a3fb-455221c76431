import { describe, it, expect, vi, beforeEach } from 'vitest'
import type { UserInfosModel } from '@/types/api'
import { getServerUserInfoCached } from '../userInfoCache'
import { getUserSettings, getUserSettingsSync } from '../userSettings'
import { logger } from '@/utils/logger'

// Mock dependencies
vi.mock('../userInfoCache')
vi.mock('@/utils/logger')

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(global, 'localStorage', {
  value: localStorageMock,
  writable: true,
})

describe('userSettings with shared cache', () => {
  const mockUserInfoFull: UserInfosModel = {
    Id: 123,
    Firstname: '<PERSON>',
    Lastname: '<PERSON>e',
    Email: '<EMAIL>',
    MassUnit: 'kg',
    IsNormalSet: false, // Rest-pause preference
    IsPyramid: true,
    IsQuickMode: true,
  }

  const mockUserInfoNormal: UserInfosModel = {
    ...mockUserInfoFull,
    IsNormalSet: true,
    IsPyramid: false,
    IsQuickMode: false,
  }

  beforeEach(() => {
    vi.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
  })

  describe('getUserSettings', () => {
    it('should map server IsNormalSet=false to setStyle=RestPause', async () => {
      vi.mocked(getServerUserInfoCached).mockResolvedValue(mockUserInfoFull)

      const settings = await getUserSettings()

      expect(settings.setStyle).toBe('RestPause')
      expect(logger.debug).toHaveBeenCalledWith(
        'Mapped server user info to settings',
        expect.objectContaining({
          setStyle: 'RestPause',
        })
      )
    })

    it('should map server IsNormalSet=true to setStyle=Normal', async () => {
      vi.mocked(getServerUserInfoCached).mockResolvedValue(mockUserInfoNormal)

      const settings = await getUserSettings()

      expect(settings.setStyle).toBe('Normal')
    })

    it('should map server IsPyramid field correctly', async () => {
      vi.mocked(getServerUserInfoCached).mockResolvedValue(mockUserInfoFull)

      const settings = await getUserSettings()

      expect(settings.isPyramid).toBe(true)
    })

    it('should map server IsQuickMode field correctly', async () => {
      vi.mocked(getServerUserInfoCached).mockResolvedValue(mockUserInfoFull)

      const settings = await getUserSettings()

      expect(settings.isQuickMode).toBe(true)
    })

    it('should use localStorage fallback when server info is null', async () => {
      vi.mocked(getServerUserInfoCached).mockResolvedValue(null)
      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'SetStyle') return 'RestPause'
        if (key === 'IsPyramid') return 'true'
        if (key === 'IsQuickMode') return 'false'
        return null
      })

      const settings = await getUserSettings()

      expect(settings.setStyle).toBe('RestPause')
      expect(settings.isPyramid).toBe(true)
      expect(settings.isQuickMode).toBe(false)
      expect(logger.debug).toHaveBeenCalledWith(
        'Using localStorage fallback for user settings'
      )
    })

    it('should handle undefined server fields gracefully', async () => {
      const partialUserInfo: UserInfosModel = {
        Id: 123,
        Firstname: 'John',
        Lastname: 'Doe',
        Email: '<EMAIL>',
        MassUnit: 'kg',
        // IsNormalSet, IsPyramid, IsQuickMode are missing
      } as UserInfosModel

      vi.mocked(getServerUserInfoCached).mockResolvedValue(partialUserInfo)

      const settings = await getUserSettings()

      // Should default to undefined when IsNormalSet is undefined (falls back to localStorage)
      expect(settings.setStyle).toBe(undefined)
      // Should default to false when IsPyramid is undefined
      expect(settings.isPyramid).toBe(false)
      // Should be null when IsQuickMode is undefined
      expect(settings.isQuickMode).toBeNull()
    })

    it('should preserve computed fields like isStrengthPhase', async () => {
      vi.mocked(getServerUserInfoCached).mockResolvedValue(mockUserInfoFull)
      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'selectedProgram') {
          return JSON.stringify({
            programId: 4,
            nextWorkoutIndex: 15,
          })
        }
        return null
      })

      const settings = await getUserSettings()

      // Should compute isStrengthPhase based on program logic
      expect(settings.isStrengthPhase).toBeDefined()
    })

    it('should preserve lightSessionDays calculation', async () => {
      vi.mocked(getServerUserInfoCached).mockResolvedValue(mockUserInfoFull)
      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'lastWorkoutCompletedDate') {
          // Set to 10 days ago (light session kicks in after 9 days)
          return new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString()
        }
        return null
      })

      const settings = await getUserSettings()

      expect(settings.lightSessionDays).toBe(10)
    })

    it('should detect free plan from localStorage', async () => {
      vi.mocked(getServerUserInfoCached).mockResolvedValue(mockUserInfoFull)
      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'DailyReset') return 'true'
        return null
      })

      const settings = await getUserSettings()

      expect(settings.isFreePlan).toBe(true)
    })
  })

  describe('getUserSettingsSync', () => {
    it('should return cached settings without network call', () => {
      // Mock the cache module to have data
      vi.mocked(getServerUserInfoCached).mockImplementation(() => {
        // This shouldn't be called in sync mode
        throw new Error('Should not make network call in sync mode')
      })

      // For sync mode, we need to check localStorage only
      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'SetStyle') return 'RestPause'
        if (key === 'IsPyramid') return 'true'
        return null
      })

      const settings = getUserSettingsSync()

      // Should use localStorage values
      expect(settings.setStyle).toBe('RestPause')
      expect(settings.isPyramid).toBe(true)
    })

    it('should use localStorage values when no cache exists', () => {
      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'SetStyle') return 'Normal'
        if (key === 'IsPyramid') return 'false'
        if (key === 'IsQuickMode') return 'true'
        return null
      })

      const settings = getUserSettingsSync()

      expect(settings.setStyle).toBe('Normal')
      expect(settings.isPyramid).toBe(false)
      expect(settings.isQuickMode).toBe(true)
    })

    it('should provide defaults when localStorage is empty', () => {
      localStorageMock.getItem.mockReturnValue(null)

      const settings = getUserSettingsSync()

      expect(settings.setStyle).toBe(undefined)
      expect(settings.isPyramid).toBe(false)
      expect(settings.isQuickMode).toBeNull()
    })
  })

  describe('mapping validation', () => {
    it('should correctly map all server fields to local settings', async () => {
      const completeUserInfo: UserInfosModel = {
        Id: 123,
        Firstname: 'Test',
        Lastname: 'User',
        Email: '<EMAIL>',
        MassUnit: 'lb',
        IsNormalSet: false,
        IsPyramid: true,
        IsQuickMode: false,
      }

      vi.mocked(getServerUserInfoCached).mockResolvedValue(completeUserInfo)

      const settings = await getUserSettings()

      // Verify all mappings
      expect(settings).toMatchObject({
        setStyle: 'RestPause', // IsNormalSet === false
        isPyramid: true,
        isQuickMode: false,
      })
    })

    it('should handle server response wrapped in Result', async () => {
      // If the API returns wrapped format, userInfoCache should handle it
      vi.mocked(getServerUserInfoCached).mockResolvedValue(mockUserInfoFull)

      const settings = await getUserSettings()

      expect(settings.setStyle).toBe('RestPause')
      expect(settings.isPyramid).toBe(true)
    })
  })
})
