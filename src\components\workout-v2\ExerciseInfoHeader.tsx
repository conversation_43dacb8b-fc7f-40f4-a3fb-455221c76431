'use client'

import { useState } from 'react'
import type { SetType } from '@/utils/setTypeUtils'
import { SetTypeBadge } from '@/components/workout/SetTypeBadge'
import { SetTypeExplainerModal } from '@/components/workout/SetTypeExplainerModal'

interface ExerciseInfoHeaderProps {
  currentSet: number
  totalSets: number
  completedSets?: number
  setType?: SetType
}

export function ExerciseInfoHeader({
  currentSet,
  totalSets,
  completedSets,
  setType,
}: ExerciseInfoHeaderProps) {
  const [showExplainer, setShowExplainer] = useState(false)

  // If completedSets is provided, use it for progress. Otherwise derive from currentSet
  const completed = completedSets !== undefined ? completedSets : currentSet - 1
  const progressPercentage = totalSets > 0 ? (completed / totalSets) * 100 : 0

  return (
    <>
      <div
        className="px-4 py-3 bg-bg-primary"
        data-testid="exercise-info-header"
      >
        <div className="flex items-center gap-3">
          <span className="text-lg text-text-secondary">
            Set {currentSet} of {totalSets}
          </span>
          {setType && (
            <SetTypeBadge
              setType={setType}
              onClick={() => setShowExplainer(true)}
              variant="compact"
            />
          )}
          <div
            className="flex-1 h-1 bg-surface-secondary rounded-full"
            data-testid="exercise-progress-bar"
          >
            <div
              className="h-full bg-gradient-to-r from-brand-gold-start to-brand-gold-end rounded-full transition-all duration-300"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
        </div>
      </div>

      {/* Set Type Explainer Modal */}
      <SetTypeExplainerModal
        setType={showExplainer ? setType || null : null}
        isOpen={showExplainer}
        onClose={() => setShowExplainer(false)}
      />
    </>
  )
}
