import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { useLoginPrefetch } from '../useLoginPrefetch'

// Mock dependencies
vi.mock('../useUserStats', () => ({
  usePrefetchUserStats: vi.fn(() => ({
    prefetch: vi.fn().mockResolvedValue(undefined),
  })),
}))

vi.mock('@/services/api/workout', () => ({
  getUserWorkoutProgramInfo: vi.fn(),
}))

vi.mock('@/api/workouts', () => ({
  workoutApi: {
    getUserWorkout: vi.fn(),
  },
}))

vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: vi.fn(() => ({
    loadAllExerciseRecommendations: vi.fn().mockResolvedValue(undefined),
    loadExerciseRecommendation: vi.fn().mockResolvedValue(undefined),
    setWorkout: vi.fn(),
    currentWorkout: null,
    workoutSession: null,
    initializePrefetchState: vi.fn(),
  })),
}))

vi.mock('@/utils/RecommendationLoadingCoordinator', () => ({
  RecommendationLoadingCoordinator: {
    getInstance: vi.fn(() => ({
      isLoading: vi.fn(() => false),
      isAnyLoading: vi.fn(() => false),
    })),
  },
}))

describe('useLoginPrefetch', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Loading Text Accuracy', () => {
    it('should show "Loading stats..." during stats phase, not "Loading stats and workout..."', async () => {
      const { getUserWorkoutProgramInfo } = await import(
        '@/services/api/workout'
      )
      const mockGetUserWorkoutProgramInfo = vi.mocked(getUserWorkoutProgramInfo)

      // Delay the workout load to test stats-only phase
      mockGetUserWorkoutProgramInfo.mockImplementation(
        () =>
          new Promise((resolve) =>
            setTimeout(
              () =>
                resolve({
                  GetUserProgramInfoResponseModel: {
                    NextWorkoutTemplate: { Id: 1 },
                  },
                }),
              100
            )
          )
      )

      const { result } = renderHook(() => useLoginPrefetch())

      // Start prefetch
      result.current.startPrefetch()

      // Wait for stats phase
      await waitFor(() => {
        expect(result.current.status).toBe('Loading stats...')
      })

      // Verify it's not showing the incorrect text
      expect(result.current.status).not.toBe('Loading stats and workout...')
    })

    it('should transition from "Loading stats..." to "Loading workout..." when workout loads', async () => {
      const { getUserWorkoutProgramInfo } = await import(
        '@/services/api/workout'
      )
      const { workoutApi } = await import('@/api/workouts')
      const mockGetUserWorkoutProgramInfo = vi.mocked(getUserWorkoutProgramInfo)
      const mockGetUserWorkout = vi.mocked(workoutApi.getUserWorkout)

      // Mock immediate stats response
      mockGetUserWorkoutProgramInfo.mockResolvedValue({
        GetUserProgramInfoResponseModel: {
          NextWorkoutTemplate: { Id: 1 },
        },
      })

      // Mock workout response
      mockGetUserWorkout.mockResolvedValue([
        {
          Id: 1,
          Exercises: [{ Id: 101, Name: 'Bench Press' }],
        },
      ])

      const { result } = renderHook(() => useLoginPrefetch())

      // Start prefetch
      result.current.startPrefetch()

      // Initially should show "Loading stats..."
      await waitFor(() => {
        expect(result.current.status).toBe('Loading stats...')
      })

      // Then transition to "Loading workout..."
      await waitFor(() => {
        expect(result.current.status).toBe('Loading workout...')
      })

      // Finally show complete
      await waitFor(() => {
        expect(result.current.status).toBe('Ready!')
      })
    })

    it('should properly track first exercise prefetch during login', async () => {
      const { getUserWorkoutProgramInfo } = await import(
        '@/services/api/workout'
      )
      const { workoutApi } = await import('@/api/workouts')
      const { useWorkoutStore } = await import('@/stores/workoutStore')

      const mockGetUserWorkoutProgramInfo = vi.mocked(getUserWorkoutProgramInfo)
      const mockGetUserWorkout = vi.mocked(workoutApi.getUserWorkout)
      const mockLoadExerciseRecommendation = vi
        .fn()
        .mockResolvedValue(undefined)
      const mockInitializePrefetchState = vi.fn()

      // Setup mocks
      mockGetUserWorkoutProgramInfo.mockResolvedValue({
        GetUserProgramInfoResponseModel: {
          NextWorkoutTemplate: { Id: 1 },
        },
      })

      mockGetUserWorkout.mockResolvedValue([
        {
          Id: 1,
          Exercises: [
            { Id: 101, Name: 'Bench Press' },
            { Id: 102, Name: 'Squats' },
          ],
        },
      ])

      vi.mocked(useWorkoutStore).mockReturnValue({
        loadAllExerciseRecommendations: vi.fn().mockResolvedValue(undefined),
        loadExerciseRecommendation: mockLoadExerciseRecommendation,
        setWorkout: vi.fn(),
        currentWorkout: null,
        workoutSession: null,
        initializePrefetchState: mockInitializePrefetchState,
      } as any)

      const { result } = renderHook(() => useLoginPrefetch())

      // Start prefetch
      result.current.startPrefetch()

      // Wait for first exercise to be loaded
      await waitFor(() => {
        expect(mockInitializePrefetchState).toHaveBeenCalled()
        expect(mockLoadExerciseRecommendation).toHaveBeenCalledWith(101)
      })

      // Verify first exercise is loaded with priority
      expect(mockLoadExerciseRecommendation).toHaveBeenCalledTimes(1)
      expect(mockLoadExerciseRecommendation).toHaveBeenCalledWith(101)
    })
  })

  describe('State Transitions', () => {
    it('should follow correct state progression sequence', async () => {
      const { getUserWorkoutProgramInfo } = await import(
        '@/services/api/workout'
      )
      const { workoutApi } = await import('@/api/workouts')
      const mockGetUserWorkoutProgramInfo = vi.mocked(getUserWorkoutProgramInfo)
      const mockGetUserWorkout = vi.mocked(workoutApi.getUserWorkout)

      const stateSequence: string[] = []

      mockGetUserWorkoutProgramInfo.mockImplementation(
        () =>
          new Promise((resolve) =>
            setTimeout(() => {
              resolve({
                GetUserProgramInfoResponseModel: {
                  NextWorkoutTemplate: { Id: 1 },
                },
              })
            }, 50)
          )
      )

      mockGetUserWorkout.mockImplementation(
        () =>
          new Promise((resolve) =>
            setTimeout(() => {
              resolve([
                {
                  Id: 1,
                  Exercises: [{ Id: 101, Name: 'Bench Press' }],
                },
              ])
            }, 50)
          )
      )

      const { result } = renderHook(() => useLoginPrefetch())

      // Track state changes
      result.current.startPrefetch()

      // Capture initial state
      stateSequence.push(result.current.status)

      // Wait and capture state transitions
      await waitFor(
        () => {
          if (
            result.current.status !== stateSequence[stateSequence.length - 1]
          ) {
            stateSequence.push(result.current.status)
          }
          return result.current.isComplete
        },
        { timeout: 1000 }
      )

      // Verify the sequence follows expected pattern
      expect(stateSequence).toContain('Starting...')
      expect(stateSequence).toContain('Loading stats...')
      expect(stateSequence).toContain('Loading workout...')
      expect(stateSequence).toContain('Ready!')

      // Verify no incorrect states
      expect(stateSequence).not.toContain('Loading stats and workout...')
    })
  })
})
