import type { UserInfosModel } from '@/types/api'
import { CacheManager } from '@/cache/CacheManager'
import { MemoryAdapter } from '@/cache/adapters/MemoryAdapter'
import { LocalStorageAdapter } from '@/cache/adapters/LocalStorageAdapter'
import { logger } from '@/utils/logger'

// Cache configuration
export const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes
const CACHE_KEY = 'userInfo'
const CACHE_NAMESPACE = 'userInfo'

// Singleton CacheManager instance with fallback strategy
let cacheManager: CacheManager | null = null

/**
 * Get or create CacheManager instance with proper adapter fallback
 */
function getCacheManager(): CacheManager {
  if (!cacheManager) {
    try {
      // Try localStorage first for persistence across page reloads
      if (typeof window !== 'undefined' && window.localStorage) {
        cacheManager = new CacheManager(new LocalStorageAdapter())
      } else {
        // Fallback to memory adapter if localStorage unavailable
        cacheManager = new CacheManager(new MemoryAdapter())
      }
    } catch {
      // Final fallback to memory adapter if any error occurs
      cacheManager = new CacheManager(new MemoryAdapter())
    }
  }
  return cacheManager
}

// Module-level pending request for deduplication
let pendingRequest: Promise<UserInfosModel | null> | null = null

/**
 * Internal function to fetch user info from API
 * Handles error cases and normalizes response
 */
async function fetchUserInfo(token?: string): Promise<UserInfosModel | null> {
  try {
    // Use standalone API to avoid circular dependency
    const { getUserInfoStandalone } = await import('./userInfoApi')

    const response = await getUserInfoStandalone(token)

    // Handle null/undefined responses
    if (!response) {
      logger.debug('User info API returned null')
      return null
    }

    // Security: Validate response structure
    if (typeof response !== 'object') {
      logger.error('Invalid user info response type:', typeof response)
      return null
    }

    return response
  } catch (error) {
    logger.error('Failed to fetch user info:', error)
    return null
  }
}

/**
 * Fetches user info from server with caching using CacheManager
 * Returns cached data if fresh, otherwise fetches new data
 * @param token Authorization token (optional)
 * @returns User info or null on error
 */
export async function getServerUserInfoCached(
  token?: string
): Promise<UserInfosModel | null> {
  try {
    // Check if we have a pending request to avoid duplicates
    if (pendingRequest) {
      logger.debug('Returning pending user info request')
      return await pendingRequest
    }

    const cache = getCacheManager()

    // Try to get from cache
    const cachedData = await cache.get<UserInfosModel>(
      CACHE_KEY,
      CACHE_NAMESPACE
    )

    if (cachedData) {
      logger.debug('Returning cached user info from CacheManager')
      return cachedData
    }

    logger.debug('Cache miss, fetching fresh user info from server')

    // Create new request and store as pending for deduplication
    pendingRequest = fetchUserInfo(token)
    const result = await pendingRequest

    // Cache the result (even if null) with TTL
    await cache.set(CACHE_KEY, result, {
      namespace: CACHE_NAMESPACE,
      ttl: CACHE_DURATION,
    })

    return result
  } catch (error) {
    logger.error('Failed to get cached user info:', error)
    return null
  } finally {
    // Clear pending request
    pendingRequest = null
  }
}

/**
 * Clears the user info cache
 * Used on logout or when cache needs to be invalidated
 */
export function clearUserInfoCache(): void {
  const cache = getCacheManager()
  cache.delete(CACHE_KEY, CACHE_NAMESPACE)
  pendingRequest = null
  logger.debug('User info cache cleared')
}
