/**
 * Cross-Browser Compatibility Tests for Comprehensive Sets Implementation
 *
 * Focuses on browser-specific issues:
 * - localStorage in Safari private mode
 * - In-memory caching patterns
 * - Promise handling differences
 * - Auth token handling
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'

// Mock different browser environments
const mockSafariPrivateMode = () => {
  const localStorage = {
    getItem: vi.fn(() => {
      throw new Error('QuotaExceededError')
    }),
    setItem: vi.fn(() => {
      throw new Error('QuotaExceededError')
    }),
    removeItem: vi.fn(() => {
      throw new Error('QuotaExceededError')
    }),
  }
  Object.defineProperty(window, 'localStorage', {
    value: localStorage,
    writable: true,
  })
  return localStorage
}

const mockChromeAndroid = () => {
  Object.defineProperty(navigator, 'userAgent', {
    value:
      'Mozilla/5.0 (Linux; Android 12; Pixel 5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Mobile Safari/537.36',
    writable: true,
  })

  // Mock viewport behavior
  Object.defineProperty(window, 'innerHeight', {
    value: 851,
    writable: true,
  })

  // Mock touch events
  Object.defineProperty(window, 'ontouchstart', {
    value: {},
    writable: true,
  })
}

const mockSafariIOS = () => {
  Object.defineProperty(navigator, 'userAgent', {
    value:
      'Mozilla/5.0 (iPhone; CPU iPhone OS 15_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.5 Mobile/15E148 Safari/604.1',
    writable: true,
  })

  // Mock iOS viewport behavior
  Object.defineProperty(window, 'innerHeight', {
    value: 667,
    writable: true,
  })

  // Mock iOS-specific APIs
  Object.defineProperty(window, 'DeviceMotionEvent', {
    value: class DeviceMotionEvent extends Event {},
    writable: true,
  })
}

const mockFirefoxMobile = () => {
  Object.defineProperty(navigator, 'userAgent', {
    value: 'Mozilla/5.0 (Mobile; rv:100.0) Gecko/100.0 Firefox/100.0',
    writable: true,
  })
}

describe('Cross-Browser Compatibility: Comprehensive Sets', () => {
  let originalLocalStorage: Storage
  let originalNavigator: Navigator

  beforeEach(() => {
    // Store original values
    originalLocalStorage = window.localStorage
    originalNavigator = window.navigator

    // Reset modules before each test
    vi.resetModules()
  })

  afterEach(() => {
    // Restore original values
    Object.defineProperty(window, 'localStorage', {
      value: originalLocalStorage,
      writable: true,
    })
    Object.defineProperty(window, 'navigator', {
      value: originalNavigator,
      writable: true,
    })
    vi.restoreAllMocks()
  })

  describe('localStorage Compatibility', () => {
    it('should handle Safari private mode gracefully in userSettings/calculations.ts', async () => {
      // Mock Safari private mode
      mockSafariPrivateMode()
      mockSafariIOS()

      const { getUserPreferencesFromLocalStorage } = await import(
        '@/services/userSettings/calculations'
      )

      // Should not throw and return defaults
      const result = getUserPreferencesFromLocalStorage()

      expect(result).toEqual({
        isQuickMode: null,
        setStyle: undefined,
        isPyramid: false,
      })
    })

    it('should handle calculateStrengthPhase localStorage access in Safari private mode', async () => {
      mockSafariPrivateMode()
      mockSafariIOS()

      const { calculateStrengthPhase } = await import(
        '@/services/userSettings/calculations'
      )

      // Should not throw and return defaults
      const result = calculateStrengthPhase()

      expect(result).toEqual({
        isStrengthPhase: false,
        isFirstStrengthPhase: false,
      })
    })

    it('should handle calculateIsFreePlan localStorage access in Safari private mode', async () => {
      mockSafariPrivateMode()
      mockSafariIOS()

      const { calculateIsFreePlan } = await import(
        '@/services/userSettings/calculations'
      )

      // Should not throw and return defaults
      const result = calculateIsFreePlan()

      expect(result).toBe(false)
    })

    it('should handle calculateLightSessionDays localStorage access in Safari private mode', async () => {
      mockSafariPrivateMode()
      mockSafariIOS()

      const { calculateLightSessionDays } = await import(
        '@/services/userSettings/calculations'
      )

      // Should not throw and return null
      const result = calculateLightSessionDays()

      expect(result).toBe(null)
    })
  })

  describe('In-Memory Cache Behavior', () => {
    it('should handle memory pressure in Chrome Android', async () => {
      mockChromeAndroid()

      const { getServerUserInfoCached, clearUserInfoCache } = await import(
        '@/services/userInfoCache'
      )

      // Clear cache first
      clearUserInfoCache()

      // Mock low memory scenario - cache should still work
      const mockUserInfo = { IsNormalSet: true, IsPyramid: false }

      // Mock the API call
      vi.doMock('@/api/userProfile', () => ({
        userProfileApi: {
          getUserInfo: vi.fn().mockResolvedValue(mockUserInfo),
        },
      }))

      const result = await getServerUserInfoCached()
      expect(result).toEqual(mockUserInfo)
    })

    it('should handle Safari iOS memory management', async () => {
      mockSafariIOS()

      const { getServerUserInfoCached, clearUserInfoCache } = await import(
        '@/services/userInfoCache'
      )

      clearUserInfoCache()

      const mockUserInfo = { IsNormalSet: false, IsPyramid: true }

      vi.doMock('@/api/userProfile', () => ({
        userProfileApi: {
          getUserInfo: vi.fn().mockResolvedValue(mockUserInfo),
        },
      }))

      // First call should fetch
      const result1 = await getServerUserInfoCached()
      expect(result1).toEqual(mockUserInfo)

      // Second call should use cache (within 5 minutes)
      const result2 = await getServerUserInfoCached()
      expect(result2).toEqual(mockUserInfo)

      // Verify cache is working by checking we didn't make multiple API calls
      // This would fail if Safari was aggressively clearing memory
      expect(result1).toBe(result2) // Should be same reference if cached
    })
  })

  describe('Promise Handling Differences', () => {
    it('should handle Promise.race timeout in Chrome Android', async () => {
      mockChromeAndroid()

      // Mock a slow API response
      vi.doMock('@/services/api/workout', () => ({
        getExerciseRecommendation: vi.fn().mockImplementation(
          () => new Promise((resolve) => setTimeout(resolve, 35000)) // 35 seconds
        ),
      }))

      // This should timeout and return null, not hang
      const startTime = Date.now()
      // Note: This is a simplified test - in real usage, we'd need to properly mock the hook
      const endTime = Date.now()

      expect(endTime - startTime).toBeLessThan(31000) // Should timeout before 31 seconds
    })

    it('should handle Safari iOS promise timing differences', async () => {
      mockSafariIOS()

      const { getServerUserInfoCached } = await import(
        '@/services/userInfoCache'
      )

      // Test concurrent requests - Safari handles these differently
      const promises = [
        getServerUserInfoCached(),
        getServerUserInfoCached(),
        getServerUserInfoCached(),
      ]

      const results = await Promise.allSettled(promises)

      // All should complete, none should hang
      results.forEach((result) => {
        expect(result.status).toBe('fulfilled')
      })
    })

    it('should handle Firefox mobile service worker timing', async () => {
      mockFirefoxMobile()

      // Mock service worker registration
      Object.defineProperty(navigator, 'serviceWorker', {
        value: {
          register: vi.fn().mockResolvedValue({
            installing: null,
            waiting: null,
            active: { state: 'activated' },
          }),
        },
        writable: true,
      })

      // Test should complete without hanging on service worker
      const result = await Promise.resolve(true)
      expect(result).toBe(true)
    })
  })

  describe('Auth Token Handling', () => {
    it('should handle Safari iOS cookie restrictions', async () => {
      mockSafariIOS()

      // Mock fetch to simulate cookie issues
      global.fetch = vi.fn().mockResolvedValue({
        ok: false,
        status: 403,
        json: () => Promise.resolve({ error: 'Cookie blocked' }),
      })

      const { useAuthStore } = await import('@/stores/authStore')

      const mockLoginData = {
        access_token: 'test-token',
        userName: '<EMAIL>',
      }

      // Should handle cookie exchange failure gracefully
      const store = useAuthStore.getState()
      await expect(store.setAuth(mockLoginData)).resolves.not.toThrow()
    })

    it('should handle Chrome Android auth flow', async () => {
      mockChromeAndroid()

      // Mock successful fetch
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        status: 200,
        json: () => Promise.resolve({ success: true }),
      })

      const { useAuthStore } = await import('@/stores/authStore')

      const mockLoginData = {
        access_token: 'test-token',
        userName: '<EMAIL>',
      }

      const store = useAuthStore.getState()
      await expect(store.setAuth(mockLoginData)).resolves.not.toThrow()

      expect(store.isAuthenticated).toBe(true)
      expect(store.token).toBe('test-token')
    })

    it('should handle token persistence across browser sessions', async () => {
      // Test Zustand persistence with different browsers
      mockChromeAndroid()

      const { useAuthStore } = await import('@/stores/authStore')

      // Simulate hydration from localStorage
      const store = useAuthStore.getState()
      store.setHasHydrated(true)

      // Should maintain authentication state
      expect(store.hasHydrated).toBe(true)
    })
  })

  describe('Network Request Patterns', () => {
    it('should handle Chrome Android network variations', async () => {
      mockChromeAndroid()

      // Mock intermittent network issues
      let callCount = 0
      global.fetch = vi.fn().mockImplementation(() => {
        callCount++
        if (callCount === 1) {
          return Promise.reject(new Error('Network Error'))
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ IsNormalSet: true }),
        })
      })

      const { getServerUserInfoCached } = await import(
        '@/services/userInfoCache'
      )

      // First call fails, should handle gracefully
      const result = await getServerUserInfoCached()
      expect(result).toBe(null) // Should not throw
    })

    it('should handle Safari iOS popup blocking for OAuth', async () => {
      mockSafariIOS()

      // Mock window.open being blocked
      Object.defineProperty(window, 'open', {
        value: vi.fn().mockReturnValue(null), // Blocked popup
        writable: true,
      })

      // OAuth should fall back to same-window redirect
      const result = window.open('https://oauth.example.com', '_blank')
      expect(result).toBe(null) // Popup was blocked
    })
  })

  describe('Viewport and UI Behavior', () => {
    it('should handle Chrome Android viewport changes', async () => {
      mockChromeAndroid()

      // Simulate keyboard showing (viewport height change)
      const originalHeight = window.innerHeight

      // Mock viewport change
      Object.defineProperty(window, 'innerHeight', {
        value: 400, // Reduced height (keyboard showing)
        writable: true,
      })

      // Trigger resize event
      window.dispatchEvent(new Event('resize'))

      expect(window.innerHeight).toBe(400)

      // Restore
      Object.defineProperty(window, 'innerHeight', {
        value: originalHeight,
        writable: true,
      })
    })

    it('should handle Safari iOS safe area and notch', async () => {
      mockSafariIOS()

      // Mock CSS environment variables
      const mockComputedStyle = {
        getPropertyValue: vi.fn((prop) => {
          if (prop === 'env(safe-area-inset-top)') return '44px'
          if (prop === 'env(safe-area-inset-bottom)') return '34px'
          return ''
        }),
      }

      Object.defineProperty(window, 'getComputedStyle', {
        value: vi.fn().mockReturnValue(mockComputedStyle),
        writable: true,
      })

      const topInset = window
        .getComputedStyle(document.body)
        .getPropertyValue('env(safe-area-inset-top)')
      expect(topInset).toBe('44px')
    })
  })

  describe('Service Worker Compatibility', () => {
    it('should handle Firefox mobile service worker quirks', async () => {
      mockFirefoxMobile()

      // Mock service worker with Firefox-specific behavior
      Object.defineProperty(navigator, 'serviceWorker', {
        value: {
          register: vi.fn().mockResolvedValue({
            installing: { state: 'installing' },
            waiting: null,
            active: null,
          }),
          ready: Promise.resolve({
            installing: null,
            waiting: null,
            active: { state: 'activated' },
          }),
        },
        writable: true,
      })

      const registration = await navigator.serviceWorker.register('/sw.js')
      expect(registration.installing?.state).toBe('installing')
    })

    it('should handle Safari iOS service worker limitations', async () => {
      mockSafariIOS()

      // Safari has limited service worker support
      Object.defineProperty(navigator, 'serviceWorker', {
        value: {
          register: vi
            .fn()
            .mockRejectedValue(new Error('Service Worker not supported')),
        },
        writable: true,
      })

      // Should handle gracefully
      try {
        await navigator.serviceWorker.register('/sw.js')
        expect(true).toBe(false) // Should not reach here
      } catch (error) {
        expect(error).toBeInstanceOf(Error)
        expect(error.message).toContain('not supported')
      }
    })
  })
})
