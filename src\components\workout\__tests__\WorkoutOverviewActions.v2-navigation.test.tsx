import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useRouter } from 'next/navigation'
import { useWorkoutActions } from '../WorkoutOverviewActions'
import type { WorkoutTemplateGroupModel } from '@/types'

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
}))

// Mock debugLog to avoid console noise
vi.mock('@/utils/debugLog', () => ({
  debugLog: {
    log: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
  },
}))

describe('WorkoutOverviewActions - V2 Navigation', () => {
  const mockPush = vi.fn()
  const mockRouter = { push: mockPush }
  const mockStartWorkout = vi.fn()
  const mockLoadExerciseRecommendation = vi.fn()
  const mockUpdateExerciseWorkSets = vi.fn()
  const mockFinishWorkout = vi.fn()

  const mockWorkout: WorkoutTemplateGroupModel[] = [
    {
      Id: 1,
      Label: 'Test Workout',
      WorkoutTemplates: [
        {
          Id: 1,
          Label: 'Workout 1',
          Exercises: [
            {
              Id: 123,
              Label: 'Bench Press',
              IsFinished: false,
              IsFlexibility: false,
              SetStyle: 'Normal',
              CurrentForeignWorkoutExerciseId: 123,
              sets: [],
            },
            {
              Id: 456,
              Label: 'Squats',
              IsFinished: false,
              IsFlexibility: false,
              SetStyle: 'Normal',
              CurrentForeignWorkoutExerciseId: 456,
              sets: [],
            },
          ],
        },
      ],
    },
  ]

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useRouter).mockReturnValue(mockRouter as any)
    mockStartWorkout.mockResolvedValue({
      success: true,
      firstExerciseId: 123,
    })
  })

  describe('Start workout - V2 Navigation', () => {
    it('should navigate directly to V2 exercise page when starting workout', async () => {
      // Given: User is on workout overview with a valid workout
      const { result } = renderHook(() =>
        useWorkoutActions({
          todaysWorkout: mockWorkout,
          startWorkout: mockStartWorkout,
          exercises: mockWorkout[0].WorkoutTemplates[0].Exercises,
          workoutSession: null,
          loadExerciseRecommendation: mockLoadExerciseRecommendation,
          updateExerciseWorkSets: mockUpdateExerciseWorkSets,
          finishWorkout: mockFinishWorkout,
        })
      )

      // When: User clicks Start workout button for V2
      await act(async () => {
        await result.current.handleStartWorkoutV2()
      })

      // Then: Should navigate directly to V2 exercise page (not V1)
      expect(mockPush).toHaveBeenCalledWith(
        '/workout/exercise-v2/123?exerciseName=Bench%20Press'
      )
      expect(mockPush).not.toHaveBeenCalledWith('/workout/exercise/123')
    })

    it('should call loadAllExerciseRecommendations before navigation', async () => {
      // Given: Workout with exercises
      const { result } = renderHook(() =>
        useWorkoutActions({
          todaysWorkout: mockWorkout,
          startWorkout: mockStartWorkout,
          exercises: mockWorkout[0].WorkoutTemplates[0].Exercises,
          workoutSession: null,
          loadExerciseRecommendation: mockLoadExerciseRecommendation,
          updateExerciseWorkSets: mockUpdateExerciseWorkSets,
          finishWorkout: mockFinishWorkout,
        })
      )

      // When: Starting workout V2
      await act(async () => {
        await result.current.handleStartWorkoutV2()
      })

      // Then: Should start workout (which includes preloading) before navigation
      expect(mockStartWorkout).toHaveBeenCalledWith(mockWorkout)
      expect(mockStartWorkout).toHaveBeenCalledBefore(mockPush as any)
    })

    it('should handle invalid first exercise ID by navigating to workout page', async () => {
      // Given: Workout start returns invalid exercise ID (negative number which is clearly invalid)
      mockStartWorkout.mockResolvedValue({
        success: true,
        firstExerciseId: -1, // Invalid ID (negative)
      })

      const { result } = renderHook(() =>
        useWorkoutActions({
          todaysWorkout: mockWorkout,
          startWorkout: mockStartWorkout,
          exercises: mockWorkout[0].WorkoutTemplates[0].Exercises,
          workoutSession: null,
          loadExerciseRecommendation: mockLoadExerciseRecommendation,
          updateExerciseWorkSets: mockUpdateExerciseWorkSets,
          finishWorkout: mockFinishWorkout,
        })
      )

      // When: Starting workout V2
      await act(async () => {
        await result.current.handleStartWorkoutV2()
      })

      // Then: Should navigate to fallback workout page
      expect(mockPush).toHaveBeenCalledWith('/workout')
      expect(mockPush).not.toHaveBeenCalledWith('/workout/exercise-v2/-1')
    })

    it('should handle undefined first exercise ID', async () => {
      // Given: Workout start returns no exercise ID
      mockStartWorkout.mockResolvedValue({
        success: true,
        firstExerciseId: undefined,
      })

      const { result } = renderHook(() =>
        useWorkoutActions({
          todaysWorkout: mockWorkout,
          startWorkout: mockStartWorkout,
          exercises: [],
          workoutSession: null,
          loadExerciseRecommendation: mockLoadExerciseRecommendation,
          updateExerciseWorkSets: mockUpdateExerciseWorkSets,
          finishWorkout: mockFinishWorkout,
        })
      )

      // When: Starting workout V2
      await act(async () => {
        await result.current.handleStartWorkoutV2()
      })

      // Then: Should not navigate anywhere
      expect(mockPush).not.toHaveBeenCalled()
    })

    it('should not block navigation if recommendations fail to load', async () => {
      // Given: Recommendations will fail but workout starts successfully
      mockStartWorkout.mockResolvedValue({
        success: true,
        firstExerciseId: 123,
      })

      const { result } = renderHook(() =>
        useWorkoutActions({
          todaysWorkout: mockWorkout,
          startWorkout: mockStartWorkout,
          exercises: mockWorkout[0].WorkoutTemplates[0].Exercises,
          workoutSession: null,
          loadExerciseRecommendation: mockLoadExerciseRecommendation,
          updateExerciseWorkSets: mockUpdateExerciseWorkSets,
          finishWorkout: mockFinishWorkout,
        })
      )

      // When: Starting workout V2
      await act(async () => {
        await result.current.handleStartWorkoutV2()
      })

      // Then: Should still navigate to V2 exercise page
      expect(mockPush).toHaveBeenCalledWith(
        '/workout/exercise-v2/123?exerciseName=Bench%20Press'
      )
    })

    it('should handle workout start failure gracefully', async () => {
      // Given: Workout start will fail
      mockStartWorkout.mockResolvedValue({
        success: false,
        firstExerciseId: undefined,
      })

      const { result } = renderHook(() =>
        useWorkoutActions({
          todaysWorkout: mockWorkout,
          startWorkout: mockStartWorkout,
          exercises: mockWorkout[0].WorkoutTemplates[0].Exercises,
          workoutSession: null,
          loadExerciseRecommendation: mockLoadExerciseRecommendation,
          updateExerciseWorkSets: mockUpdateExerciseWorkSets,
          finishWorkout: mockFinishWorkout,
        })
      )

      // When: Starting workout V2
      await act(async () => {
        await result.current.handleStartWorkoutV2()
      })

      // Then: Should not navigate
      expect(mockPush).not.toHaveBeenCalled()
    })
  })

  describe('Exercise Click - V2 Navigation', () => {
    it('should navigate to V2 exercise page when clicking an exercise', async () => {
      // Given: User clicks on a specific exercise
      const { result } = renderHook(() =>
        useWorkoutActions({
          todaysWorkout: mockWorkout,
          startWorkout: mockStartWorkout,
          exercises: mockWorkout[0].WorkoutTemplates[0].Exercises,
          workoutSession: { id: '123', exercises: [] }, // Session already started
          loadExerciseRecommendation: mockLoadExerciseRecommendation,
          updateExerciseWorkSets: mockUpdateExerciseWorkSets,
          finishWorkout: mockFinishWorkout,
        })
      )

      // When: Clicking on exercise
      await act(async () => {
        await result.current.handleExerciseClick(456)
      })

      // Then: Should navigate to V2 exercise page
      expect(mockPush).toHaveBeenCalledWith(
        '/workout/exercise-v2/456?exerciseName=Squats'
      )
    })
  })
})
