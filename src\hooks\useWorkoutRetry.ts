import { useState, useEffect } from 'react'
import { debugLog } from '@/utils/debugLog'

interface UseWorkoutRetryProps {
  isLoading: boolean
  isLoadingWorkout: boolean
  todaysWorkout: unknown
  workoutError: unknown
  isOffline: boolean
  refreshWorkout: () => Promise<void>
}

export function useWorkoutRetry({
  isLoading,
  isLoadingWorkout,
  todaysWorkout,
  workoutError,
  isOffline,
  refreshWorkout,
}: UseWorkoutRetryProps) {
  const [retryCount, setRetryCount] = useState(0)
  const [isRetrying, setIsRetrying] = useState(false)

  // Automatic retry logic when no workout data is available
  useEffect(() => {
    const shouldRetry =
      !isLoading &&
      !isLoadingWorkout &&
      !todaysWorkout &&
      !workoutError &&
      !isOffline &&
      retryCount < 3 &&
      !isRetrying

    if (shouldRetry) {
      const retryDelay = Math.pow(2, retryCount) * 1000 // Exponential backoff: 1s, 2s, 4s

      debugLog('[WorkoutOverview] Scheduling automatic retry', {
        retryCount,
        delay: retryDelay,
      })

      const timer = setTimeout(async () => {
        setIsRetrying(true)
        try {
          await refreshWorkout()
          // If successful, reset retry count
          if (todaysWorkout) {
            setRetryCount(0)
          }
        } catch (error) {
          debugLog('[WorkoutOverview] Retry failed:', error)
        } finally {
          setIsRetrying(false)
          setRetryCount((prev) => prev + 1)
        }
      }, retryDelay)

      return () => clearTimeout(timer)
    }
    // Return undefined when no retry is needed
    return undefined
  }, [
    isLoading,
    isLoadingWorkout,
    todaysWorkout,
    workoutError,
    isOffline,
    retryCount,
    isRetrying,
    refreshWorkout,
  ])

  const handleManualRetry = async () => {
    setRetryCount(0)
    setIsRetrying(true)
    try {
      await refreshWorkout()
    } finally {
      setIsRetrying(false)
    }
  }

  return {
    retryCount,
    isRetrying,
    handleManualRetry,
  }
}
