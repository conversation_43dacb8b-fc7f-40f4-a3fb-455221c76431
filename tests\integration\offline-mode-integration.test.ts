import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useOfflineStore } from '@/stores/offlineStore'
import { WorkoutCacheService } from '@/services/workoutCacheService'
import { offlineWorkoutService } from '@/services/offlineWorkoutService'

// Mock the cache manager
vi.mock('@/cache', () => ({
  createDefaultCacheManager: vi.fn(() => ({
    set: vi.fn().mockResolvedValue(undefined),
    get: vi.fn().mockResolvedValue(null),
    has: vi.fn().mockResolvedValue(false),
    delete: vi.fn().mockResolvedValue(true),
    clear: vi.fn().mockResolvedValue(undefined),
    getStats: vi.fn().mockReturnValue({
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      size: 0,
      memoryUsage: 0,
    }),
  })),
}))

// Mock workout API
vi.mock('@/api/workouts', () => ({
  workoutApi: {
    getUserProgramInfo: vi.fn(),
    getWorkoutDetails: vi.fn(),
    getExerciseRecommendation: vi.fn(),
  },
}))

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
})

describe('Offline Mode Integration', () => {
  let mockWorkoutApi: any

  beforeEach(async () => {
    vi.clearAllMocks()

    // Reset localStorage mock
    mockLocalStorage.getItem.mockReturnValue(null)

    // Get the mocked workout API
    const { workoutApi } = await import('@/api/workouts')
    mockWorkoutApi = workoutApi
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Offline Store', () => {
    it('should initialize with default state', () => {
      const { result } = renderHook(() => useOfflineStore())

      expect(result.current.isOfflineWorkoutLoaded).toBe(false)
      expect(result.current.loadedWorkoutId).toBeNull()
      expect(result.current.isLoadingOfflineWorkout).toBe(false)
      expect(result.current.syncStatus).toBe('idle')
    })

    it('should manage workout loading state', () => {
      const { result } = renderHook(() => useOfflineStore())

      act(() => {
        result.current.setLoadingOfflineWorkout(true)
      })

      expect(result.current.isLoadingOfflineWorkout).toBe(true)

      act(() => {
        result.current.setOfflineWorkoutLoaded('workout-123')
      })

      expect(result.current.isOfflineWorkoutLoaded).toBe(true)
      expect(result.current.loadedWorkoutId).toBe('workout-123')
      expect(result.current.isLoadingOfflineWorkout).toBe(false)
    })

    it('should manage sync status', () => {
      const { result } = renderHook(() => useOfflineStore())

      act(() => {
        result.current.setSyncStatus('syncing')
      })

      expect(result.current.syncStatus).toBe('syncing')

      act(() => {
        result.current.recordSyncAttempt()
      })

      expect(result.current.lastSyncAttempt).toBeGreaterThan(0)
    })
  })

  describe('Workout Cache Service', () => {
    let workoutCacheService: WorkoutCacheService

    beforeEach(() => {
      workoutCacheService = new WorkoutCacheService()
    })

    it('should cache program info', async () => {
      const mockProgramInfo = {
        WorkoutTemplateId: 456,
        WorkoutTemplateGroupId: 123,
        NextWorkoutDate: '2025-08-06',
      }

      mockWorkoutApi.getUserProgramInfo.mockResolvedValue(mockProgramInfo)

      const result = await workoutCacheService.getCachedProgramInfo()

      expect(mockWorkoutApi.getUserProgramInfo).toHaveBeenCalledTimes(1)
      expect(result).toEqual(mockProgramInfo)
    })

    it('should cache workout details', async () => {
      const mockWorkoutDetails = {
        Id: 456,
        Name: 'Test Workout',
        Exercises: [
          {
            Id: 1,
            Name: 'Bench Press',
            ExerciseId: 1,
          },
        ],
      }

      mockWorkoutApi.getWorkoutDetails.mockResolvedValue(mockWorkoutDetails)

      const result = await workoutCacheService.getCachedWorkoutDetails('456')

      expect(mockWorkoutApi.getWorkoutDetails).toHaveBeenCalledWith('456')
      expect(result).toEqual(mockWorkoutDetails)
    })

    it('should preload complete workout data', async () => {
      const mockProgramInfo = {
        GetUserProgramInfoResponseModel: {
          NextWorkoutTemplate: { Id: 456 },
        },
      }
      const mockWorkoutDetails = {
        Id: 456,
        Exercises: [{ Id: 1 }, { Id: 2 }],
      }
      const mockRecommendation = { Weight: { Lb: 135 }, Reps: 8 }

      mockWorkoutApi.getUserProgramInfo.mockResolvedValue(mockProgramInfo)
      mockWorkoutApi.getWorkoutDetails.mockResolvedValue(mockWorkoutDetails)
      mockWorkoutApi.getExerciseRecommendation.mockResolvedValue(
        mockRecommendation
      )

      const result =
        await workoutCacheService.preloadWorkoutData('<EMAIL>')

      expect(result.success).toBe(true)
      expect(result.programInfo).toEqual(mockProgramInfo)
      expect(result.workoutDetails).toEqual(mockWorkoutDetails)
      expect(result.recommendations).toHaveLength(2)
    })
  })

  describe('Offline Workout Service', () => {
    it('should save sets offline', async () => {
      const setData = {
        ExerciseId: 1,
        UserId: '<EMAIL>',
        LogDate: new Date(),
        Reps: 10,
        Weight: { Lb: 135, Kg: 61.2 },
        OneRM: { Lb: 180, Kg: 81.6 },
        IsWarmups: false,
        Isbodyweight: false,
        RIR: 2,
        RestTime: 90,
        Notes: 'Test set',
      }

      const success = await offlineWorkoutService.saveSetOffline(setData)

      expect(success).toBe(true)
      expect(mockLocalStorage.setItem).toHaveBeenCalled()
    })

    it('should manage workout progress', () => {
      const workoutId = 'workout-123'
      const progress = {
        completedSets: [],
        startTime: new Date(),
        currentExerciseId: 1,
      }

      offlineWorkoutService.updateOfflineWorkoutProgress(workoutId, progress)

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        `offline-workout-progress-${workoutId}`,
        expect.stringContaining('"currentExerciseId":1')
      )

      const retrieved =
        offlineWorkoutService.getOfflineWorkoutProgress(workoutId)
      expect(retrieved.completedSets).toEqual([])
    })

    it('should check offline capability', async () => {
      const workoutId = 'workout-123'

      // Mock cached workout data
      const mockWorkout = {
        Id: 123,
        Exercises: [{ ExerciseId: 1, Name: 'Bench Press' }],
      }

      // Mock the cache service to return workout data
      vi.spyOn(offlineWorkoutService, 'getCachedWorkout').mockResolvedValue(
        mockWorkout
      )
      vi.spyOn(
        offlineWorkoutService,
        'getCachedRecommendation'
      ).mockResolvedValue({
        Weight: { Lb: 135, Kg: 61.2 },
        Reps: 8,
        Series: 3,
        RIR: 2,
      })

      const capability =
        await offlineWorkoutService.canExecuteOffline(workoutId)

      expect(capability.canExecute).toBe(true)
      expect(capability.missingData).toHaveLength(0)
    })
  })

  describe('Integration Scenarios', () => {
    it('should complete full offline workflow', async () => {
      const { result } = renderHook(() => useOfflineStore())
      const workoutCacheService = new WorkoutCacheService()

      // Step 1: Load workout data
      const mockProgramInfo = {
        GetUserProgramInfoResponseModel: {
          NextWorkoutTemplate: { Id: 456 },
        },
      }
      const mockWorkoutDetails = {
        Id: 456,
        Name: 'Test Workout',
        Exercises: [{ Id: 1, Name: 'Bench Press' }],
      }
      const mockRecommendation = { Weight: { Lb: 135 }, Reps: 8 }

      mockWorkoutApi.getUserProgramInfo.mockResolvedValue(mockProgramInfo)
      mockWorkoutApi.getWorkoutDetails.mockResolvedValue(mockWorkoutDetails)
      mockWorkoutApi.getExerciseRecommendation.mockResolvedValue(
        mockRecommendation
      )

      // Preload workout data
      const preloadResult =
        await workoutCacheService.preloadWorkoutData('<EMAIL>')
      expect(preloadResult.success).toBe(true)

      // Step 2: Mark workout as loaded
      act(() => {
        result.current.setOfflineWorkoutLoaded('456')
      })

      expect(result.current.isOfflineWorkoutLoaded).toBe(true)
      expect(result.current.loadedWorkoutId).toBe('456')

      // Step 3: Save offline set
      const setData = {
        ExerciseId: 1,
        UserId: '<EMAIL>',
        Reps: 10,
        Weight: { Lb: 135, Kg: 61.2 },
      }

      const saveSuccess = await offlineWorkoutService.saveSetOffline(setData)
      expect(saveSuccess).toBe(true)

      // Step 4: Complete workout
      const workoutData = {
        workoutId: '456',
        completedSets: [setData as any],
        startTime: new Date(),
        endTime: new Date(),
      }

      const completeSuccess =
        await offlineWorkoutService.completeWorkoutOffline(workoutData)
      expect(completeSuccess).toBe(true)

      // Full offline workflow completed successfully
    })

    it('should handle error scenarios gracefully', async () => {
      const workoutCacheService = new WorkoutCacheService()

      // Test API error handling
      mockWorkoutApi.getUserProgramInfo.mockRejectedValue(
        new Error('Network error')
      )

      const result =
        await workoutCacheService.preloadWorkoutData('<EMAIL>')

      expect(result.success).toBe(false)
      expect(result.error).toBe('Network error')
    })
  })
})
