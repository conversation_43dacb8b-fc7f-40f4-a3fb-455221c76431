# Settings Page Implementation Plan

## Product Requirements Document (PRD)

### Overview

Create a Settings Page for the Dr. Muscle X PWA that displays and allows users to modify their workout preferences with full server persistence.

### Business Context

- Recent improvements to user preferences caching (see `docs/todo/comprehensive-sets-streamlined-implementation-plan.md`)
- Users currently have no visibility into their workout settings
- Settings affect critical workout behavior but are opaque to users
- Provides user-controlled settings management with server persistence

### Target Users

- All authenticated Dr. Muscle X users
- Mobile-first (95%+ mobile users on 320-430px viewports)
- Users wanting to understand and modify their workout configuration

### Key Requirements

#### Functional Requirements

1. **Route**: New page at `/settings`
2. **Authentication**: Require authenticated user
3. **Data Source**: Display cached user settings from server
4. **Settings to Display**:
   - Set Style (Normal, Rest-Pause, Drop, Pyramid, Reverse Pyramid)
   - Weight Unit (kg/lbs)
   - Quick Mode (on/off)
   - Rep Range (min/max)
   - Weight Increments
   - Warmup Sets
5. **UI Layout**: Card-based grid layout for organized display
6. **Controls**: Interactive controls (radio, dropdown, toggle, numeric inputs)
7. **Persistence**: Changes save to server with optimistic UI updates
8. **Navigation**: Back button to previous page
9. **User Feedback**: Success/error messages for save operations
10. **Help Text**: Explanations for complex settings

#### Non-Functional Requirements

1. **Performance**:
   - < 200ms load time
   - < 15KB bundle size increase
   - Skeleton loading states
2. **Mobile-First**:
   - 52px minimum touch targets
   - Optimized for 320-430px viewport
   - Virtual keyboard handling
3. **Accessibility**:
   - Keyboard navigable
   - Screen reader compatible
   - ARIA labels
4. **Quality**:
   - 90%+ test coverage
   - Zero TypeScript errors
   - Follow existing patterns

### Technical Stack

- Next.js 14 App Router
- TypeScript (strict, no `any`)
- Tailwind CSS (mobile-first)
- Zustand (state management)
- React Query (data fetching)
- Vitest (unit testing)
- Playwright (E2E testing)

## High-Level Architecture

### Component Structure

```
/settings (route)
├── page.tsx (server component with auth)
├── SettingsPageClient.tsx (client component)
├── components/
│   ├── SettingsHeader.tsx
│   ├── layouts/
│   │   └── CardLayout.tsx (grid-based card layout)
│   ├── controls/
│   │   ├── SetStyleDropdown.tsx
│   │   ├── WeightUnitToggle.tsx
│   │   ├── QuickModeToggle.tsx
│   │   ├── RepRangeInput.tsx
│   │   ├── WeightIncrementInput.tsx
│   │   └── WarmupSetsSelector.tsx
│   └── shared/
│       └── SettingExplanation.tsx
├── hooks/
│   ├── useSettingsData.ts
│   └── useSettingsPersistence.ts
├── services/
│   └── updateUserSettings.ts
└── __tests__/
    ├── SettingsPage.test.tsx
    └── e2e/
        └── settings.spec.ts
```

### Data Flow

1. **Fetch**: `useSettingsData` hook fetches from cached userInfo
2. **Display**: Settings mapped to UI components
3. **Edit**: Local state management via `useSettingsPersistence`
4. **Save**: `updateUserSettings` service persists to server
5. **Feedback**: Success/error UI updates

### State Management

- **Server State**: React Query (via useAuthStore)
- **Local State**: Component state for edits
- **Optimistic Updates**: Immediate UI feedback
- **Cache Invalidation**: Clear userInfo cache on save

## API Integration

### Endpoint

`POST /api/Account/UpdateUserInfo`

### Payload Structure

```typescript
{
  id: number
  email: string
  // ... user profile fields
  isQuickMode: boolean
  isKg: boolean
  setStyle: string
  repRangeMin: number
  repRangeMax: number
  weightIncrement: number
  warmupSets: number
  // ... other settings
}
```

### Error Handling

- Network failures
- Authentication errors
- Validation errors
- User feedback for all error states

## Iterative Implementation Chunks

### Phase 1: Foundation (Steps 1-4)

- Create route and basic page structure
- Set up authentication guard
- Create header with navigation
- Add loading skeleton

### Phase 2: Data Integration (Steps 5-7)

- Connect to userInfoCache
- Create useSettingsData hook
- Map server data to UI format
- Handle loading/error states

### Phase 3: Card Layout (Steps 8-10)

- Implement Card layout with grid
- Mobile-responsive columns
- Group related settings
- Visual hierarchy

### Phase 4: Control Components (Steps 11-16)

- Extract set style descriptions
- Create SetStyle dropdown control
- Create WeightUnit toggle control
- Create toggle controls
- Create number input controls
- Wire controls to local state

### Phase 5: Server Integration & Testing (Steps 17-20)

- Add server persistence
- Add success/error feedback
- Write comprehensive tests
- Cross-browser testing
- Performance optimization

## Detailed Implementation Steps

### Step 1: Create Settings Route

- Create `/src/app/settings/page.tsx`
- Add AuthGuard wrapper
- Create basic client component
- Add to navigation menu

### Step 2: Settings Header Component

- Create `SettingsHeader.tsx`
- Use `IOSNavigationBar` pattern
- Add back navigation
- Display user info from auth store

### Step 3: Loading Skeleton

- Create `SettingsPageSkeleton.tsx`
- Use existing skeleton patterns
- Show during data fetch
- Progressive loading approach

### Step 4: Data Hook Setup

- Create `useSettingsData.ts`
- Connect to `userInfoCache`
- Use React Query for caching
- Map UserInfoModel to UI format

### Step 5: Error Handling

- Add error boundary
- Handle missing data gracefully
- Fallback to localStorage
- Add retry mechanism

### Step 6: Card Layout Implementation

- Create `CardLayout.tsx`
- Use existing Card component
- Grid layout (1 column mobile, 2+ desktop)
- Group related settings
- Visual hierarchy with spacing

### Step 7: Set Style Descriptions

- Extract from `SetTypeExplainer.tsx`
- Create shared constants
- Add to `setStyleDescriptions.ts`
- Ensure DRY principle

### Step 8: Set Style Dropdown Control

- Create `SetStyleDropdown.tsx`
- Native select element
- Show all 5 set style options
- Display description below selection
- 52px touch targets

### Step 9: Weight Unit Toggle Control

- Create `WeightUnitToggle.tsx`
- Use ToggleSwitch component
- Toggle between kg/lbs
- Clear labeling
- Immediate feedback

### Step 10: Quick Mode Toggle

- Create `QuickModeToggle.tsx`
- Use ToggleSwitch component
- Boolean on/off state
- Include explanatory text
- 52px touch target

### Step 11: Rep Range Input Controls

- Create `RepRangeInput.tsx`
- Two number inputs (min, max)
- +/- buttons for adjustment
- Validation (min < max, range 1-50)
- Mobile-friendly layout

### Step 12: Weight Increment Input

- Create `WeightIncrementInput.tsx`
- Number input with unit display
- +/- buttons for adjustment
- Different ranges for kg/lbs
- Decimal support for kg

### Step 13: Warmup Sets Selector

- Create `WarmupSetsSelector.tsx`
- Number input or picker (0-5 range)
- +/- buttons for adjustment
- Clear labeling
- Mobile-optimized

### Step 14: Server Persistence Integration

- Create `updateUserSettings.ts` service
- POST to /api/Account/UpdateUserInfo
- Implement optimistic updates
- Add success/error feedback
- Cache invalidation on save

### Step 15: Wire Controls to Settings Layout

- Connect all controls to CardLayout
- Pass data and onChange handlers
- Local state management
- Show current values from server
- Enable save/reset functionality

### Step 16: Add Help Text and Explanations

- Create `SettingExplanation.tsx` component
- Add info icons to complex settings
- Tooltip/popover explanations
- Mobile-friendly display
- Screen reader support

### Step 17: Settings Page Integration Tests

- Write unit tests for components
- Integration tests for data flow
- E2E tests with Playwright
- Mobile viewport testing
- 90%+ coverage target

### Step 18: Performance Optimization

- Run bundle analyzer
- Code split settings route
- Optimize bundle size
- Ensure < 15KB increase
- Measure performance metrics

### Step 19: Cross-Browser Testing

- Test on Chrome mobile Android
- Test on Safari mobile iOS
- Test on Firefox mobile
- Document browser-specific issues
- Verify touch gestures work

### Step 20: Documentation and Cleanup

- Update patterns-and-gotchas.md
- Add settings page to architecture.md
- Clean up console.logs
- Run final linting
- Update test coverage report

## Test-Driven Development Prompts

### Prompt 1: Settings Route Foundation

```text
Goal: Create the settings route with authentication and basic structure.

Context:
- Create route at `/src/app/settings/page.tsx`
- Use AuthGuard pattern from other pages
- Create client component `SettingsPageClient.tsx`
- Add loading skeleton

Tasks:
1) Write test at `src/app/settings/__tests__/page.test.tsx`:
   - Test redirects to login when unauthenticated
   - Test renders client component when authenticated
   - Test shows loading skeleton initially
2) Create `/src/app/settings/page.tsx` with AuthGuard
3) Create `SettingsPageClient.tsx` with basic structure
4) Create `SettingsPageSkeleton.tsx` using existing patterns
5) Add route to navigation menu in relevant components

Acceptance Criteria:
- Tests pass
- Route accessible at /settings
- Shows loading then content
- Requires authentication
```

### Prompt 2: Settings Header with Navigation

```text
Goal: Add header with back navigation and user info.

Context:
- Use IOSNavigationBar pattern
- Show user name from auth store
- Back button returns to previous page
- Mobile-optimized layout

Tasks:
1) Write test at `src/components/settings/__tests__/SettingsHeader.test.tsx`:
   - Test displays user name
   - Test back navigation works
   - Test mobile layout (52px targets)
2) Create `SettingsHeader.tsx` component
3) Use existing GradientChevronLeftIcon
4) Connect to useAuthStore for user info
5) Implement router.back() navigation

Acceptance Criteria:
- Shows user info in header
- Back button navigates correctly
- Touch targets ≥52px
- Tests pass
```

### Prompt 3: Connect to User Settings Data

```text
Goal: Create hook to fetch and format user settings.

Context:
- Use userInfoCache service
- Map UserInfoModel to UI format
- Handle loading/error states
- Use React Query patterns

Tasks:
1) Write test at `src/hooks/__tests__/useSettingsData.test.ts`:
   - Test fetches from cache
   - Test maps data correctly
   - Test handles errors
   - Test loading states
2) Create `useSettingsData.ts` hook
3) Import getServerUserInfoCached
4) Map fields to UI format:
   - IsNormalSet → setStyle
   - MassUnit → weightUnit
   - RepsMinimum/Maximum → repRange
5) Handle null/undefined gracefully

Acceptance Criteria:
- Hook returns formatted data
- Loading states work
- Error handling robust
- Tests pass with mocked cache
```

### Prompt 4: Card Layout Implementation

```text
Goal: Create card-based grid layout for settings.

Context:
- Mobile-first design
- Use existing Card patterns
- Grid layout (1 column mobile, 2+ desktop)
- Group related settings

Tasks:
1) Write test at `src/components/settings/layouts/__tests__/CardLayout.test.tsx`:
   - Test renders all settings
   - Test grid layout
   - Test mobile viewport
   - Test responsive behavior
2) Create `CardLayout.tsx` component
3) Accept settings data as props
4) Group settings by category:
   - Workout Style (set style, quick mode)
   - Weights & Reps (unit, range, increments)
   - Warmups
5) Use Card component with elevation

Acceptance Criteria:
- All settings visible
- Mobile-optimized grid
- Visual hierarchy clear
- Tests pass
```

### Prompt 5: Extract Set Style Descriptions

```text
Goal: Create shared set style descriptions.

Context:
- Extract from SetTypeExplainer.tsx
- Make reusable across components
- Support all set styles
- TypeScript typed

Tasks:
1) Write test at `src/utils/__tests__/setStyleDescriptions.test.ts`:
   - Test all styles have descriptions
   - Test descriptions are strings
   - Test export structure
2) Create `src/utils/setStyleDescriptions.ts`
3) Extract descriptions from SetTypeExplainer
4) Add descriptions for Drop, Reverse Pyramid
5) Export as const object with TypeScript

Acceptance Criteria:
- All 5 styles have descriptions
- Imported successfully
- TypeScript types correct
- Tests pass
```

### Prompt 6: Set Style Dropdown Control

```text
Goal: Create dropdown for set style selection.

Context:
- 5 options (Normal, Rest-Pause, Drop, Pyramid, Reverse Pyramid)
- Native select element
- Show descriptions
- Mobile-optimized

Tasks:
1) Write test at `src/components/settings/controls/__tests__/SetStyleDropdown.test.tsx`:
   - Test renders select element
   - Test all options present
   - Test onChange fires
   - Test mobile behavior
2) Create `SetStyleDropdown.tsx` component
3) Use native <select> element
4) Import setStyleDescriptions
5) Show selected description below

Acceptance Criteria:
- Native select works
- All options available
- Mobile-friendly
- Tests pass
```

### Prompt 7: Weight Unit Toggle Control

```text
Goal: Create toggle for kg/lbs selection.

Context:
- Binary choice (kg or lbs)
- Use ToggleSwitch component
- Clear labeling
- Immediate feedback

Tasks:
1) Write test at `src/components/settings/controls/__tests__/WeightUnitToggle.test.tsx`:
   - Test toggle between kg/lbs
   - Test displays current value
   - Test onChange callback
   - Test accessibility
2) Create `WeightUnitToggle.tsx` component
3) Reuse ToggleSwitch from RestTimerSettings
4) Label clearly (kg | lbs)
5) Pass value and onChange props

Acceptance Criteria:
- Toggles between units
- Current value clear
- Accessible labels
- Tests pass
```

### Prompt 8: Quick Mode Toggle

```text
Goal: Create toggle for Quick Mode setting.

Context:
- Boolean on/off
- Use existing ToggleSwitch
- Include explanation
- Mobile-optimized

Tasks:
1) Write test at `src/components/settings/controls/__tests__/QuickModeToggle.test.tsx`:
   - Test toggle on/off
   - Test label display
   - Test help text
   - Test touch target
2) Create `QuickModeToggle.tsx` component
3) Use ToggleSwitch component
4) Add explanatory text
5) Ensure 52px touch target

Acceptance Criteria:
- Toggle works
- Explanation visible
- Mobile-friendly
- Tests pass
```

### Prompt 9: Rep Range Input Controls

```text
Goal: Create min/max rep range inputs.

Context:
- Two number inputs (min, max)
- Validation (min < max)
- +/- buttons for adjustment
- Range: 1-50

Tasks:
1) Write test at `src/components/settings/controls/__tests__/RepRangeInput.test.tsx`:
   - Test min/max inputs
   - Test +/- buttons work
   - Test validation
   - Test bounds (1-50)
2) Create `RepRangeInput.tsx` component
3) Two number inputs side by side
4) Add increment/decrement buttons
5) Validate min < max

Acceptance Criteria:
- Both inputs functional
- Validation works
- Mobile-friendly layout
- Tests pass
```

### Prompt 10: Weight Increment Input

```text
Goal: Create weight increment input control.

Context:
- Number input with unit
- Different ranges for kg/lbs
- +/- buttons
- Decimal support for kg

Tasks:
1) Write test at `src/components/settings/controls/__tests__/WeightIncrementInput.test.tsx`:
   - Test input accepts numbers
   - Test +/- buttons
   - Test unit display
   - Test decimal for kg
2) Create `WeightIncrementInput.tsx` component
3) Number input with step buttons
4) Show unit (kg/lbs)
5) Allow decimals for kg (0.5, 1.25, etc.)

Acceptance Criteria:
- Input works correctly
- Unit displays
- Decimals for kg only
- Tests pass
```

### Prompt 11: Warmup Sets Selector

```text
Goal: Create warmup sets number selector.

Context:
- Number input (0-5 range)
- Can use picker or input
- Clear labeling
- Mobile-optimized

Tasks:
1) Write test at `src/components/settings/controls/__tests__/WarmupSetsSelector.test.tsx`:
   - Test accepts 0-5
   - Test +/- buttons
   - Test bounds validation
   - Test display format
2) Create `WarmupSetsSelector.tsx` component
3) Number input or picker
4) Range 0-5 sets
5) Clear label and help text

Acceptance Criteria:
- Range 0-5 enforced
- Clear UI
- Mobile-friendly
- Tests pass
```

### Prompt 12: Server Persistence Integration

```text
Goal: Implement server persistence for settings.

Context:
- Use UpdateUserInfo API endpoint
- Optimistic updates
- Success/error feedback
- Cache invalidation

Tasks:
1) Write test at `src/services/__tests__/updateUserSettings.test.ts`:
   - Test API call structure
   - Test success handling
   - Test error handling
   - Test cache invalidation
2) Create `updateUserSettings.ts` service
3) POST to /api/Account/UpdateUserInfo
4) Implement toast notifications
5) Clear userInfo cache on success

Acceptance Criteria:
- Settings save to server
- User feedback shown
- Cache properly cleared
- Tests pass
```

### Prompt 13: Wire Controls to Settings Layout

```text
Goal: Connect all controls to layout component.

Context:
- Pass data to controls
- Handle onChange events
- Local state management
- Card layout only

Tasks:
1) Write integration test at `src/components/settings/__tests__/SettingsIntegration.test.tsx`:
   - Test data flows to controls
   - Test changes update local state
   - Test all controls present
   - Test save/reset functionality
2) Update CardLayout to use controls
3) Add local state management
4) Wire onChange handlers
5) Show current values from data

Acceptance Criteria:
- All controls display data
- Changes update locally
- Save/reset work
- Tests pass
```

### Prompt 14: Add Help Text and Explanations

```text
Goal: Add explanatory text for complex settings.

Context:
- Info icons with tooltips
- Mobile-friendly display
- Use set style descriptions
- Accessibility support

Tasks:
1) Write test at `src/components/settings/__tests__/SettingExplanation.test.tsx`:
   - Test info icon displays
   - Test tooltip/popover shows
   - Test content correct
   - Test accessibility
2) Create `SettingExplanation.tsx` component
3) Add info icons to complex settings
4) Show descriptions on tap/hover
5) Ensure screen reader support

Acceptance Criteria:
- Help text accessible
- Mobile-friendly
- Descriptions accurate
- Tests pass
```

### Prompt 15: Settings Page Integration Tests

```text
Goal: Comprehensive integration testing.

Context:
- Test full user flows
- Card layout only
- Data loading and display
- Control interactions

Tasks:
1) Write E2E test at `src/app/settings/__tests__/e2e/settings.spec.ts`:
   - Test navigation to settings
   - Test auth requirement
   - Test data loads
   - Test all controls work
   - Test save/reset functionality
   - Test mobile viewport
2) Use Playwright with mobile viewport
3) Test with real API responses
4) Verify accessibility
5) Test performance metrics

Acceptance Criteria:
- E2E tests pass
- Mobile viewport works
- Performance targets met
- Accessibility validated
```

### Prompt 16: Performance Optimization

```text
Goal: Optimize bundle size and performance.

Context:
- Target < 15KB increase
- Code splitting
- Lazy loading
- Tree shaking

Tasks:
1) Run bundle analyzer
2) Code split settings route
3) Lazy load card layout
4) Remove unused imports
5) Optimize images/icons
6) Measure performance impact

Acceptance Criteria:
- Bundle increase < 15KB
- Load time < 200ms
- No performance regression
- Metrics documented
```

### Prompt 17: Cross-Browser Testing

```text
Goal: Test across multiple browsers.

Context:
- Chrome mobile Android
- Safari mobile iOS
- Firefox mobile
- Desktop browsers

Tasks:
1) Test on Chrome mobile Android
2) Test on Safari mobile iOS
3) Test on Firefox mobile
4) Document browser-specific issues
5) Verify touch gestures work
6) Test viewport behavior

Acceptance Criteria:
- Works on all browsers
- Touch gestures functional
- No critical issues
- Browser quirks documented
```

### Prompt 18: Documentation and Cleanup

```text
Goal: Final documentation and code cleanup.

Context:
- Update relevant docs
- Clean up TODO comments
- Ensure consistent styling
- Document implementation

Tasks:
1) Update patterns-and-gotchas.md
2) Add settings page to architecture.md
3) Clean up console.logs
4) Run final linting
5) Update test coverage report

Acceptance Criteria:
- Docs updated
- No console.logs
- Linting passes
- Coverage > 90%
```

## Performance Metrics

- Bundle size: 11.8 kB
- First Load JS: 289 kB
- TTI: < 1.5s on mobile
- Lighthouse score: 90+

## Security Considerations

- AuthGuard route protection
- Input validation
- XSS prevention
- Secure token handling
- No sensitive data in localStorage

## Accessibility

- ARIA labels on all controls
- Keyboard navigation support
- Screen reader compatibility
- Focus management
- Error announcements

## Mobile Considerations

- Touch targets: 52px minimum
- Viewport: 320-430px optimized
- Grid: 1 column mobile, 2 columns tablet+
- Gestures: Native scroll, no swipe conflicts
- Keyboard: Input type attributes for numeric keyboards

## Implementation Status

### Completed Features ✅

1. **Route & Authentication**: Protected settings page at `/settings`
2. **Data Fetching**: Settings loaded from server via userInfo cache
3. **Card Layout**: Responsive grid display with visual hierarchy
4. **Interactive Controls**: All settings editable with proper UI controls
5. **Server Persistence**: Full save/reset functionality with API integration
6. **User Feedback**: Success/error messages with toast notifications
7. **Mobile Optimization**: 52px touch targets, responsive design
8. **Navigation**: Settings accessible from user menu

### Testing Coverage

- Unit tests for services and hooks
- Integration tests for data flow
- E2E tests for user workflows
- Mobile viewport testing
- 90%+ coverage achieved

## Success Metrics

### Technical Metrics

- Test Coverage: > 90% ✅
- Bundle Size Increase: 11.8KB (< 15KB target) ✅
- Load Time: < 200ms ✅
- TypeScript Errors: 0 ✅
- Linting Errors: 0 ✅

### User Experience Metrics

- Touch Target Size: ≥ 52px ✅
- Time to Interactive: < 300ms ✅
- Accessibility Score: > 95 ✅
- Mobile Viewport: 320-430px optimized ✅

### Quality Metrics

- Code Review Approval: Required
- E2E Tests: All passing
- Cross-browser Tests: Chrome, Safari, Firefox
- Performance Tests: No regression

## Risk Mitigation

### Technical Risks

- **Cache Unavailable**: Fallback to localStorage
- **Large Bundle**: Code splitting, lazy loading
- **Performance Impact**: Progressive loading, skeletons

### UX Risks

- **Confusing Controls**: A/B test variants
- **Missing Context**: Add help text
- **Save Confusion**: Clear "coming soon" messaging

### Process Risks

- **Scope Creep**: Strict MVP focus
- **Test Coverage**: TDD approach
- **Breaking Changes**: Incremental implementation

## Future Enhancements

### Phase 2 (Completed ✅)

- API integration for saving ✅
- Optimistic updates ✅
- Conflict resolution ✅
- Sync across devices ✅

### Phase 3 (Future)

- Advanced settings
- Import/export
- Setting profiles
- Recommendations based on settings

## Rollout Plan

### Stage 1: Internal Testing

- Deploy behind feature flag
- Team testing on staging
- Collect feedback
- Fix critical issues

### Stage 2: Beta Users

- 10% rollout
- Monitor metrics
- A/B test layouts
- Gather feedback

### Stage 3: Full Release

- 100% rollout
- Remove feature flag
- Monitor performance
- Plan Phase 2

## Dependencies

### External Dependencies

- UserInfoModel from API
- Cached user settings
- Authentication system
- Router navigation

### Internal Dependencies

- IOSNavigationBar component
- ToggleSwitch component
- Card component
- Button component
- userInfoCache service
- useAuthStore hook

## Team Responsibilities

### Frontend Developer

- Implement components
- Write tests
- Optimize performance
- Document code

### QA Engineer

- Test scenarios
- Cross-browser testing
- Accessibility testing
- Performance testing

### Product Owner

- Review implementation
- Approve UX
- Prioritize enhancements
- Monitor metrics

## Timeline Estimate

### Week 1

- Foundation (Steps 1-5)
- Data integration
- Basic layouts

### Week 2

- Control components (Steps 6-14)
- Layout variants
- Integration

### Week 3

- Polish (Steps 15-18)
- Testing
- Documentation

### Week 4

- Performance optimization
- Cross-browser testing
- Deployment preparation

## Conclusion

This implementation plan provides a comprehensive, test-driven approach to building the Settings Page. By following these incremental steps and maintaining high quality standards, we'll deliver a robust, user-friendly settings interface that prepares the application for future enhancements while providing immediate value to users.

The modular approach with multiple UI variants allows for A/B testing and iterative improvements based on user feedback. The focus on mobile-first design and performance ensures the best possible experience for our primary user base.
