import { renderHook, act } from '@testing-library/react'
import { vi } from 'vitest'
import { useRobustPrefetchErrorHandling } from '../useRobustPrefetchErrorHandling'

describe('useRobustPrefetchErrorHandling', () => {
  const mockPrefetchExerciseSets = vi.fn()
  const defaultProps = {
    prefetchExerciseSets: mockPrefetchExerciseSets,
    maxRetries: 3,
    retryDelayMs: 1000,
    exponentialBackoff: true,
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it('should retry failed prefetch requests', async () => {
    mockPrefetchExerciseSets
      .mockRejectedValueOnce(new Error('Network error'))
      .mockResolvedValueOnce(undefined)

    const { result } = renderHook(() =>
      useRobustPrefetchErrorHandling(defaultProps)
    )

    await act(async () => {
      await result.current.prefetchWithRetry([1, 2])
    })

    expect(mockPrefetchExerciseSets).toHaveBeenCalledTimes(2)
    expect(result.current.retryCount).toBe(1)
    expect(result.current.errorHistory).toHaveLength(1)
  })

  it('should respect max retry limit', async () => {
    mockPrefetchExerciseSets.mockRejectedValue(new Error('Persistent error'))

    const { result } = renderHook(() =>
      useRobustPrefetchErrorHandling(defaultProps)
    )

    await act(async () => {
      try {
        await result.current.prefetchWithRetry([1, 2])
      } catch (error) {
        // Expected to fail after max retries
      }
    })

    expect(mockPrefetchExerciseSets).toHaveBeenCalledTimes(4) // Initial + 3 retries
    expect(result.current.retryCount).toBe(3)
    expect(result.current.hasExceededMaxRetries).toBe(true)
  })

  it('should use exponential backoff for retry delays', async () => {
    mockPrefetchExerciseSets.mockRejectedValue(new Error('Network error'))

    const { result } = renderHook(() =>
      useRobustPrefetchErrorHandling(defaultProps)
    )

    // Track timing for exponential backoff test

    act(() => {
      result.current.prefetchWithRetry([1, 2]).catch(() => {
        // Expected to fail for exponential backoff test
      })
    })

    // Fast forward through first retry
    act(() => {
      vi.advanceTimersByTime(1000) // 1s delay
    })

    // Fast forward through second retry
    act(() => {
      vi.advanceTimersByTime(2000) // 2s delay (exponential)
    })

    // Fast forward through third retry
    act(() => {
      vi.advanceTimersByTime(4000) // 4s delay (exponential)
    })

    expect(mockPrefetchExerciseSets).toHaveBeenCalledTimes(4)
  })

  it('should categorize errors by type', async () => {
    const networkError = new Error('Network error')
    const timeoutError = new Error('Request timeout')

    mockPrefetchExerciseSets
      .mockRejectedValueOnce(networkError)
      .mockRejectedValueOnce(timeoutError)
      .mockResolvedValueOnce(undefined)

    const { result } = renderHook(() =>
      useRobustPrefetchErrorHandling(defaultProps)
    )

    await act(async () => {
      await result.current.prefetchWithRetry([1])
    })

    await act(async () => {
      await result.current.prefetchWithRetry([2])
    })

    expect(result.current.errorsByType.network).toBe(1)
    expect(result.current.errorsByType.timeout).toBe(1)
    expect(result.current.errorHistory).toHaveLength(2)
  })

  it('should handle circuit breaker pattern', async () => {
    mockPrefetchExerciseSets.mockRejectedValue(new Error('Service unavailable'))

    const { result } = renderHook(() =>
      useRobustPrefetchErrorHandling({
        ...defaultProps,
        circuitBreakerThreshold: 3,
        circuitBreakerTimeoutMs: 5000,
      })
    )

    // Cause multiple failures to trip circuit breaker
    await act(async () => {
      try {
        await result.current.prefetchWithRetry([1])
      } catch (error) {
        // Expected to fail
      }
      try {
        await result.current.prefetchWithRetry([2])
      } catch (error) {
        // Expected to fail
      }
      try {
        await result.current.prefetchWithRetry([3])
      } catch (error) {
        // Expected to fail
      }
    })

    expect(result.current.isCircuitBreakerOpen).toBe(true)

    // Next request should be rejected immediately
    await act(async () => {
      const startTime = performance.now()
      try {
        await result.current.prefetchWithRetry([4])
      } catch (error) {
        const endTime = performance.now()
        expect(endTime - startTime).toBeLessThan(100) // Should fail fast
      }
    })
  })

  it('should recover from circuit breaker after timeout', async () => {
    mockPrefetchExerciseSets
      .mockRejectedValue(new Error('Service unavailable'))
      .mockResolvedValueOnce(undefined) // Recovery

    const { result } = renderHook(() =>
      useRobustPrefetchErrorHandling({
        ...defaultProps,
        circuitBreakerThreshold: 2,
        circuitBreakerTimeoutMs: 1000,
      })
    )

    // Trip circuit breaker
    await act(async () => {
      try {
        await result.current.prefetchWithRetry([1])
      } catch (error) {
        // Expected to fail
      }
      try {
        await result.current.prefetchWithRetry([2])
      } catch (error) {
        // Expected to fail
      }
    })

    expect(result.current.isCircuitBreakerOpen).toBe(true)

    // Wait for circuit breaker timeout
    act(() => {
      vi.advanceTimersByTime(1100)
    })

    // Should be able to retry now
    await act(async () => {
      await result.current.prefetchWithRetry([3])
    })

    expect(result.current.isCircuitBreakerOpen).toBe(false)
  })

  it('should provide graceful degradation', async () => {
    mockPrefetchExerciseSets.mockRejectedValue(new Error('Memory error'))

    const { result } = renderHook(() =>
      useRobustPrefetchErrorHandling({
        ...defaultProps,
        enableGracefulDegradation: true,
      })
    )

    await act(async () => {
      try {
        await result.current.prefetchWithRetry([1, 2, 3, 4, 5])
      } catch (error) {
        // Should try with smaller batches
      }
    })

    expect(result.current.isInDegradedMode).toBe(true)
    expect(mockPrefetchExerciseSets).toHaveBeenCalledWith([1, 2]) // Smaller batch
  })

  it('should clear error history when requested', () => {
    const { result } = renderHook(() =>
      useRobustPrefetchErrorHandling(defaultProps)
    )

    act(() => {
      // Manually add some errors for testing
      result.current.clearErrorHistory()
    })

    expect(result.current.errorHistory).toHaveLength(0)
    expect(result.current.retryCount).toBe(0)
    expect(result.current.errorsByType).toEqual({
      network: 0,
      timeout: 0,
      memory: 0,
      server: 0,
      unknown: 0,
    })
  })

  it('should handle different error types correctly', async () => {
    const errors = [
      new TypeError('Network request failed'), // Network
      new Error('Request timeout'), // Timeout
      new Error('out of memory'), // Memory
      new Error('500 Internal Server Error'), // Server
      new Error('Some other error'), // Unknown
    ]

    mockPrefetchExerciseSets
      .mockRejectedValueOnce(errors[0])
      .mockRejectedValueOnce(errors[1])
      .mockRejectedValueOnce(errors[2])
      .mockRejectedValueOnce(errors[3])
      .mockRejectedValueOnce(errors[4])
      .mockResolvedValue(undefined)

    const { result } = renderHook(() =>
      useRobustPrefetchErrorHandling(defaultProps)
    )

    for (let i = 0; i < errors.length; i++) {
      // eslint-disable-next-line no-await-in-loop
      await act(async () => {
        try {
          await result.current.prefetchWithRetry([i + 1])
        } catch (error) {
          // Expected to fail for error categorization test
        }
      })
    }

    expect(result.current.errorsByType.network).toBe(1)
    expect(result.current.errorsByType.timeout).toBe(1)
    expect(result.current.errorsByType.memory).toBe(1)
    expect(result.current.errorsByType.server).toBe(1)
    expect(result.current.errorsByType.unknown).toBe(1)
  })
})
