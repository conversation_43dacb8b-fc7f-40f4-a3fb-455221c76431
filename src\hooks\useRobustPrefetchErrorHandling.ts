import { useState, useCallback, useRef } from 'react'
import { logger } from '@/utils/logger'

interface ErrorInfo {
  timestamp: number
  error: Error
  exerciseIds: number[]
  retryAttempt: number
  type: ErrorType
}

type ErrorType = 'network' | 'timeout' | 'memory' | 'server' | 'unknown'

interface ErrorsByType {
  network: number
  timeout: number
  memory: number
  server: number
  unknown: number
}

interface UseRobustPrefetchErrorHandlingProps {
  prefetchExerciseSets: (exerciseIds: number[]) => Promise<void>
  maxRetries?: number
  retryDelayMs?: number
  exponentialBackoff?: boolean
  circuitBreakerThreshold?: number
  circuitBreakerTimeoutMs?: number
  enableGracefulDegradation?: boolean
}

interface UseRobustPrefetchErrorHandlingReturn {
  prefetchWithRetry: (exerciseIds: number[]) => Promise<void>
  retryCount: number
  errorHistory: ErrorInfo[]
  errorsByType: ErrorsByType
  hasExceededMaxRetries: boolean
  isCircuitBreakerOpen: boolean
  isInDegradedMode: boolean
  clearErrorHistory: () => void
}

/**
 * Hook that provides robust error handling and retry logic for prefetch operations
 * Includes circuit breaker pattern, exponential backoff, and graceful degradation
 */
export function useRobustPrefetchErrorHandling({
  prefetchExerciseSets,
  maxRetries = 3,
  retryDelayMs = 1000,
  exponentialBackoff = true,
  circuitBreakerThreshold = 5,
  circuitBreakerTimeoutMs = 30000,
  enableGracefulDegradation = true,
}: UseRobustPrefetchErrorHandlingProps): UseRobustPrefetchErrorHandlingReturn {
  const [retryCount, setRetryCount] = useState(0)
  const [errorHistory, setErrorHistory] = useState<ErrorInfo[]>([])
  const [errorsByType, setErrorsByType] = useState<ErrorsByType>({
    network: 0,
    timeout: 0,
    memory: 0,
    server: 0,
    unknown: 0,
  })
  const [hasExceededMaxRetries, setHasExceededMaxRetries] = useState(false)
  const [isCircuitBreakerOpen, setIsCircuitBreakerOpen] = useState(false)
  const [isInDegradedMode, setIsInDegradedMode] = useState(false)

  const circuitBreakerOpenTimeRef = useRef<number>(0)
  const consecutiveFailuresRef = useRef(0)

  /**
   * Categorize error by type for analytics and handling
   */
  const categorizeError = useCallback((error: Error): ErrorType => {
    const message = error.message.toLowerCase()
    const errorName = error.name.toLowerCase()

    if (
      errorName.includes('typeerror') ||
      message.includes('network') ||
      message.includes('fetch')
    ) {
      return 'network'
    }
    if (message.includes('timeout') || message.includes('aborted')) {
      return 'timeout'
    }
    if (message.includes('memory') || message.includes('out of memory')) {
      return 'memory'
    }
    if (
      message.includes('500') ||
      message.includes('server') ||
      message.includes('internal')
    ) {
      return 'server'
    }
    return 'unknown'
  }, [])

  /**
   * Record error for analytics and circuit breaker logic
   */
  const recordError = useCallback(
    (error: Error, exerciseIds: number[], retryAttempt: number) => {
      const errorType = categorizeError(error)
      const errorInfo: ErrorInfo = {
        timestamp: Date.now(),
        error,
        exerciseIds,
        retryAttempt,
        type: errorType,
      }

      setErrorHistory((prev) => [...prev.slice(-19), errorInfo]) // Keep last 20 errors
      setErrorsByType((prev) => ({
        ...prev,
        [errorType]: prev[errorType] + 1,
      }))

      consecutiveFailuresRef.current += 1

      // Check circuit breaker threshold
      if (consecutiveFailuresRef.current >= circuitBreakerThreshold) {
        setIsCircuitBreakerOpen(true)
        circuitBreakerOpenTimeRef.current = Date.now()
        logger.warn(
          `[useRobustPrefetchErrorHandling] Circuit breaker opened after ${consecutiveFailuresRef.current} failures`
        )
      }

      logger.error(`[useRobustPrefetchErrorHandling] Error recorded:`, {
        type: errorType,
        message: error.message,
        exerciseIds,
        retryAttempt,
        consecutiveFailures: consecutiveFailuresRef.current,
      })
    },
    [categorizeError, circuitBreakerThreshold]
  )

  /**
   * Check if circuit breaker should be closed (recovery)
   */
  const checkCircuitBreakerRecovery = useCallback((): boolean => {
    if (!isCircuitBreakerOpen) return true

    const timeSinceOpen = Date.now() - circuitBreakerOpenTimeRef.current
    if (timeSinceOpen >= circuitBreakerTimeoutMs) {
      setIsCircuitBreakerOpen(false)
      consecutiveFailuresRef.current = 0
      logger.log(
        '[useRobustPrefetchErrorHandling] Circuit breaker closed - attempting recovery'
      )
      return true
    }

    return false
  }, [isCircuitBreakerOpen, circuitBreakerTimeoutMs])

  /**
   * Calculate retry delay with optional exponential backoff
   */
  const getRetryDelay = useCallback(
    (attempt: number): number => {
      if (!exponentialBackoff) {
        return retryDelayMs
      }

      // Exponential backoff: 1s, 2s, 4s, 8s, etc.
      return retryDelayMs * Math.pow(2, attempt)
    },
    [exponentialBackoff, retryDelayMs]
  )

  /**
   * Attempt graceful degradation by reducing batch size
   */
  const attemptGracefulDegradation = useCallback(
    async (originalExerciseIds: number[], attempt: number): Promise<void> => {
      if (!enableGracefulDegradation || originalExerciseIds.length <= 1) {
        throw new Error('Cannot degrade further')
      }

      setIsInDegradedMode(true)

      // Reduce batch size progressively
      const batchSize = Math.max(
        1,
        Math.floor(originalExerciseIds.length / Math.pow(2, attempt))
      )
      const reducedBatch = originalExerciseIds.slice(0, batchSize)

      logger.log(
        `[useRobustPrefetchErrorHandling] Attempting graceful degradation: ` +
          `batch size ${originalExerciseIds.length} -> ${batchSize}`
      )

      return prefetchExerciseSets(reducedBatch)
    },
    [enableGracefulDegradation, prefetchExerciseSets]
  )

  /**
   * Sleep utility for retry delays
   */
  const sleep = (ms: number): Promise<void> => {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }

  /**
   * Prefetch with retry logic, circuit breaker, and graceful degradation
   */
  // eslint-disable-next-line @typescript-eslint/no-loop-func
  const prefetchWithRetry = useCallback(
    async (exerciseIds: number[]): Promise<void> => {
      // Check circuit breaker
      if (!checkCircuitBreakerRecovery()) {
        throw new Error(
          'Circuit breaker is open - service temporarily unavailable'
        )
      }

      let lastError: Error | null = null
      let currentRetryCount = 0

      for (let attempt = 0; attempt <= maxRetries; attempt++) {
        try {
          // Add delay for retries
          if (attempt > 0) {
            const delay = getRetryDelay(attempt - 1)
            logger.log(
              `[useRobustPrefetchErrorHandling] Retrying in ${delay}ms (attempt ${attempt}/${maxRetries})`
            )
            // eslint-disable-next-line no-await-in-loop
            await sleep(delay)
          }

          // Attempt the prefetch
          // eslint-disable-next-line no-await-in-loop
          await prefetchExerciseSets(exerciseIds)

          // Success - reset failure counters
          consecutiveFailuresRef.current = 0
          // eslint-disable-next-line @typescript-eslint/no-loop-func
          setRetryCount((prev) => prev + currentRetryCount)

          if (isInDegradedMode) {
            setIsInDegradedMode(false)
            logger.log(
              '[useRobustPrefetchErrorHandling] Recovered from degraded mode'
            )
          }

          return
        } catch (error) {
          lastError = error as Error
          currentRetryCount++

          recordError(lastError, exerciseIds, attempt)

          logger.warn(
            `[useRobustPrefetchErrorHandling] Attempt ${attempt + 1}/${maxRetries + 1} failed:`,
            lastError.message
          )

          // Try graceful degradation on memory/server errors
          if (attempt === maxRetries && enableGracefulDegradation) {
            const errorType = categorizeError(lastError)
            if (errorType === 'memory' || errorType === 'server') {
              try {
                // eslint-disable-next-line no-await-in-loop
                await attemptGracefulDegradation(exerciseIds, 1)
                return
              } catch (degradationError) {
                logger.error(
                  '[useRobustPrefetchErrorHandling] Graceful degradation failed:',
                  degradationError
                )
              }
            }
          }
        }
      }

      // All retries exhausted
      setHasExceededMaxRetries(true)
      setRetryCount((prev) => prev + maxRetries)

      throw lastError || new Error('All retry attempts failed')
    },
    [
      checkCircuitBreakerRecovery,
      maxRetries,
      getRetryDelay,
      prefetchExerciseSets,
      recordError,
      isInDegradedMode,
      enableGracefulDegradation,
      categorizeError,
      attemptGracefulDegradation,
    ]
  )

  /**
   * Clear error history and reset counters
   */
  const clearErrorHistory = useCallback(() => {
    setErrorHistory([])
    setErrorsByType({
      network: 0,
      timeout: 0,
      memory: 0,
      server: 0,
      unknown: 0,
    })
    setRetryCount(0)
    setHasExceededMaxRetries(false)
    setIsCircuitBreakerOpen(false)
    setIsInDegradedMode(false)
    consecutiveFailuresRef.current = 0

    logger.log('[useRobustPrefetchErrorHandling] Error history cleared')
  }, [])

  return {
    prefetchWithRetry,
    retryCount,
    errorHistory,
    errorsByType,
    hasExceededMaxRetries,
    isCircuitBreakerOpen,
    isInDegradedMode,
    clearErrorHistory,
  }
}
