import { test, expect } from '@playwright/test'
import { login } from './helpers/auth'

const MOBILE_VIEWPORT = { width: 390, height: 844 }

test.describe('Exercise V2 - No Logout on Mount', () => {
  test.use({ viewport: MOBILE_VIEWPORT })

  test('should NOT log out user when opening exercise v2 page after extended time', async ({
    page,
  }) => {
    // Login the test user
    await page.goto('/login')
    await login(page)

    // Navigate to workout page to start a workout
    await page.goto('/workout')

    // Wait for the workout overview to load
    await page.waitForSelector('[data-testid="workout-overview"]', {
      timeout: 30000,
    })

    // Click the "Start workout" button
    await page.click('button:has-text("Start workout")')

    // Wait for navigation to exercise page
    await page.waitForURL(/\/workout\/exercise\/\d+/, { timeout: 10000 })

    // Navigate back to workout overview
    await page.goto('/workout')

    // Simulate extended time passing by modifying localStorage timestamp
    // This would have previously triggered session validation failure
    await page.evaluate(() => {
      const thirteenHoursAgo = Date.now() - 13 * 60 * 60 * 1000
      const workoutData = localStorage.getItem('drmuscle-workout')
      if (workoutData) {
        const parsed = JSON.parse(workoutData)
        if (parsed.state) {
          parsed.state.lastActiveTimestamp = thirteenHoursAgo
          localStorage.setItem('drmuscle-workout', JSON.stringify(parsed))
        }
      }
    })

    // Now navigate to exercise v2 page
    // Get the first exercise link with data-v2 attribute
    const exerciseV2Link = await page.locator('[data-v2="true"]').first()
    await exerciseV2Link.click()

    // Wait for the exercise v2 page to load
    await page.waitForURL(/\/workout\/exercise-v2\/\d+/, { timeout: 10000 })

    // Verify user is still logged in - check for exercise page elements
    await expect(
      page.locator('[data-testid="exercise-page-container"]')
    ).toBeVisible()

    // Verify we're NOT redirected to login page
    await expect(page).not.toHaveURL('/login')

    // Verify workout session is still active by checking for exercise UI elements
    await expect(page.locator('text=/Set \\d+ of \\d+/')).toBeVisible({
      timeout: 10000,
    })
  })

  test('should maintain workout session when repeatedly visiting exercise v2', async ({
    page,
  }) => {
    // Login the test user
    await page.goto('/login')
    await login(page)

    // Navigate to workout and start it
    await page.goto('/workout')
    await page.waitForSelector('[data-testid="workout-overview"]')
    await page.click('button:has-text("Start workout")')

    // Wait for navigation to first exercise
    await page.waitForURL(/\/workout\/exercise\/\d+/, { timeout: 10000 })

    // Go back to workout overview
    await page.goto('/workout')

    // Click multiple exercise v2 links to ensure session persists
    const exerciseV2Links = await page.locator('[data-v2="true"]').all()
    const linksToTest = Math.min(3, exerciseV2Links.length)

    for (let i = 0; i < linksToTest; i++) {
      // eslint-disable-next-line no-await-in-loop
      await exerciseV2Links[i].click()

      // Wait for exercise v2 page
      // eslint-disable-next-line no-await-in-loop
      await page.waitForURL(/\/workout\/exercise-v2\/\d+/, { timeout: 10000 })

      // Verify still logged in
      // eslint-disable-next-line no-await-in-loop
      await expect(
        page.locator('[data-testid="exercise-page-container"]')
      ).toBeVisible()

      // Go back to workout overview
      // eslint-disable-next-line no-await-in-loop
      await page.goto('/workout')
    }

    // Final verification - still logged in
    await expect(page).not.toHaveURL('/login')
  })
})
