import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { ExercisePageStates } from '../ExercisePageStates'
import type { ExerciseModel, RecommendationModel } from '@/types'

vi.mock('../ExerciseCompleteViewV2', () => ({
  ExerciseCompleteViewV2: ({ onContinue }: { onContinue: () => void }) => (
    <div data-testid="exercise-complete-view">
      <button onClick={onContinue}>Continue</button>
    </div>
  ),
}))

vi.mock('@/components/workout/WorkoutCompleteView', () => ({
  WorkoutCompleteView: () => <div>Workout Complete</div>,
}))

vi.mock('@/components/workout/SetScreenLoadingState', () => ({
  SetScreenLoadingState: () => <div>Loading...</div>,
}))

vi.mock('@/components/workout/SetScreenErrorState', () => ({
  SetScreenErrorState: ({ onRetry }: { onRetry: () => void }) => (
    <div>
      Error
      <button onClick={onRetry}>Retry</button>
    </div>
  ),
}))

vi.mock('@/components/workout/ExercisePageErrorBoundary', () => ({
  ExercisePageErrorBoundary: ({
    error,
    onRetry,
  }: {
    error: Error
    onRetry: () => void
  }) => (
    <div>
      Error: {error.message}
      <button onClick={onRetry}>Retry</button>
    </div>
  ),
  WorkoutErrorBoundary: ({ error }: { error: Error }) => (
    <div>Workout Error: {error.message}</div>
  ),
}))

describe('ExercisePageStates', () => {
  const mockExercise: ExerciseModel = {
    Id: 1,
    Label: 'Test Exercise',
    IsFinished: false,
  } as ExerciseModel

  const mockRecommendation: RecommendationModel = {
    Id: 1,
    OneRMProgress: 5,
  } as RecommendationModel

  const defaultProps = {
    loadingError: null,
    workoutError: null,
    retryInitialization: vi.fn(),
    isInitializing: false,
    isLoadingWorkout: false,
    isLoadingRecommendation: false,
    isLoading: false,
    recommendation: mockRecommendation,
    currentExercise: mockExercise,
    workoutSession: {},
    error: null,
    refetchRecommendation: vi.fn(),
    showComplete: false,
    showExerciseComplete: false,
    currentSet: null,
    isLastExercise: false,
    handleSaveSet: vi.fn(),
    handleNavigateToNext: vi.fn(),
    completedSets: [],
  }

  it('should not call handleNavigateToNext immediately when showing exercise complete', () => {
    const handleNavigateToNext = vi.fn()
    const handleSaveSet = vi.fn()

    render(
      <ExercisePageStates
        {...defaultProps}
        showExerciseComplete
        handleNavigateToNext={handleNavigateToNext}
        handleSaveSet={handleSaveSet}
      />
    )

    // The handlers should not be called immediately
    expect(handleNavigateToNext).not.toHaveBeenCalled()
    expect(handleSaveSet).not.toHaveBeenCalled()

    // Find and click the continue button
    const continueButton = screen.getByText('Continue')
    fireEvent.click(continueButton)

    // Now the handler should be called
    expect(handleNavigateToNext).toHaveBeenCalledTimes(1)
    expect(handleSaveSet).not.toHaveBeenCalled()
  })

  it('should use handleSaveSet when handleNavigateToNext is not provided', () => {
    const handleSaveSet = vi.fn()

    render(
      <ExercisePageStates
        {...defaultProps}
        showExerciseComplete
        handleNavigateToNext={undefined}
        handleSaveSet={handleSaveSet}
      />
    )

    // The handler should not be called immediately
    expect(handleSaveSet).not.toHaveBeenCalled()

    // Find and click the continue button
    const continueButton = screen.getByText('Continue')
    fireEvent.click(continueButton)

    // Now the handler should be called
    expect(handleSaveSet).toHaveBeenCalledTimes(1)
  })

  it('should show exercise complete when recommendation exists but no current set', () => {
    const handleNavigateToNext = vi.fn()

    render(
      <ExercisePageStates
        {...defaultProps}
        currentSet={null}
        recommendation={mockRecommendation}
        handleNavigateToNext={handleNavigateToNext}
      />
    )

    // Should show exercise complete view
    expect(screen.getByTestId('exercise-complete-view')).toBeInTheDocument()

    // Handler should not be called immediately
    expect(handleNavigateToNext).not.toHaveBeenCalled()
  })
})
