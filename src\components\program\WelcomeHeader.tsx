import React from 'react'
import { useAuthStore } from '@/stores/authStore'
import { useUserStatsStore } from '@/stores/userStatsStore'

/**
 * Get motivational message based on user's workout data
 * Simplified logic: streak first, then total workouts, then new user message
 */
export function getMotivationalMessage(): string {
  const { stats } = useUserStatsStore.getState()

  if (stats) {
    // If they have a current streak (priority)
    if (stats.weekStreak && stats.weekStreak >= 1) {
      if (stats.weekStreak === 1) {
        return '1 week strong—keep it up!'
      } else if (stats.weekStreak < 5) {
        return `${stats.weekStreak} weeks strong—keep it up!`
      } else {
        return `${stats.weekStreak} week streak—you're unstoppable!`
      }
    }

    // If they have completed workouts (no streak)
    if (stats.workoutsCompleted && stats.workoutsCompleted >= 1) {
      return `${stats.workoutsCompleted} workouts completed`
    }
  }

  // New user or no workout data
  return 'Ready to start your journey?'
}

/**
 * Safely truncate long names
 */
function truncateName(name: string, maxLength = 20): string {
  if (name.length <= maxLength) return name
  return `${name.substring(0, maxLength - 3)}...`
}

/**
 * Welcome header that displays immediately with available user data
 * Premium gold accent card design - powerful and engaging
 */
export function WelcomeHeader() {
  const user = useAuthStore((state) => state.user)
  const getCachedUserInfo = useAuthStore((state) => state.getCachedUserInfo)

  // Get user's name from cache or user object
  const cachedInfo = getCachedUserInfo()
  const firstName = cachedInfo?.firstName || user?.firstName

  // Safely handle the name display
  const displayName = firstName ? truncateName(firstName) : null
  const motivationalMessage = getMotivationalMessage()

  return (
    <div className="mb-6 animate-in fade-in zoom-in-95 duration-300">
      {/* Premium gold accent card */}
      <div className="relative">
        {/* Full premium gold gradient accent */}
        <div className="absolute -inset-[1px] bg-gradient-to-r from-brand-gold-start to-brand-gold-end rounded-lg" />

        {/* Premium card with enhanced shadow */}
        <div className="relative bg-surface-primary rounded-lg p-5 shadow-theme-lg border border-surface-tertiary/10">
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gradient-gold mb-2">
            {displayName ? <>Welcome back, {displayName}!</> : 'Welcome back!'}
          </h1>
          <p className="text-text-secondary">{motivationalMessage}</p>
        </div>
      </div>
    </div>
  )
}
