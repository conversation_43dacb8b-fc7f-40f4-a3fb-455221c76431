import { test, expect } from '@playwright/test'

const testUser = {
  email: '<EMAIL>',
  password: 'Dr123456',
}

test.describe('Auth Persistence', () => {
  test('should maintain user session after page reload', async ({ page }) => {
    // Given: User logs in successfully
    await page.goto('/login')

    // Fill in login form
    await page.fill('input[type="email"]', testUser.email)
    await page.fill('input[type="password"]', testUser.password)

    // Click login button
    await page.click('button[type="submit"]')

    // Wait for successful login and navigation
    await page.waitForURL('**/workout', { timeout: 10000 })

    // Verify user is logged in
    await expect(page.locator("text=Today's Workout")).toBeVisible()

    // When: Page is reloaded
    await page.reload()

    // Then: User should remain logged in
    await page.waitForLoadState('networkidle')

    // Should NOT be redirected to login page
    expect(page.url()).not.toContain('/login')

    // Should still see workout page
    await expect(page.locator("text=Today's Workout")).toBeVisible({
      timeout: 5000,
    })

    // Verify auth state is maintained
    const isAuthenticated = await page.evaluate(() => {
      // Access the auth store to check if user is still authenticated
      return (
        window.__ZUSTAND_STORES__?.auth?.getState?.()?.isAuthenticated ?? false
      )
    })
    expect(isAuthenticated).toBe(true)
  })

  test('should restore user data from cookies after reload', async ({
    page,
  }) => {
    // Given: User logs in
    await page.goto('/login')
    await page.fill('input[type="email"]', testUser.email)
    await page.fill('input[type="password"]', testUser.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('**/workout', { timeout: 10000 })

    // Get user email before reload
    const emailBeforeReload = await page.evaluate(() => {
      return window.__ZUSTAND_STORES__?.auth?.getState?.()?.user?.email
    })
    expect(emailBeforeReload).toBe(testUser.email)

    // When: Page is reloaded
    await page.reload()
    await page.waitForLoadState('networkidle')

    // Then: User data should be restored
    const emailAfterReload = await page.evaluate(() => {
      return window.__ZUSTAND_STORES__?.auth?.getState?.()?.user?.email
    })
    expect(emailAfterReload).toBe(testUser.email)

    // Token should be present in API client
    const hasToken = await page.evaluate(() => {
      // Check if the API client has an auth token
      return (
        window.__API_CLIENT__?.defaults?.headers?.common?.Authorization !==
        undefined
      )
    })
    expect(hasToken).toBe(true)
  })

  test('should handle missing auth cookies gracefully', async ({
    page,
    context,
  }) => {
    // Given: User is logged in
    await page.goto('/login')
    await page.fill('input[type="email"]', testUser.email)
    await page.fill('input[type="password"]', testUser.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('**/workout', { timeout: 10000 })

    // When: Auth cookies are cleared and page is reloaded
    await context.clearCookies()
    await page.reload()

    // Then: User should be redirected to login
    await page.waitForURL('**/login', { timeout: 5000 })

    // Auth state should be cleared
    const authState = await page.evaluate(() => {
      const state = window.__ZUSTAND_STORES__?.auth?.getState?.()
      return {
        isAuthenticated: state?.isAuthenticated ?? false,
        user: state?.user ?? null,
        token: state?.token ?? null,
      }
    })
    expect(authState.isAuthenticated).toBe(false)
    expect(authState.user).toBeNull()
    expect(authState.token).toBeNull()
  })
})
