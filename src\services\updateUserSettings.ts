import { apiClient } from '@/api/client'
import type {
  LocalSettings,
  UserSettingsApiPayload,
  SettingsValidationError,
} from '@/types/settings'

/**
 * Gets authentication token from the established auth system.
 * Uses dynamic import to avoid circular dependencies.
 */
function getAuthToken(): string | null {
  // Access token from API client headers (set by authStore)
  const authHeader = apiClient.defaults.headers.common[
    'Authorization'
  ] as string
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7) // Remove "Bearer " prefix
  }
  return null
}

/**
 * Validates settings input to prevent invalid API calls.
 * Security-first approach - validate on client before server.
 */
function validateSettingsInput(settings: LocalSettings): void {
  // Validate rep range
  if (settings.repsMin < 1 || settings.repsMin > 50) {
    throw new Error('Minimum reps must be between 1 and 50')
  }

  if (settings.repsMax < 1 || settings.repsMax > 50) {
    throw new Error('Maximum reps must be between 1 and 50')
  }

  if (settings.repsMin >= settings.repsMax) {
    throw new Error('Minimum reps must be less than maximum reps')
  }

  // Validate weight increment
  if (settings.weightIncrement <= 0) {
    throw new Error('Weight increment must be positive')
  }

  // Validate warmup sets
  if (settings.warmupSets < 0 || settings.warmupSets > 6) {
    throw new Error('Warmup sets must be between 0 and 6')
  }
}

/**
 * Maps local settings format to Dr. Muscle API payload structure.
 * Handles complex business logic for set style mapping.
 */
function mapSettingsToApiPayload(
  settings: LocalSettings
): UserSettingsApiPayload {
  const payload: UserSettingsApiPayload = {
    IsQuickMode: settings.quickMode,
    MassUnit: settings.weightUnit,
    RepsMinimum: settings.repsMin,
    RepsMaximum: settings.repsMax,
    WeightIncrement: settings.weightIncrement,
    WarmupSets: settings.warmupSets,
    IsNormalSet: true, // Default
  }

  // Map set style to API flags (complex business logic)
  switch (settings.setStyle) {
    case 'Normal':
      payload.IsNormalSet = true
      break
    case 'Rest-Pause':
      payload.IsNormalSet = false
      break
    case 'Drop':
      payload.IsNormalSet = true
      payload.IsDropSet = true
      break
    case 'Pyramid':
      payload.IsNormalSet = false
      payload.IsPyramid = true
      break
    case 'Reverse Pyramid':
      payload.IsNormalSet = false
      payload.IsReversePyramid = true
      break
    default:
      payload.IsNormalSet = true
  }

  return payload
}

/**
 * Maps API errors to user-friendly error messages.
 * Handles all error scenarios defined in our tests.
 */
function mapApiErrorToUserError(error: unknown): Error {
  // Handle error objects that look like AxiosError (for testing and real errors)
  if (
    error &&
    typeof error === 'object' &&
    ('response' in error || 'request' in error)
  ) {
    const axiosLikeError = error as {
      response?: {
        status: number
        data?: { message?: string; errors?: Record<string, string> }
      }
      request?: unknown
      message?: string
    }

    // Handle HTTP error responses
    if (axiosLikeError.response) {
      const { status } = axiosLikeError.response
      const { data } = axiosLikeError.response

      switch (status) {
        case 401:
          return new Error('Authentication failed. Please log in again.')

        case 400: {
          // Validation errors - preserve field details for UI consumption
          const validationError: SettingsValidationError = {
            name: 'ValidationError' as const,
            message: data?.message || 'Validation failed',
            validationErrors:
              data?.errors && typeof data.errors === 'object'
                ? data.errors
                : {},
            stack: new Error().stack,
          }

          return validationError as Error
        }

        case 403:
          return new Error('You do not have permission to update settings.')

        case 404:
          return new Error(
            'Settings endpoint not found. Please try again later.'
          )

        case 429:
          return new Error(
            'Too many requests. Please wait a moment and try again.'
          )

        case 500:
          return new Error('Server error. Please try again later.')

        case 502:
        case 503:
          return new Error(
            'Service temporarily unavailable. Please try again later.'
          )

        case 504:
          return new Error(
            'Request timeout. Please check your connection and try again.'
          )

        default:
          return new Error(`Unexpected error (${status}). Please try again.`)
      }
    }

    // Network errors (no response received)
    if (axiosLikeError.request) {
      return new Error(
        'Network error. Please check your connection and try again.'
      )
    }

    // Request setup errors
    return new Error(
      `Request error: ${axiosLikeError.message || 'Unknown error'}`
    )
  }

  // Generic errors
  if (error instanceof Error) {
    return error
  }

  // Unknown error types
  return new Error('An unexpected error occurred. Please try again.')
}

/**
 * Updates user settings on the server.
 * Implements comprehensive error handling and validation.
 *
 * @param settings - Local settings to save to server
 * @returns Promise with success status and optional data
 * @throws Error for validation failures or authentication issues
 */
export async function updateUserSettings(
  settings: LocalSettings
): Promise<{ success: boolean; data?: unknown }> {
  try {
    // Step 1: Validate input (security-first approach)
    validateSettingsInput(settings)

    // Step 2: Check authentication
    const token = getAuthToken()
    if (!token) {
      throw new Error('No authentication token available')
    }

    // Step 3: Map to API format
    const apiPayload = mapSettingsToApiPayload(settings)

    // Step 4: Make API call with proper headers
    const response = await apiClient.patch(
      '/api/User/UpdateSettings',
      apiPayload,
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      }
    )

    // Step 5: Return success result
    return {
      success: true,
      data: response.data,
    }
  } catch (error) {
    // Map all errors to user-friendly messages
    throw mapApiErrorToUserError(error)
  }
}

/**
 * Type guard to check if an error is a settings validation error.
 */
export function isSettingsValidationError(
  error: unknown
): error is SettingsValidationError {
  return error instanceof Error && 'validationErrors' in error
}
