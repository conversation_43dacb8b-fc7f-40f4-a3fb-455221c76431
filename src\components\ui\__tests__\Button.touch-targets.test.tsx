/**
 * UI Button Component Touch Target Compliance Tests
 *
 * Purpose: Validate Button component meets 52px minimum touch target requirement
 * Context: Tests written to fail initially, enforcing TDD implementation
 * Critical: Button is most widely used interactive component
 */

import React from 'react'
import { render, screen } from '@testing-library/react'
import { But<PERSON> } from '../Button'

// Utility function to measure rendered element dimensions
function measureElementDimensions(element: Element) {
  const computedStyle = getComputedStyle(element)
  return {
    height: parseFloat(computedStyle.height) || 0,
    minHeight: parseFloat(computedStyle.minHeight) || 0,
    width: parseFloat(computedStyle.width) || 0,
    minWidth: parseFloat(computedStyle.minWidth) || 0,
  }
}

// Utility to check touch target compliance
function assertTouchTargetCompliance(element: Element, minimum = 52) {
  const { height, minHeight, width, minWidth } =
    measureElementDimensions(element)
  const effectiveHeight = Math.max(height, minHeight)
  const effectiveWidth = Math.max(width, minWidth)

  expect(effectiveHeight).toBeGreaterThanOrEqual(minimum)
  expect(Math.min(effectiveHeight, effectiveWidth)).toBeGreaterThanOrEqual(
    minimum
  )
}

describe('Button Component - Touch Target Compliance', () => {
  describe('size variant touch targets', () => {
    it('should render small buttons with minimum 52px touch target', () => {
      render(<Button size="sm">Small Button</Button>)
      const button = screen.getByRole('button')

      // This test SHOULD FAIL initially as current sm size is h-11 (44px)
      assertTouchTargetCompliance(button, 52)

      // Specific height assertion - should be at least 52px
      const { height } = measureElementDimensions(button)
      expect(height).toBeGreaterThanOrEqual(52)
    })

    it('should render medium buttons with minimum 52px touch target', () => {
      render(<Button size="md">Medium Button</Button>)
      const button = screen.getByRole('button')

      // This test SHOULD FAIL initially as current md size is h-12 (48px)
      assertTouchTargetCompliance(button, 52)

      // Medium should be larger than small but still meet minimum
      const { height } = measureElementDimensions(button)
      expect(height).toBeGreaterThanOrEqual(52)
    })

    it('should render large buttons with touch targets exceeding 52px', () => {
      render(<Button size="lg">Large Button</Button>)
      const button = screen.getByRole('button')

      // Large size should already meet requirement (currently h-14 = 56px)
      assertTouchTargetCompliance(button, 52)

      const { height } = measureElementDimensions(button)
      expect(height).toBeGreaterThanOrEqual(56) // Should exceed minimum comfortably
    })
  })

  describe('button states maintain touch targets', () => {
    it('should maintain 52px touch targets in loading state', () => {
      render(<Button loading>Loading Button</Button>)
      const button = screen.getByRole('button')

      assertTouchTargetCompliance(button, 52)

      // Loading state should not reduce touch target size
      const { height } = measureElementDimensions(button)
      expect(height).toBeGreaterThanOrEqual(52)
    })

    it('should maintain 52px touch targets when disabled', () => {
      render(<Button disabled>Disabled Button</Button>)
      const button = screen.getByRole('button')

      assertTouchTargetCompliance(button, 52)

      // Disabled state should preserve dimensions
      const { height } = measureElementDimensions(button)
      expect(height).toBeGreaterThanOrEqual(52)
    })

    it('should maintain 52px touch targets with fullWidth prop', () => {
      render(<Button fullWidth>Full Width Button</Button>)
      const button = screen.getByRole('button')

      assertTouchTargetCompliance(button, 52)

      // Full width should not affect height
      const { height, width } = measureElementDimensions(button)
      expect(height).toBeGreaterThanOrEqual(52)
      expect(width).toBeGreaterThan(200) // Should be wide
    })
  })

  describe('button variants maintain touch targets', () => {
    const variants = [
      'primary',
      'secondary',
      'ghost',
      'danger',
      'gold',
    ] as const

    variants.forEach((variant) => {
      it(`should maintain 52px touch targets for ${variant} variant`, () => {
        render(<Button variant={variant}>{variant} Button</Button>)
        const button = screen.getByRole('button')

        assertTouchTargetCompliance(button, 52)

        const { height } = measureElementDimensions(button)
        expect(height).toBeGreaterThanOrEqual(52)
      })
    })
  })

  describe('touch target regression prevention', () => {
    it('should never have touch targets smaller than 52px', () => {
      // Test all size and variant combinations
      const sizes = ['sm', 'md', 'lg'] as const
      const variants = ['primary', 'secondary', 'ghost'] as const

      sizes.forEach((size) => {
        variants.forEach((variant) => {
          render(
            <Button
              size={size}
              variant={variant}
              data-testid={`${size}-${variant}`}
            >
              Test Button
            </Button>
          )

          const button = screen.getByTestId(`${size}-${variant}`)
          assertTouchTargetCompliance(button, 52)
        })
      })
    })

    it('should maintain consistent minimum touch targets across renders', () => {
      const { rerender } = render(<Button size="sm">Initial</Button>)
      let button = screen.getByRole('button')
      const initialHeight = measureElementDimensions(button).height

      // Re-render with different content
      rerender(<Button size="sm">Different Content Length Here</Button>)
      button = screen.getByRole('button')
      const newHeight = measureElementDimensions(button).height

      // Height should remain consistent (≥52px) regardless of content
      expect(initialHeight).toBeGreaterThanOrEqual(52)
      expect(newHeight).toBeGreaterThanOrEqual(52)
      expect(Math.abs(newHeight - initialHeight)).toBeLessThan(4) // Allow minor variations
    })
  })

  describe('accessibility and mobile optimization', () => {
    it('should provide adequate touch targets for mobile users', () => {
      render(<Button>Mobile Touch Target</Button>)
      const button = screen.getByRole('button')

      // Mobile-specific touch target requirements
      assertTouchTargetCompliance(button, 52)

      // Should have touch-manipulation class for mobile optimization
      expect(button).toHaveClass('touch-manipulation')
    })

    it('should maintain touch targets with custom className', () => {
      render(<Button className="custom-styles">Custom Button</Button>)
      const button = screen.getByRole('button')

      // Custom classes should not override touch target minimums
      assertTouchTargetCompliance(button, 52)
    })
  })
})

/**
 * Test Rationale:
 *
 * 1. These tests are designed to FAIL initially because current Button implementation
 *    uses h-11 (44px) for sm and h-12 (48px) for md sizes
 *
 * 2. Tests enforce 52px minimum across all size variants and states
 *
 * 3. Comprehensive coverage of button states (loading, disabled, variants)
 *    ensures touch target compliance is maintained in all scenarios
 *
 * 4. Regression prevention tests ensure future changes don't break compliance
 *
 * 5. Mobile-specific tests validate touch-manipulation and responsive behavior
 *
 * Expected Coverage: 95%+ - All button variants, states, and edge cases
 * Test Reliability: High - Uses actual DOM measurement utilities
 * Maintenance: Easy - Clear test structure with utility functions
 */
