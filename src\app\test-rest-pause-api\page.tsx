'use client'

import { useState, useEffect } from 'react'
import { getRecommendationForExercise } from '@/api/exercise/recommendations'
import { generateAllSets } from '@/utils/generateAllSets'
import type { RecommendationModel, ExerciseModel } from '@/types'

export default function TestRestPauseAPIPage() {
  const [recommendation, setRecommendation] =
    useState<RecommendationModel | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [setStyle, setSetStyleState] = useState<string>('')

  useEffect(() => {
    // Get current SetStyle from localStorage
    const currentSetStyle = localStorage.getItem('SetStyle') || 'Normal'
    setSetStyleState(currentSetStyle)
  }, [])

  const handleSetStyleChange = (newSetStyle: string) => {
    localStorage.setItem('SetStyle', newSetStyle)
    setSetStyleState(newSetStyle)
    // SetStyle changed
  }

  const testRestPauseAPI = async () => {
    setLoading(true)
    setError(null)
    setRecommendation(null)

    try {
      // Testing rest-pause API

      const request = {
        ExerciseId: 123,
        WorkoutId: 1,
        Username: '<EMAIL>',
        IsQuickMode: false,
        // SetStyle: removed - now determined server-side
        IsFlexibility: false,
        IsStrengthPhashe: false,
        IsFreePlan: false,
        IsFirstWorkoutOfStrengthPhase: false,
        LightSessionDays: null,
        SwapedExId: undefined,
        VersionNo: 1,
      }

      // Create exercise object for endpoint selection
      const exercise = {
        Id: 123,
        Label: 'Test Exercise',
        IsBodyweight: false,
        IsFlexibility: false,
        SetStyle: 'Normal',
      } as ExerciseModel

      const result = await getRecommendationForExercise(request, exercise)

      // API Response received
      setRecommendation(result)
    } catch (err) {
      console.error('API Error:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }

  const allSets = recommendation
    ? generateAllSets({
        recommendation,
        completedSets: [],
        currentSetIndex: 0,
        setData: null,
        unit: 'lbs',
        isBodyweight: false, // Default to false for test page
      })
    : []

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Rest-Pause API Test</h1>

      <div className="mb-6 p-4 bg-gray-50 rounded">
        <h2 className="text-lg font-semibold mb-3">SetStyle Configuration</h2>
        <div className="flex gap-4 mb-4">
          <button
            onClick={() => handleSetStyleChange('Normal')}
            className={`px-4 py-2 rounded ${setStyle === 'Normal' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
          >
            Normal
          </button>
          <button
            onClick={() => handleSetStyleChange('RestPause')}
            className={`px-4 py-2 rounded ${setStyle === 'RestPause' ? 'bg-orange-500 text-white' : 'bg-gray-200'}`}
          >
            RestPause
          </button>
          <button
            onClick={() => handleSetStyleChange('Pyramid')}
            className={`px-4 py-2 rounded ${setStyle === 'Pyramid' ? 'bg-green-500 text-white' : 'bg-gray-200'}`}
          >
            Pyramid
          </button>
        </div>
        <p className="text-sm text-gray-600">
          Current SetStyle: <strong>{setStyle}</strong>
        </p>
      </div>

      <div className="mb-6">
        <button
          onClick={testRestPauseAPI}
          disabled={loading}
          className="px-6 py-3 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
        >
          {loading ? 'Loading...' : 'Test API Call'}
        </button>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded">
          <h3 className="font-semibold text-red-800">Error:</h3>
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {recommendation && (
        <div className="space-y-6">
          <div className="p-4 bg-blue-50 border border-blue-200 rounded">
            <h2 className="text-lg font-semibold mb-2">API Response:</h2>
            <pre className="text-sm overflow-auto bg-white p-3 rounded border">
              {JSON.stringify(
                {
                  ExerciseId: recommendation.ExerciseId,
                  Series: recommendation.Series,
                  Reps: recommendation.Reps,
                  NbPauses: recommendation.NbPauses,
                  NbRepsPauses: recommendation.NbRepsPauses,
                  RpRest: recommendation.RpRest,
                  IsNormalSets: recommendation.IsNormalSets,
                  IsPyramid: recommendation.IsPyramid,
                  IsReversePyramid: recommendation.IsReversePyramid,
                  IsBackOffSet: recommendation.IsBackOffSet,
                  IsDropSet: recommendation.IsDropSet,
                  Weight: recommendation.Weight,
                  WarmupsCount: recommendation.WarmupsCount,
                },
                null,
                2
              )}
            </pre>
          </div>

          <div className="p-4 bg-green-50 border border-green-200 rounded">
            <h2 className="text-lg font-semibold mb-2">
              Generated Sets ({allSets.length} total):
            </h2>
            <div className="space-y-2">
              {allSets.map((set) => {
                const isRestPause =
                  set.SetTitle?.includes('Rest-pause') ||
                  set.SetTitle?.includes('All right!')

                let setClass = 'bg-green-100 border-green-300'
                if (set.IsWarmups) {
                  setClass = 'bg-blue-100 border-blue-300'
                } else if (isRestPause) {
                  setClass = 'bg-orange-100 border-orange-300'
                }

                let setType = 'Working'
                if (set.IsWarmups) {
                  setType = 'Warmup'
                } else if (isRestPause) {
                  setType = 'Rest-pause'
                }

                return (
                  <div
                    key={`set-${set.Id}-${set.SetNo}`}
                    className={`p-3 rounded border ${setClass}`}
                  >
                    <div className="flex justify-between items-center">
                      <span className="font-medium">
                        Set {set.SetNo} {set.SetTitle && `- ${set.SetTitle}`}
                      </span>
                      <span className="text-sm text-gray-600">{setType}</span>
                    </div>
                    <div className="text-sm mt-1">
                      {set.Reps} reps @ {set.Weight.Lb}lbs ({set.Weight.Kg}kg)
                      {set.restTime && ` • ${set.restTime}s rest`}
                      {(set.NbPause ?? 0) > 0 && ` • NbPause: ${set.NbPause}`}
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
