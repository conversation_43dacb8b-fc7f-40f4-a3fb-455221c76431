---
description: Create a PR from staging to main, update version, and generate announcement following docs/web-app-announcement-format.md
allowedTools: ['Bash', 'Read', 'Write', 'Edit', 'Grep', 'WebFetch']
---

Following the process documented in our repository, I'll create a pull request from staging into main and generate the announcement for our blog following docs/web-app-announcement-format.md.

## Steps:

1. Update version number to today's date format (0.YYYY.MM.DD.X)
2. Ensure we're up to date and check the staging branch status
3. Analyze changes between staging and main
4. Generate performance metrics
5. Create the pull request
6. Generate the announcement following the template

Let me execute this workflow:

### 1. Update version number

First, I'll update the version to today's date format:

```bash
# Get today's date in YYYY.MM.DD format
TODAY=$(date +"%Y.%m.%d")
NEW_VERSION="0.${TODAY}.1"

echo "Updating version to: ${NEW_VERSION}"
```

Now I'll update the version in the codebase:

- src/constants/version.ts
- src/components/**tests**/UserMenu.test.tsx
- tests/e2e/user-menu-version.spec.ts

### 2. Update and check branch status

```bash
git fetch origin
git checkout main
git pull origin main
git checkout staging
git pull origin staging
```

### 3. Analyze changes

```bash
# Get the changes between main and staging
git log main..staging --oneline

# Get detailed diff statistics
git diff main...staging --stat

# Check for any test coverage changes
npm run test:coverage || true
```

### 4. Gather performance metrics

```bash
# Build and analyze bundle size
npm run build
npm run analyze || true

# Get current bundle size
ls -lh .next/static/chunks/*.js | head -5

# Run performance tests if available
npm run test:performance || true
```

### 5. Create the pull request

Based on the changes, I'll create a comprehensive pull request from staging to main with:

- Clear title summarizing the main changes
- Detailed description of all features, improvements, and fixes
- Performance metrics
- Test results

```bash
# Create PR from staging to main
gh pr create \
  --base main \
  --head staging \
  --title "chore(release): merge staging updates to main" \
  --body "$(cat <<'EOF'
## Summary
This PR merges the latest staging updates into main, including performance improvements, bug fixes, and new features.

## Changes
[Will be populated based on git log analysis]

## Performance
- Bundle size: [SIZE]
- LCP: [TIME]
- Test coverage: [PERCENTAGE]

## Testing
- All tests passing
- E2E tests validated on mobile viewports
- Cross-browser compatibility verified

EOF
)"
```

### 6. Generate announcement

Following docs/web-app-announcement-format.md, I'll analyze the changes and output the announcement directly here in markdown format for you to copy and use:

---

## 📢 Blog Announcement

_The announcement below is generated based on the actual changes between staging and main:_

```markdown
## [Main Feature Title from Changes]

### ✨ New

- **[Feature Name]** - [User-focused description]

### 🚀 Improvements

- **[Improvement Name]** - [Description of what was improved]

### 🔧 Fixes

- Fixed [issue description in user-friendly terms]

### 📊 Tech

- PR: [#NUMBER](https://github.com/dr-muscle/DrMuscleWebApp/pull/NUMBER)
- Performance:
  - Bundle size: [SIZE] ([CHANGE from previous])
  - LCP: [TIME] (✅/⚠️/❌)
  - Test coverage: [PERCENTAGE] ([CHANGE])
- Version: ${NEW_VERSION}

### 🔜 Coming soon

- [Upcoming feature or improvement]
```

---

You can copy this announcement and post it to your blog or communication channels.

$ARGUMENTS
