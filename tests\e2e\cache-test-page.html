<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cache System Test Page</title>
</head>
<body>
    <h1>Cache System Test Page</h1>
    <div id="test-results"></div>

    <script>
        // Cache System Test Utilities
        window.CacheSystemTest = {
            async testBasicOperations() {
                const results = {
                    localStorage: { canStore: false, canRetrieve: false, canDelete: false },
                    sessionStorage: { canStore: false, canRetrieve: false, canDelete: false },
                    memory: { canStore: true, canRetrieve: true, canDelete: true }
                };

                // Test localStorage
                try {
                    localStorage.setItem('test-key', JSON.stringify({ value: 'test-value', timestamp: Date.now() }));
                    results.localStorage.canStore = true;
                    
                    const stored = localStorage.getItem('test-key');
                    const parsed = stored ? JSON.parse(stored) : null;
                    results.localStorage.canRetrieve = parsed?.value === 'test-value';
                    
                    localStorage.removeItem('test-key');
                    results.localStorage.canDelete = !localStorage.getItem('test-key');
                } catch (e) {
                    console.warn('localStorage test failed:', e);
                }

                // Test sessionStorage
                try {
                    sessionStorage.setItem('test-key', JSON.stringify({ value: 'test-value', timestamp: Date.now() }));
                    results.sessionStorage.canStore = true;
                    
                    const stored = sessionStorage.getItem('test-key');
                    const parsed = stored ? JSON.parse(stored) : null;
                    results.sessionStorage.canRetrieve = parsed?.value === 'test-value';
                    
                    sessionStorage.removeItem('test-key');
                    results.sessionStorage.canDelete = !sessionStorage.getItem('test-key');
                } catch (e) {
                    console.warn('sessionStorage test failed:', e);
                }

                return results;
            },

            async testTTL() {
                const now = Date.now();
                const ttl = 100; // 100ms

                try {
                    // Store with TTL
                    localStorage.setItem('ttl-test', JSON.stringify({
                        value: 'ttl-value',
                        expires: now + ttl
                    }));

                    // Should be available immediately
                    const immediate = localStorage.getItem('ttl-test');
                    const immediateValid = immediate && JSON.parse(immediate).expires > Date.now();

                    // Wait for expiration
                    await new Promise(resolve => setTimeout(resolve, ttl + 50));

                    // Should be expired
                    const expired = localStorage.getItem('ttl-test');
                    const expiredValid = expired && JSON.parse(expired).expires <= Date.now();

                    // Clean up
                    localStorage.removeItem('ttl-test');

                    return {
                        immediatelyAvailable: !!immediateValid,
                        expiredCorrectly: !!expiredValid
                    };
                } catch (e) {
                    console.warn('TTL test failed:', e);
                    return {
                        immediatelyAvailable: false,
                        expiredCorrectly: false
                    };
                }
            },

            async testNamespaces() {
                try {
                    // Test namespace isolation
                    localStorage.setItem('ns1:shared-key', JSON.stringify({ value: 'value1', namespace: 'ns1' }));
                    localStorage.setItem('ns2:shared-key', JSON.stringify({ value: 'value2', namespace: 'ns2' }));

                    const ns1Value = localStorage.getItem('ns1:shared-key');
                    const ns2Value = localStorage.getItem('ns2:shared-key');

                    const result = {
                        ns1Isolated: ns1Value && JSON.parse(ns1Value).value === 'value1',
                        ns2Isolated: ns2Value && JSON.parse(ns2Value).value === 'value2',
                        properIsolation: ns1Value !== ns2Value
                    };

                    // Clean up
                    localStorage.removeItem('ns1:shared-key');
                    localStorage.removeItem('ns2:shared-key');

                    return result;
                } catch (e) {
                    console.warn('Namespace test failed:', e);
                    return {
                        ns1Isolated: false,
                        ns2Isolated: false,
                        properIsolation: false
                    };
                }
            },

            async testQuotaHandling() {
                try {
                    // Try to store a moderately large amount of data
                    const largeData = 'x'.repeat(100 * 1024); // 100KB
                    localStorage.setItem('large-data', largeData);
                    
                    const retrieved = localStorage.getItem('large-data');
                    const success = retrieved === largeData;
                    
                    // Clean up
                    localStorage.removeItem('large-data');

                    return {
                        canHandleLargeData: success,
                        quotaExceeded: false
                    };
                } catch (error) {
                    return {
                        canHandleLargeData: false,
                        quotaExceeded: error.name === 'QuotaExceededError' || error.code === 22
                    };
                }
            },

            async testCrossTabSync() {
                // This would test cross-tab synchronization
                // For now, just return a placeholder
                return {
                    crossTabSync: true
                };
            }
        };

        // Log that the test utilities are ready
        console.log('Cache System Test utilities loaded');
    </script>
</body>
</html>
