/**
 * Race Condition Prevention Tests for useSettingsPersistence
 *
 * These tests verify that the auto-save effect does not create infinite loops
 * when saveError dependency changes, specifically targeting the root cause
 * identified at line 270 in useSettingsPersistence.ts
 */

import React from 'react'
import { renderHook, act } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest'
import { useSettingsPersistence } from '../useSettingsPersistence'

import { updateUserSettings } from '@/services/updateUserSettings'
import { useSettingsData } from '@/hooks/useSettingsData'
import { useAuthStore } from '@/stores/authStore'
import { useDebounce } from '@/hooks/useDebounce'
import { toast } from '@/components/ui/toast'

// Mock dependencies - NOT the hook we're testing
vi.mock('@/services/updateUserSettings')
vi.mock('@/hooks/useSettingsData')
vi.mock('@/stores/authStore')
vi.mock('@/hooks/useDebounce')
vi.mock('@/components/ui/toast')

const mockUpdateUserSettings = vi.mocked(updateUserSettings)
const mockUseSettingsData = vi.mocked(useSettingsData)
const mockUseAuthStore = vi.mocked(useAuthStore)
const mockUseDebounce = vi.mocked(useDebounce)
const mockToast = vi.mocked(toast)

// Test data
const mockServerSettings = {
  quickMode: false,
  weightUnit: 'lbs' as const,
  setStyle: 'Normal' as const,
  repRange: { min: 6, max: 12 },
  weightIncrement: 5,
  warmupSets: 2,
}

const mockUserInfo = {
  Email: '<EMAIL>',
  MassUnit: 'lbs',
  IsQuickMode: false,
  IsNormalSet: true,
}

describe('useSettingsPersistence - Race Condition Prevention', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.useFakeTimers()

    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: vi.fn(),
        setItem: vi.fn(),
        removeItem: vi.fn(),
        clear: vi.fn(),
      },
      writable: true,
    })

    // Setup default mocks
    mockUseSettingsData.mockReturnValue({
      data: mockServerSettings,
      isLoading: false,
      error: null,
    })

    mockUseAuthStore.mockReturnValue({
      clearUserInfoCache: vi.fn(),
      getCachedUserInfo: vi.fn().mockReturnValue(mockUserInfo),
    })

    mockUpdateUserSettings.mockResolvedValue({ success: true })
    mockToast.mockImplementation(() => {})

    // Setup useDebounce to return input immediately for testing
    mockUseDebounce.mockImplementation((value) => value)
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  describe('Effect Dependency Loop Prevention', () => {
    /**
     * Critical Test: Tests the root cause of race condition
     *
     * This test should FAIL initially due to saveError dependency causing
     * infinite loops in the auto-save effect (line 270).
     * After fix implementation, this test should PASS.
     */
    it('should prevent infinite loops when saveError dependency changes', async () => {
      const { result } = renderHook(() => useSettingsPersistence())

      // Wait for initial render
      await act(async () => {
        await new Promise((resolve) => setTimeout(resolve, 0))
      })

      expect(result.current).toBeDefined()
      expect(typeof result.current.updateSetting).toBe('function')

      // Step 1: Create a setting that will cause validation issues
      act(() => {
        result.current.updateSetting('repsMin', 15) // Invalid: > repsMax (12)
      })

      // Should have validation error
      expect(result.current.saveError).toContain('Minimum reps')

      // Step 2: Fix validation error
      act(() => {
        result.current.updateSetting('repsMax', 20) // This should clear validation error
      })

      // Validation error should be cleared
      expect(result.current.saveError).toBeNull()

      // Step 3: Trigger auto-save
      act(() => {
        vi.advanceTimersByTime(2500) // Trigger debounced auto-save
      })

      // Wait for async operations
      await act(async () => {
        await vi.runAllTimersAsync()
      })

      // Should have attempted auto-save exactly once
      expect(mockUpdateUserSettings).toHaveBeenCalledTimes(1)
      expect(result.current.isSaving).toBe(false)
    }, 10000)

    /**
     * Test that performAutoSave function reference remains stable
     * to prevent unnecessary effect re-runs
     */
    it('should maintain stable performAutoSave function reference', () => {
      const { result, rerender } = renderHook(() => useSettingsPersistence())

      const initialPerformAutoSave = result.current.performAutoSave

      // Force multiple re-renders
      rerender()
      rerender()
      rerender()

      // Function reference should remain stable (useCallback)
      expect(result.current.performAutoSave).toBe(initialPerformAutoSave)
    })

    /**
     * Test rapid validation state changes don't create loops
     */
    it('should handle rapid validation state changes without infinite loops', async () => {
      const saveErrorChangeSpy = vi.fn()

      const { result } = renderHook(() => {
        const persistence = useSettingsPersistence()

        // Track saveError changes
        React.useEffect(() => {
          saveErrorChangeSpy(persistence.saveError)
        }, [persistence.saveError])

        return persistence
      })

      // Rapid changes creating alternating validation states
      act(() => {
        result.current.updateSetting('repsMin', 15) // Error
      })
      act(() => {
        result.current.updateSetting('repsMin', 5) // Valid
      })
      act(() => {
        result.current.updateSetting('repsMin', 20) // Error
      })
      act(() => {
        result.current.updateSetting('repsMin', 8) // Valid
      })

      act(() => {
        vi.advanceTimersByTime(5000) // Well beyond debounce
      })

      // Should not have excessive saveError state changes
      expect(saveErrorChangeSpy.mock.calls.length).toBeLessThan(15) // Will FAIL initially
    })
  })

  describe('Validation Blocking Behavior', () => {
    /**
     * Test that validation errors still properly block auto-save
     * even after removing saveError from effect dependencies
     */
    it('should block auto-save when validation errors exist', async () => {
      const { result } = renderHook(() => useSettingsPersistence())

      // Create validation error
      act(() => {
        result.current.updateSetting('repsMin', 15) // Invalid: > repsMax
      })

      expect(result.current.saveError).toContain('Minimum reps')

      // Trigger auto-save attempt
      act(() => {
        vi.advanceTimersByTime(2500) // Beyond debounce delay
      })

      // Should NOT have called auto-save due to validation error
      expect(mockUpdateUserSettings).not.toHaveBeenCalled()
      expect(result.current.isSaving).toBe(false)
    })

    /**
     * Test that auto-save resumes after validation errors are fixed
     */
    it('should resume auto-save after validation errors are fixed', async () => {
      const { result } = renderHook(() => useSettingsPersistence())

      // Create and then fix validation error
      act(() => {
        result.current.updateSetting('repsMin', 15) // Invalid
      })
      expect(result.current.saveError).toContain('Minimum reps')

      act(() => {
        result.current.updateSetting('repsMax', 20) // Fix validation
      })
      expect(result.current.saveError).toBeNull()

      // Trigger auto-save
      act(() => {
        vi.advanceTimersByTime(2500)
      })

      // Should now proceed with auto-save
      expect(mockUpdateUserSettings).toHaveBeenCalledWith({
        quickMode: false,
        weightUnit: 'lbs',
        setStyle: 'Normal',
        repsMin: 15,
        repsMax: 20,
        weightIncrement: 5,
        warmupSets: 2,
      })
    })
  })

  describe('Memory Leak Prevention', () => {
    /**
     * Test that component cleanup works properly during validation error states
     */
    it('should cleanup properly when unmounting during validation errors', () => {
      const { result, unmount } = renderHook(() => useSettingsPersistence())

      // Create validation error
      act(() => {
        result.current.updateSetting('repsMin', 15) // Invalid
      })

      expect(result.current.saveError).toContain('Minimum reps')

      // Unmount component
      unmount()

      // Advance timers to see if any async operations continue
      act(() => {
        vi.advanceTimersByTime(5000)
      })

      // Should not have attempted any saves after unmount
      expect(mockUpdateUserSettings).not.toHaveBeenCalled()
    })
  })

  describe('Debounce Integration', () => {
    /**
     * Test that debounce works properly with validation error states
     */
    it('should respect debounce timing even with validation state changes', () => {
      // Setup debounce to actually delay (not immediate)
      mockUseDebounce.mockImplementation((value, delay) => {
        const [debouncedValue, setDebouncedValue] = React.useState(value)

        React.useEffect(() => {
          const timer = setTimeout(() => setDebouncedValue(value), delay)
          return () => clearTimeout(timer)
        }, [value, delay])

        return debouncedValue
      })

      const { result } = renderHook(() => useSettingsPersistence())

      // Make change that would trigger auto-save
      act(() => {
        result.current.updateSetting('quickMode', true)
      })

      // Should not save immediately (still debouncing)
      expect(mockUpdateUserSettings).not.toHaveBeenCalled()

      // Advance time but not full debounce period
      act(() => {
        vi.advanceTimersByTime(1000) // Less than 2-second debounce
      })

      expect(mockUpdateUserSettings).not.toHaveBeenCalled()

      // Complete debounce period
      act(() => {
        vi.advanceTimersByTime(1500) // Total 2.5 seconds
      })

      expect(mockUpdateUserSettings).toHaveBeenCalledTimes(1)
    })
  })
})

/**
 * Test Rationale:
 *
 * These tests are designed to FAIL initially, demonstrating the race condition
 * caused by saveError dependency in the auto-save effect. The key indicators:
 *
 * 1. Effect runs > 10 times (infinite loop detection)
 * 2. Excessive saveError state changes during rapid input
 * 3. Auto-save attempts despite validation errors
 *
 * After implementing the fix (removing saveError from dependencies and moving
 * validation check inside performAutoSave), these tests should PASS, proving
 * the race condition is resolved while maintaining validation blocking behavior.
 *
 * Coverage Target: 100% of race condition prevention logic paths
 */
