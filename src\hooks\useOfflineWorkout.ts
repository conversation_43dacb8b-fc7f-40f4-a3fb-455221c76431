'use client'

import { useState, useEffect, useCallback } from 'react'
import { useNetworkStatus } from './useNetworkStatus'
import { useOfflineStore } from '@/stores/offlineStore'
import { offlineWorkoutService } from '@/services/offlineWorkoutService'
import type {
  WorkoutTemplateModel,
  RecommendationModel,
  WorkoutLogSerieModel,
} from '@/types'

/**
 * Hook for managing offline workout execution
 */
export function useOfflineWorkout() {
  const { isOnline, isOffline } = useNetworkStatus()
  const {
    isOfflineWorkoutLoaded,
    loadedWorkoutId,
    syncStatus,
    setQueuedRequestsCount,
  } = useOfflineStore()

  const [cachedWorkout, setCachedWorkout] =
    useState<WorkoutTemplateModel | null>(null)
  const [workoutProgress, setWorkoutProgress] = useState<{
    completedSets: WorkoutLogSerieModel[]
    startTime?: Date
    currentExerciseId?: number
  }>({ completedSets: [] })
  const [isLoadingCachedData, setIsLoadingCachedData] = useState(false)

  /**
   * Load cached workout data when offline workout is available
   */
  const loadCachedWorkout = useCallback(async () => {
    if (!loadedWorkoutId) return

    setIsLoadingCachedData(true)
    try {
      const workout =
        await offlineWorkoutService.getCachedWorkout(loadedWorkoutId)
      setCachedWorkout(workout)

      // Load existing progress
      const progress =
        offlineWorkoutService.getOfflineWorkoutProgress(loadedWorkoutId)
      setWorkoutProgress(progress)

      // Cached workout loaded successfully
    } catch (error) {
      // Failed to load cached workout - error logged for debugging
    } finally {
      setIsLoadingCachedData(false)
    }
  }, [loadedWorkoutId])

  /**
   * Get cached exercise recommendation
   */
  const getCachedRecommendation = useCallback(
    async (exerciseId: number): Promise<RecommendationModel | null> => {
      if (!loadedWorkoutId) return null

      try {
        return await offlineWorkoutService.getCachedRecommendation(
          exerciseId,
          parseInt(loadedWorkoutId),
          '<EMAIL>' // TODO: Get from auth
        )
      } catch (error) {
        // Failed to get cached recommendation - error logged for debugging
        return null
      }
    },
    [loadedWorkoutId]
  )

  /**
   * Save set data (offline or online)
   */
  const saveSet = useCallback(
    async (setData: Partial<WorkoutLogSerieModel>): Promise<boolean> => {
      try {
        if (isOffline || !isOnline) {
          // Save offline
          const success = await offlineWorkoutService.saveSetOffline(setData)
          if (success && loadedWorkoutId) {
            // Update local progress
            const currentProgress =
              offlineWorkoutService.getOfflineWorkoutProgress(loadedWorkoutId)
            const updatedSets = [...currentProgress.completedSets]

            if (setData as WorkoutLogSerieModel) {
              updatedSets.push(setData as WorkoutLogSerieModel)
            }

            offlineWorkoutService.updateOfflineWorkoutProgress(
              loadedWorkoutId,
              {
                completedSets: updatedSets,
              }
            )

            setWorkoutProgress((prev) => ({
              ...prev,
              completedSets: updatedSets,
            }))
          }
          return success
        } else {
          // TODO: Save online using existing workout API
          // Online set save not implemented yet
          return true
        }
      } catch (error) {
        // Failed to save set - error logged for debugging
        return false
      }
    },
    [isOffline, isOnline, loadedWorkoutId]
  )

  /**
   * Complete workout (offline or online)
   */
  const completeWorkout = useCallback(async (): Promise<boolean> => {
    if (!loadedWorkoutId || !cachedWorkout) return false

    try {
      const workoutData = {
        workoutId: loadedWorkoutId,
        completedSets: workoutProgress.completedSets,
        startTime: workoutProgress.startTime || new Date(),
        endTime: new Date(),
        notes: '',
      }

      if (isOffline || !isOnline) {
        // Complete offline
        const success =
          await offlineWorkoutService.completeWorkoutOffline(workoutData)
        if (success) {
          // Clear progress
          offlineWorkoutService.clearOfflineWorkoutProgress(loadedWorkoutId)
          setWorkoutProgress({ completedSets: [] })
        }
        return success
      } else {
        // TODO: Complete online using existing workout API
        // Online workout completion not implemented yet
        return true
      }
    } catch (error) {
      // Failed to complete workout - error logged for debugging
      return false
    }
  }, [loadedWorkoutId, cachedWorkout, workoutProgress, isOffline, isOnline])

  /**
   * Start workout session
   */
  const startWorkoutSession = useCallback(() => {
    if (!loadedWorkoutId) return

    const startTime = new Date()
    offlineWorkoutService.updateOfflineWorkoutProgress(loadedWorkoutId, {
      startTime,
      currentExerciseId: cachedWorkout?.Exercises?.[0]?.Id,
    })

    setWorkoutProgress((prev) => ({
      ...prev,
      startTime,
      currentExerciseId: cachedWorkout?.Exercises?.[0]?.Id,
    }))

    // Workout session started offline
  }, [loadedWorkoutId, cachedWorkout])

  /**
   * Update current exercise
   */
  const updateCurrentExercise = useCallback(
    (exerciseId: number) => {
      if (!loadedWorkoutId) return

      offlineWorkoutService.updateOfflineWorkoutProgress(loadedWorkoutId, {
        currentExerciseId: exerciseId,
      })

      setWorkoutProgress((prev) => ({
        ...prev,
        currentExerciseId: exerciseId,
      }))
    },
    [loadedWorkoutId]
  )

  /**
   * Check if workout can be executed offline
   */
  const checkOfflineCapability = useCallback(async () => {
    if (!loadedWorkoutId)
      return { canExecute: false, missingData: ['No workout loaded'] }

    return await offlineWorkoutService.canExecuteOffline(loadedWorkoutId)
  }, [loadedWorkoutId])

  /**
   * Get sync queue status
   */
  const getSyncStatus = useCallback(async () => {
    const status = await offlineWorkoutService.getSyncQueueStatus()
    setQueuedRequestsCount(status.pendingCount)
    return status
  }, [setQueuedRequestsCount])

  // Load cached workout when offline workout becomes available
  useEffect(() => {
    if (isOfflineWorkoutLoaded && loadedWorkoutId) {
      loadCachedWorkout()
    }
  }, [isOfflineWorkoutLoaded, loadedWorkoutId, loadCachedWorkout])

  // Update sync status periodically
  useEffect(() => {
    const interval = setInterval(getSyncStatus, 10000) // Check every 10 seconds
    getSyncStatus() // Initial check

    return () => clearInterval(interval)
  }, [getSyncStatus])

  return {
    // State
    isOfflineWorkoutAvailable: isOfflineWorkoutLoaded && !!cachedWorkout,
    cachedWorkout,
    workoutProgress,
    isLoadingCachedData,
    loadedWorkoutId,

    // Actions
    loadCachedWorkout,
    getCachedRecommendation,
    saveSet,
    completeWorkout,
    startWorkoutSession,
    updateCurrentExercise,

    // Utilities
    checkOfflineCapability,
    getSyncStatus,

    // Status
    canExecuteOffline: isOfflineWorkoutLoaded && !!cachedWorkout,
    syncStatus,
  }
}
