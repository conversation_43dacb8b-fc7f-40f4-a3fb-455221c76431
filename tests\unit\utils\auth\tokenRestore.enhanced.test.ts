import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import {
  restoreAuthToken,
  resetTokenRestoration,
} from '@/utils/auth/tokenRestore'
import { setAuthToken } from '@/api/client'
import { useAuthStore } from '@/stores/authStore'

// Mock the API client
vi.mock('@/api/client', () => ({
  setAuthToken: vi.fn(),
  hasAuthToken: vi.fn(() => false),
}))

// Mock the auth store
vi.mock('@/stores/authStore', () => ({
  useAuthStore: {
    getState: vi.fn(() => ({
      restoreAuthFromCookies: vi.fn(),
    })),
  },
}))

// Mock fetch
global.fetch = vi.fn()

// Mock sessionStorage
const sessionStorageMock = (() => {
  let store: Record<string, string> = {}
  return {
    getItem: vi.fn((key: string) => store[key] || null),
    setItem: vi.fn((key: string, value: string) => {
      store[key] = value
    }),
    removeItem: vi.fn((key: string) => {
      delete store[key]
    }),
    clear: vi.fn(() => {
      store = {}
    }),
  }
})()

Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
  writable: true,
})

describe('restoreAuthToken with user data', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    sessionStorageMock.clear()
    // Reset the token restoration state
    resetTokenRestoration()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('should restore auth token and user data when authenticated', async () => {
    const mockToken = 'test-auth-token-12345'
    const mockUser = {
      email: '<EMAIL>',
      name: 'Test User',
    }

    // Mock the API response with user data
    vi.mocked(global.fetch).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        token: mockToken,
        authenticated: true,
        user: mockUser,
      }),
    } as Response)

    // Mock the auth store
    const mockRestoreAuth = vi.fn()
    vi.mocked(useAuthStore.getState).mockReturnValue({
      restoreAuthFromCookies: mockRestoreAuth,
    } as any)

    const result = await restoreAuthToken()

    // Should return success with user data
    expect(result).toEqual({
      success: true,
      token: mockToken,
      user: mockUser,
    })

    // Should call the API endpoint
    expect(global.fetch).toHaveBeenCalledWith('/api/auth/token', {
      credentials: 'include',
    })

    // Should set token in API client
    expect(setAuthToken).toHaveBeenCalledWith(mockToken)

    // Should call auth store to restore full state
    expect(mockRestoreAuth).toHaveBeenCalledWith(mockToken, mockUser)

    // Should mark as restored in sessionStorage
    expect(sessionStorageMock.setItem).toHaveBeenCalledWith(
      'dr-muscle-token-restored',
      'true'
    )
  })

  it('should return failure when not authenticated', async () => {
    vi.mocked(global.fetch).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        token: null,
        authenticated: false,
        user: null,
      }),
    } as Response)

    const result = await restoreAuthToken()

    expect(result).toEqual({
      success: false,
      token: null,
      user: null,
    })
    expect(setAuthToken).not.toHaveBeenCalled()
    expect(
      useAuthStore.getState().restoreAuthFromCookies
    ).not.toHaveBeenCalled()
  })

  it('should handle missing user data gracefully', async () => {
    const mockToken = 'test-token'

    vi.mocked(global.fetch).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        token: mockToken,
        authenticated: true,
        user: null,
      }),
    } as Response)

    const mockRestoreAuth = vi.fn()
    vi.mocked(useAuthStore.getState).mockReturnValue({
      restoreAuthFromCookies: mockRestoreAuth,
    } as any)

    const result = await restoreAuthToken()

    expect(result).toEqual({
      success: true,
      token: mockToken,
      user: null,
    })

    // Should still set token and call restore
    expect(setAuthToken).toHaveBeenCalledWith(mockToken)
    expect(mockRestoreAuth).toHaveBeenCalledWith(mockToken, null)
  })

  it('should skip restoration if already restored in session', async () => {
    // Mark as already restored
    sessionStorageMock.setItem('dr-muscle-token-restored', 'true')

    const result = await restoreAuthToken()

    expect(result).toEqual({
      success: true,
      token: null,
      user: null,
    })
    expect(global.fetch).not.toHaveBeenCalled()
  })

  it('should clear restoration flag when not authenticated', async () => {
    vi.mocked(global.fetch).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        token: null,
        authenticated: false,
        user: null,
      }),
    } as Response)

    const result = await restoreAuthToken()

    expect(result.success).toBe(false)
    // Should clear the restoration flag for expired sessions
    expect(sessionStorageMock.removeItem).toHaveBeenCalledWith(
      'dr-muscle-token-restored'
    )
  })

  it('should handle network errors', async () => {
    vi.mocked(global.fetch).mockRejectedValueOnce(new Error('Network error'))

    const result = await restoreAuthToken()

    expect(result).toEqual({
      success: false,
      token: null,
      user: null,
    })
  })

  it('should handle non-ok responses', async () => {
    vi.mocked(global.fetch).mockResolvedValueOnce({
      ok: false,
      status: 500,
    } as Response)

    const result = await restoreAuthToken()

    expect(result).toEqual({
      success: false,
      token: null,
      user: null,
    })
  })
})
