import type { RecommendationModel, WorkoutLogSerieModel } from '@/types'
import type { WorkoutLogSerieModelRef } from '@/types/api/WorkoutLogSerieModelRef'
import { WarmupCalculator, type WarmupSet } from './warmupCalculator'
import { logger } from '@/utils/logger'

// Minimal interface for user info - only need IsDropSet field
interface UserInfoWithDropSet {
  IsDropSet?: boolean
  [key: string]: unknown
}

interface GenerateAllSetsParams {
  recommendation: RecommendationModel | null
  completedSets: WorkoutLogSerieModel[]
  currentSetIndex: number
  setData: { reps: number; weight: number } | null
  unit: 'kg' | 'lbs'
  userInfo?: UserInfoWithDropSet | null
  isBodyweight?: boolean // Add bodyweight flag for proper drop set handling
}

/**
 * Generates all sets (warmup and work sets) based on recommendation and completed sets
 */
// Extended type to include warmup properties
type ExtendedWorkoutLogSerieModel = WorkoutLogSerieModel &
  Partial<WorkoutLogSerieModelRef> & {
    WarmUpReps?: number
    WarmUpWeightSet?: { Lb: number; Kg: number }
  }

/**
 * COMPREHENSIVE DROP SET NORMALIZATION
 * Handles inconsistent server responses for drop sets
 *
 * Server inconsistencies observed:
 * - Sometimes returns: IsDropSet=true, Series=4, NbPauses=0
 * - Sometimes returns: IsPyramid=true, Series=4, IsDropSet=false
 * - Sometimes returns: IsDropSet=true, Series=1, NbPauses=3
 *
 * IMPORTANT: Bodyweight exercises CANNOT be drop sets
 * - You cannot progressively reduce bodyweight during a set
 * - Bodyweight exercises should preserve their original set type
 */
function normalizeDropSetRecommendation(
  rec: RecommendationModel,
  userInfo?: UserInfoWithDropSet | null,
  isBodyweight = false
): RecommendationModel {
  // Get user's drop set preference from their profile
  // Default to false if not available (safer default)
  const userHasDropSetPreference = userInfo?.IsDropSet ?? false

  // Debug logging to see what we're getting
  logger.debug('🔧 [NORMALIZATION] Input:', {
    IsDropSet: rec.IsDropSet,
    IsPyramid: rec.IsPyramid,
    Series: rec.Series,
    NbPauses: rec.NbPauses,
    IsBodyweight: rec.IsBodyweight,
    isBodyweight,
    userHasDropSetPreference,
  })

  // CRITICAL: Bodyweight exercises CANNOT be drop sets
  // Check both rec.IsBodyweight and isBodyweight parameter
  const isBodyweightExercise = rec.IsBodyweight || isBodyweight

  if (isBodyweightExercise) {
    // If server incorrectly marked bodyweight as drop set, correct it
    if (rec.IsDropSet) {
      logger.warn(
        '[BODYWEIGHT FIX] Correcting bodyweight exercise incorrectly marked as drop set:',
        {
          ExerciseName: rec.ExerciseName,
          IsBodyweight: rec.IsBodyweight,
          isBodyweight,
          serverIsDropSet: rec.IsDropSet,
        }
      )

      // Convert to rest-pause if it has NbPauses, otherwise normal sets
      if (rec.NbPauses > 0) {
        return {
          ...rec,
          IsDropSet: false, // Cannot be drop set
          IsNormalSets: false, // Will be rest-pause
          // Keep NbPauses for rest-pause sets
        }
      } else {
        return {
          ...rec,
          IsDropSet: false, // Cannot be drop set
          IsNormalSets: true, // Regular sets
        }
      }
    }

    // Bodyweight pyramid/rest-pause should NOT convert to drop sets
    logger.debug(
      '[BODYWEIGHT] Preserving original set type for bodyweight exercise:',
      {
        ExerciseName: rec.ExerciseName,
        IsPyramid: rec.IsPyramid,
        IsRestPause: !rec.IsNormalSets && rec.NbPauses > 0,
      }
    )

    // Return unchanged - preserve pyramid/rest-pause for bodyweight
    return rec
  }

  // CASE 1: Server returns IsDropSet=true (ideal case for non-bodyweight)
  if (rec.IsDropSet) {
    logger.debug('[CASE 1] Server correctly returns IsDropSet=true:', {
      IsDropSet: rec.IsDropSet,
      Series: rec.Series,
      NbPauses: rec.NbPauses,
    })
    return rec // Already correct
  }

  // CASE 2: Server returns IsPyramid=true but user wants drop sets (non-bodyweight only)
  else if (rec.IsPyramid && userHasDropSetPreference) {
    logger.warn(
      '[CASE 2] Converting Pyramid to Drop Set per user preference:',
      {
        serverIsPyramid: rec.IsPyramid,
        serverSeries: rec.Series,
        userDropSetPreference: userHasDropSetPreference,
      }
    )

    return {
      ...rec,
      IsDropSet: true, // Enable drop set behavior
      IsPyramid: false, // Clear pyramid flag
      IsReversePyramid: false, // Clear reverse pyramid flag
    }
  }

  // CASE 3: Server returns rest-pause but user wants drop sets (non-bodyweight only)
  else if (!rec.IsNormalSets && rec.NbPauses > 0 && userHasDropSetPreference) {
    logger.warn(
      '[CASE 3] Converting Rest-pause to Drop Set per user preference:',
      {
        serverNbPauses: rec.NbPauses,
        userDropSetPreference: userHasDropSetPreference,
      }
    )

    return {
      ...rec,
      IsDropSet: true,
    }
  }

  // Debug logging to see what we're returning
  logger.debug('🔧 [NORMALIZATION] Output:', {
    IsDropSet: rec.IsDropSet,
    IsPyramid: rec.IsPyramid,
    Series: rec.Series,
    NbPauses: rec.NbPauses,
    IsBodyweight: rec.IsBodyweight,
  })

  return rec
}

export function generateAllSets({
  recommendation,
  completedSets,
  currentSetIndex,
  setData,
  unit,
  userInfo,
  isBodyweight = false,
}: GenerateAllSetsParams): ExtendedWorkoutLogSerieModel[] {
  if (!recommendation) return []

  // Normalize recommendation to handle server inconsistencies
  const normalizedRecommendation = normalizeDropSetRecommendation(
    recommendation,
    userInfo,
    isBodyweight
  )

  const sets: ExtendedWorkoutLogSerieModel[] = []
  const warmupCount = normalizedRecommendation.WarmupsCount || 0
  const workSetCount = normalizedRecommendation.Series || 0

  // ALWAYS calculate warmups locally - never use API warmup data
  let calculatedWarmups: WarmupSet[] = []
  if (warmupCount > 0) {
    // Always use warmup calculator to generate proper warmup progression
    // Never rely on API WarmUpsList or fallback values
    try {
      calculatedWarmups = WarmupCalculator.computeWarmups({
        warmupsCount: warmupCount,
        workingWeight: normalizedRecommendation.Weight,
        workingReps: normalizedRecommendation.Reps,
        incrementValue: normalizedRecommendation.Increments?.Kg || 1,
        minWeight: normalizedRecommendation.Min?.Kg,
        maxWeight: normalizedRecommendation.Max?.Kg,
        isPlateAvailable: false, // Conservative default
        isBodyweight: normalizedRecommendation.IsBodyweight || false,
        barbellWeight: 20, // Default 20kg barbell
        availablePlates: 'all',
        userBodyWeight: 70, // Default 70kg
        isKg: unit === 'kg',
      })

      // Calculated warmups successfully
    } catch (error) {
      // Failed to calculate warmups
      calculatedWarmups = []
    }
  }

  // Add warmup sets
  for (let i = 0; i < warmupCount; i++) {
    const isCompleted = completedSets.some(
      (s) => s.IsWarmups && parseInt(s.SetNo || '0') === i + 1
    )
    const completedSet = completedSets.find(
      (s) => s.IsWarmups && parseInt(s.SetNo || '0') === i + 1
    ) as ExtendedWorkoutLogSerieModel | undefined
    const isActiveSet = !isCompleted && currentSetIndex === i

    // Get warmup data from calculated warmups ONLY - never use API data
    const calculatedWarmup = calculatedWarmups[i]

    // Use calculated warmup data or safe defaults
    const warmupReps = calculatedWarmup?.WarmUpReps || 5
    const warmupWeight = calculatedWarmup?.WarmUpWeightSet || { Lb: 0, Kg: 0 }

    sets.push({
      Id: completedSet?.Id || -(1000 + i), // Use larger negative numbers to avoid conflicts
      SetNo: `${i + 1}`,
      // For warmup sets, use WarmUpReps and WarmUpWeightSet properties
      WarmUpReps:
        isActiveSet && setData
          ? setData.reps
          : completedSet?.WarmUpReps || warmupReps,
      WarmUpWeightSet: (() => {
        if (isActiveSet && setData) {
          return unit === 'kg'
            ? { Lb: Math.round(setData.weight * 2.20462), Kg: setData.weight }
            : { Lb: setData.weight, Kg: Math.round(setData.weight / 2.20462) }
        }
        return completedSet?.WarmUpWeightSet || warmupWeight
      })(),
      // Also include Reps and Weight for backward compatibility
      Reps: 0,
      Weight: { Lb: 0, Kg: 0 },
      IsFinished: isCompleted,
      IsNext: isActiveSet,
      IsWarmups: true,
    })

    // ------------------------------------------------------------------
    // TEMP DEBUG — log the first couple of warm-ups that are generated.
    // Remove once the blank-weight bug is fixed.
    // ------------------------------------------------------------------
    if (process.env.NODE_ENV === 'development') {
      const dbg = sets[sets.length - 1]
      if (dbg && dbg.IsWarmups && i < 2) {
        // eslint-disable-next-line no-console
        console.debug('[DEBUG] generateAllSets warm-up pushed', {
          index: i,
          fromCompletedSet:
            completedSet?.WarmUpWeightSet ?? completedSet?.Weight ?? null,
          pushedWarmUpWeightSet: dbg.WarmUpWeightSet,
          pushedWeight: dbg.Weight,
        })
      }
    }
  }

  // Calculate total work sets - for rest-pause, Series represents total work sets
  const isRestPause =
    !normalizedRecommendation.IsNormalSets &&
    !normalizedRecommendation.IsPyramid &&
    !normalizedRecommendation.IsReversePyramid &&
    !normalizedRecommendation.IsDropSet && // Drop sets take priority over rest-pause
    normalizedRecommendation.NbPauses > 0

  // MAUI Drop Set Rule: Enforce minimum 3 total sets for drop sets
  let totalWorkSets = workSetCount + (normalizedRecommendation.NbPauses || 0)
  let additionalDropSets = 0

  if (normalizedRecommendation.IsDropSet && totalWorkSets < 3) {
    additionalDropSets = 3 - totalWorkSets
    totalWorkSets = 3
    logger.debug('💧 [DROP SET] Enforcing minimum 3 sets rule:', {
      originalTotal: workSetCount + (normalizedRecommendation.NbPauses || 0),
      adjustedTotal: totalWorkSets,
      additionalDropSets,
    })
  }

  // Debug logging for set types
  if (isRestPause || normalizedRecommendation.NbPauses > 0) {
    logger.debug('🔄 REST-PAUSE detected:', {
      isRestPause,
      NbPauses: normalizedRecommendation.NbPauses,
      Series: normalizedRecommendation.Series,
      totalWorkSets,
    })
  }
  if (normalizedRecommendation.IsDropSet) {
    logger.debug('💧 DROP SET detected:', {
      IsDropSet: normalizedRecommendation.IsDropSet,
      Series: normalizedRecommendation.Series,
      Weight: normalizedRecommendation.Weight,
    })
  }
  if (normalizedRecommendation.IsPyramid) {
    logger.debug('🔺 PYRAMID detected:', {
      IsPyramid: normalizedRecommendation.IsPyramid,
      Series: normalizedRecommendation.Series,
    })
  }

  // Add work sets
  for (let i = 0; i < totalWorkSets; i++) {
    const setIndex = warmupCount + i
    const isCompleted = completedSets.some(
      (s) => !s.IsWarmups && parseInt(s.SetNo || '0') === setIndex + 1
    )
    const completedSet = completedSets.find(
      (s) => !s.IsWarmups && parseInt(s.SetNo || '0') === setIndex + 1
    ) as ExtendedWorkoutLogSerieModel | undefined
    const isActiveSet = !isCompleted && currentSetIndex === setIndex

    // Determine SetTitle based on mobile app's dual-loop pattern
    // Series loop (i < Series): Initial work sets
    // NbPauses loop (i >= Series): Additional sets based on IsDropSet flag
    let setTitle = ''
    let isBackOffSet = false
    let isDropSet = false
    let nbPause = 0

    // Mobile app dual-loop pattern implementation
    const isInSeriesLoop = i < normalizedRecommendation.Series
    // For drop sets with min 3 sets rule, we may have additional sets beyond NbPauses
    const isInNbPausesLoop = i >= normalizedRecommendation.Series
    const isAdditionalDropSet =
      normalizedRecommendation.IsDropSet &&
      i >=
        normalizedRecommendation.Series +
          (normalizedRecommendation.NbPauses || 0)

    // CORRECTED: When IsDropSet=true, ALL sets should be labeled "Drop set"
    if (normalizedRecommendation.IsDropSet) {
      setTitle = 'Drop set'
      isDropSet = true
    } else if (isInSeriesLoop) {
      // Series loop: Initial work set(s) when NOT drop sets
      if (normalizedRecommendation.IsPyramid && i === 0) {
        setTitle = 'Pyramid set:'
      } else if (normalizedRecommendation.IsReversePyramid && i === 0) {
        setTitle = 'Reverse pyramid set:'
      } else if (normalizedRecommendation.IsBackOffSet) {
        setTitle = 'Back-off set:'
        isBackOffSet = true
      } else if (i === 0 && normalizedRecommendation.Series > 1) {
        setTitle = 'Working sets:'
      }
    } else if (isInNbPausesLoop && !normalizedRecommendation.IsDropSet) {
      // NbPauses loop: Rest-pause sets when IsDropSet=false
      const pauseIndex = i - normalizedRecommendation.Series + 1
      setTitle =
        pauseIndex === 1
          ? "All right! Now let's try:"
          : `Rest-pause set ${pauseIndex}`
      nbPause = 1
    }

    // Determine reps based on mobile app dual-loop pattern with MAUI parity
    const targetReps = (() => {
      if (isInSeriesLoop) {
        // Series loop: Use main Reps
        return normalizedRecommendation.Reps || 10
      } else if (isInNbPausesLoop || isAdditionalDropSet) {
        // NbPauses loop or additional drop sets
        if (normalizedRecommendation.IsDropSet) {
          // MAUI Drop Set Rule: One-third reps for drop sets
          const baseReps = normalizedRecommendation.Reps || 10
          const dropReps = Math.max(1, Math.floor(baseReps / 3))
          logger.debug('💧 [DROP SET] Applying one-third reps rule:', {
            baseReps,
            dropReps,
            setIndex: i,
          })
          return dropReps
        } else {
          return (
            normalizedRecommendation.NbRepsPauses ||
            normalizedRecommendation.Reps ||
            10
          ) // Rest-pause sets
        }
      }
      return normalizedRecommendation.Reps || 10
    })()

    sets.push({
      Id: completedSet?.Id || -(2000 + i), // Use different range for work sets
      SetNo: `${setIndex + 1}`,
      Reps:
        isActiveSet && setData
          ? setData.reps
          : completedSet?.Reps || targetReps,
      Weight: (() => {
        if (isActiveSet && setData) {
          return unit === 'kg'
            ? { Lb: Math.round(setData.weight * 2.20462), Kg: setData.weight }
            : { Lb: setData.weight, Kg: Math.round(setData.weight / 2.20462) }
        }

        // If this is a completed set, use its weight
        if (completedSet?.Weight) {
          return completedSet.Weight
        }

        // Calculate weight based on set type and dual-loop pattern
        if (
          normalizedRecommendation.IsDropSet &&
          normalizedRecommendation.Weight
        ) {
          // Drop set weight calculation per guide:
          // - Series sets: use base weight
          // - NbPauses sets: apply 10% cumulative reduction per drop
          if (isInSeriesLoop) {
            // Series loop: use base weight
            return normalizedRecommendation.Weight
          } else if (isInNbPausesLoop || isAdditionalDropSet) {
            // NbPauses loop or additional drop sets: apply 10% reduction per drop from base weight
            const dropIndex = i - normalizedRecommendation.Series // 0, 1, 2...
            const dropMultiplier = Math.pow(0.9, dropIndex + 1) // 10% reduction per drop
            return {
              Kg:
                Math.round(
                  normalizedRecommendation.Weight.Kg * dropMultiplier * 4
                ) / 4, // Round to nearest 0.25kg
              Lb:
                Math.round(
                  normalizedRecommendation.Weight.Lb * dropMultiplier * 2
                ) / 2, // Round to nearest 0.5lb
            }
          }
        }

        // Default to recommendation weight
        return normalizedRecommendation.Weight || { Lb: 0, Kg: 0 }
      })(),
      IsFinished: isCompleted,
      IsNext: isActiveSet,
      IsWarmups: false,
      SetTitle: setTitle,
      IsBackOffSet: isBackOffSet,
      IsDropSet: isDropSet,
      NbPause: nbPause,
      restTime:
        isRestPause &&
        i >= workSetCount - (normalizedRecommendation.NbPauses || 0)
          ? normalizedRecommendation.RpRest
          : undefined,
    })
  }

  // If no set is marked as next, mark the first incomplete set as next
  if (!sets.some((s) => s.IsNext)) {
    const firstIncompleteIndex = sets.findIndex((s) => !s.IsFinished)
    if (firstIncompleteIndex !== -1 && sets[firstIncompleteIndex]) {
      sets[firstIncompleteIndex].IsNext = true
    }
  }

  // ------------------------------------------------------------------
  // TEMP DEBUG — log the final warm-up rows before returning.
  // Remove once the issue is resolved.
  // ------------------------------------------------------------------
  if (process.env.NODE_ENV === 'development') {
    // eslint-disable-next-line no-console
    console.debug(
      '[DEBUG] generateAllSets final warm-ups',
      sets.filter((s) => s.IsWarmups)
    )
  }

  return sets
}
