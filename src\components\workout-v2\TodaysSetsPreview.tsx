'use client'

import React, { useState } from 'react'
import type { WorkoutLogSerieModel } from '@/types'
import type { WorkoutLogSerieModelRef } from '@/types/api/WorkoutLogSerieModelRef'
import type { SetType } from '@/utils/setTypeUtils'
import { formatWeightForDisplay } from '@/utils/weightUtils'
import { getSetTypeFromSet } from '@/utils/getSetTypeFromSet'
import { SetTypeBadge } from '@/components/workout/SetTypeBadge'
import { SetTypeExplainerModal } from '@/components/workout/SetTypeExplainerModal'

// Extended type to include all properties
type ExtendedWorkoutLogSerieModel = WorkoutLogSerieModel &
  Partial<WorkoutLogSerieModelRef> & {
    WarmUpReps?: number
    WarmUpWeightSet?: { Lb: number; Kg: number }
    IsSkipped?: boolean
    IsNext?: boolean
    IsFinished?: boolean
    NbPause?: number
    SetTitle?: string
  }

interface TodaysSetsPreviewProps {
  allSets: ExtendedWorkoutLogSerieModel[]
  unit: 'kg' | 'lbs'
}

export function TodaysSetsPreview({ allSets, unit }: TodaysSetsPreviewProps) {
  const [selectedSetType, setSelectedSetType] = useState<SetType | null>(null)

  if (allSets.length === 0) {
    return null
  }

  // Separate sets for counting
  const warmupSets = allSets.filter((s) => s.IsWarmups)
  const workSets = allSets.filter((s) => !s.IsWarmups)

  return (
    <div
      className="w-full max-w-md bg-surface-secondary/50 rounded-lg p-2 sm:p-3"
      data-testid="todays-sets-container"
    >
      <h3 className="text-lg font-medium text-text-secondary mb-1.5 sm:mb-2">
        Today&apos;s sets
      </h3>
      <div className="space-y-1">
        {allSets.map((set, index) => {
          const isWarmup = set.IsWarmups
          const reps = isWarmup ? set.WarmUpReps : set.Reps
          const weight = isWarmup
            ? set.WarmUpWeightSet?.[unit === 'kg' ? 'Kg' : 'Lb']
            : set.Weight?.[unit === 'kg' ? 'Kg' : 'Lb']
          const isActive = set.IsNext
          const isCompleted = set.IsFinished

          // Count warmup sets
          let warmupNumber = 1
          if (isWarmup) {
            warmupNumber = warmupSets
              .slice(0, warmupSets.indexOf(set) + 1)
              .filter((s) => s.IsWarmups).length
          }

          // Count work sets
          let workSetNumber = 1
          if (!isWarmup) {
            workSetNumber = workSets
              .slice(0, workSets.indexOf(set) + 1)
              .filter((s) => !s.IsWarmups).length
          }

          // Determine set label
          let setLabel = ''
          if (isWarmup) {
            setLabel = `W${warmupNumber}`
          } else if (set.NbPause && set.NbPause > 0) {
            // Rest-pause set
            setLabel = `RP ${workSetNumber}`
          } else {
            setLabel = `Set ${workSetNumber}`
          }

          // Get set type
          const setType = getSetTypeFromSet(set)

          return (
            <div
              key={set.Id || index}
              data-testid="set-row"
              className="flex items-center justify-between py-1 px-2 sm:py-1.5 sm:px-2.5 bg-surface-primary/50 rounded-md transition-all"
            >
              <div className="flex items-center gap-2">
                <span className="text-lg font-medium text-text-primary">
                  {setLabel}
                </span>
                {/* Check mark for completed sets */}
                {isCompleted && (
                  <svg
                    className="w-4 h-4 text-green-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                )}
                {/* Active indicator - gold gradient dot */}
                {isActive && (
                  <div className="w-2 h-2 bg-gradient-metallic-gold rounded-full animate-pulse" />
                )}
              </div>
              <div className="flex items-center gap-1.5 sm:gap-2 text-lg text-text-secondary">
                <SetTypeBadge
                  setType={setType}
                  onClick={() => setSelectedSetType(setType)}
                  variant="compact"
                />
                <span>{reps || 0} reps</span>
                <span className="text-text-tertiary">×</span>
                <span>
                  {formatWeightForDisplay(weight || 0)} {unit}
                </span>
              </div>
            </div>
          )
        })}
      </div>

      {/* Set Type Explainer Modal */}
      <SetTypeExplainerModal
        setType={selectedSetType}
        isOpen={selectedSetType !== null}
        onClose={() => setSelectedSetType(null)}
      />
    </div>
  )
}
