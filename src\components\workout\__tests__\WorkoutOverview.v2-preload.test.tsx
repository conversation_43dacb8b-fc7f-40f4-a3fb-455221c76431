import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { WorkoutOverview } from '../WorkoutOverview'
import * as workoutHook from '@/hooks/useWorkout'
import { useRouter } from 'next/navigation'
import type { WorkoutTemplateGroupModel } from '@/types'

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
  useSearchParams: vi.fn(() => ({
    get: vi.fn(),
  })),
}))

// Mock hooks
vi.mock('@/hooks/useWorkout')
vi.mock('@/hooks/usePullToRefresh', () => ({
  usePullToRefresh: vi.fn(() => ({
    pullDistance: 0,
    isRefreshing: false,
    isPulling: false,
  })),
}))

// Mock stores
vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: vi.fn((selector) => {
    const state = {
      previewExerciseSkips: new Set(),
      setPreviewExerciseSkips: vi.fn(),
      clearPreviewExerciseSkips: vi.fn(),
    }
    return selector ? selector(state) : state
  }),
}))

// Mock components
vi.mock('@/components/workout/ExerciseCard', () => ({
  ExerciseCard: vi.fn(() => null),
}))

vi.mock('@/components/PullToRefreshIndicator', () => ({
  PullToRefreshIndicator: vi.fn(() => null),
}))

vi.mock('@/components/ui', () => ({
  FloatingCTAButton: vi.fn(({ onClick }) => (
    <button onClick={onClick} data-testid="start-workout-button">
      Start workout
    </button>
  )),
}))

describe('WorkoutOverview - V2 UI Button Pre-loading', () => {
  const mockPush = vi.fn()
  const mockStartWorkout = vi.fn()
  const mockLoadExerciseRecommendation = vi.fn()

  const mockWorkout: WorkoutTemplateGroupModel[] = [
    {
      Id: 1,
      Label: 'Day 1',
      Position: 1,
      DayNo: 1,
      WorkoutTemplates: [
        {
          Id: 1,
          Exercises: [
            {
              Id: 100,
              Label: 'Bench Press',
              BodyPartId: 1,
              IsBodyweight: false,
            },
          ],
        },
      ],
    },
  ]

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useRouter).mockReturnValue({
      push: mockPush,
      replace: vi.fn(),
      refresh: vi.fn(),
      back: vi.fn(),
      forward: vi.fn(),
      prefetch: vi.fn(),
    })

    vi.mocked(workoutHook.useWorkout).mockReturnValue({
      todaysWorkout: mockWorkout,
      isLoadingWorkout: false,
      workoutError: null,
      startWorkout: mockStartWorkout,
      userProgramInfo: null,
      exerciseWorkSetsModels: null,
      workoutSession: null,
      previewExercisesState: null,
      loadExerciseRecommendation: mockLoadExerciseRecommendation,
      updateExerciseWorkSets: vi.fn(),
      finishWorkout: vi.fn(),
      exercises: [
        {
          Id: 100,
          Label: 'Bench Press',
          BodyPartId: 1,
          IsFinished: false,
          IsNextExercise: true,
          isLoadingSets: false,
          setsError: null,
          lastSetsUpdate: 0,
          sets: [],
          IsBodyweight: false,
        },
      ],
      expectedExerciseCount: 1,
      hasInitialData: true,
      isOffline: false,
      refreshWorkout: vi.fn(),
      isLoading: false,
    })
  })

  it('should pre-load recommendation before navigating to v2 page', async () => {
    // Setup: startWorkout returns success with first exercise ID
    mockStartWorkout.mockResolvedValue({
      success: true,
      firstExerciseId: 100,
    })

    // Render component
    render(<WorkoutOverview />)

    // Find and click the "Try the new UI" button
    const tryNewUIButton = await screen.findByText(
      'Start with new exercise view →'
    )
    expect(tryNewUIButton).toBeInTheDocument()

    // Click the button
    fireEvent.click(tryNewUIButton)

    // Verify workout starts
    await waitFor(() => {
      expect(mockStartWorkout).toHaveBeenCalledWith(mockWorkout)
    })

    // CRITICAL: Verify recommendation is pre-loaded BEFORE navigation
    await waitFor(() => {
      expect(mockLoadExerciseRecommendation).toHaveBeenCalledWith(100)
    })

    // Then verify navigation happens to v2 page
    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith(
        '/workout/exercise-v2/100?exerciseName=Bench%20Press'
      )
    })

    // Ensure pre-load happens before navigation
    const loadCallOrder =
      mockLoadExerciseRecommendation.mock.invocationCallOrder[0]
    const pushCallOrder = mockPush.mock.invocationCallOrder[0]
    expect(loadCallOrder).toBeLessThan(pushCallOrder)
  })

  it('should navigate even if pre-load fails', async () => {
    // Setup: startWorkout returns success
    mockStartWorkout.mockResolvedValue({
      success: true,
      firstExerciseId: 100,
    })

    // Setup: recommendation load fails
    mockLoadExerciseRecommendation.mockRejectedValue(
      new Error('Failed to load recommendation')
    )

    // Render component
    render(<WorkoutOverview />)

    // Find and click the "Try the new UI" button
    const tryNewUIButton = await screen.findByText(
      'Start with new exercise view →'
    )
    fireEvent.click(tryNewUIButton)

    // Wait for operations
    await waitFor(() => {
      expect(mockStartWorkout).toHaveBeenCalled()
    })

    // Verify pre-load was attempted
    await waitFor(() => {
      expect(mockLoadExerciseRecommendation).toHaveBeenCalledWith(100)
    })

    // Verify navigation still happens despite pre-load failure
    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith(
        '/workout/exercise-v2/100?exerciseName=Bench%20Press'
      )
    })
  })

  it('should handle workout already started scenario', async () => {
    // Setup: Workout session already exists
    const mockWorkoutData = {
      todaysWorkout: mockWorkout,
      isLoadingWorkout: false,
      workoutError: null,
      startWorkout: mockStartWorkout,
      userProgramInfo: null,
      exercises: [
        {
          Id: 100,
          Label: 'Bench Press',
          BodyPartId: 1,
          IsFinished: false,
          IsNextExercise: true,
          isLoadingSets: false,
          setsError: null,
          lastSetsUpdate: 0,
          sets: [],
          IsBodyweight: false,
        },
      ],
      exerciseWorkSetsModels: [
        {
          Id: 100,
          Label: 'Bench Press',
          BodyPartId: 1,
          IsFinished: false,
          IsNextExercise: true,
          isLoadingSets: false,
          setsError: null,
          lastSetsUpdate: 0,
          sets: [],
          IsBodyweight: false,
        },
      ],
      expectedExerciseCount: 1,
      hasInitialData: true,
      isOffline: false,
      refreshWorkout: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
      workoutSession: {
        id: '123',
        startTime: Date.now(),
        exercises: [
          {
            Id: 100,
            Label: 'Bench Press',
            sets: [],
            IsFinished: false,
          },
        ],
      },
      finishWorkout: vi.fn(),
      isLoading: false,
      loadExerciseRecommendation: mockLoadExerciseRecommendation,
    }

    vi.mocked(workoutHook.useWorkout).mockReturnValue(mockWorkoutData)

    // Render component
    render(<WorkoutOverview />)

    // Find and click the "Try the new UI" button
    const tryNewUIButton = await screen.findByText(
      'Start with new exercise view →'
    )
    fireEvent.click(tryNewUIButton)

    // Should NOT start workout again
    expect(mockStartWorkout).not.toHaveBeenCalled()

    // Should still pre-load recommendation
    await waitFor(() => {
      expect(mockLoadExerciseRecommendation).toHaveBeenCalledWith(100)
    })

    // Should navigate to v2 page
    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith(
        '/workout/exercise-v2/100?exerciseName=Bench%20Press'
      )
    })
  })
})
