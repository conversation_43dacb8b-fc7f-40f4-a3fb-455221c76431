import { test, expect } from '@playwright/test'

test.describe('Weight Decimal Formatting', () => {
  test('should format weight with max 2 decimals and no trailing zeros', async ({
    page,
  }) => {
    // Login first
    await page.goto('/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'Dr123456')
    await page.click('button[type="submit"]')
    await page.waitForURL('/program', { timeout: 10000 })

    // Navigate to workout
    await page.goto('/workout')

    // Mock recommendation with floating point weight values
    await page.route(
      /.*GetRecommendationNormalRIRForExerciseWithoutWarmupsNew.*/,
      async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            StatusCode: 200,
            Result: {
              ExerciseId: 16508,
              Series: 3,
              Reps: 10,
              Weight: { Lb: 135.12345678, Kg: 61.23456789 },
              Increments: { Lb: 2.5, Kg: 1 },
              IsBodyweight: false,
              IsNormalSets: true,
              WarmUpsList: [
                {
                  WarmUpReps: 5,
                  WarmUpWeightSet: { Lb: 95.87654321, Kg: 43.51234567 },
                },
                {
                  WarmUpReps: 3,
                  WarmUpWeightSet: { Lb: 115.0, Kg: 52.16313933 },
                },
              ],
            },
          }),
        })
      }
    )

    // Start workout
    await page.getByRole('button', { name: /start workout/i }).click()

    // Wait for exercise page to load
    await page.waitForURL(/\/workout\/exercise\/\d+/, { timeout: 10000 })

    // Wait for sets grid to be visible
    await page.waitForSelector('input[aria-label*="Weight for set"]', {
      timeout: 10000,
    })

    // Check warmup weights formatting
    const warmup1Weight = page.locator('input[aria-label*="Weight for set W1"]')
    const warmup2Weight = page.locator('input[aria-label*="Weight for set W2"]')

    // Should show proper formatting: max 2 decimals, no excessive precision
    await expect(warmup1Weight).toHaveValue(/^95\.88$|^95\.9$|^96$/)
    await expect(warmup2Weight).toHaveValue(/^115$/) // Whole number, no decimals

    // Check work set weight formatting
    const workSetWeight = page.locator('input[aria-label="Weight for set 1"]')
    await expect(workSetWeight).toHaveValue(/^135\.12$|^135\.1$|^135$/)
  })
})
