import { test, expect } from '@playwright/test'

// Helper function to login
async function loginUser(page: any) {
  await page.goto('/login')
  await page.fill('[type="email"]', '<EMAIL>')
  await page.fill('[type="password"]', 'Dr123456')
  await page.click('button[type="submit"]')
  await page.waitForURL('/program', { timeout: 10000 })
  await page.waitForLoadState('networkidle')
}

// Test surface theme token rendering across browsers
test.describe('Surface Theme Token Compatibility', () => {
  test('Surface tokens render correctly on Mobile Safari', async ({ page }) => {
    // Login first to access the program page
    await loginUser(page)

    // Check bg-surface-primary elements exist
    const surfacePrimaryElements = await page
      .locator('[class*="bg-surface-primary"]')
      .count()
    expect(surfacePrimaryElements).toBeGreaterThan(0)

    // Check bg-surface-secondary elements exist
    const surfaceSecondaryElements = await page
      .locator('[class*="bg-surface-secondary"]')
      .count()
    expect(surfaceSecondaryElements).toBeGreaterThan(0)

    // Verify CSS variables are resolved
    const bgColor = await page
      .locator('[class*="bg-surface-primary"]')
      .first()
      .evaluate((el) => {
        return getComputedStyle(el).backgroundColor
      })

    expect(bgColor).not.toBe('rgba(0, 0, 0, 0)')
    expect(bgColor).not.toBe('transparent')
    expect(bgColor).not.toBe('')

    // Surface primary color verified: bgColor
  })

  test('Transition theme utility works correctly', async ({ page }) => {
    // Login first
    await loginUser(page)

    // Check transition-theme-normal elements exist
    const transitionElements = await page
      .locator('.transition-theme-normal')
      .count()

    // If no transition-theme-normal elements, check for elements with the actual CSS properties
    if (transitionElements === 0) {
      const elementsWithTransition = await page.evaluate(() => {
        const allElements = Array.from(document.querySelectorAll('*'))
        return allElements.filter((el) => {
          const style = getComputedStyle(el)
          return (
            style.transitionDuration === '300ms' ||
            style.transitionDuration === '0.3s'
          )
        }).length
      })
      expect(elementsWithTransition).toBeGreaterThan(0)
    } else {
      expect(transitionElements).toBeGreaterThan(0)

      // Verify transition properties are applied
      const transitionDuration = await page
        .locator('.transition-theme-normal')
        .first()
        .evaluate((el) => {
          return getComputedStyle(el).transitionDuration
        })

      expect(transitionDuration).toBe('300ms')
    }
  })

  test('Verify theme token CSS variables are defined', async ({ page }) => {
    await loginUser(page)

    // Check that theme variables are defined in CSS
    const themeVariables = await page.evaluate(() => {
      const root = document.documentElement
      const style = getComputedStyle(root)

      return {
        bgPrimary: style.getPropertyValue('--color-bg-primary').trim(),
        bgSecondary: style.getPropertyValue('--color-bg-secondary').trim(),
        bgTertiary: style.getPropertyValue('--color-bg-tertiary').trim(),
      }
    })

    expect(themeVariables.bgPrimary).not.toBe('')
    expect(themeVariables.bgSecondary).not.toBe('')
    expect(themeVariables.bgTertiary).not.toBe('')

    // Theme variables validated successfully
  })
})
