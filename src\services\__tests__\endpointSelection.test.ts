import { describe, it, expect, vi, beforeEach } from 'vitest'
import type { UserInfosModel } from '@/types/api'
import type { ExerciseDetailModel } from '@/types/models'
import { getServerUserInfoCached, clearUserInfoCache } from '../userInfoCache'
import { logger } from '@/utils/logger'
import {
  selectRecommendationEndpoint,
  clearUserSettingsCache,
} from '../endpointSelection'

// Mock dependencies
vi.mock('../userInfoCache')
vi.mock('@/utils/logger')

describe('endpointSelection', () => {
  const mockUserInfoNormal: UserInfosModel = {
    Id: 123,
    Firstname: 'John',
    Lastname: 'Doe',
    Email: '<EMAIL>',
    MassUnit: 'kg',
    IsNormalSet: true,
    IsPyramid: false,
    IsQuickMode: false,
  }

  const mockUserInfoRestPause: UserInfosModel = {
    ...mockUserInfoNormal,
    IsNormalSet: false,
  }

  const mockUserInfoPyramid: UserInfosModel = {
    ...mockUserInfoNormal,
    IsNormalSet: true,
    IsPyramid: true,
  }

  const mockExerciseNormal: ExerciseDetailModel = {
    Id: 1,
    Name: 'Bench Press',
    IsBodyweight: false,
    Path: 'bench-press',
    IsDeleted: false,
  }

  const mockExerciseBodyweight: ExerciseDetailModel = {
    Id: 2,
    Name: 'Push-ups',
    IsBodyweight: true,
    Path: 'push-ups',
    IsDeleted: false,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('selectRecommendationEndpoint', () => {
    it('should select Normal endpoint for user with normal preference and non-bodyweight exercise', async () => {
      vi.mocked(getServerUserInfoCached).mockResolvedValue(mockUserInfoNormal)

      const result = await selectRecommendationEndpoint(mockExerciseNormal)

      expect(result.endpointType).toBe('Normal')
      expect(result.endpoint).toContain('GetRecommendationNormalRIR')
      expect(logger.log).toHaveBeenCalledWith(
        'Selected endpoint:',
        expect.any(Object)
      )
    })

    it('should select RestPause endpoint for user with rest-pause preference', async () => {
      vi.mocked(getServerUserInfoCached).mockResolvedValue(
        mockUserInfoRestPause
      )

      const result = await selectRecommendationEndpoint(mockExerciseNormal)

      expect(result.endpointType).toBe('RestPause')
      expect(result.endpoint).toContain('GetRecommendationRestPauseRIR')
      expect(logger.log).toHaveBeenCalledWith(
        'Selected endpoint:',
        expect.any(Object)
      )
    })

    it('should select RestPause endpoint for bodyweight exercise with pyramid user', async () => {
      vi.mocked(getServerUserInfoCached).mockResolvedValue(mockUserInfoPyramid)

      const result = await selectRecommendationEndpoint(mockExerciseBodyweight)

      expect(result.endpointType).toBe('RestPause')
      expect(result.endpoint).toContain('GetRecommendationRestPauseRIR')
      expect(logger.log).toHaveBeenCalledWith(
        'Applied MAUI special case: bodyweight + pyramid -> RestPause endpoint'
      )
    })

    it('should select Normal endpoint for bodyweight exercise with non-pyramid user', async () => {
      vi.mocked(getServerUserInfoCached).mockResolvedValue(mockUserInfoNormal)

      const result = await selectRecommendationEndpoint(mockExerciseBodyweight)

      expect(result.endpointType).toBe('Normal')
      expect(result.endpoint).toContain('GetRecommendationNormalRIR')
    })

    it('should fallback to Normal endpoint when user info fetch fails', async () => {
      vi.mocked(getServerUserInfoCached).mockResolvedValue(null)

      const result = await selectRecommendationEndpoint(mockExerciseNormal)

      expect(result.endpointType).toBe('Normal')
      expect(result.endpoint).toContain('GetRecommendationNormalRIR')
      expect(logger.warn).toHaveBeenCalledWith(
        'No user settings available, falling back to Normal endpoint'
      )
    })

    it('should reuse cached data for subsequent calls', async () => {
      vi.mocked(getServerUserInfoCached).mockResolvedValue(mockUserInfoNormal)

      // First call
      await selectRecommendationEndpoint(mockExerciseNormal)

      // Second call
      await selectRecommendationEndpoint(mockExerciseBodyweight)

      // Should use shared cache, called twice (once per selectRecommendationEndpoint call)
      expect(getServerUserInfoCached).toHaveBeenCalledTimes(2)
    })

    it('should handle undefined IsNormalSet field gracefully', async () => {
      const userInfoWithoutPreference: UserInfosModel = {
        ...mockUserInfoNormal,
        IsNormalSet: undefined as any,
      }
      vi.mocked(getServerUserInfoCached).mockResolvedValue(
        userInfoWithoutPreference
      )

      const result = await selectRecommendationEndpoint(mockExerciseNormal)

      // Should default to Normal when IsNormalSet is undefined
      expect(result.endpointType).toBe('Normal')
    })

    it('should handle undefined IsPyramid field gracefully', async () => {
      const userInfoWithoutPyramid: UserInfosModel = {
        ...mockUserInfoNormal,
        IsPyramid: undefined as any,
      }
      vi.mocked(getServerUserInfoCached).mockResolvedValue(
        userInfoWithoutPyramid
      )

      const result = await selectRecommendationEndpoint(mockExerciseBodyweight)

      // Should not trigger special case when IsPyramid is undefined
      expect(result.endpointType).toBe('Normal')
    })

    it('should handle missing exercise IsBodyweight field', async () => {
      const exerciseWithoutBodyweight = {
        ...mockExerciseNormal,
        IsBodyweight: undefined,
      }
      vi.mocked(getServerUserInfoCached).mockResolvedValue(mockUserInfoPyramid)

      const result = await selectRecommendationEndpoint(
        exerciseWithoutBodyweight as any
      )

      // Should treat undefined IsBodyweight as false
      expect(result.endpointType).toBe('Normal')
    })

    it('should build correct endpoint path', async () => {
      vi.mocked(getServerUserInfoCached).mockResolvedValue(mockUserInfoNormal)

      const result = await selectRecommendationEndpoint(mockExerciseNormal)

      expect(result.endpoint).toBe(
        '/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew'
      )
      expect(result.endpointType).toBe('Normal')
    })
  })

  describe('cache clearing', () => {
    it('should delegate to clearUserInfoCache when clearing', () => {
      clearUserSettingsCache()

      expect(clearUserInfoCache).toHaveBeenCalledTimes(1)
    })
  })
})
