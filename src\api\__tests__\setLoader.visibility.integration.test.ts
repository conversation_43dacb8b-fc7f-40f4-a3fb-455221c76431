import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { SetLoader } from '../setLoader'
import { workoutApi } from '@/api/workouts'
import type { WorkoutLogSerieModel } from '@/types'

vi.mock('@/api/workouts')

describe('SetLoader - Visibility Integration', () => {
  let setLoader: SetLoader

  const mockSets: WorkoutLogSerieModel[] = [
    {
      Id: 1,
      ExerciseId: 123,
      Reps: 10,
      Weight: { Kg: 50, Lb: 110 },
      IsWarmups: false,
      IsFinished: true,
      IsNext: false,
      SetNo: '1',
    },
  ]

  beforeEach(() => {
    vi.clearAllMocks()
    setLoader = new SetLoader()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  describe('App Background/Foreground Scenarios', () => {
    it('should handle request cancellation and retry on visibility change', async () => {
      // Simulate a slow API call
      let resolveRequest: (value: WorkoutLogSerieModel[]) => void
      const pendingPromise = new Promise<WorkoutLogSerieModel[]>((resolve) => {
        resolveRequest = resolve
      })

      vi.mocked(workoutApi.getExerciseSets).mockReturnValue(pendingPromise)

      // Start loading
      const loadPromise = setLoader.loadExerciseSets(123)

      // Verify request is pending
      expect(setLoader.getPendingRequestCount()).toBe(1)

      // App goes to background - cancel pending requests
      setLoader.cancelPendingRequests()

      // Pending count should be 0
      expect(setLoader.getPendingRequestCount()).toBe(0)

      // Resolve the original request
      resolveRequest!(mockSets)

      // Original promise should handle cancellation gracefully
      const result = await loadPromise
      expect(result).toEqual(mockSets)

      // App returns to foreground - clear stuck states and reload
      setLoader.clearStuckStates()

      // Mock successful reload
      vi.mocked(workoutApi.getExerciseSets).mockResolvedValueOnce(mockSets)

      const reloadResult = await setLoader.loadExerciseSets(123, true)
      expect(reloadResult).toEqual(mockSets)
      expect(workoutApi.getExerciseSets).toHaveBeenCalledTimes(2)
    })

    it('should handle multiple exercises during visibility changes', async () => {
      // Start multiple requests
      vi.mocked(workoutApi.getExerciseSets).mockImplementation(
        () => new Promise(() => {}) // Never resolves
      )

      setLoader.loadExerciseSets(123)
      setLoader.loadExerciseSets(456)
      setLoader.loadExerciseSets(789)

      expect(setLoader.getPendingRequestCount()).toBe(3)

      // Cancel all on background
      setLoader.cancelPendingRequests()
      expect(setLoader.getPendingRequestCount()).toBe(0)

      // Mark some as failed
      setLoader.markAsFailed(123)
      setLoader.markAsFailed(456)

      // Setup successful responses
      vi.mocked(workoutApi.getExerciseSets).mockImplementation((exerciseId) => {
        if (exerciseId === 123) return Promise.resolve(mockSets)
        if (exerciseId === 456) return Promise.resolve([])
        return Promise.reject(new Error('Still failing'))
      })

      // Retry failed on foreground
      const retryResults = await setLoader.retryFailed()

      expect(retryResults).toHaveLength(2)
      expect(retryResults[0].success).toBe(true)
      expect(retryResults[0].exerciseId).toBe(123)
      expect(retryResults[1].success).toBe(true)
      expect(retryResults[1].exerciseId).toBe(456)
    })

    it('should clear cache for stuck states during background', async () => {
      // Pre-populate cache
      vi.mocked(workoutApi.getExerciseSets).mockResolvedValueOnce(mockSets)
      await setLoader.loadExerciseSets(123)

      // Start a new request that gets stuck
      vi.mocked(workoutApi.getExerciseSets).mockImplementation(
        () => new Promise(() => {}) // Never resolves
      )

      setLoader.loadExerciseSets(123, true)

      // Simulate background
      setLoader.cancelPendingRequests()

      // Clear stuck states should clear cache
      setLoader.clearStuckStates()

      // New request should hit API, not cache
      vi.mocked(workoutApi.getExerciseSets).mockResolvedValueOnce(mockSets)
      const result = await setLoader.loadExerciseSets(123)

      expect(workoutApi.getExerciseSets).toHaveBeenCalledTimes(2)
      expect(result).toEqual(mockSets)
    })

    it('should handle rapid visibility changes gracefully', async () => {
      // Simulate rapid background/foreground cycles
      for (let i = 0; i < 5; i++) {
        // Start request
        vi.mocked(workoutApi.getExerciseSets).mockImplementation(
          () => new Promise(() => {}) // Never resolves
        )

        setLoader.loadExerciseSets(123)

        // Immediately cancel
        setLoader.cancelPendingRequests()

        // Clear states
        setLoader.clearStuckStates()
      }

      // Final successful load
      vi.mocked(workoutApi.getExerciseSets).mockResolvedValueOnce(mockSets)
      const result = await setLoader.loadExerciseSets(123)

      expect(result).toEqual(mockSets)
      expect(setLoader.getPendingRequestCount()).toBe(0)
    })

    it('should maintain data consistency across visibility changes', async () => {
      // Load initial data
      vi.mocked(workoutApi.getExerciseSets).mockResolvedValueOnce(mockSets)
      await setLoader.loadExerciseSets(123)

      // Simulate background with pending update
      const updatedSets = [
        ...mockSets,
        {
          ...mockSets[0],
          Id: 2,
          SetNo: '2',
        },
      ]

      vi.mocked(workoutApi.getExerciseSets).mockImplementation(
        () => new Promise(() => {}) // Never resolves
      )

      setLoader.loadExerciseSets(123, true)

      // Cancel due to background
      setLoader.cancelPendingRequests()

      // On foreground, should get fresh data
      vi.mocked(workoutApi.getExerciseSets).mockResolvedValueOnce(updatedSets)
      const freshData = await setLoader.loadExerciseSets(123, true)

      expect(freshData).toEqual(updatedSets)
      expect(freshData.length).toBe(2)
    })
  })
})
