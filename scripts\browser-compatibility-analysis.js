#!/usr/bin/env node

/**
 * Browser Compatibility Analysis Tool
 * Analyzes code for potential cross-browser compatibility issues
 */

const fs = require('fs')
const path = require('path')

// Browser-specific issue patterns
const compatibilityChecks = {
  safari: {
    localStorage: {
      pattern: /localStorage\.(getItem|setItem|removeItem)/g,
      severity: 'high',
      description: 'Safari private mode throws QuotaExceededError on localStorage operations',
      files: ['src/services/userSettings/calculations.ts'],
      mitigation: 'Wrap localStorage calls in try-catch blocks'
    },
    
    popupBlocking: {
      pattern: /window\.open\(/g,
      severity: 'medium',
      description: 'Safari blocks popups aggressively, affecting OAuth flows',
      files: ['src/components/auth/', 'src/hooks/useAuth'],
      mitigation: 'Use same-window redirects instead of popups'
    },
    
    dateConstructor: {
      pattern: /new Date\([^)]*\)/g,
      severity: 'medium',
      description: 'Safari has stricter date parsing than other browsers',
      files: ['src/services/userSettings/calculations.ts'],
      mitigation: 'Use ISO 8601 format or date-fns library'
    },
    
    viewportHeight: {
      pattern: /100vh|innerHeight/g,
      severity: 'medium',
      description: 'Safari iOS viewport height changes with address bar visibility',
      files: ['src/styles/', 'src/components/'],
      mitigation: 'Use -webkit-fill-available or viewport units'
    }
  },

  chrome: {
    pullToRefresh: {
      pattern: /touchstart|touchmove|touchend/g,
      severity: 'medium',
      description: 'Chrome Android pull-to-refresh conflicts with touch events',
      files: ['src/components/workout/'],
      mitigation: 'Use passive event listeners and prevent default selectively'
    },
    
    memoryPressure: {
      pattern: /cache\s*[=:]/gi,
      severity: 'low',
      description: 'Chrome may clear caches under memory pressure',
      files: ['src/services/userInfoCache.ts'],
      mitigation: 'Implement graceful cache fallbacks'
    }
  },

  firefox: {
    serviceWorkerTiming: {
      pattern: /serviceWorker\.register/g,
      severity: 'medium',
      description: 'Firefox mobile has different service worker lifecycle timing',
      files: ['public/sw.js', 'src/hooks/usePWA'],
      mitigation: 'Add proper event listeners for service worker states'
    },
    
    promiseHandling: {
      pattern: /Promise\.(race|all)/g,
      severity: 'low',
      description: 'Firefox handles promise timing differently than Chromium',
      files: ['src/hooks/useWorkoutRecommendations.ts'],
      mitigation: 'Add timeout handling for promise races'
    }
  },

  general: {
    unhandledPromises: {
      pattern: /async\s+function|\.then\(/g,
      severity: 'medium',
      description: 'Unhandled promise rejections can cause different behavior across browsers',
      files: ['src/'],
      mitigation: 'Add .catch() handlers or try-catch blocks'
    },
    
    cookieSupport: {
      pattern: /document\.cookie|httpOnly/gi,
      severity: 'high',
      description: 'Cookie behavior varies across browsers and privacy modes',
      files: ['src/stores/authStore.ts'],
      mitigation: 'Implement fallback auth storage mechanisms'
    }
  }
}

class BrowserCompatibilityAnalyzer {
  constructor() {
    this.issues = []
    this.scannedFiles = 0
  }

  async analyzeFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8')
      this.scannedFiles++
      
      // Check each browser's compatibility issues
      Object.entries(compatibilityChecks).forEach(([browser, checks]) => {
        Object.entries(checks).forEach(([checkName, checkConfig]) => {
          const matches = content.match(checkConfig.pattern)
          
          if (matches) {
            // Check if this file is specifically mentioned as affected
            const isAffectedFile = checkConfig.files.some(filePattern => 
              filePath.includes(filePattern)
            )
            
            if (isAffectedFile || checkConfig.files.includes('src/')) {
              const lines = content.split('\n')
              const matchedLines = []
              
              lines.forEach((line, index) => {
                if (checkConfig.pattern.test(line)) {
                  matchedLines.push({
                    lineNumber: index + 1,
                    content: line.trim()
                  })
                }
              })

              this.issues.push({
                browser,
                check: checkName,
                severity: checkConfig.severity,
                description: checkConfig.description,
                mitigation: checkConfig.mitigation,
                file: filePath,
                matches: matches.length,
                matchedLines,
                pattern: checkConfig.pattern.toString()
              })
            }
          }
        })
      })
    } catch (error) {
      console.warn(`Warning: Could not read file ${filePath}:`, error.message)
    }
  }

  async analyzeDirectory(dirPath) {
    const files = fs.readdirSync(dirPath)
    
    for (const file of files) {
      const fullPath = path.join(dirPath, file)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory() && !file.startsWith('.') && !file.includes('node_modules')) {
        await this.analyzeDirectory(fullPath)
      } else if (file.endsWith('.ts') || file.endsWith('.tsx') || file.endsWith('.js')) {
        await this.analyzeFile(fullPath)
      }
    }
  }

  generateReport() {
    const severityOrder = { high: 3, medium: 2, low: 1 }
    const sortedIssues = this.issues.sort((a, b) => 
      severityOrder[b.severity] - severityOrder[a.severity]
    )

    const groupedByBrowser = sortedIssues.reduce((acc, issue) => {
      if (!acc[issue.browser]) {
        acc[issue.browser] = []
      }
      acc[issue.browser].push(issue)
      return acc
    }, {})

    let report = '# Browser Compatibility Analysis Report\n\n'
    report += `**Files Scanned**: ${this.scannedFiles}\n`
    report += `**Issues Found**: ${this.issues.length}\n\n`

    Object.entries(groupedByBrowser).forEach(([browser, issues]) => {
      const browserName = browser.charAt(0).toUpperCase() + browser.slice(1)
      report += `## ${browserName} Issues (${issues.length})\n\n`
      
      issues.forEach(issue => {
        const severityEmoji = {
          high: '🔴',
          medium: '🟡', 
          low: '🟢'
        }[issue.severity]
        
        report += `### ${severityEmoji} ${issue.check}\n\n`
        report += `**Severity**: ${issue.severity.toUpperCase()}\n\n`
        report += `**Description**: ${issue.description}\n\n`
        report += `**File**: \`${issue.file}\`\n\n`
        report += `**Matches**: ${issue.matches}\n\n`
        
        if (issue.matchedLines.length > 0 && issue.matchedLines.length <= 5) {
          report += '**Code Examples**:\n```typescript\n'
          issue.matchedLines.forEach(match => {
            report += `Line ${match.lineNumber}: ${match.content}\n`
          })
          report += '```\n\n'
        }
        
        report += `**Mitigation**: ${issue.mitigation}\n\n`
        report += '---\n\n'
      })
    })

    // Summary recommendations
    report += '## Summary & Recommendations\n\n'
    
    const highSeverity = sortedIssues.filter(i => i.severity === 'high')
    const mediumSeverity = sortedIssues.filter(i => i.severity === 'medium')
    const lowSeverity = sortedIssues.filter(i => i.severity === 'low')
    
    if (highSeverity.length > 0) {
      report += `### 🔴 High Priority (${highSeverity.length} issues)\n`
      report += 'These issues can cause complete functionality failures on specific browsers:\n\n'
      highSeverity.forEach(issue => {
        report += `- **${issue.browser}**: ${issue.check} in \`${path.basename(issue.file)}\`\n`
      })
      report += '\n'
    }

    if (mediumSeverity.length > 0) {
      report += `### 🟡 Medium Priority (${mediumSeverity.length} issues)\n`
      report += 'These issues can cause degraded user experience:\n\n'
      const browserGroups = mediumSeverity.reduce((acc, issue) => {
        if (!acc[issue.browser]) acc[issue.browser] = []
        acc[issue.browser].push(issue.check)
        return acc
      }, {})
      
      Object.entries(browserGroups).forEach(([browser, checks]) => {
        report += `- **${browser}**: ${checks.join(', ')}\n`
      })
      report += '\n'
    }

    report += '### Testing Recommendations\n\n'
    report += '1. **Priority Testing**: Focus on Safari iOS and Chrome Android\n'
    report += '2. **localStorage Testing**: Test in Safari private mode\n'
    report += '3. **Memory Testing**: Test with limited device memory\n'
    report += '4. **Network Testing**: Test with slow/intermittent connections\n'
    report += '5. **Gesture Testing**: Test touch interactions don\'t conflict with browser gestures\n\n'

    return report
  }

  async run() {
    console.log('🔍 Starting browser compatibility analysis...')
    
    // Target directories and files for comprehensive sets implementation
    const targetPaths = [
      'src/services/userInfoCache.ts',
      'src/services/endpointSelection.ts', 
      'src/services/userSettings.ts',
      'src/services/userSettings/calculations.ts',
      'src/hooks/useWorkoutRecommendations.ts',
      'src/stores/authStore.ts'
    ]

    // Also scan broader directories
    const directories = [
      'src/services',
      'src/hooks', 
      'src/stores',
      'src/components/auth',
      'src/components/workout'
    ]

    // Analyze specific files
    for (const filePath of targetPaths) {
      if (fs.existsSync(filePath)) {
        await this.analyzeFile(filePath)
      }
    }

    // Analyze directories
    for (const dir of directories) {
      if (fs.existsSync(dir)) {
        await this.analyzeDirectory(dir)
      }
    }

    const report = this.generateReport()
    
    // Write report to file
    fs.writeFileSync('browser-compatibility-report.md', report)
    
    console.log(`✅ Analysis complete! Found ${this.issues.length} potential issues`)
    console.log(`📄 Report saved to: browser-compatibility-report.md`)
    
    return report
  }
}

// Run the analysis
if (require.main === module) {
  const analyzer = new BrowserCompatibilityAnalyzer()
  analyzer.run().then(report => {
    console.log('\n' + '='.repeat(50))
    console.log('BROWSER COMPATIBILITY ANALYSIS')
    console.log('='.repeat(50))
    
    // Show critical issues in console
    const criticalIssues = analyzer.issues.filter(i => i.severity === 'high')
    if (criticalIssues.length > 0) {
      console.log('\n🔴 CRITICAL ISSUES FOUND:')
      criticalIssues.forEach(issue => {
        console.log(`  • ${issue.browser}: ${issue.description}`)
        console.log(`    File: ${issue.file}`)
        console.log(`    Fix: ${issue.mitigation}\n`)
      })
    } else {
      console.log('\n✅ No critical browser compatibility issues found!')
    }
  }).catch(error => {
    console.error('❌ Analysis failed:', error)
    process.exit(1)
  })
}

module.exports = BrowserCompatibilityAnalyzer
