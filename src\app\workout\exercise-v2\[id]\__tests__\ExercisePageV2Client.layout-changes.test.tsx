import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { ExercisePageV2Client } from '../ExercisePageV2Client'
import { NavigationProvider } from '@/contexts/NavigationContext'

// Mock all dependencies
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
  }),
  useSearchParams: () => ({
    get: vi.fn(() => null),
  }),
}))

vi.mock('@/stores/authStore', () => ({
  useAuthStore: () => ({
    getCachedUserInfo: () => ({ MassUnit: 'kg' }),
  }),
}))

vi.mock('@/hooks/useWorkout', () => ({
  useWorkout: () => ({
    isLoadingWorkout: false,
    workoutError: null,
    workoutSession: { id: '123' },
    saveSet: vi.fn(),
  }),
}))

vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: () => ({
    loadingStates: new Map(),
    nextSet: vi.fn(),
    restTimerState: {
      isActive: false,
    },
  }),
}))

vi.mock('@/hooks/useExercisePageInitialization', () => ({
  useExercisePageInitialization: () => ({
    isInitializing: false,
    loadingError: null,
    retryInitialization: vi.fn(),
  }),
}))

vi.mock('@/hooks/useSetScreenLogic', () => ({
  useSetScreenLogic: () => ({
    currentExercise: { Id: 1, Label: 'Bench Press' },
    exercises: [{ Id: 1, Label: 'Bench Press' }],
    currentSetIndex: 0,
    isSaving: false,
    saveError: null,
    showComplete: false,
    showExerciseComplete: false,
    recommendation: { WeightIncrement: 5 },
    isLoading: false,
    error: null,
    isLastExercise: false,
    isLastSet: false,
    isWarmup: false,
    isFirstWorkSet: false,
    completedSets: [],
    setData: { reps: 10, weight: 80, duration: 0 },
    setSetData: vi.fn(),
    setSaveError: vi.fn(),
    handleSaveSet: vi.fn(),
    refetchRecommendation: vi.fn(),
  }),
}))

vi.mock('@/components/workout-v2/RestTimer', () => ({
  RestTimer: () => <div data-testid="rest-timer">Rest Timer</div>,
  useRestTimer: () => ({ startRestTimer: vi.fn() }),
}))

vi.mock('@/components/workout-v2/CurrentSetCard', () => ({
  CurrentSetCard: () => (
    <div data-testid="current-set-card">Current Set Card</div>
  ),
}))

vi.mock('@/components/workout-v2/ExerciseQuickNav', () => ({
  ExerciseQuickNav: () => (
    <div data-testid="exercise-quick-nav">Exercise Quick Nav</div>
  ),
}))

vi.mock('@/utils/generateAllSets', () => ({
  generateAllSets: () => [
    { Id: 1, IsNext: true, IsWarmups: false, IsFinished: false },
  ],
}))

vi.mock('@/hooks/useExerciseV2Actions', () => ({
  useExerciseV2Actions: () => ({
    handleCompleteSet: vi.fn(),
    handleSkipSet: vi.fn(),
  }),
}))

vi.mock('@/hooks/usePullToRefresh', () => ({
  usePullToRefresh: () => ({
    pullDistance: 0,
    isRefreshing: false,
    isPulling: false,
  }),
}))

vi.mock('@/components/PullToRefreshIndicator', () => ({
  PullToRefreshIndicator: () => null,
}))

vi.mock('@/components/workout-v2/ExercisePageStates', () => ({
  ExercisePageStates: () => null,
}))

vi.mock('@/components/workout-v2/ExerciseInfoHeader', () => ({
  ExerciseInfoHeader: () => <div>Exercise Info Header</div>,
}))

vi.mock('@/components/workout-v2/TodaysSetsPreview', () => ({
  TodaysSetsPreview: () => <div>Today's sets</div>,
}))

vi.mock('@/components/workout/RIRPicker', () => ({
  RIRPicker: () => null,
}))

describe('ExercisePageV2Client - Layout Changes', () => {
  const renderWithProvider = (component: React.ReactElement) => {
    return render(<NavigationProvider>{component}</NavigationProvider>)
  }

  it('should position rest timer at bottom under current set', () => {
    // Given: Exercise page with rest timer
    renderWithProvider(<ExercisePageV2Client exerciseId={1} />)

    // When: Rest timer and current set render
    const restTimer = screen.getByTestId('rest-timer')
    const currentSetCard = screen.getByTestId('current-set-card')

    // Then: Rest timer should be positioned after current set in DOM order
    expect(restTimer).toBeInTheDocument()
    expect(currentSetCard).toBeInTheDocument()

    // Timer should come after set card in layout flow
    const mainContent = restTimer.closest('.flex-1')
    expect(mainContent).toContainElement(restTimer)
    expect(mainContent).toContainElement(currentSetCard)
  })

  it('should maintain proper spacing with standard navigation', () => {
    // Given: Exercise page structure
    renderWithProvider(<ExercisePageV2Client exerciseId={1} />)

    // When: Content renders
    const mainContainer = screen
      .getByTestId('current-set-card')
      .closest('.flex-1')

    // Then: Should have proper spacing classes for navigation
    expect(mainContainer).toHaveClass('flex-1')
    expect(mainContainer).toHaveClass('flex', 'flex-col')
  })

  it('should not have overflow-y-auto on main content area (uses document scroll)', () => {
    // Given: Exercise page is rendered with all components
    renderWithProvider(<ExercisePageV2Client exerciseId={1} />)

    // When: Examining the main content area for scroll containers
    const container = screen.getByTestId('exercise-page-container')

    // Then: Main content area should NOT have overflow-y-auto (document handles scrolling)
    const scrollContainers = container.querySelectorAll(
      '[class*="overflow-y-auto"]'
    )
    expect(scrollContainers).toHaveLength(0)
  })

  it('should have minimal white space between CurrentSetCard and TodaysSetsPreview', () => {
    // Given: Exercise page renders with components
    renderWithProvider(<ExercisePageV2Client exerciseId={1} />)

    // When: Examining layout structure
    const container = screen.getByTestId('exercise-page-container')
    const mainContent = container.querySelector('.flex-1.flex.flex-col')

    // Then: Should have compact spacing with gap-4
    expect(mainContent).toBeTruthy()

    // Check for the flex column with gap
    const hybridView = mainContent?.querySelector('.flex.flex-col.gap-4')
    expect(hybridView).toBeTruthy()

    // Check that excessive flex ratios are not used
    const currentSetContainer = hybridView?.querySelector('.flex-\\[6\\]')
    const nextSetsContainer = hybridView?.querySelector('.flex-\\[3\\]')

    // These should be null (using better structure)
    expect(currentSetContainer).toBeNull()
    expect(nextSetsContainer).toBeNull()
  })

  it('should have reduced padding in main content area', () => {
    // Given: Exercise page renders
    renderWithProvider(<ExercisePageV2Client exerciseId={1} />)

    // When: Examining main content padding
    const container = screen.getByTestId('exercise-page-container')
    const mainContent = container.querySelector('.px-4')

    // Then: Main content should exist
    expect(mainContent).toBeTruthy()
  })

  it('should allow vertical scrolling through document flow', () => {
    // Given: Exercise page renders with content
    renderWithProvider(<ExercisePageV2Client exerciseId={1} />)

    // When: Examining the main content area
    const container = screen.getByTestId('exercise-page-container')
    const mainContent = container.querySelector('.flex-1.flex.flex-col.px-4')

    // Then: Should NOT have overflow-y-auto (document handles scrolling)
    expect(mainContent).not.toHaveClass('overflow-y-auto')

    // And: Should NOT have overflow-hidden
    expect(mainContent).not.toHaveClass('overflow-hidden')
  })

  it('should display "Today\'s sets" section at bottom instead of separate completed/next sections', () => {
    // Given: Exercise page with sets
    renderWithProvider(<ExercisePageV2Client exerciseId={1} />)

    // When: Looking for sets sections
    const container = screen.getByTestId('exercise-page-container')

    // Then: Should have single "Today's sets" section
    expect(screen.getByText("Today's sets")).toBeInTheDocument()

    // Should NOT have separate "Completed Sets" or "Next sets" sections
    expect(screen.queryByText('Completed Sets')).not.toBeInTheDocument()
    expect(screen.queryByText('Next sets')).not.toBeInTheDocument()

    // Today's sets should be in the main content area
    const todaysSets = screen.getByText("Today's sets").closest('div')
    const mainContent = container.querySelector('.flex-1')
    expect(mainContent).toContainElement(todaysSets)
  })

  it('should use min-h-screen instead of h-screen for flexible height', () => {
    // Given: Exercise page is rendered
    renderWithProvider(<ExercisePageV2Client exerciseId={1} />)

    // When: Examining the main container
    const container = screen.getByTestId('exercise-page-container')

    // Then: Should use min-h-screen for flexible height
    expect(container).toHaveClass('min-h-screen')

    // And: Should NOT have fixed h-screen class
    expect(container).not.toHaveClass('h-screen')
  })

  it('should not have nested scroll containers', () => {
    // Given: Exercise page is rendered
    renderWithProvider(<ExercisePageV2Client exerciseId={1} />)

    // When: Looking for overflow classes
    const container = screen.getByTestId('exercise-page-container')
    const scrollContainers = container.querySelectorAll(
      '[class*="overflow-y-auto"], [class*="overflow-auto"], [class*="overflow-scroll"]'
    )

    // Then: Should have NO nested scroll containers (page body handles scrolling)
    expect(scrollContainers).toHaveLength(0)
  })

  it('should allow natural document flow without height constraints', () => {
    // Given: Exercise page is rendered
    renderWithProvider(<ExercisePageV2Client exerciseId={1} />)

    // When: Examining the flex structure
    const container = screen.getByTestId('exercise-page-container')
    const mainContent = container.querySelector('.flex-1')

    // Then: Main content should NOT have overflow-y-auto
    expect(mainContent).toBeTruthy()
    expect(mainContent).not.toHaveClass('overflow-y-auto')

    // And: Container should not constrain height with h-screen
    expect(container).not.toHaveClass('h-screen')
  })
})
