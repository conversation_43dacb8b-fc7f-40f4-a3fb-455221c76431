/* eslint-disable no-await-in-loop */
/* eslint-disable no-console */
import { test, expect, Page } from '@playwright/test'

// Test credentials for et<PERSON><PERSON><PERSON>@gmail.com account
const TEST_USER = 'etienne<PERSON><PERSON>@gmail.com'
const TEST_PASSWORD = 'lobla911'

async function loginToApp(page: Page) {
  await page.goto('/')

  // Wait for the app to load and check if already logged in
  await page.waitForLoadState('networkidle')

  // Check if we need to login
  const needsLogin = await page
    .locator('button:has-text("Sign in")')
    .isVisible()
    .catch(() => false)

  if (needsLogin) {
    // Click sign in button
    await page.locator('button:has-text("Sign in")').click()

    // Fill in credentials
    await page.fill(
      'input[type="email"], input[name="email"], input[placeholder*="email" i]',
      TEST_USER
    )
    await page.fill(
      'input[type="password"], input[name="password"], input[placeholder*="password" i]',
      TEST_PASSWORD
    )

    // Submit login form
    await page
      .locator(
        'button[type="submit"], button:has-text("Sign in"), button:has-text("Log in")'
      )
      .click()

    // Wait for navigation after login
    await page.waitForURL('**/workout/**', { timeout: 30000 })
  }
}

test.describe('Drop Sets - MAUI Parity', () => {
  test('should display drop sets with minimum 3 sets and one-third reps', async ({
    page,
  }) => {
    // Login first
    await loginToApp(page)

    // Navigate to workout page
    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Look for any exercise card
    const exerciseCards = page.locator(
      '[data-testid*="exercise"], .exercise-card, [class*="exercise"]'
    )
    const exerciseCount = await exerciseCards.count()

    console.log(`Found ${exerciseCount} exercises`)

    // Try to find an exercise with drop sets
    let foundDropSet = false

    for (let i = 0; i < Math.min(exerciseCount, 5); i++) {
      const card = exerciseCards.nth(i)
      const exerciseName = await card.textContent()
      console.log(`Checking exercise ${i}: ${exerciseName}`)

      // Click on the exercise
      await card.click()

      // Wait for navigation
      await page.waitForLoadState('networkidle')
      await page.waitForTimeout(2000) // Give time for sets to load

      // Check if this exercise has drop sets
      const dropSetLabels = page.locator('text="Drop set"')
      const dropSetCount = await dropSetLabels.count()

      if (dropSetCount > 0) {
        console.log(`Found ${dropSetCount} drop set labels in ${exerciseName}`)
        foundDropSet = true

        // Verify minimum 3 sets for drop sets
        expect(dropSetCount).toBeGreaterThanOrEqual(3)

        // Look for reps inputs
        const repsInputs = page
          .locator('input[type="number"], input[inputmode="numeric"]')
          .filter({ hasText: /\d+/ })
        const inputCount = await repsInputs.count()

        if (inputCount > 0) {
          // Get first work set reps (after warmups)
          const warmupCount = await page.locator('text=/warm.*up/i').count()
          const firstWorkSetIndex = warmupCount

          const firstSetReps = await repsInputs
            .nth(firstWorkSetIndex)
            .inputValue()
            .catch(() => '12') // Default if not found

          const baseReps = parseInt(firstSetReps)
          const expectedDropReps = Math.max(1, Math.floor(baseReps / 3))

          console.log(
            `Base reps: ${baseReps}, Expected drop reps: ${expectedDropReps}`
          )

          // Check drop sets have one-third reps
          for (
            let j = firstWorkSetIndex + 1;
            j < Math.min(inputCount, firstWorkSetIndex + 3);
            j++
          ) {
            const dropSetReps = await repsInputs
              .nth(j)
              .inputValue()
              .catch(() => null)

            if (dropSetReps) {
              const actualDropReps = parseInt(dropSetReps)
              console.log(
                `Drop set ${j - firstWorkSetIndex} reps: ${actualDropReps}`
              )

              // Verify one-third reps rule
              expect(actualDropReps).toBe(expectedDropReps)
            }
          }
        }

        break // Found and tested drop sets
      }

      // Go back to workout page
      await page.goBack()
      await page.waitForLoadState('networkidle')
    }

    if (!foundDropSet) {
      console.log('No exercises with drop sets found in current workout')
      test.skip()
    }
  })

  test('should enforce minimum 3 sets even with fewer from API', async ({
    page,
  }) => {
    await loginToApp(page)

    // Navigate to workout
    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Look for any exercise with drop sets
    const dropSetIndicator = page.locator('text="Drop set"').first()
    const hasDropSets = await dropSetIndicator.isVisible().catch(() => false)

    if (hasDropSets) {
      // Click on the parent exercise card
      await dropSetIndicator.locator('..').locator('..').click()

      // Wait for exercise details
      await page.waitForLoadState('networkidle')

      // Count total drop sets
      const dropSetLabels = page.locator('text="Drop set"')
      const count = await dropSetLabels.count()

      console.log(`Total drop sets found: ${count}`)

      // Verify minimum 3 sets
      expect(count).toBeGreaterThanOrEqual(3)
    } else {
      console.log('No exercises with drop sets found in current workout')
      test.skip()
    }
  })
})
