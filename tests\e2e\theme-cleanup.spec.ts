import { test, expect } from '@playwright/test'

test.describe('Theme Cleanup Verification', () => {
  test('should only have subtle-depth theme', async ({ page }) => {
    // Navigate to any page
    await page.goto('/', { waitUntil: 'domcontentloaded' })

    // Check that the default theme is subtle-depth
    const theme = await page.evaluate(
      () => document.documentElement.dataset.theme
    )
    expect(theme).toBe('subtle-depth')
  })

  test('modals should not have glassmorphism effects', async ({ page }) => {
    // Go to login page which has modals
    await page.goto('/login')

    // Check for any elements with backdrop-blur
    const blurElements = await page.locator('[class*="backdrop-blur"]').count()
    expect(blurElements).toBe(0)
  })
})
