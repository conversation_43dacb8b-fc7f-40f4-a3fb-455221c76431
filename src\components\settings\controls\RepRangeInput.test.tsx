import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import RepRangeInput from './RepRangeInput'

// Mock window.matchMedia for responsive testing
const mockMatchMedia = (matches: boolean) => {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation((query) => ({
      matches,
      media: query,
      onchange: null,
      addListener: vi.fn(), // deprecated
      removeListener: vi.fn(), // deprecated
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  })
}

describe('RepRangeInput', () => {
  const mockOnChange = vi.fn()

  beforeEach(() => {
    mockOnChange.mockClear()
    // matchMedia will be mocked in each test group
  })

  describe('Mobile Column Layout', () => {
    beforeEach(() => {
      // Mock mobile viewport (<640px)
      mockMatchMedia(false)
    })

    it('should display inputs in column layout on mobile viewports', () => {
      render(
        <RepRangeInput
          minReps={6}
          maxReps={12}
          onChange={mockOnChange}
          disabled={false}
        />
      )

      const container = screen.getByTestId('rep-range-container')
      // Component uses responsive classes: flex-col sm:flex-row
      expect(container).toHaveClass('flex-col')
      expect(container).toHaveClass('sm:flex-row')
    })

    it('should have proper spacing between stacked inputs on mobile', () => {
      render(
        <RepRangeInput
          minReps={6}
          maxReps={12}
          onChange={mockOnChange}
          disabled={false}
        />
      )

      const container = screen.getByTestId('rep-range-container')
      // Component uses responsive spacing: space-y-4 sm:space-y-0 sm:space-x-4
      expect(container).toHaveClass('space-y-4')
      expect(container).toHaveClass('sm:space-y-0')
      expect(container).toHaveClass('sm:space-x-4')
    })
  })

  describe('Desktop Side-by-Side Layout', () => {
    beforeEach(() => {
      // Mock desktop viewport (>=640px)
      mockMatchMedia(true)
    })

    it('should display inputs side-by-side on desktop viewports', () => {
      render(
        <RepRangeInput
          minReps={6}
          maxReps={12}
          onChange={mockOnChange}
          disabled={false}
        />
      )

      const container = screen.getByTestId('rep-range-container')
      // Component uses responsive classes that apply on desktop via sm: prefix
      expect(container).toHaveClass('sm:flex-row')
      expect(container).toHaveClass('flex-col') // Mobile-first default
    })

    it('should have proper spacing between side-by-side inputs on desktop', () => {
      render(
        <RepRangeInput
          minReps={6}
          maxReps={12}
          onChange={mockOnChange}
          disabled={false}
        />
      )

      const container = screen.getByTestId('rep-range-container')
      // Component uses responsive spacing applied on desktop
      expect(container).toHaveClass('sm:space-x-4')
      expect(container).toHaveClass('sm:space-y-0')
    })
  })

  describe('Touch Target Compliance', () => {
    it('should maintain 52px minimum height for all buttons and inputs', () => {
      render(
        <RepRangeInput
          minReps={6}
          maxReps={12}
          onChange={mockOnChange}
          disabled={false}
        />
      )

      // Check min reps increment/decrement buttons
      const minDecrementBtn = screen.getByLabelText('Decrease minimum reps')
      const minIncrementBtn = screen.getByLabelText('Increase minimum reps')
      const minInput = screen.getByLabelText('Minimum reps')

      expect(minDecrementBtn).toHaveClass('min-h-[52px]')
      expect(minIncrementBtn).toHaveClass('min-h-[52px]')
      expect(minInput).toHaveClass('min-h-[52px]')

      // Check max reps increment/decrement buttons
      const maxDecrementBtn = screen.getByLabelText('Decrease maximum reps')
      const maxIncrementBtn = screen.getByLabelText('Increase maximum reps')
      const maxInput = screen.getByLabelText('Maximum reps')

      expect(maxDecrementBtn).toHaveClass('min-h-[52px]')
      expect(maxIncrementBtn).toHaveClass('min-h-[52px]')
      expect(maxInput).toHaveClass('min-h-[52px]')
    })
  })

  describe('Input Functionality', () => {
    it('should call onChange when min reps is incremented', () => {
      render(
        <RepRangeInput
          minReps={6}
          maxReps={12}
          onChange={mockOnChange}
          disabled={false}
        />
      )

      const incrementBtn = screen.getByLabelText('Increase minimum reps')
      fireEvent.click(incrementBtn)

      expect(mockOnChange).toHaveBeenCalledWith({ minReps: 7, maxReps: 12 })
    })

    it('should call onChange when max reps is decremented', () => {
      render(
        <RepRangeInput
          minReps={6}
          maxReps={12}
          onChange={mockOnChange}
          disabled={false}
        />
      )

      const decrementBtn = screen.getByLabelText('Decrease maximum reps')
      fireEvent.click(decrementBtn)

      expect(mockOnChange).toHaveBeenCalledWith({ minReps: 6, maxReps: 11 })
    })

    it('should prevent min reps from exceeding max reps - 1', () => {
      render(
        <RepRangeInput
          minReps={11}
          maxReps={12}
          onChange={mockOnChange}
          disabled={false}
        />
      )

      const incrementBtn = screen.getByLabelText('Increase minimum reps')
      fireEvent.click(incrementBtn)

      // Should not allow min to equal or exceed max
      expect(mockOnChange).not.toHaveBeenCalled()
    })

    it('should prevent max reps from being less than min reps + 1', () => {
      render(
        <RepRangeInput
          minReps={11}
          maxReps={12}
          onChange={mockOnChange}
          disabled={false}
        />
      )

      const decrementBtn = screen.getByLabelText('Decrease maximum reps')
      fireEvent.click(decrementBtn)

      // Should not allow max to equal or go below min
      expect(mockOnChange).not.toHaveBeenCalled()
    })
  })

  describe('Validation Display', () => {
    it('should display current target range below inputs', () => {
      render(
        <RepRangeInput
          minReps={8}
          maxReps={10}
          onChange={mockOnChange}
          disabled={false}
        />
      )

      expect(screen.getByText('Target range: 8-10 reps')).toBeInTheDocument()
    })
  })

  describe('Disabled State', () => {
    it('should disable all buttons when disabled prop is true', () => {
      render(
        <RepRangeInput
          minReps={6}
          maxReps={12}
          onChange={mockOnChange}
          disabled
        />
      )

      const buttons = screen.getAllByRole('button')
      buttons.forEach((button) => {
        expect(button).toBeDisabled()
      })
    })

    it('should disable inputs when disabled prop is true', () => {
      render(
        <RepRangeInput
          minReps={6}
          maxReps={12}
          onChange={mockOnChange}
          disabled
        />
      )

      const minInput = screen.getByLabelText('Minimum reps')
      const maxInput = screen.getByLabelText('Maximum reps')

      expect(minInput).toBeDisabled()
      expect(maxInput).toBeDisabled()
    })
  })

  describe('Edge Cases', () => {
    it('should handle boundary values correctly (min = 1, max = 50)', () => {
      render(
        <RepRangeInput
          minReps={1}
          maxReps={50}
          onChange={mockOnChange}
          disabled={false}
        />
      )

      // Should not be able to decrement min below 1
      const minDecrementBtn = screen.getByLabelText('Decrease minimum reps')
      fireEvent.click(minDecrementBtn)
      expect(mockOnChange).not.toHaveBeenCalled()

      // Should not be able to increment max above 50
      const maxIncrementBtn = screen.getByLabelText('Increase maximum reps')
      fireEvent.click(maxIncrementBtn)
      expect(mockOnChange).not.toHaveBeenCalled()
    })

    /**
     * Test verifies component's built-in validation prevents obviously invalid states
     * while parent hook handles edge case validation for responsiveness
     */
    it('should prevent obviously invalid states at UI level', () => {
      render(
        <RepRangeInput
          minReps={11}
          maxReps={12}
          onChange={mockOnChange}
          disabled={false}
        />
      )

      // Component should disable increment when at boundary (minReps >= maxReps - 1)
      const incrementBtn = screen.getByLabelText('Increase minimum reps')
      expect(incrementBtn).toBeDisabled()

      // Clicking disabled button should not trigger onChange
      fireEvent.click(incrementBtn)
      expect(mockOnChange).not.toHaveBeenCalled()
    })

    it('should remain responsive for valid range changes', () => {
      render(
        <RepRangeInput
          minReps={8}
          maxReps={12}
          onChange={mockOnChange}
          disabled={false}
        />
      )

      // Component should allow valid increment (8 -> 9, still < 12)
      const incrementBtn = screen.getByLabelText('Increase minimum reps')
      expect(incrementBtn).not.toBeDisabled()

      fireEvent.click(incrementBtn)
      expect(mockOnChange).toHaveBeenCalledWith({ minReps: 9, maxReps: 12 })
    })
  })
})
