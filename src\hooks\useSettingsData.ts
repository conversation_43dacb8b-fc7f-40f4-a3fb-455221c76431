'use client'

import { useState, useEffect } from 'react'
import { getServerUserInfoCached } from '@/services/userInfoCache'
import { useAuthStore } from '@/stores/authStore'

export interface SettingsData {
  email: string
  weightUnit: 'kg' | 'lbs'
  setStyle: 'Normal' | 'Rest-Pause' | 'Drop' | 'Pyramid' | 'Reverse Pyramid'
  quickMode: boolean
  repRange: {
    min: number
    max: number
  }
  weightIncrement: number
  warmupSets: number
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function mapUserInfoToSettings(userInfo: any): SettingsData {
  if (!userInfo) {
    return {
      email: '',
      weightUnit: 'lbs',
      setStyle: 'Normal',
      quickMode: false,
      repRange: { min: 6, max: 12 },
      weightIncrement: 5,
      warmupSets: 0,
    }
  }

  // Determine set style from user preferences
  let setStyle: SettingsData['setStyle'] = 'Normal'
  if (userInfo.IsDropSet) {
    setStyle = 'Drop'
  } else if (userInfo.IsPyramid) {
    setStyle = 'Pyramid'
  } else if (userInfo.IsReversePyramid) {
    setStyle = 'Reverse Pyramid'
  } else if (userInfo.IsRestPause) {
    setStyle = 'Rest-Pause'
  } else if (userInfo.IsNormalSet === false) {
    // If not normal but no other style is set, default to Rest-Pause
    setStyle = 'Rest-Pause'
  }

  return {
    email: userInfo.Email || '',
    weightUnit: userInfo.MassUnit === 'kg' ? 'kg' : 'lbs',
    setStyle,
    quickMode: userInfo.IsQuickMode || false,
    repRange: {
      min: userInfo.RepsMinimum || 6,
      max: userInfo.RepsMaximum || 12,
    },
    weightIncrement:
      userInfo.WeightIncrement || (userInfo.MassUnit === 'kg' ? 2.5 : 5),
    warmupSets: userInfo.WarmupSets || 0,
  }
}

export function useSettingsData() {
  const [data, setData] = useState<SettingsData | undefined>()
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)
  const { getCachedUserInfo } = useAuthStore()

  useEffect(() => {
    async function fetchSettings() {
      try {
        setIsLoading(true)
        setError(null)

        // Try to get from cache first
        const cachedInfo = getCachedUserInfo()
        if (cachedInfo) {
          setData(mapUserInfoToSettings(cachedInfo))
          setIsLoading(false)
          return
        }

        // Fetch from server if not in cache
        const userInfo = await getServerUserInfoCached()
        setData(mapUserInfoToSettings(userInfo))
      } catch (err) {
        setError(
          err instanceof Error ? err : new Error('Failed to fetch settings')
        )
      } finally {
        setIsLoading(false)
      }
    }

    fetchSettings()
  }, [getCachedUserInfo])

  return { data, isLoading, error }
}
