'use client'

import { render, screen } from '@testing-library/react'
import { ExerciseCompleteViewV2 } from '../ExerciseCompleteViewV2'
import { vi } from 'vitest'
import type { ExerciseModel, RecommendationModel } from '@/types'

// Mock framer-motion to avoid animation issues in tests
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, initial, animate, transition, ...props }: any) => (
      <div {...props}>{children}</div>
    ),
    button: ({ children, whileHover, whileTap, ...props }: any) => (
      <button {...props}>{children}</button>
    ),
  },
}))

// Mock haptics
vi.mock('@/utils/haptics', () => ({
  vibrate: vi.fn(),
}))

// Mock SuccessIcon component to avoid import issues
vi.mock('@/components/workout/SuccessIcon', () => ({
  SuccessIcon: ({ size = 120, className = '' }: any) => {
    return (
      <div
        data-testid="success-icon"
        className={className}
        style={{ width: size, height: size }}
      >
        Golden Checkmark
      </div>
    )
  },
}))

describe('ExerciseCompleteViewV2 - Enhanced', () => {
  const mockExercise: ExerciseModel = {
    Id: 123,
    Label: 'Bench Press',
    IsBodyweight: false,
  } as ExerciseModel

  const mockOnContinue = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Golden Success Icon', () => {
    it('should display golden SuccessIcon instead of plain CheckCircle', () => {
      render(
        <ExerciseCompleteViewV2
          exercise={mockExercise}
          isLastExercise={false}
          onContinue={mockOnContinue}
          recommendation={null}
        />
      )

      // Should use SuccessIcon component
      expect(screen.getByTestId('success-icon')).toBeInTheDocument()
      expect(screen.getByText('Golden Checkmark')).toBeInTheDocument()

      // Should not have the plain CheckCircle
      expect(
        screen.queryByRole('img', { name: 'Success checkmark' })
      ).not.toBeInTheDocument()
    })
  })

  describe('Percentage Increase Display', () => {
    it('should display positive percentage increase when OneRMProgress is positive', () => {
      const mockRecommendation: RecommendationModel = {
        ExerciseId: 123,
        Series: 3,
        Reps: 10,
        Weight: { Kg: 60, Lb: 132 },
        OneRMProgress: 5.2,
        WarmupsCount: 0,
        RpRest: 120,
        NbPauses: 0,
        NbRepsPauses: 0,
      } as RecommendationModel

      render(
        <ExerciseCompleteViewV2
          exercise={mockExercise}
          isLastExercise={false}
          onContinue={mockOnContinue}
          recommendation={mockRecommendation}
        />
      )

      // Should display percentage with + sign
      expect(screen.getByText('+5.2% stronger!')).toBeInTheDocument()
    })

    it('should display negative percentage when OneRMProgress is negative', () => {
      const mockRecommendation: RecommendationModel = {
        ExerciseId: 123,
        Series: 3,
        Reps: 10,
        Weight: { Kg: 60, Lb: 132 },
        OneRMProgress: -2.5,
        WarmupsCount: 0,
        RpRest: 120,
        NbPauses: 0,
        NbRepsPauses: 0,
      } as RecommendationModel

      render(
        <ExerciseCompleteViewV2
          exercise={mockExercise}
          isLastExercise={false}
          onContinue={mockOnContinue}
          recommendation={mockRecommendation}
        />
      )

      // Should display percentage with appropriate message
      expect(screen.getByText('-2.5% today')).toBeInTheDocument()
    })

    it('should not display percentage when OneRMProgress is zero', () => {
      const mockRecommendation: RecommendationModel = {
        ExerciseId: 123,
        Series: 3,
        Reps: 10,
        Weight: { Kg: 60, Lb: 132 },
        OneRMProgress: 0,
        WarmupsCount: 0,
        RpRest: 120,
        NbPauses: 0,
        NbRepsPauses: 0,
      } as RecommendationModel

      render(
        <ExerciseCompleteViewV2
          exercise={mockExercise}
          isLastExercise={false}
          onContinue={mockOnContinue}
          recommendation={mockRecommendation}
        />
      )

      // Should not show any percentage text
      expect(screen.queryByText(/stronger/)).not.toBeInTheDocument()
      expect(screen.queryByText(/today/)).not.toBeInTheDocument()
    })

    it('should not display percentage when recommendation is null', () => {
      render(
        <ExerciseCompleteViewV2
          exercise={mockExercise}
          isLastExercise={false}
          onContinue={mockOnContinue}
          recommendation={null}
        />
      )

      // Should not show any percentage text
      expect(screen.queryByText(/stronger/)).not.toBeInTheDocument()
      expect(screen.queryByText(/today/)).not.toBeInTheDocument()
    })

    it('should handle very large percentage increases', () => {
      const mockRecommendation: RecommendationModel = {
        ExerciseId: 123,
        Series: 3,
        Reps: 10,
        Weight: { Kg: 60, Lb: 132 },
        OneRMProgress: 150.5,
        WarmupsCount: 0,
        RpRest: 120,
        NbPauses: 0,
        NbRepsPauses: 0,
      } as RecommendationModel

      render(
        <ExerciseCompleteViewV2
          exercise={mockExercise}
          isLastExercise={false}
          onContinue={mockOnContinue}
          recommendation={mockRecommendation}
        />
      )

      // Should display large percentage correctly
      expect(screen.getByText('+150.5% stronger!')).toBeInTheDocument()
    })
  })

  describe('Animation Sequence', () => {
    it('should show golden checkmark animation on mount', () => {
      render(
        <ExerciseCompleteViewV2
          exercise={mockExercise}
          isLastExercise={false}
          onContinue={mockOnContinue}
          recommendation={null}
        />
      )

      const successIcon = screen.getByTestId('success-icon')
      expect(successIcon).toHaveClass('animate-scale-bounce')
    })

    it('should apply fade-in animations to text elements', () => {
      const mockRecommendation: RecommendationModel = {
        ExerciseId: 123,
        Series: 3,
        Reps: 10,
        Weight: { Kg: 60, Lb: 132 },
        OneRMProgress: 5.0,
        WarmupsCount: 0,
        RpRest: 120,
        NbPauses: 0,
        NbRepsPauses: 0,
      } as RecommendationModel

      render(
        <ExerciseCompleteViewV2
          exercise={mockExercise}
          isLastExercise={false}
          onContinue={mockOnContinue}
          recommendation={mockRecommendation}
        />
      )

      // Check for fade-in animations
      const heading = screen.getByText('Bench Press Complete!')
      expect(heading.closest('div')).toHaveClass('animate-fade-in')

      const percentageText = screen.getByText('+5% stronger!')
      expect(percentageText).toHaveClass('animate-fade-in')
    })
  })

  describe('Golden Theme Styling', () => {
    it('should use golden/yellow color scheme for success elements', () => {
      const mockRecommendation: RecommendationModel = {
        ExerciseId: 123,
        Series: 3,
        Reps: 10,
        Weight: { Kg: 60, Lb: 132 },
        OneRMProgress: 10.0,
        WarmupsCount: 0,
        RpRest: 120,
        NbPauses: 0,
        NbRepsPauses: 0,
      } as RecommendationModel

      render(
        <ExerciseCompleteViewV2
          exercise={mockExercise}
          isLastExercise={false}
          onContinue={mockOnContinue}
          recommendation={mockRecommendation}
        />
      )

      // Check for golden theme classes
      const percentageText = screen.getByText('+10% stronger!')
      expect(percentageText).toHaveClass('text-yellow-500')
    })
  })

  describe('Confetti Animation', () => {
    it('should render confetti particles for positive progress', () => {
      const mockRecommendation: RecommendationModel = {
        ExerciseId: 123,
        Series: 3,
        Reps: 10,
        Weight: { Kg: 60, Lb: 132 },
        OneRMProgress: 5.0,
        WarmupsCount: 0,
        RpRest: 120,
        NbPauses: 0,
        NbRepsPauses: 0,
      } as RecommendationModel

      render(
        <ExerciseCompleteViewV2
          exercise={mockExercise}
          isLastExercise={false}
          onContinue={mockOnContinue}
          recommendation={mockRecommendation}
        />
      )

      // Should have confetti particles
      const confettiParticles = screen.getAllByTestId(/confetti-particle/)
      expect(confettiParticles.length).toBeGreaterThan(0)
    })

    it('should not render confetti for negative or zero progress', () => {
      const mockRecommendation: RecommendationModel = {
        ExerciseId: 123,
        Series: 3,
        Reps: 10,
        Weight: { Kg: 60, Lb: 132 },
        OneRMProgress: -5.0,
        WarmupsCount: 0,
        RpRest: 120,
        NbPauses: 0,
        NbRepsPauses: 0,
      } as RecommendationModel

      render(
        <ExerciseCompleteViewV2
          exercise={mockExercise}
          isLastExercise={false}
          onContinue={mockOnContinue}
          recommendation={mockRecommendation}
        />
      )

      // Should not have confetti particles
      const confettiParticles = screen.queryAllByTestId(/confetti-particle/)
      expect(confettiParticles.length).toBe(0)
    })
  })
})
