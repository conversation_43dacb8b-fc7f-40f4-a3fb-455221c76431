import React from 'react'
import { render, screen } from '@testing-library/react'
import { vi } from 'vitest'
import { SetCellArrowButtons } from '../SetCellArrowButtons'

describe('SetCellArrowButtons - 52px Touch Target Compliance', () => {
  const mockHandlers = {
    onRepsUp: vi.fn(),
    onRepsDown: vi.fn(),
    onWeightUp: vi.fn(),
    onWeightDown: vi.fn(),
  }

  it('should have 52px minimum touch targets for all arrow buttons', () => {
    render(<SetCellArrowButtons isActive position="above" {...mockHandlers} />)

    const arrowButtons = screen.getAllByRole('button')
    expect(arrowButtons).toHaveLength(2)

    // Verify each button has the 52px classes
    arrowButtons.forEach((button) => {
      expect(button).toHaveClass('min-h-[52px]')
      expect(button).toHaveClass('min-w-[52px]')
    })
  })

  it('should maintain 52px touch targets for both positions', () => {
    const { rerender } = render(
      <SetCellArrowButtons isActive position="above" {...mockHandlers} />
    )

    // Test above position
    let buttons = screen.getAllByRole('button')
    buttons.forEach((button) => {
      expect(button).toHaveClass('min-h-[52px]')
      expect(button).toHaveClass('min-w-[52px]')
    })

    // Test below position
    rerender(
      <SetCellArrowButtons isActive position="below" {...mockHandlers} />
    )

    buttons = screen.getAllByRole('button')
    buttons.forEach((button) => {
      expect(button).toHaveClass('min-h-[52px]')
      expect(button).toHaveClass('min-w-[52px]')
    })
  })

  it('should not render buttons when inactive', () => {
    render(
      <SetCellArrowButtons
        isActive={false}
        position="above"
        {...mockHandlers}
      />
    )

    const buttons = screen.queryAllByRole('button')
    expect(buttons).toHaveLength(0)
  })
})
