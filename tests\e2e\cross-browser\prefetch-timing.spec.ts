import { test, expect } from '@playwright/test'

// Test 100ms prefetch timing performance
test.describe('100ms Prefetch Timing', () => {
  test('Prefetch timing reduced from 500ms to 100ms', async ({ page }) => {
    // Setup console log capture
    const logs: string[] = []
    page.on('console', (msg) => logs.push(msg.text()))

    await page.goto('/login')

    // Login first
    await page.fill('[type="email"]', '<EMAIL>')
    await page.fill('[type="password"]', 'Dr123456')
    await page.click('button[type="submit"]')

    // Wait for redirect to program page
    await page.waitForURL('/program', { timeout: 10000 })
    await page.waitForLoadState('networkidle')

    // Wait for prefetch logs
    await page.waitForTimeout(1000)

    // Check for 100ms timing in logs
    const has100msLog = logs.some((log) => log.includes('100ms'))
    const hasPrefetchLog = logs.some(
      (log) => log.includes('preload') || log.includes('Preloading')
    )

    expect(has100msLog || hasPrefetchLog).toBeTruthy()

    // Verify no 500ms logs (old timing)
    const has500msLog = logs.some((log) => log.includes('500ms'))
    expect(has500msLog).toBeFalsy()
  })

  test('Progressive loading shows immediate content', async ({ page }) => {
    await page.goto('/login')

    // Login
    await page.fill('[type="email"]', '<EMAIL>')
    await page.fill('[type="password"]', 'Dr123456')
    await page.click('button[type="submit"]')

    await page.waitForURL('/program')

    // Welcome header should appear immediately
    const welcomeHeader = page.locator('[data-testid="welcome-header"]')
    await expect(welcomeHeader).toBeVisible({ timeout: 1000 })

    // First-time user card should appear quickly for new users
    const firstTimeCard = page.locator('[data-testid="first-time-user-card"]')
    if ((await firstTimeCard.count()) > 0) {
      await expect(firstTimeCard).toBeVisible({ timeout: 2000 })
    }
  })
})
