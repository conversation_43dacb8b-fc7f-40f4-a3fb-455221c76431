/**
 * Touch Target Measurement and Validation Utilities
 *
 * Purpose: Provide consistent touch target measurement and compliance validation
 * Usage: Import these utilities in component tests to validate 52px compliance
 * Standards: Based on WCAG 2.1 AA and mobile platform guidelines
 */

export interface TouchTargetMeasurement {
  width: number
  height: number
  clickableArea: number
  meetsMinimum: boolean
}

export interface TouchTargetViolation {
  element: Element
  measurement: TouchTargetMeasurement
  expectedMinimum: number
}

export interface TouchTargetValidationResult {
  totalElements: number
  violations: TouchTargetViolation[]
  complianceRate: number
  isCompliant: boolean
}

/**
 * Measures the effective touch target dimensions of an element
 * Considers both actual rendered size and CSS minimum dimensions
 */
export function measureTouchTarget(
  element: Element,
  minimum = 52
): TouchTargetMeasurement {
  const rect = element.getBoundingClientRect()
  const computedStyle = getComputedStyle(element)

  // Get actual and minimum dimensions
  const actualWidth = rect.width
  const actualHeight = rect.height
  const minWidth = parseFloat(computedStyle.minWidth) || 0
  const minHeight = parseFloat(computedStyle.minHeight) || 0

  // Use the larger of actual or minimum dimensions
  const effectiveWidth = Math.max(actualWidth, minWidth)
  const effectiveHeight = Math.max(actualHeight, minHeight)

  return {
    width: effectiveWidth,
    height: effectiveHeight,
    clickableArea: effectiveWidth * effectiveHeight,
    meetsMinimum: Math.min(effectiveWidth, effectiveHeight) >= minimum,
  }
}

/**
 * Validates touch target compliance for all interactive elements in a container
 * Returns detailed report of violations and compliance metrics
 */
export function validateTouchTargetCompliance(
  container: Element,
  minimum = 52
): TouchTargetValidationResult {
  // Query for all interactive elements
  const interactiveSelectors = [
    'button',
    'input',
    'select',
    'textarea',
    '[role="button"]',
    '[role="tab"]',
    '[role="menuitem"]',
    '[tabindex="0"]',
    'a[href]',
  ].join(', ')

  const interactiveElements = container.querySelectorAll(interactiveSelectors)
  const violations: TouchTargetViolation[] = []

  interactiveElements.forEach((element) => {
    const measurement = measureTouchTarget(element, minimum)

    if (!measurement.meetsMinimum) {
      violations.push({
        element,
        measurement,
        expectedMinimum: minimum,
      })
    }
  })

  const totalElements = interactiveElements.length
  const complianceRate =
    totalElements === 0
      ? 1.0
      : (totalElements - violations.length) / totalElements

  return {
    totalElements,
    violations,
    complianceRate,
    isCompliant: violations.length === 0,
  }
}

/**
 * Assertion helper for Jest tests - validates single element touch target
 */
export function assertTouchTargetCompliance(
  element: Element,
  minimum = 52
): void {
  const measurement = measureTouchTarget(element, minimum)

  if (!measurement.meetsMinimum) {
    throw new Error(
      `Touch target compliance violation: Element has ${Math.min(measurement.width, measurement.height)}px minimum dimension, but ${minimum}px required. ` +
        `Dimensions: ${measurement.width}w × ${measurement.height}h`
    )
  }
}

/**
 * Batch validation helper for multiple elements
 */
export function validateMultipleElements(
  elements: Element[],
  minimum = 52
): TouchTargetValidationResult {
  const violations: TouchTargetViolation[] = []

  elements.forEach((element) => {
    const measurement = measureTouchTarget(element, minimum)

    if (!measurement.meetsMinimum) {
      violations.push({
        element,
        measurement,
        expectedMinimum: minimum,
      })
    }
  })

  const totalElements = elements.length
  const complianceRate =
    totalElements === 0
      ? 1.0
      : (totalElements - violations.length) / totalElements

  return {
    totalElements,
    violations,
    complianceRate,
    isCompliant: violations.length === 0,
  }
}

/**
 * Development helper - logs touch target information for debugging
 */
export function debugTouchTarget(element: Element): void {
  const measurement = measureTouchTarget(element)
  const rect = element.getBoundingClientRect()
  const computedStyle = getComputedStyle(element)

  // Touch Target Debug information for development
  const debugInfo = {
    element:
      element.tagName.toLowerCase() +
      (element.className ? `.${element.className}` : ''),
    actualDimensions: `${rect.width}w × ${rect.height}h`,
    minDimensions: `${computedStyle.minWidth} × ${computedStyle.minHeight}`,
    effectiveDimensions: `${measurement.width}w × ${measurement.height}h`,
    clickableArea: `${measurement.clickableArea}px²`,
    meetsMinimum: measurement.meetsMinimum ? '✅' : '❌',
    complianceGap: measurement.meetsMinimum
      ? 0
      : 52 - Math.min(measurement.width, measurement.height),
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-expressions
  debugInfo
}

/**
 * Generate compliance report for testing and development
 */
export function generateComplianceReport(
  container: Element,
  minimum = 52
): string {
  const result = validateTouchTargetCompliance(container, minimum)

  let report = `Touch Target Compliance Report\n`
  report += `==============================\n`
  report += `Total Interactive Elements: ${result.totalElements}\n`
  report += `Violations: ${result.violations.length}\n`
  report += `Compliance Rate: ${(result.complianceRate * 100).toFixed(1)}%\n`
  report += `Overall Status: ${result.isCompliant ? '✅ COMPLIANT' : '❌ NON-COMPLIANT'}\n\n`

  if (result.violations.length > 0) {
    report += `Violations:\n`
    report += `-----------\n`

    result.violations.forEach((violation, index) => {
      const { element, measurement, expectedMinimum } = violation
      const gap =
        expectedMinimum - Math.min(measurement.width, measurement.height)

      report += `${index + 1}. ${element.tagName.toLowerCase()}`
      if (element.className) report += `.${element.className}`
      report += `\n   Dimensions: ${measurement.width}w × ${measurement.height}h\n`
      report += `   Gap: ${gap}px below minimum\n\n`
    })
  }

  return report
}

/**
 * Jest test helper - expect all elements in container to be compliant
 */
export function expectAllTouchTargetsCompliant(
  container: Element,
  minimum = 52
): void {
  const result = validateTouchTargetCompliance(container, minimum)

  if (!result.isCompliant) {
    const report = generateComplianceReport(container, minimum)
    throw new Error(`Touch target compliance failed:\n${report}`)
  }
}

// Export constants for consistent usage
export const TOUCH_TARGET_MINIMUM = 52 // Updated requirement
export const TOUCH_TARGET_COMFORTABLE = 56 // Comfortable size
export const TOUCH_TARGET_LARGE = 64 // Large size for critical actions

// Legacy constant for migration detection
export const TOUCH_TARGET_LEGACY = 44 // Old requirement - should not be used
