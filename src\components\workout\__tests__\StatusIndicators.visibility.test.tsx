import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import { StatusIndicators } from '../WorkoutOverviewStates'

describe('StatusIndicators Visibility', () => {
  const defaultProps = {
    isOffline: false,
    isRefreshing: false,
    isPrefetching: false,
    prefetchedCount: 0,
    errorCount: 0,
    prefetchProgress: undefined,
    isSmartScheduling: false,
    retryCount: 0,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Component should only appear on program page', () => {
    it('should render StatusIndicators when placed in program page context', () => {
      render(
        <StatusIndicators {...defaultProps} isPrefetching prefetchedCount={3} />
      )

      const container = screen.getByTestId('status-indicators-container')
      expect(container).toBeInTheDocument()
    })

    it('should not render StatusIndicators on workout page (removed)', () => {
      // This test will verify StatusIndicators is removed from WorkoutOverview
      // After removal, WorkoutOverview should not contain StatusIndicators
      expect(true).toBe(true) // Placeholder - will verify after removal
    })
  })

  describe('Theme token compliance', () => {
    it('should use correct bg-bg-secondary classes instead of undefined surface-secondary', () => {
      const { container } = render(
        <StatusIndicators {...defaultProps} isPrefetching />
      )

      // Check that correct theme tokens are used
      const badges = container.querySelectorAll('[role="status"]')
      badges.forEach((badge) => {
        const classList = badge.classList.toString()
        // Should NOT contain undefined surface tokens
        expect(classList).not.toContain('surface-secondary')
        // Should contain proper theme tokens
        if (classList.includes('bg-')) {
          expect(classList).toMatch(
            /bg-bg-secondary|bg-bg-tertiary|bg-brand-primary/
          )
        }
      })
    })

    it('should maintain 52px minimum touch targets', () => {
      const { container } = render(
        <StatusIndicators {...defaultProps} isPrefetching />
      )

      const badges = container.querySelectorAll('[role="status"]')
      badges.forEach((badge) => {
        expect(badge.classList.toString()).toContain('min-h-[52px]')
      })
    })
  })

  describe('Loading states visibility', () => {
    it('should show loading indicator when prefetching', () => {
      render(
        <StatusIndicators
          {...defaultProps}
          isPrefetching
          prefetchProgress={60}
        />
      )

      expect(screen.getByText(/Loading exercises... 60%/)).toBeInTheDocument()
    })

    it('should show success state when exercises are ready', () => {
      render(
        <StatusIndicators
          {...defaultProps}
          prefetchedCount={5}
          errorCount={0}
        />
      )

      expect(screen.getByText('5 exercises ready')).toBeInTheDocument()
    })

    it('should show offline mode indicator', () => {
      render(
        <StatusIndicators {...defaultProps} isOffline prefetchedCount={3} />
      )

      expect(screen.getByText('Offline Mode')).toBeInTheDocument()
      expect(screen.getByText('3 exercises ready')).toBeInTheDocument()
    })

    it('should show error state with retry count', () => {
      render(
        <StatusIndicators {...defaultProps} errorCount={2} retryCount={3} />
      )

      expect(screen.getByText('2 failed (retrying...)')).toBeInTheDocument()
    })
  })

  describe('Conditional visibility rules', () => {
    it('should not show any indicators when no activity', () => {
      const { container } = render(<StatusIndicators {...defaultProps} />)

      const badges = container.querySelectorAll('[role="status"]')
      expect(badges.length).toBe(0)
    })

    it('should show when there is any prefetch activity', () => {
      render(<StatusIndicators {...defaultProps} isPrefetching />)

      expect(
        screen.getByTestId('status-indicators-container')
      ).toBeInTheDocument()
    })

    it('should show when exercises are prefetched', () => {
      render(<StatusIndicators {...defaultProps} prefetchedCount={1} />)

      expect(
        screen.getByTestId('status-indicators-container')
      ).toBeInTheDocument()
    })

    it('should show when there are errors', () => {
      render(<StatusIndicators {...defaultProps} errorCount={1} />)

      expect(
        screen.getByTestId('status-indicators-container')
      ).toBeInTheDocument()
    })
  })
})
