import { renderHook, act } from '@testing-library/react'
import { vi } from 'vitest'
import { usePrefetchMemoryOptimization } from '../usePrefetchMemoryOptimization'

describe('usePrefetchMemoryOptimization', () => {
  const mockClearCache = vi.fn()
  const defaultProps = {
    prefetchedData: new Map([
      [1, { exerciseId: 1, sets: [], timestamp: Date.now() - 60000 }],
      [2, { exerciseId: 2, sets: [], timestamp: Date.now() - 120000 }],
      [3, { exerciseId: 3, sets: [], timestamp: Date.now() - 30000 }],
    ]),
    clearCache: mockClearCache,
    maxCacheSize: 10,
    maxCacheAge: 300000, // 5 minutes
    memoryThreshold: 50, // MB
  }

  beforeEach(() => {
    vi.clearAllMocks()
    // Mock performance.memory API
    Object.defineProperty(performance, 'memory', {
      value: {
        usedJSHeapSize: 30 * 1024 * 1024, // 30MB
        totalJSHeapSize: 100 * 1024 * 1024, // 100MB
        jsHeapSizeLimit: 2 * 1024 * 1024 * 1024, // 2GB
      },
      configurable: true,
    })
  })

  it('should initialize with correct default values', () => {
    const { result } = renderHook(() =>
      usePrefetchMemoryOptimization(defaultProps)
    )

    expect(result.current.cacheSize).toBe(3)
    expect(result.current.memoryUsage).toBeGreaterThan(0)
    expect(result.current.isMemoryPressure).toBe(false)
    expect(result.current.cacheHitRate).toBe(0)
  })

  it('should detect memory pressure when usage exceeds threshold', () => {
    // Mock high memory usage
    Object.defineProperty(performance, 'memory', {
      value: {
        usedJSHeapSize: 60 * 1024 * 1024, // 60MB (above 50MB threshold)
        totalJSHeapSize: 100 * 1024 * 1024,
        jsHeapSizeLimit: 2 * 1024 * 1024 * 1024,
      },
      configurable: true,
    })

    const { result } = renderHook(() =>
      usePrefetchMemoryOptimization(defaultProps)
    )

    expect(result.current.isMemoryPressure).toBe(true)
  })

  it('should clean expired cache entries', () => {
    const expiredData = new Map([
      [1, { exerciseId: 1, sets: [], timestamp: Date.now() - 600000 }], // 10 minutes old
      [2, { exerciseId: 2, sets: [], timestamp: Date.now() - 30000 }], // 30 seconds old
    ])

    const { result } = renderHook(() =>
      usePrefetchMemoryOptimization({
        ...defaultProps,
        prefetchedData: expiredData,
      })
    )

    act(() => {
      result.current.cleanExpiredEntries()
    })

    expect(result.current.expiredEntries).toBe(1)
  })

  it('should clean cache when size exceeds limit', () => {
    const largeData = new Map()
    // Create 15 entries (exceeds maxCacheSize of 10)
    for (let i = 1; i <= 15; i++) {
      largeData.set(i, {
        exerciseId: i,
        sets: [],
        timestamp: Date.now() - i * 10000, // Spread timestamps
      })
    }

    const { result } = renderHook(() =>
      usePrefetchMemoryOptimization({
        ...defaultProps,
        prefetchedData: largeData,
      })
    )

    act(() => {
      result.current.cleanLRUEntries()
    })

    expect(result.current.lruEvictions).toBeGreaterThan(0)
  })

  it('should prioritize memory cleanup under pressure', () => {
    // Mock high memory usage
    Object.defineProperty(performance, 'memory', {
      value: {
        usedJSHeapSize: 80 * 1024 * 1024, // 80MB (well above threshold)
        totalJSHeapSize: 100 * 1024 * 1024,
        jsHeapSizeLimit: 2 * 1024 * 1024 * 1024,
      },
      configurable: true,
    })

    const { result } = renderHook(() =>
      usePrefetchMemoryOptimization(defaultProps)
    )

    act(() => {
      result.current.performMemoryCleanup()
    })

    expect(mockClearCache).toHaveBeenCalled()
  })

  it('should calculate cache hit rate correctly', () => {
    const { result } = renderHook(() =>
      usePrefetchMemoryOptimization(defaultProps)
    )

    act(() => {
      result.current.recordCacheHit()
      result.current.recordCacheHit()
      result.current.recordCacheMiss()
    })

    // 2 hits out of 3 total = 66.67%
    expect(result.current.cacheHitRate).toBeCloseTo(66.67, 1)
  })

  it('should provide memory optimization recommendations', () => {
    const { result } = renderHook(() =>
      usePrefetchMemoryOptimization(defaultProps)
    )

    const recommendations = result.current.getOptimizationRecommendations()

    expect(recommendations).toBeInstanceOf(Array)
    expect(recommendations.length).toBeGreaterThan(0)
  })

  it('should handle graceful degradation under extreme memory pressure', () => {
    // Mock extreme memory usage
    Object.defineProperty(performance, 'memory', {
      value: {
        usedJSHeapSize: 1.8 * 1024 * 1024 * 1024, // 1.8GB (near limit)
        totalJSHeapSize: 2 * 1024 * 1024 * 1024,
        jsHeapSizeLimit: 2 * 1024 * 1024 * 1024,
      },
      configurable: true,
    })

    const { result } = renderHook(() =>
      usePrefetchMemoryOptimization(defaultProps)
    )

    expect(result.current.shouldReducePrefetching).toBe(true)
    expect(result.current.recommendedBatchSize).toBeLessThan(3)
  })

  it('should track memory metrics over time', () => {
    const { result } = renderHook(() =>
      usePrefetchMemoryOptimization(defaultProps)
    )

    act(() => {
      result.current.recordMemorySnapshot()
    })

    expect(result.current.memoryHistory).toHaveLength(1)
    expect(result.current.memoryHistory[0]).toHaveProperty('timestamp')
    expect(result.current.memoryHistory[0]).toHaveProperty('usedMemory')
  })

  it('should handle missing performance.memory API gracefully', () => {
    // Remove performance.memory to simulate unsupported browsers
    Object.defineProperty(performance, 'memory', {
      value: undefined,
      configurable: true,
    })

    const { result } = renderHook(() =>
      usePrefetchMemoryOptimization(defaultProps)
    )

    expect(result.current.memoryUsage).toBe(0)
    expect(result.current.isMemoryPressure).toBe(false)
    expect(result.current.shouldReducePrefetching).toBe(false)
  })

  it('should automatically clean cache at intervals', () => {
    vi.useFakeTimers()

    const { result } = renderHook(() =>
      usePrefetchMemoryOptimization({
        ...defaultProps,
        autoCleanupInterval: 1000, // 1 second
      })
    )

    act(() => {
      vi.advanceTimersByTime(1000)
    })

    // Should have performed some cleanup operations
    expect(result.current.cleanupOperations).toBeGreaterThan(0)

    vi.useRealTimers()
  })
})
