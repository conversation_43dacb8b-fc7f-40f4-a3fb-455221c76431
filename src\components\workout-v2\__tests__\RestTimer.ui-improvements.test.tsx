import { render, screen, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { RestTimer } from '../RestTimer'
import { useWorkoutStore } from '@/stores/workoutStore'

// Mock the workout store
vi.mock('@/stores/workoutStore')
const mockUseWorkoutStore = vi.mocked(useWorkoutStore)

// Mock haptics
vi.mock('@/utils/haptics', () => ({
  vibrate: vi.fn(),
}))

// Mock framer-motion to render immediately without animations
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => children,
}))

// Mock notifications
Object.defineProperty(window, 'Notification', {
  value: vi.fn().mockImplementation(() => ({
    close: vi.fn(),
  })),
  configurable: true,
})

Object.defineProperty(window.Notification, 'permission', {
  value: 'granted',
  configurable: true,
})

describe('RestTimer UI Improvements', () => {
  const mockSetRestTimerState = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    mockUseWorkoutStore.mockReturnValue({
      restTimerState: {
        isActive: true,
        duration: 90,
        nextSetInfo: {
          reps: 8,
          weight: 135,
          unit: 'lbs',
        },
      },
      setRestTimerState: mockSetRestTimerState,
    } as any)
  })

  afterEach(() => {
    vi.clearAllTimers()
  })

  it('displays time in two-row layout with large countdown in first row', async () => {
    render(<RestTimer />)

    // Wait for the component to render
    await waitFor(() => {
      expect(screen.getByTestId('rest-timer-container')).toBeInTheDocument()
    })

    // Timer row should have large time display
    const timerRow = screen.getByTestId('timer-row')
    expect(timerRow).toBeInTheDocument()

    // Time should be displayed as a single element
    const timeDisplay = screen.getByTestId('time-display')
    expect(timeDisplay).toHaveTextContent('1:30')

    // Check for large font classes
    expect(timeDisplay).toHaveClass('text-7xl', 'font-bold')
  })

  it('displays buttons in second row', async () => {
    render(<RestTimer />)

    await waitFor(() => {
      expect(screen.getByTestId('rest-timer-container')).toBeInTheDocument()
    })

    // Buttons should be in buttons row
    const buttonsRow = screen.getByTestId('buttons-row')
    expect(buttonsRow).toBeInTheDocument()

    // Buttons should be in second row
    const durationButton = screen.getByTestId('duration-setting-button')
    const hideButton = screen.getByText('Hide')
    expect(buttonsRow).toContainElement(durationButton)
    expect(buttonsRow).toContainElement(hideButton)
  })

  it('displays progress indicator at bottom of container', async () => {
    render(<RestTimer />)

    await waitFor(() => {
      expect(screen.getByTestId('rest-timer-container')).toBeInTheDocument()
    })

    const progressBar = screen.getByTestId('rest-timer-progress')
    expect(progressBar).toBeInTheDocument()
  })

  it('formats single digit seconds with leading zero', async () => {
    mockUseWorkoutStore.mockReturnValue({
      restTimerState: {
        isActive: true,
        duration: 65, // 1:05
        nextSetInfo: null,
      },
      setRestTimerState: mockSetRestTimerState,
    } as any)

    render(<RestTimer />)

    await waitFor(() => {
      expect(screen.getByTestId('rest-timer-container')).toBeInTheDocument()
    })

    const timeDisplay = screen.getByTestId('time-display')
    expect(timeDisplay).toHaveTextContent('1:05')
  })

  it('displays zero minutes correctly', async () => {
    mockUseWorkoutStore.mockReturnValue({
      restTimerState: {
        isActive: true,
        duration: 45, // 0:45
        nextSetInfo: null,
      },
      setRestTimerState: mockSetRestTimerState,
    } as any)

    render(<RestTimer />)

    await waitFor(() => {
      expect(screen.getByTestId('rest-timer-container')).toBeInTheDocument()
    })

    const timeDisplay = screen.getByTestId('time-display')
    expect(timeDisplay).toHaveTextContent('0:45')
  })

  it('shows Hide button instead of Skip', async () => {
    render(<RestTimer />)

    await waitFor(() => {
      expect(screen.getByTestId('rest-timer-container')).toBeInTheDocument()
    })

    const hideButton = screen.getByText('Hide')
    expect(hideButton).toBeInTheDocument()
    expect(screen.queryByText('Skip')).not.toBeInTheDocument()
  })

  it('applies gold gradient to progress bar', async () => {
    render(<RestTimer />)

    await waitFor(() => {
      expect(screen.getByTestId('rest-timer-container')).toBeInTheDocument()
    })

    const progressBar = screen.getByTestId('rest-timer-progress')
    expect(progressBar).toHaveClass(
      'bg-gradient-to-r',
      'from-brand-gold-start',
      'to-brand-gold-end'
    )
  })

  it('applies gold gradient to countdown text', async () => {
    render(<RestTimer />)

    await waitFor(() => {
      expect(screen.getByTestId('rest-timer-container')).toBeInTheDocument()
    })

    // Check for gold gradient on time display
    const timeDisplay = screen.getByTestId('time-display')
    expect(timeDisplay).toHaveClass(
      'bg-gradient-to-r',
      'from-brand-gold-start',
      'to-brand-gold-end',
      'bg-clip-text',
      'text-transparent'
    )
  })

  it('has semi-opaque background like top navigation', async () => {
    render(<RestTimer />)

    await waitFor(() => {
      expect(screen.getByTestId('rest-timer-container')).toBeInTheDocument()
    })

    const container = screen.getByTestId('rest-timer-container')
    expect(container).toHaveClass('bg-surface-primary/90', 'backdrop-blur-sm')
  })

  it('displays next set info in correct format', async () => {
    render(<RestTimer />)

    await waitFor(() => {
      expect(screen.getByTestId('rest-timer-container')).toBeInTheDocument()
    })

    // Next set info has been removed from the UI
    expect(
      screen.queryByText(/Get ready for: 8 x 135 lbs/)
    ).not.toBeInTheDocument()
  })

  it('displays next set info for kg units', async () => {
    mockUseWorkoutStore.mockReturnValue({
      restTimerState: {
        isActive: true,
        duration: 90,
        nextSetInfo: {
          reps: 10,
          weight: 60,
          unit: 'kg',
        },
      },
      setRestTimerState: mockSetRestTimerState,
    } as any)

    render(<RestTimer />)

    await waitFor(() => {
      expect(screen.getByTestId('rest-timer-container')).toBeInTheDocument()
    })

    // Next set info has been removed from the UI
    expect(
      screen.queryByText(/Get ready for: 10 x 60 kg/)
    ).not.toBeInTheDocument()
  })

  it('displays reps only for bodyweight exercises', async () => {
    mockUseWorkoutStore.mockReturnValue({
      restTimerState: {
        isActive: true,
        duration: 90,
        nextSetInfo: {
          reps: 15,
          weight: 0,
          unit: 'lbs',
        },
      },
      setRestTimerState: mockSetRestTimerState,
    } as any)

    render(<RestTimer />)

    await waitFor(() => {
      expect(screen.getByTestId('rest-timer-container')).toBeInTheDocument()
    })

    // Next set info has been removed from the UI
    expect(screen.queryByText(/Get ready for: 15 reps/)).not.toBeInTheDocument()
  })

  it('shows fallback message when no next set info', async () => {
    mockUseWorkoutStore.mockReturnValue({
      restTimerState: {
        isActive: true,
        duration: 90,
        nextSetInfo: null,
      },
      setRestTimerState: mockSetRestTimerState,
    } as any)

    render(<RestTimer />)

    await waitFor(() => {
      expect(screen.getByTestId('rest-timer-container')).toBeInTheDocument()
    })

    // Next set info has been removed from the UI
    expect(
      screen.queryByText('Get ready for your next set!')
    ).not.toBeInTheDocument()
  })

  it('does not render when rest timer is not active', () => {
    mockUseWorkoutStore.mockReturnValue({
      restTimerState: {
        isActive: false,
        duration: 0,
        nextSetInfo: null,
      },
      setRestTimerState: mockSetRestTimerState,
    } as any)

    render(<RestTimer />)

    expect(screen.queryByTestId('rest-timer-container')).not.toBeInTheDocument()
  })

  it('displays "Rest" text on second row between buttons', async () => {
    render(<RestTimer />)

    await waitFor(() => {
      expect(screen.getByTestId('rest-timer-container')).toBeInTheDocument()
    })

    // Timer row should only contain time display
    const timerRow = screen.getByTestId('timer-row')
    const timeDisplay = screen.getByTestId('time-display')
    const restText = screen.getByText('Rest')

    // Time should be in timer row
    expect(timerRow).toContainElement(timeDisplay)

    // Rest should NOT be in timer row (prevents layout shift)
    expect(timerRow).not.toContainElement(restText)

    // Rest should be in buttons row
    const buttonsRow = screen.getByTestId('buttons-row')
    expect(buttonsRow).toContainElement(restText)

    // Rest text should have appropriate styling
    expect(restText).toHaveClass('text-text-secondary')
  })

  it('has wider buttons for better touch targets', async () => {
    render(<RestTimer />)

    await waitFor(() => {
      expect(screen.getByTestId('rest-timer-container')).toBeInTheDocument()
    })

    // Check that buttons have wider styling classes
    const durationButton = screen.getByTestId('duration-setting-button')
    const hideButton = screen.getByText('Hide').closest('button')

    // Buttons should have padding for wider touch targets
    expect(durationButton).toHaveClass('px-8')
    expect(hideButton).toHaveClass('px-8')
  })

  it('has compact layout with only two rows', async () => {
    render(<RestTimer />)

    await waitFor(() => {
      expect(screen.getByTestId('rest-timer-container')).toBeInTheDocument()
    })

    // Should only have timer row and buttons row
    const container = screen.getByTestId('rest-timer-container')
    const rows = container.querySelectorAll('[data-testid$="-row"]')

    expect(rows).toHaveLength(2)
    expect(screen.getByTestId('timer-row')).toBeInTheDocument()
    expect(screen.getByTestId('buttons-row')).toBeInTheDocument()
  })
})
