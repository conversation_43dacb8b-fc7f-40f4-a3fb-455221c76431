'use client'

import { SET_STYLE_DESCRIPTIONS, SetStyleKey } from '@/constants/setStyles'

interface SetStyleRadioGroupProps {
  value: SetStyleKey
  onChange: (value: SetStyleKey) => void
  disabled?: boolean
}

export default function SetStyleRadioGroup({
  value,
  onChange,
  disabled = false,
}: SetStyleRadioGroupProps) {
  return (
    <div className="space-y-2" role="radiogroup" aria-label="Set Style">
      {Object.entries(SET_STYLE_DESCRIPTIONS).map(([key, style]) => (
        <label
          key={key}
          className={`
            flex items-start p-3 rounded-lg border cursor-pointer
            transition-all min-h-[52px]
            ${
              value === key
                ? 'border-brand-primary bg-brand-primary/5'
                : 'border-surface-tertiary hover:bg-surface-secondary'
            }
            ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
          `}
        >
          <input
            type="radio"
            name="setStyle"
            value={key}
            checked={value === key}
            onChange={() => !disabled && onChange(key as <PERSON><PERSON>ty<PERSON><PERSON><PERSON>)}
            disabled={disabled}
            className="mt-1 mr-3"
            aria-label={style.name}
          />
          <div className="flex-1">
            <div className="text-sm font-medium text-text-primary">
              {style.name}
            </div>
            <div className="text-xs text-text-secondary mt-1">
              {style.shortDescription}
            </div>
          </div>
        </label>
      ))}
    </div>
  )
}
