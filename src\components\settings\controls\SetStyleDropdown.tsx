'use client'

import { memo } from 'react'
import { SET_STYLE_DESCRIPTIONS, SetStyleKey } from '@/constants/setStyles'

interface SetStyleDropdownProps {
  value: SetStyleKey
  onChange: (value: SetStyleKey) => void
  disabled?: boolean
}

function SetStyleDropdown({
  value,
  onChange,
  disabled = false,
}: SetStyleDropdownProps) {
  const selectedStyle = SET_STYLE_DESCRIPTIONS[value]
  const explainerId = `set-style-explainer-${value.toLowerCase().replace(/[^a-z0-9]/g, '-')}`

  return (
    <div className="space-y-3">
      <select
        value={value}
        onChange={(e) => !disabled && onChange(e.target.value as SetStyleKey)}
        disabled={disabled}
        className={`
          w-full px-3 py-2 rounded-lg border 
          border-surface-tertiary bg-surface-primary
          text-sm text-text-primary
          min-h-[52px]
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
        aria-label="Set style selection"
        aria-describedby={explainerId}
      >
        {Object.entries(SET_STYLE_DESCRIPTIONS).map(([key, style]) => (
          <option key={key} value={key}>
            {style.name} - {style.shortDescription}
          </option>
        ))}
      </select>

      {selectedStyle && (
        <div
          id={explainerId}
          className="bg-brand-primary/5 rounded-theme p-4 border border-brand-primary/10"
        >
          <p className="text-sm text-text-primary mb-3">
            {selectedStyle.fullDescription}
          </p>

          {selectedStyle.benefits && selectedStyle.benefits.length > 0 && (
            <div>
              <p className="text-xs font-medium text-text-secondary mb-2">
                Benefits:
              </p>
              <ul className="text-xs text-text-secondary space-y-1">
                {selectedStyle.benefits.map((benefit) => (
                  <li key={benefit} className="flex items-center">
                    <span className="w-1 h-1 bg-brand-primary rounded-full mr-2 flex-shrink-0" />
                    {benefit}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default memo(SetStyleDropdown)
