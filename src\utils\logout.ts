import { queryClient } from '@/utils/queryClient'
import { clearAllCacheData } from '@/cache'

/**
 * Perform a complete logout clearing all user data
 * This should be called from the authStore logout method
 * NOTE: userInfoCache is cleared by the caller to avoid circular imports
 */
export async function performCompleteLogout() {
  // eslint-disable-next-line no-console
  console.log('[Logout] Starting complete logout process...')

  // 1. Cancel all ongoing React Query requests
  await queryClient.cancelQueries()

  // 2. Clear React Query cache completely
  queryClient.clear()

  // 3. Remove any queries to ensure no stale data persists
  queryClient.removeQueries()

  // 4. Clear the unified CacheManager to prevent cross-user data leakage
  // This must happen before clearing localStorage to ensure proper cleanup order
  try {
    await clearAllCacheData()
  } catch (error) {
    console.error('Failed to clear unified cache:', error)
  }

  // 5. Clear all localStorage keys
  const storesToClear = [
    'drmuscle-auth',
    'drmuscle-program',
    'drmuscle-cache',
    'drmuscle-failed-requests',
    'drmuscle-offline-queue',
    'drmuscle-workout',
    'drmuscle-workout-cache', // Clear workout cache
    'user-stats-storage',
    'restDuration', // Also clear rest duration preference
    // User preferences that must be cleared
    'DailyReset',
    'lastWorkoutDate',
    'quickMode',
    'SetStyle',
    'IsPyramid',
    // Additional caches
    'dr-muscle-recommendation-cache',
    'temp_swap_exercise_contexts',
  ]

  storesToClear.forEach((key) => {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.error(`Failed to clear ${key}:`, error)
    }
  })

  // 6. Clear service worker caches
  if ('caches' in window) {
    try {
      const cacheNames = await caches.keys()
      await Promise.all(cacheNames.map((cacheName) => caches.delete(cacheName)))
    } catch (error) {
      console.error('Failed to clear service worker caches:', error)
    }
  }

  // 7. Clear IndexedDB
  if ('indexedDB' in window) {
    try {
      const databases = (await indexedDB.databases?.()) || []
      await Promise.all(
        databases
          .filter((db) => db.name)
          .map((db) => indexedDB.deleteDatabase(db.name!))
      )
    } catch (error) {
      console.warn('Failed to clear IndexedDB:', error)
    }
  }

  // 8. Clear sessionStorage
  try {
    sessionStorage.clear()
  } catch (error) {
    console.error('Failed to clear sessionStorage:', error)
  }

  // 9. Clear Zustand store states by clearing their persisted data
  // The stores will be cleared via localStorage removal above
  // This avoids circular dependencies

  // eslint-disable-next-line no-console
  console.log('[Logout] Complete logout process finished')

  // 10. Force a hard reload to completely clear all in-memory state
  // This is the most bulletproof way to prevent any cross-user data leakage
  // as it resets the entire JavaScript context and all Zustand stores
  if (typeof window !== 'undefined') {
    // Use replace to prevent back button from returning to authenticated state
    window.location.replace('/login')
  }
}
