/* eslint-disable max-classes-per-file */
/**
 * Core types and interfaces for the unified cache management system
 *
 * This module defines the foundational types that enable a single, unified
 * cache management system across the Dr. Muscle X PWA application.
 */

/**
 * Options for cache operations
 */
export interface CacheOptions {
  /** Time to live in milliseconds. If not specified, uses default TTL */
  ttl?: number
  /** Cache namespace for isolation */
  namespace?: string
  /** Priority for eviction (higher = keep longer) */
  priority?: number
  /** Whether to compress large values */
  compress?: boolean
  /** Custom serialization function */
  serialize?: (value: unknown) => string
  /** Custom deserialization function */
  deserialize?: (value: string) => unknown
}

/**
 * Metadata associated with cached entries
 */
export interface CacheMetadata {
  /** Size of the cached value in bytes (approximate) */
  size: number
  /** Timestamp when the entry was created */
  created: number
  /** Timestamp when the entry was last accessed */
  accessed: number
  /** Timestamp when the entry expires (0 = never expires) */
  expires: number
  /** Cache namespace */
  namespace: string
  /** Entry priority for eviction */
  priority: number
  /** Whether the value is compressed */
  compressed: boolean
  /** Version of the cache entry format */
  version: number
}

/**
 * Cache entry combining value and metadata
 */
export interface CacheEntry<T = unknown> {
  /** The cached value */
  value: T
  /** Entry metadata */
  metadata: CacheMetadata
}

/**
 * Statistics about cache usage
 */
export interface CacheStats {
  /** Total number of entries */
  entryCount: number
  /** Total size in bytes */
  totalSize: number
  /** Number of cache hits */
  hits: number
  /** Number of cache misses */
  misses: number
  /** Hit ratio (0-1) */
  hitRatio: number
  /** Number of evicted entries */
  evictions: number
  /** Number of expired entries cleaned up */
  expirations: number
  /** Statistics per namespace */
  namespaces: Record<string, NamespaceStats>
}

/**
 * Statistics for a specific namespace
 */
export interface NamespaceStats {
  /** Number of entries in this namespace */
  entryCount: number
  /** Total size of entries in this namespace */
  totalSize: number
  /** Number of hits for this namespace */
  hits: number
  /** Number of misses for this namespace */
  misses: number
  /** Last access time for this namespace */
  lastAccess: number
}

/**
 * Configuration for the cache manager
 */
export interface CacheManagerConfig {
  /** Default TTL for entries (in milliseconds) */
  defaultTTL: number
  /** Maximum total cache size (in bytes) */
  maxSize: number
  /** Maximum entries per namespace */
  maxEntriesPerNamespace: number
  /** Cleanup interval (in milliseconds) */
  cleanupInterval: number
  /** Default namespace for operations */
  defaultNamespace: string
  /** Whether to enable compression for large values */
  enableCompression: boolean
  /** Compression threshold in bytes */
  compressionThreshold: number
  /** Whether to enable statistics tracking */
  enableStats: boolean
}

/**
 * Core cache adapter interface that storage backends must implement
 *
 * This is the low-level interface that different storage mechanisms
 * (memory, localStorage, IndexedDB, etc.) implement.
 */
export interface CacheAdapter {
  /**
   * Get a value from the cache
   * @param key The cache key
   * @returns The cached entry or null if not found
   */
  get<T>(key: string): Promise<CacheEntry<T> | null>

  /**
   * Set a value in the cache
   * @param key The cache key
   * @param value The value to cache
   * @param metadata Entry metadata
   */
  set<T>(key: string, value: T, metadata: CacheMetadata): Promise<void>

  /**
   * Delete a value from the cache
   * @param key The cache key
   */
  delete(key: string): Promise<void>

  /**
   * Clear all entries from the cache
   */
  clear(): Promise<void>

  /**
   * Get all keys in the cache
   * @returns Array of all cache keys
   */
  getAllKeys(): Promise<string[]>

  /**
   * Get the total size of the cache in bytes
   * @returns Total cache size
   */
  getSize(): Promise<number>

  /**
   * Check if the adapter supports batch operations
   * @returns True if batch operations are supported
   */
  supportsBatch(): boolean

  /**
   * Batch get operation (optional, falls back to individual gets)
   * @param keys Array of keys to retrieve
   * @returns Map of key to cache entry
   */
  getMany?<T>(keys: string[]): Promise<Map<string, CacheEntry<T>>>

  /**
   * Batch set operation (optional, falls back to individual sets)
   * @param entries Map of key to value and metadata
   */
  setMany?<T>(
    entries: Map<string, { value: T; metadata: CacheMetadata }>
  ): Promise<void>

  /**
   * Batch delete operation (optional, falls back to individual deletes)
   * @param keys Array of keys to delete
   */
  deleteMany?(keys: string[]): Promise<void>
}

/**
 * High-level cache manager interface
 *
 * This is the main interface that application code uses for all cache operations.
 * It provides additional features like namespacing, TTL management, and statistics.
 */
export interface CacheManager {
  /**
   * Get a value from the cache
   * @param key The cache key
   * @param namespace Optional namespace (uses default if not specified)
   * @returns The cached value or null if not found/expired
   */
  get<T>(key: string, namespace?: string): Promise<T | null>

  /**
   * Set a value in the cache
   * @param key The cache key
   * @param value The value to cache
   * @param options Cache options (TTL, namespace, etc.)
   */
  set<T>(key: string, value: T, options?: CacheOptions): Promise<void>

  /**
   * Delete a value from the cache
   * @param key The cache key
   * @param namespace Optional namespace (uses default if not specified)
   */
  delete(key: string, namespace?: string): Promise<void>

  /**
   * Clear cache entries
   * @param namespace Optional namespace to clear (clears all if not specified)
   */
  clear(namespace?: string): Promise<void>

  /**
   * Get multiple values from the cache
   * @param keys Array of cache keys
   * @param namespace Optional namespace (uses default if not specified)
   * @returns Map of key to value (only includes found entries)
   */
  getMany<T>(keys: string[], namespace?: string): Promise<Map<string, T>>

  /**
   * Set multiple values in the cache
   * @param entries Map of key to value
   * @param options Cache options applied to all entries
   */
  setMany<T>(entries: Map<string, T>, options?: CacheOptions): Promise<void>

  /**
   * Delete multiple values from the cache
   * @param keys Array of cache keys
   * @param namespace Optional namespace (uses default if not specified)
   */
  deleteMany(keys: string[], namespace?: string): Promise<void>

  /**
   * Get metadata for a cache entry
   * @param key The cache key
   * @param namespace Optional namespace (uses default if not specified)
   * @returns Entry metadata or null if not found
   */
  getMetadata(key: string, namespace?: string): Promise<CacheMetadata | null>

  /**
   * Get all keys in the cache
   * @param namespace Optional namespace (gets all if not specified)
   * @returns Array of cache keys
   */
  getAllKeys(namespace?: string): Promise<string[]>

  /**
   * Get the total size of the cache
   * @param namespace Optional namespace (gets total if not specified)
   * @returns Cache size in bytes
   */
  getSize(namespace?: string): Promise<number>

  /**
   * Invalidate cache entries matching a pattern
   * @param pattern String pattern or RegExp to match keys
   * @param namespace Optional namespace (searches all if not specified)
   */
  invalidate(pattern: string | RegExp, namespace?: string): Promise<void>

  /**
   * Manually trigger cache cleanup (remove expired entries)
   */
  cleanup(): Promise<void>

  /**
   * Get cache statistics
   * @returns Current cache statistics
   */
  getStats(): Promise<CacheStats>

  /**
   * Start background cleanup process
   */
  startCleanup(): void

  /**
   * Stop background cleanup process
   */
  stopCleanup(): void
}

/**
 * Custom error types for cache operations
 */
export class CacheError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public override readonly cause?: Error
  ) {
    super(message)
    this.name = 'CacheError'
  }
}

export class CacheQuotaExceededError extends CacheError {
  constructor(message: string, cause?: Error) {
    super(message, 'QUOTA_EXCEEDED', cause)
    this.name = 'CacheQuotaExceededError'
  }
}

export class CacheSerializationError extends CacheError {
  constructor(message: string, cause?: Error) {
    super(message, 'SERIALIZATION_ERROR', cause)
    this.name = 'CacheSerializationError'
  }
}

export class CacheAdapterError extends CacheError {
  constructor(message: string, cause?: Error) {
    super(message, 'ADAPTER_ERROR', cause)
    this.name = 'CacheAdapterError'
  }
}

export class CacheKeyError extends CacheError {
  constructor(message: string, cause?: Error) {
    super(message, 'INVALID_KEY', cause)
    this.name = 'CacheKeyError'
  }
}

/**
 * Default configuration values
 */
export const DEFAULT_CACHE_CONFIG: CacheManagerConfig = {
  defaultTTL: 24 * 60 * 60 * 1000, // 24 hours
  maxSize: 50 * 1024 * 1024, // 50MB
  maxEntriesPerNamespace: 1000,
  cleanupInterval: 5 * 60 * 1000, // 5 minutes
  defaultNamespace: 'default',
  enableCompression: true,
  compressionThreshold: 1024, // 1KB
  enableStats: true,
}

/**
 * Cache entry version for compatibility
 */
export const CACHE_ENTRY_VERSION = 1
