import { useCallback } from 'react'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useExerciseSetsPrefetch } from './useExerciseSetsPrefetch'
import { logger } from '@/utils/logger'

export interface UseWorkoutStartPrefetchReturn {
  startWorkoutWithPrefetch: () => Promise<void>
  prefetchStatus: Record<number, 'loading' | 'success' | 'error'>
  isPrefetching: boolean
  prefetchedExerciseIds: number[]
}

/**
 * Hook that integrates exercise set prefetching with workout start flow
 * Automatically prefetches the first few exercises when a workout begins
 */
export function useWorkoutStartPrefetch(): UseWorkoutStartPrefetchReturn {
  const { startWorkout, exercises, workoutSession, previewExerciseSkips } =
    useWorkoutStore()
  const {
    prefetchExerciseSets,
    prefetchStatus,
    isPrefetching,
    prefetchedExerciseIds,
  } = useExerciseSetsPrefetch()

  const startWorkoutWithPrefetch = useCallback(async () => {
    // Don't start if workout already in progress
    if (workoutSession) {
      logger.warn(
        '[useWorkoutStartPrefetch] Workout already in progress, ignoring start request'
      )
      return
    }

    // Start the workout first
    startWorkout()

    // Filter out preview-skipped exercises
    const exercisesToPrefetch = exercises
      .filter((exercise) => !previewExerciseSkips?.has(exercise.Id))
      .slice(0, 3) // Take first 3 non-skipped exercises
      .map((exercise) => exercise.Id)

    // If we have exercises to prefetch, do it in the background
    if (exercisesToPrefetch.length > 0) {
      logger.log(
        `[useWorkoutStartPrefetch] Starting prefetch for exercises: ${exercisesToPrefetch.join(', ')}`
      )

      try {
        // Don't await - let it run in the background
        prefetchExerciseSets(exercisesToPrefetch).catch((error) => {
          logger.error('[useWorkoutStartPrefetch] Prefetch failed:', error)
        })
      } catch (error) {
        // Log but don't throw - prefetch failure shouldn't block workout
        logger.error(
          '[useWorkoutStartPrefetch] Failed to start prefetch:',
          error
        )
      }
    }
  }, [
    startWorkout,
    exercises,
    workoutSession,
    previewExerciseSkips,
    prefetchExerciseSets,
  ])

  return {
    startWorkoutWithPrefetch,
    prefetchStatus,
    isPrefetching,
    prefetchedExerciseIds,
  }
}
