'use client'

import { useState, useEffect, useCallback } from 'react'
import { useNetworkStatus } from '@/hooks/useNetworkStatus'
import { useOfflineStore } from '@/stores/offlineStore'
import { useOfflineWorkout } from '@/hooks/useOfflineWorkout'
import { LoadOfflineWorkout } from '@/components/LoadOfflineWorkout'
import {
  OfflineIndicator,
  OfflineIndicatorCompact,
} from '@/components/OfflineIndicator'
import {
  OfflineGuard,
  OfflineLimitationMessage,
} from '@/components/OfflineGuard'
import { Button } from '@/components/ui/Button'

/**
 * Test page for offline mode functionality
 * This page allows testing all offline features in one place
 */
export default function TestOfflinePage() {
  const { isOnline } = useNetworkStatus()
  const {
    isOfflineWorkoutLoaded,
    loadedWorkoutId,
    syncStatus,
    queuedRequestsCount,
    clearOfflineWorkout,
  } = useOfflineStore()

  const {
    isOfflineWorkoutAvailable,
    cachedWorkout,
    workoutProgress,
    isLoadingCachedData,
    saveSet,
    completeWorkout,
    startWorkoutSession,
    getCachedRecommendation,
    checkOfflineCapability,
  } = useOfflineWorkout()

  const [testResults, setTestResults] = useState<string[]>([])
  const [offlineCapability, setOfflineCapability] = useState<{
    canExecute: boolean
    missingData: string[]
  } | null>(null)

  // Test functions
  const addTestResult = useCallback((message: string) => {
    setTestResults((prev) => [
      ...prev,
      `${new Date().toLocaleTimeString()}: ${message}`,
    ])
  }, [])

  const testCachedRecommendation = async () => {
    if (!cachedWorkout?.Exercises?.[0]?.Id) {
      addTestResult('❌ No cached workout or exercises available')
      return
    }

    const exerciseId = cachedWorkout.Exercises[0].Id
    const recommendation = await getCachedRecommendation(exerciseId)

    if (recommendation) {
      addTestResult(
        `✅ Got cached recommendation for exercise ${exerciseId}: ${recommendation.Reps} reps @ ${recommendation.Weight?.Lb}lbs`
      )
    } else {
      addTestResult(
        `❌ No cached recommendation found for exercise ${exerciseId}`
      )
    }
  }

  const testSaveSet = async () => {
    if (!cachedWorkout?.Exercises?.[0]?.Id) {
      addTestResult('❌ No cached workout available for set saving')
      return
    }

    const setData = {
      ExerciseId: cachedWorkout.Exercises[0].Id,
      UserId: '<EMAIL>',
      LogDate: new Date(),
      Reps: 10,
      Weight: { Lb: 135, Kg: 61.2 },
      OneRM: { Lb: 180, Kg: 81.6 },
      IsWarmups: false,
      Isbodyweight: false,
      RIR: 2,
      RestTime: 90,
      Notes: 'Test set from offline mode',
    }

    const success = await saveSet(setData)
    if (success) {
      addTestResult(
        `✅ Set saved successfully: ${setData.Reps} reps @ ${setData.Weight.Lb}lbs`
      )
    } else {
      addTestResult('❌ Failed to save set')
    }
  }

  const testStartWorkout = () => {
    startWorkoutSession()
    addTestResult('✅ Workout session started')
  }

  const testCompleteWorkout = async () => {
    const success = await completeWorkout()
    if (success) {
      addTestResult('✅ Workout completed successfully')
    } else {
      addTestResult('❌ Failed to complete workout')
    }
  }

  const checkCapability = useCallback(async () => {
    const capability = await checkOfflineCapability()
    setOfflineCapability(capability)

    if (capability.canExecute) {
      addTestResult('✅ Workout can be executed offline')
    } else {
      addTestResult(
        `❌ Cannot execute offline. Missing: ${capability.missingData.join(', ')}`
      )
    }
  }, [checkOfflineCapability, addTestResult])

  const clearTestResults = () => {
    setTestResults([])
  }

  // Check offline capability when workout is loaded
  useEffect(() => {
    if (isOfflineWorkoutAvailable) {
      checkCapability()
    }
  }, [isOfflineWorkoutAvailable, checkCapability])

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">Offline Mode Test Page</h1>
        <p className="text-gray-600">
          Test all offline functionality including loading, caching, execution,
          and sync.
        </p>
      </div>

      {/* Network Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <div className="bg-white p-4 rounded-lg border">
          <h3 className="font-semibold mb-2">Network Status</h3>
          <div className="space-y-2">
            <div
              className={`px-3 py-1 rounded text-sm ${isOnline ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}
            >
              {isOnline ? '🟢 Online' : '🔴 Offline'}
            </div>
            <div className="flex gap-2">
              <OfflineIndicator />
              <OfflineIndicatorCompact />
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border">
          <h3 className="font-semibold mb-2">Offline Store Status</h3>
          <div className="space-y-1 text-sm">
            <div>Workout Loaded: {isOfflineWorkoutLoaded ? '✅' : '❌'}</div>
            <div>Workout ID: {loadedWorkoutId || 'None'}</div>
            <div>Sync Status: {syncStatus}</div>
            <div>Queued Requests: {queuedRequestsCount}</div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border">
          <h3 className="font-semibold mb-2">Cached Workout</h3>
          <div className="space-y-1 text-sm">
            <div>Available: {isOfflineWorkoutAvailable ? '✅' : '❌'}</div>
            <div>Loading: {isLoadingCachedData ? '⏳' : '✅'}</div>
            <div>Name: {cachedWorkout?.Label || 'None'}</div>
            <div>Exercises: {cachedWorkout?.Exercises?.length || 0}</div>
          </div>
        </div>
      </div>

      {/* Offline Limitation Message */}
      <OfflineLimitationMessage className="mb-6" />

      {/* Load Offline Workout */}
      <div className="bg-white p-6 rounded-lg border mb-6">
        <h3 className="font-semibold mb-4">Load Offline Workout</h3>
        <div className="max-w-sm">
          <LoadOfflineWorkout />
        </div>
      </div>

      {/* Offline Capability Check */}
      {offlineCapability && (
        <div className="bg-white p-6 rounded-lg border mb-6">
          <h3 className="font-semibold mb-4">Offline Capability</h3>
          <div
            className={`p-3 rounded ${offlineCapability.canExecute ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`}
          >
            {offlineCapability.canExecute ? (
              '✅ Workout can be executed offline'
            ) : (
              <>
                ❌ Cannot execute offline
                <div className="mt-2 text-sm">
                  Missing: {offlineCapability.missingData.join(', ')}
                </div>
              </>
            )}
          </div>
        </div>
      )}

      {/* Test Actions */}
      <div className="bg-white p-6 rounded-lg border mb-6">
        <h3 className="font-semibold mb-4">Test Actions</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
          <Button
            onClick={testCachedRecommendation}
            disabled={!isOfflineWorkoutAvailable}
            size="sm"
          >
            Test Recommendation
          </Button>

          <Button
            onClick={testStartWorkout}
            disabled={!isOfflineWorkoutAvailable}
            size="sm"
          >
            Start Workout
          </Button>

          <Button
            onClick={testSaveSet}
            disabled={!isOfflineWorkoutAvailable}
            size="sm"
          >
            Save Test Set
          </Button>

          <Button
            onClick={testCompleteWorkout}
            disabled={!isOfflineWorkoutAvailable}
            size="sm"
          >
            Complete Workout
          </Button>

          <Button onClick={checkCapability} size="sm" variant="secondary">
            Check Capability
          </Button>

          <Button onClick={clearOfflineWorkout} size="sm" variant="danger">
            Clear Workout
          </Button>

          <Button onClick={clearTestResults} size="sm" variant="ghost">
            Clear Results
          </Button>
        </div>
      </div>

      {/* Feature Guards Demo */}
      <div className="bg-white p-6 rounded-lg border mb-6">
        <h3 className="font-semibold mb-4">Feature Guards Demo</h3>
        <div className="space-y-4">
          <OfflineGuard requiresOnline>
            <Button>Online Only Feature</Button>
          </OfflineGuard>

          <OfflineGuard requiresCachedWorkout>
            <Button>Requires Cached Workout</Button>
          </OfflineGuard>

          <OfflineGuard requiresOnline hideWhenBlocked>
            <Button>Hidden When Offline</Button>
          </OfflineGuard>
        </div>
      </div>

      {/* Test Results */}
      <div className="bg-white p-6 rounded-lg border">
        <h3 className="font-semibold mb-4">Test Results</h3>
        <div className="bg-gray-50 p-4 rounded max-h-64 overflow-y-auto">
          {testResults.length === 0 ? (
            <p className="text-gray-500 italic">
              No test results yet. Try the test actions above.
            </p>
          ) : (
            <div className="space-y-1">
              {testResults.map((result) => (
                <div
                  key={`result-${result.slice(0, 50).replace(/[^a-zA-Z0-9]/g, '')}`}
                  className="text-sm font-mono"
                >
                  {result}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Workout Progress */}
      {workoutProgress.completedSets.length > 0 && (
        <div className="bg-white p-6 rounded-lg border mt-6">
          <h3 className="font-semibold mb-4">Workout Progress</h3>
          <div className="space-y-2">
            <div>
              Start Time: {workoutProgress.startTime?.toLocaleTimeString()}
            </div>
            <div>Current Exercise: {workoutProgress.currentExerciseId}</div>
            <div>Completed Sets: {workoutProgress.completedSets.length}</div>
            <div className="text-sm text-gray-600">
              {workoutProgress.completedSets.map((set, index) => (
                <div
                  key={`set-${set.ExerciseId}-${set.Reps}-${set.Weight?.Lb || 0}-${Date.now()}-${Math.random()}`}
                >
                  Set {index + 1}: {set.Reps} reps @ {set.Weight?.Lb}lbs
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
