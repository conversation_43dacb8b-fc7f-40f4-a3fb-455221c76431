import { test, expect } from '@playwright/test'

test.describe('Firebase OAuth Popup Fallback', () => {
  test('should handle popup blocked scenario gracefully', async ({ page }) => {
    // Navigate to login page
    await page.goto('/login')

    // Wait for the page to load
    await page.waitForLoadState('networkidle')

    // Check that the Google sign-in button exists
    const googleButton = page.locator('button:has-text("Sign in with Google")')
    await expect(googleButton).toBeVisible()

    // Note: We can't actually trigger a popup block in Playwright,
    // but we can verify the button exists and is clickable
    await expect(googleButton).toBeEnabled()

    // Verify the login page loaded correctly
    await expect(page).toHaveTitle(/Dr\. Muscle/)
  })

  test('should display login page with OAuth options', async ({ page }) => {
    await page.goto('/login')

    // Check for Google sign-in button
    const googleButton = page.locator('button:has-text("Sign in with Google")')
    await expect(googleButton).toBeVisible()

    // Check for Apple sign-in button if it exists
    const appleButton = page.locator('button:has-text("Sign in with Apple")')
    const appleButtonCount = await appleButton.count()
    if (appleButtonCount > 0) {
      await expect(appleButton).toBeVisible()
    }
  })
})
