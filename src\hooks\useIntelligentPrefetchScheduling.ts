import { useState, useEffect, useCallback, useRef } from 'react'
import { logger } from '@/utils/logger'

interface Exercise {
  Id: number
  Label: string
}

interface WorkoutSession {
  startTime: number | Date
}

interface UseIntelligentPrefetchSchedulingProps {
  exercises: Exercise[]
  currentExerciseIndex: number
  workoutSession: WorkoutSession | null
  prefetchedExerciseIds: number[]
  previewExerciseSkips?: Set<number>
  prefetchExerciseSets: (exerciseIds: number[]) => Promise<void>
  autoSchedule?: boolean
}

interface UseIntelligentPrefetchSchedulingReturn {
  userPace: number // exercises per minute
  schedulingActive: boolean
  nextPrefetchTiming?: number // ms until next prefetch
  maxPrefetchCount: number
  schedulePrefetch: () => Promise<void>
}

/**
 * Hook that implements intelligent prefetch scheduling based on user pace
 * Analyzes user progression speed and adjusts prefetch timing and quantity accordingly
 */
export function useIntelligentPrefetchScheduling({
  exercises,
  currentExerciseIndex,
  workoutSession,
  prefetchedExerciseIds,
  previewExerciseSkips = new Set(),
  prefetchExerciseSets,
  autoSchedule = false,
}: UseIntelligentPrefetchSchedulingProps): UseIntelligentPrefetchSchedulingReturn {
  const [userPace, setUserPace] = useState(0)
  const [schedulingActive, setSchedulingActive] = useState(false)
  const [nextPrefetchTiming, setNextPrefetchTiming] = useState<number>()

  const autoScheduleIntervalRef = useRef<NodeJS.Timeout | undefined>(undefined)
  const lastScheduleTimeRef = useRef(0)

  /**
   * Calculate device performance to determine optimal prefetch count
   */
  const maxPrefetchCount = (() => {
    // Base prefetch count
    let count = 3

    // Adjust based on hardware capabilities
    if (typeof navigator !== 'undefined') {
      const cores = navigator.hardwareConcurrency || 4
      const memory = (navigator as { deviceMemory?: number }).deviceMemory || 4

      // More cores and memory = more prefetch capacity
      if (cores >= 8 && memory >= 8) {
        count = 5
      } else if (cores >= 4 && memory >= 4) {
        count = 4
      } else if (cores <= 2 || memory <= 2) {
        count = 2
      }
    }

    return Math.min(count, exercises.length - currentExerciseIndex - 1)
  })()

  /**
   * Calculate user pace based on current progress and time elapsed
   */
  const calculateUserPace = useCallback((): number => {
    if (!workoutSession || currentExerciseIndex <= 0) {
      return 0
    }

    const startTime =
      workoutSession.startTime instanceof Date
        ? workoutSession.startTime.getTime()
        : workoutSession.startTime
    const elapsedTime = Date.now() - startTime
    const elapsedMinutes = elapsedTime / (1000 * 60)

    if (elapsedMinutes <= 0) {
      return 0
    }

    // Calculate exercises per minute
    const pace = currentExerciseIndex / elapsedMinutes
    return Math.max(0, pace)
  }, [workoutSession, currentExerciseIndex])

  /**
   * Determine how many exercises to prefetch based on user pace
   */
  const getPrefetchCount = useCallback(
    (pace: number): number => {
      if (pace === 0) {
        return 1 // Default for new users
      }

      // Fast users (>1 exercise per minute) get more prefetch
      if (pace > 1) {
        return Math.min(maxPrefetchCount, 4)
      }

      // Medium pace users (0.5-1 exercise per minute)
      if (pace > 0.5) {
        return Math.min(maxPrefetchCount, 3)
      }

      // Slow users (<0.5 exercise per minute) get minimal prefetch
      return Math.min(maxPrefetchCount, 2)
    },
    [maxPrefetchCount]
  )

  /**
   * Calculate optimal timing for next prefetch based on user pace
   */
  const calculateNextPrefetchTiming = useCallback(
    (pace: number): number | undefined => {
      if (pace === 0 || currentExerciseIndex >= exercises.length - 1) {
        return undefined
      }

      // Estimate time per exercise based on pace
      const minutesPerExercise = pace > 0 ? 1 / pace : 5 // Default 5 minutes if pace unknown
      const msPerExercise = minutesPerExercise * 60 * 1000

      // Prefetch 1 exercise ahead of time
      return Math.max(5000, msPerExercise * 0.7) // At least 5 seconds, but prefer 70% through current exercise
    },
    [currentExerciseIndex, exercises.length]
  )

  /**
   * Get exercises that need to be prefetched
   */
  const getExercisesToPrefetch = useCallback(
    (count: number): number[] => {
      const exercisesToPrefetch: number[] = []

      // Start from next exercise
      let searchIndex = currentExerciseIndex + 1

      while (
        exercisesToPrefetch.length < count &&
        searchIndex < exercises.length
      ) {
        const exercise = exercises[searchIndex]

        if (
          exercise &&
          !prefetchedExerciseIds.includes(exercise.Id) &&
          !previewExerciseSkips.has(exercise.Id)
        ) {
          exercisesToPrefetch.push(exercise.Id)
        }

        searchIndex++
      }

      return exercisesToPrefetch
    },
    [
      exercises,
      currentExerciseIndex,
      prefetchedExerciseIds,
      previewExerciseSkips,
    ]
  )

  /**
   * Schedule prefetch based on current user pace
   */
  const schedulePrefetch = useCallback(async (): Promise<void> => {
    if (!workoutSession) {
      return
    }

    const currentPace = calculateUserPace()
    const prefetchCount = getPrefetchCount(currentPace)
    const exercisesToPrefetch = getExercisesToPrefetch(prefetchCount)

    if (exercisesToPrefetch.length === 0) {
      logger.log('[useIntelligentPrefetchScheduling] No exercises to prefetch')
      return
    }

    logger.log(
      `[useIntelligentPrefetchScheduling] Scheduling prefetch for ${exercisesToPrefetch.length} exercises ` +
        `(pace: ${currentPace.toFixed(2)} ex/min, exercises: ${exercisesToPrefetch.join(', ')})`
    )

    try {
      await prefetchExerciseSets(exercisesToPrefetch)
      lastScheduleTimeRef.current = Date.now()
    } catch (error) {
      logger.error(
        '[useIntelligentPrefetchScheduling] Failed to schedule prefetch:',
        error
      )
    }
  }, [
    workoutSession,
    calculateUserPace,
    getPrefetchCount,
    getExercisesToPrefetch,
    prefetchExerciseSets,
  ])

  /**
   * Update user pace and timing calculations
   */
  useEffect(() => {
    if (!workoutSession) {
      setUserPace(0)
      setSchedulingActive(false)
      setNextPrefetchTiming(undefined)
      return
    }

    const currentPace = calculateUserPace()
    setUserPace(currentPace)
    setSchedulingActive(true)

    const timing = calculateNextPrefetchTiming(currentPace)
    setNextPrefetchTiming(timing)

    logger.log(
      `[useIntelligentPrefetchScheduling] Updated pace: ${currentPace.toFixed(2)} ex/min, ` +
        `next prefetch in: ${timing ? Math.round(timing / 1000) : 'N/A'}s`
    )
  }, [
    workoutSession,
    currentExerciseIndex,
    calculateUserPace,
    calculateNextPrefetchTiming,
  ])

  /**
   * Automatic scheduling based on calculated timing
   */
  useEffect(() => {
    if (!autoSchedule || !schedulingActive || !nextPrefetchTiming) {
      return
    }

    // Clear existing interval
    if (autoScheduleIntervalRef.current) {
      clearTimeout(autoScheduleIntervalRef.current)
    }

    // Schedule next prefetch
    autoScheduleIntervalRef.current = setTimeout(() => {
      schedulePrefetch().catch((error) => {
        logger.error(
          '[useIntelligentPrefetchScheduling] Auto-schedule failed:',
          error
        )
      })
    }, nextPrefetchTiming)

    return () => {
      if (autoScheduleIntervalRef.current) {
        clearTimeout(autoScheduleIntervalRef.current)
      }
    }
  }, [autoSchedule, schedulingActive, nextPrefetchTiming, schedulePrefetch])

  /**
   * Cleanup on unmount
   */
  useEffect(() => {
    return () => {
      if (autoScheduleIntervalRef.current) {
        clearTimeout(autoScheduleIntervalRef.current)
      }
    }
  }, [])

  return {
    userPace,
    schedulingActive,
    nextPrefetchTiming,
    maxPrefetchCount,
    schedulePrefetch,
  }
}
