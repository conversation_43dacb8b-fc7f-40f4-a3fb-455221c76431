import { renderHook, act } from '@testing-library/react'
import { vi } from 'vitest'
import { useRobustPrefetchErrorHandling } from '../useRobustPrefetchErrorHandling'

describe('useRobustPrefetchErrorHandling - Core Functionality', () => {
  const mockPrefetchExerciseSets = vi.fn()
  const defaultProps = {
    prefetchExerciseSets: mockPrefetchExerciseSets,
    maxRetries: 2, // Reduced for faster tests
    retryDelayMs: 10, // Very short delay for tests
    exponentialBackoff: false, // Disable for predictable timing
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should initialize with correct default values', () => {
    const { result } = renderHook(() =>
      useRobustPrefetchErrorHandling(defaultProps)
    )

    expect(result.current.retryCount).toBe(0)
    expect(result.current.errorHistory).toHaveLength(0)
    expect(result.current.hasExceededMaxRetries).toBe(false)
    expect(result.current.isCircuitBreakerOpen).toBe(false)
    expect(result.current.isInDegradedMode).toBe(false)
  })

  it('should successfully prefetch when no errors occur', async () => {
    mockPrefetchExerciseSets.mockResolvedValueOnce(undefined)

    const { result } = renderHook(() =>
      useRobustPrefetchErrorHandling(defaultProps)
    )

    await act(async () => {
      await result.current.prefetchWithRetry([1, 2])
    })

    expect(mockPrefetchExerciseSets).toHaveBeenCalledTimes(1)
    expect(mockPrefetchExerciseSets).toHaveBeenCalledWith([1, 2])
    expect(result.current.retryCount).toBe(0)
    expect(result.current.errorHistory).toHaveLength(0)
  })

  it('should retry on failure and eventually succeed', async () => {
    mockPrefetchExerciseSets
      .mockRejectedValueOnce(new Error('Network error'))
      .mockResolvedValueOnce(undefined)

    const { result } = renderHook(() =>
      useRobustPrefetchErrorHandling(defaultProps)
    )

    await act(async () => {
      await result.current.prefetchWithRetry([1, 2])
    })

    expect(mockPrefetchExerciseSets).toHaveBeenCalledTimes(2)
    expect(result.current.retryCount).toBe(1)
    expect(result.current.errorHistory).toHaveLength(1)
    expect(result.current.hasExceededMaxRetries).toBe(false)
  })

  it('should fail after max retries are exceeded', async () => {
    mockPrefetchExerciseSets.mockRejectedValue(new Error('Persistent error'))

    const { result } = renderHook(() =>
      useRobustPrefetchErrorHandling(defaultProps)
    )

    await act(async () => {
      try {
        await result.current.prefetchWithRetry([1, 2])
        // Should not reach here
        expect(true).toBe(false)
      } catch (error) {
        expect(error).toBeInstanceOf(Error)
      }
    })

    expect(mockPrefetchExerciseSets).toHaveBeenCalledTimes(3) // Initial + 2 retries
    expect(result.current.retryCount).toBe(2)
    expect(result.current.hasExceededMaxRetries).toBe(true)
    expect(result.current.errorHistory).toHaveLength(3)
  })

  it('should categorize errors correctly', async () => {
    const networkError = new TypeError('Network request failed')
    mockPrefetchExerciseSets
      .mockRejectedValueOnce(networkError)
      .mockResolvedValueOnce(undefined)

    const { result } = renderHook(() =>
      useRobustPrefetchErrorHandling(defaultProps)
    )

    await act(async () => {
      await result.current.prefetchWithRetry([1])
    })

    expect(result.current.errorsByType.network).toBe(1)
    expect(result.current.errorHistory[0].type).toBe('network')
  })

  it('should clear error history when requested', () => {
    const { result } = renderHook(() =>
      useRobustPrefetchErrorHandling(defaultProps)
    )

    act(() => {
      result.current.clearErrorHistory()
    })

    expect(result.current.errorHistory).toHaveLength(0)
    expect(result.current.retryCount).toBe(0)
    expect(result.current.errorsByType.network).toBe(0)
    expect(result.current.hasExceededMaxRetries).toBe(false)
  })

  it('should handle different error types', async () => {
    const timeoutError = new Error('Request timeout')
    const memoryError = new Error('out of memory')
    const serverError = new Error('500 Internal Server Error')

    mockPrefetchExerciseSets
      .mockRejectedValueOnce(timeoutError)
      .mockRejectedValueOnce(memoryError)
      .mockRejectedValueOnce(serverError)
      .mockResolvedValue(undefined)

    const { result } = renderHook(() =>
      useRobustPrefetchErrorHandling(defaultProps)
    )

    // Test timeout error
    await act(async () => {
      try {
        await result.current.prefetchWithRetry([1])
      } catch (error) {
        // Expected to fail
      }
    })

    // Test memory error
    await act(async () => {
      try {
        await result.current.prefetchWithRetry([2])
      } catch (error) {
        // Expected to fail
      }
    })

    // Test server error
    await act(async () => {
      try {
        await result.current.prefetchWithRetry([3])
      } catch (error) {
        // Expected to fail
      }
    })

    expect(result.current.errorsByType.timeout).toBe(1)
    expect(result.current.errorsByType.memory).toBe(1)
    expect(result.current.errorsByType.server).toBe(1)
  })
})
