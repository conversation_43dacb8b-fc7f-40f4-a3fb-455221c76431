import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { ExercisePageV2Client } from '../ExercisePageV2Client'

// Mock all the required hooks and components
vi.mock('next/navigation', () => ({
  useSearchParams: () => ({
    get: vi.fn(),
  }),
}))

vi.mock('@/contexts/NavigationContext', () => ({
  useNavigation: () => ({
    setTitle: vi.fn(),
  }),
}))

vi.mock('@/stores/authStore', () => ({
  useAuthStore: () => ({
    getCachedUserInfo: () => ({ MassUnit: 'lbs' }),
  }),
}))

vi.mock('@/hooks/useWorkout', () => ({
  useWorkout: () => ({
    isLoadingWorkout: true, // Start with loading state
    workoutError: null,
    workoutSession: null, // No session yet
  }),
}))

vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: () => ({
    loadingStates: new Map(),
    updateActivity: vi.fn(),
    restTimerState: { isActive: false },
  }),
}))

vi.mock('@/hooks/useExercisePageInitialization', () => ({
  useExercisePageInitialization: () => ({
    isInitializing: true, // Start with initializing
    loadingError: null,
    retryInitialization: vi.fn(),
  }),
}))

vi.mock('@/hooks/useSetScreenLogic', () => ({
  useSetScreenLogic: () => ({
    currentExercise: null, // No exercise yet
    exercises: [],
    recommendation: null, // No recommendation yet
    completedSets: [],
    currentSetIndex: 0,
    setData: { reps: '', weight: '' },
    setSetData: vi.fn(),
    isWarmup: false,
    isLastSet: false,
    isFirstWorkSet: false,
    showComplete: false,
    showExerciseComplete: false,
    isLoading: false,
    error: null,
    refetchRecommendation: vi.fn(),
    isLastExercise: false,
    handleSaveSet: vi.fn(),
    isSaving: false,
    setSaveError: vi.fn(),
  }),
}))

vi.mock('@/hooks/useExerciseV2Actions', () => ({
  useExerciseV2Actions: () => ({
    handleCompleteSet: vi.fn(),
    handleSkipSet: vi.fn(),
  }),
}))

vi.mock('@/hooks/usePullToRefresh', () => ({
  usePullToRefresh: () => ({
    pullDistance: 0,
    isRefreshing: false,
    isPulling: false,
  }),
}))

describe('ExercisePageV2Client - Immediate Loading State', () => {
  it('should show loading state immediately on mount when data is not ready', () => {
    render(<ExercisePageV2Client exerciseId={1} />)

    // Should show loading state, not blank page
    const loadingElements = screen.getAllByText((content, element) => {
      return (
        content.includes('Loading') ||
        element?.classList.contains('animate-spin') ||
        element?.classList.contains('animate-pulse') ||
        false
      )
    })

    expect(loadingElements.length).toBeGreaterThan(0)
  })

  it('should have theme-aware background immediately', () => {
    const { container } = render(<ExercisePageV2Client exerciseId={1} />)

    // Check for theme background (not blank/black)
    const bgElement = container.querySelector('.bg-bg-primary')
    expect(bgElement).toBeInTheDocument()
  })

  it('should not render the main exercise UI when data is loading', () => {
    render(<ExercisePageV2Client exerciseId={1} />)

    // Should not show main UI elements
    expect(
      screen.queryByTestId('exercise-page-container')
    ).not.toBeInTheDocument()
    expect(screen.queryByText('CurrentSetCard')).not.toBeInTheDocument()
  })
})
