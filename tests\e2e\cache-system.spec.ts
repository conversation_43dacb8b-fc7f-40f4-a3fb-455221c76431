/**
 * End-to-end tests for the unified cache system
 *
 * These tests verify that the cache system works correctly in a real browser environment,
 * including localStorage, sessionStorage, and memory adapters.
 */

import { test, expect, type Page } from '@playwright/test'
import path from 'path'

// Helper function to navigate to our test page
async function navigateToTestPage(page: Page) {
  const testPagePath = path.join(__dirname, 'cache-test-page.html')
  await page.goto(`file://${testPagePath}`)

  // Wait for the test utilities to be loaded
  await page.waitForFunction(() => {
    return typeof (window as any).CacheSystemTest !== 'undefined'
  })
}

test.describe('Cache System E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to our test page
    await navigateToTestPage(page)
  })

  test('should perform basic cache operations', async ({ page }) => {
    const result = await page.evaluate(() => {
      return (window as any).CacheSystemTest.testBasicOperations()
    })

    expect(result.localStorage.canStore).toBe(true)
    expect(result.localStorage.canRetrieve).toBe(true)
    expect(result.localStorage.canDelete).toBe(true)

    expect(result.sessionStorage.canStore).toBe(true)
    expect(result.sessionStorage.canRetrieve).toBe(true)
    expect(result.sessionStorage.canDelete).toBe(true)

    expect(result.memory.canStore).toBe(true)
    expect(result.memory.canRetrieve).toBe(true)
    expect(result.memory.canDelete).toBe(true)
  })

  test('should handle TTL correctly', async ({ page }) => {
    const result = await page.evaluate(() => {
      return (window as any).CacheSystemTest.testTTL()
    })

    expect(result.immediatelyAvailable).toBe(true)
    expect(result.expiredCorrectly).toBe(true)
  })

  test('should isolate namespaces', async ({ page }) => {
    const result = await page.evaluate(() => {
      return (window as any).CacheSystemTest.testNamespaces()
    })

    expect(result.ns1Isolated).toBe(true)
    expect(result.ns2Isolated).toBe(true)
    expect(result.properIsolation).toBe(true)
  })

  test('should handle storage quota gracefully', async ({ page }) => {
    const result = await page.evaluate(() => {
      return (window as any).CacheSystemTest.testQuotaHandling()
    })

    // Either it can handle large data, or it properly detects quota exceeded
    expect(result.canHandleLargeData || result.quotaExceeded).toBe(true)
  })

  test('should work across different storage types', async ({ page }) => {
    await page.evaluate(() => {
      // Test localStorage
      localStorage.setItem(
        'test:local',
        JSON.stringify({ type: 'localStorage', value: 'local-value' })
      )

      // Test sessionStorage
      sessionStorage.setItem(
        'test:session',
        JSON.stringify({ type: 'sessionStorage', value: 'session-value' })
      )
    })

    const localResult = await page.evaluate(() => {
      const item = localStorage.getItem('test:local')
      return item ? JSON.parse(item) : null
    })

    const sessionResult = await page.evaluate(() => {
      const item = sessionStorage.getItem('test:session')
      return item ? JSON.parse(item) : null
    })

    expect(localResult.type).toBe('localStorage')
    expect(localResult.value).toBe('local-value')

    expect(sessionResult.type).toBe('sessionStorage')
    expect(sessionResult.value).toBe('session-value')
  })

  test('should persist data across page reloads (localStorage)', async ({
    page,
  }) => {
    // Store data
    await page.evaluate(() => {
      localStorage.setItem(
        'persist-test',
        JSON.stringify({
          value: 'persistent-value',
          timestamp: Date.now(),
        })
      )
    })

    // Reload the page
    await page.reload()
    await navigateToTestPage(page)

    // Check if data persisted
    const result = await page.evaluate(() => {
      const item = localStorage.getItem('persist-test')
      return item ? JSON.parse(item) : null
    })

    expect(result.value).toBe('persistent-value')
    expect(result.timestamp).toBeGreaterThan(0)
  })

  test('should clear sessionStorage on new session', async ({ context }) => {
    const page1 = await context.newPage()
    await navigateToTestPage(page1)

    // Store data in sessionStorage
    await page1.evaluate(() => {
      sessionStorage.setItem(
        'session-test',
        JSON.stringify({ value: 'session-value' })
      )
    })

    // Verify data exists
    const beforeClose = await page1.evaluate(() => {
      return sessionStorage.getItem('session-test')
    })
    expect(beforeClose).toBeTruthy()

    // Close the page (simulates tab close)
    await page1.close()

    // Open a new page (new session)
    const page2 = await context.newPage()
    await navigateToTestPage(page2)

    // Check if sessionStorage is cleared
    const afterNewSession = await page2.evaluate(() => {
      return sessionStorage.getItem('session-test')
    })
    expect(afterNewSession).toBeNull()

    await page2.close()
  })

  test('should handle concurrent operations', async ({ page }) => {
    const result = await page.evaluate(async () => {
      const promises = []

      // Simulate concurrent cache operations
      for (let i = 0; i < 10; i++) {
        promises.push(
          new Promise((resolve) => {
            setTimeout(() => {
              localStorage.setItem(
                `concurrent-${i}`,
                JSON.stringify({
                  value: `value-${i}`,
                  index: i,
                })
              )
              resolve(i)
            }, Math.random() * 100)
          })
        )
      }

      await Promise.all(promises)

      // Verify all items were stored
      const results = []
      for (let i = 0; i < 10; i++) {
        const item = localStorage.getItem(`concurrent-${i}`)
        if (item) {
          results.push(JSON.parse(item))
        }
      }

      return results
    })

    expect(result).toHaveLength(10)
    result.forEach((item, index) => {
      expect(item.value).toBe(`value-${index}`)
      expect(item.index).toBe(index)
    })
  })

  test('should handle edge cases and error conditions', async ({ page }) => {
    const result = await page.evaluate(() => {
      const results = {
        emptyKey: false,
        nullValue: false,
        undefinedValue: false,
        circularReference: false,
        largeKey: false,
      }

      try {
        // Test empty key
        localStorage.setItem('', 'empty-key-value')
        results.emptyKey = localStorage.getItem('') === 'empty-key-value'
      } catch (e) {
        results.emptyKey = false
      }

      try {
        // Test null value
        localStorage.setItem('null-test', JSON.stringify(null))
        results.nullValue =
          JSON.parse(localStorage.getItem('null-test') || 'false') === null
      } catch (e) {
        results.nullValue = false
      }

      try {
        // Test undefined value (should be converted to string)
        localStorage.setItem('undefined-test', JSON.stringify(undefined))
        results.undefinedValue = localStorage.getItem('undefined-test') !== null
      } catch (e) {
        results.undefinedValue = false
      }

      try {
        // Test circular reference (should fail)
        const circular: any = { name: 'circular' }
        circular.self = circular
        localStorage.setItem('circular-test', JSON.stringify(circular))
        results.circularReference = false // Should not reach here
      } catch (e) {
        results.circularReference = true // Expected to fail
      }

      try {
        // Test very large key
        const largeKey = 'x'.repeat(1000)
        localStorage.setItem(largeKey, 'large-key-value')
        results.largeKey = localStorage.getItem(largeKey) === 'large-key-value'
      } catch (e) {
        results.largeKey = false
      }

      return results
    })

    expect(result.emptyKey).toBe(true) // Empty keys should work
    expect(result.nullValue).toBe(true) // Null values should work
    expect(result.undefinedValue).toBe(true) // Undefined should be serializable
    expect(result.circularReference).toBe(true) // Circular references should fail
    expect(result.largeKey).toBe(true) // Large keys should work
  })
})
