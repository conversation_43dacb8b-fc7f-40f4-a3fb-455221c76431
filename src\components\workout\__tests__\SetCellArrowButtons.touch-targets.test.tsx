/**
 * SetCellArrowButtons Touch Target Compliance Tests
 *
 * Purpose: Validate critical workout arrow buttons meet 52px minimum requirement
 * Context: Arrow buttons are essential for set adjustments during workouts
 * Priority: HIGH - Users need reliable touch targets when hands are sweaty/gloved
 */

import React from 'react'
import { render, screen } from '@testing-library/react'
import { vi } from 'vitest'
import { SetCellArrowButtons } from '../SetCellArrowButtons'

// Mock functions for button handlers
const mockHandlers = {
  onRepsUp: vi.fn(),
  onRepsDown: vi.fn(),
  onWeightUp: vi.fn(),
  onWeightDown: vi.fn(),
}

// Utility to measure touch target dimensions
function measureTouchTarget(element: Element) {
  const rect = element.getBoundingClientRect()
  const computedStyle = getComputedStyle(element)

  return {
    width: Math.max(rect.width, parseFloat(computedStyle.minWidth) || 0),
    height: Math.max(rect.height, parseFloat(computedStyle.minHeight) || 0),
    minWidth: parseFloat(computedStyle.minWidth) || 0,
    minHeight: parseFloat(computedStyle.minHeight) || 0,
  }
}

// Assert 52px minimum touch target compliance
function assertArrowButtonCompliance(button: Element, minimum = 52) {
  const { width, height, minWidth, minHeight } = measureTouchTarget(button)

  // Both width and height should meet minimum
  expect(width).toBeGreaterThanOrEqual(minimum)
  expect(height).toBeGreaterThanOrEqual(minimum)

  // Check CSS min-width and min-height properties
  expect(minWidth).toBeGreaterThanOrEqual(minimum)
  expect(minHeight).toBeGreaterThanOrEqual(minimum)
}

describe('SetCellArrowButtons - Touch Target Compliance', () => {
  describe('arrow button touch targets when active', () => {
    it('should render arrow buttons with minimum 52px touch targets', () => {
      render(
        <SetCellArrowButtons isActive position="above" {...mockHandlers} />
      )

      // Find all arrow buttons (should be 2: reps and weight)
      const arrowButtons = screen.getAllByRole('button')
      expect(arrowButtons).toHaveLength(2)

      // Verify all arrow buttons meet the 52px requirement
      arrowButtons.forEach((button) => {
        assertArrowButtonCompliance(button, 52)
      })
    })

    it('should maintain 52px touch targets for both above and below positions', () => {
      const { rerender } = render(
        <SetCellArrowButtons isActive position="above" {...mockHandlers} />
      )

      // Test above position
      let arrowButtons = screen.getAllByRole('button')
      arrowButtons.forEach((button) => {
        assertArrowButtonCompliance(button, 52)
      })

      // Test below position
      rerender(
        <SetCellArrowButtons isActive position="below" {...mockHandlers} />
      )

      arrowButtons = screen.getAllByRole('button')
      arrowButtons.forEach((button) => {
        assertArrowButtonCompliance(button, 52)
      })
    })
  })

  describe('arrow button interactive area', () => {
    it('should provide adequate clickable area for reps adjustment', () => {
      render(
        <SetCellArrowButtons isActive position="above" {...mockHandlers} />
      )

      const repsArrowButton = screen.getByLabelText(/increase reps/i)
      assertArrowButtonCompliance(repsArrowButton, 52)

      // Button should have proper interactive styling
      expect(repsArrowButton).toHaveClass('min-h-[52px]') // Updated to 52px requirement
      expect(repsArrowButton).toHaveClass('min-w-[52px]') // Updated to 52px requirement
    })

    it('should provide adequate clickable area for weight adjustment', () => {
      render(
        <SetCellArrowButtons isActive position="above" {...mockHandlers} />
      )

      const weightArrowButton = screen.getByLabelText(/increase weight/i)
      assertArrowButtonCompliance(weightArrowButton, 52)

      // Should have consistent sizing with reps button
      const repsButton = screen.getByLabelText(/increase reps/i)
      const weightDimensions = measureTouchTarget(weightArrowButton)
      const repsDimensions = measureTouchTarget(repsButton)

      expect(
        Math.abs(weightDimensions.height - repsDimensions.height)
      ).toBeLessThan(2)
      expect(
        Math.abs(weightDimensions.width - repsDimensions.width)
      ).toBeLessThan(2)
    })
  })

  describe('inactive state behavior', () => {
    it('should not render buttons when isActive is false', () => {
      render(
        <SetCellArrowButtons
          isActive={false}
          position="above"
          {...mockHandlers}
        />
      )

      // Should render nothing when inactive
      const buttons = screen.queryAllByRole('button')
      expect(buttons).toHaveLength(0)
    })
  })

  describe('workout environment optimization', () => {
    it('should provide touch targets suitable for workout conditions', () => {
      render(
        <SetCellArrowButtons isActive position="above" {...mockHandlers} />
      )

      const arrowButtons = screen.getAllByRole('button')

      arrowButtons.forEach((button) => {
        // Workout-specific requirements
        assertArrowButtonCompliance(button, 52)

        // Should have hover and transition states for feedback
        expect(button).toHaveClass('transition-colors')

        // Should have proper aria labels for accessibility
        expect(button).toHaveAttribute('aria-label')
        expect(button.getAttribute('aria-label')).toMatch(
          /(increase|decrease)/i
        )
      })
    })

    it('should maintain touch targets with rapid interactions', () => {
      render(
        <SetCellArrowButtons isActive position="above" {...mockHandlers} />
      )

      const incrementButton = screen.getByLabelText(/increase reps/i)

      // Measure before interaction
      const initialDimensions = measureTouchTarget(incrementButton)
      expect(initialDimensions.height).toBeGreaterThanOrEqual(52)

      // Simulate rapid clicks (common in workout scenarios)
      for (let i = 0; i < 5; i++) {
        incrementButton.click()
      }

      // Dimensions should remain stable after interactions
      const finalDimensions = measureTouchTarget(incrementButton)
      expect(finalDimensions.height).toBeGreaterThanOrEqual(52)
      expect(
        Math.abs(finalDimensions.height - initialDimensions.height)
      ).toBeLessThan(1)
    })
  })

  describe('svg icon sizing within touch targets', () => {
    it('should have appropriately sized icons within 52px touch targets', () => {
      render(
        <SetCellArrowButtons isActive position="above" {...mockHandlers} />
      )

      const arrowButtons = screen.getAllByRole('button')

      arrowButtons.forEach((button) => {
        // Button should meet touch target requirement
        assertArrowButtonCompliance(button, 52)

        // SVG icon should be appropriately sized (current: w-12 h-12 = 48px)
        const svg = button.querySelector('svg')
        expect(svg).toBeTruthy()

        if (svg) {
          const svgClasses = svg.className
          expect(svgClasses).toContain('w-12')
          expect(svgClasses).toContain('h-12')
        }
      })
    })
  })

  describe('accessibility compliance', () => {
    it('should provide proper accessibility labels for screen readers', () => {
      render(
        <SetCellArrowButtons isActive position="above" {...mockHandlers} />
      )

      // Check for proper aria labels
      expect(screen.getByLabelText('Increase reps')).toBeInTheDocument()
      expect(screen.getByLabelText('Increase weight')).toBeInTheDocument()

      const arrowButtons = screen.getAllByRole('button')
      arrowButtons.forEach((button) => {
        // Each button should have proper touch target and accessibility
        assertArrowButtonCompliance(button, 52)
        expect(button).toHaveAttribute('aria-label')
      })
    })
  })
})

/**
 * Test Rationale:
 *
 * 1. SetCellArrowButtons now correctly implements the 52px touch target requirement
 *    using min-h-[52px] min-w-[52px] classes
 *
 * 2. Arrow buttons are CRITICAL for workout functionality - users need reliable
 *    touch targets when hands are sweaty, wearing gloves, or in fatigue states
 *
 * 3. Tests validate both individual button compliance and consistent sizing
 *    between reps and weight adjustment buttons
 *
 * 4. Workout-specific scenarios test rapid interactions and stability under
 *    repeated use (common during set adjustments)
 *
 * 5. Accessibility tests ensure proper labeling while maintaining touch targets
 *
 * Expected Coverage: 90%+ of arrow button functionality
 * Test Reliability: High - Validates class presence and dimensions
 * Business Impact: Critical - Affects core workout experience
 */
