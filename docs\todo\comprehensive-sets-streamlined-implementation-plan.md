# Comprehensive Sets: Streamlined Implementation Plan (Web ↔ MAUI Parity)

This plan defines a safe, incremental path to centralize server-driven user settings, streamline endpoint selection, and ensure set rendering strictly follows the server’s recommendation flags. It includes multi-level breakdowns and a sequence of TDD-friendly prompts for code-generation.

## Objectives

- Single source of truth for server user settings (`GetUserInfoPyramid`) with shared cache.
- Endpoint selection uses only cached server fields (IsNormalSet, IsPyramid) plus exercise metadata (bodyweight) as per the guide.
- Eliminate redundant per-exercise user settings fetches in hooks.
- Ren<PERSON> sets strictly from recommendation response flags, with bodyweight normalization guard.
- Strong unit/integration tests at each step; keep changes incremental and reversible.

## Assumptions

- Server returns `UserInfosModel` from `userProfileApi.getUserInfo()` containing `IsNormalSet`, `IsPyramid`, `MassUnit`, etc.
- `getExerciseRecommendation()` already delegates to `selectRecommendationEndpoint()`.
- `useAuthStore` exists and is the right place to clear caches on logout.
- Guide: `docs/guides/comprehensive-sets-implementation-guide-2.md` is the authoritative behavior reference.

## Glossary

- UserInfo: `UserInfosModel` from server.
- UserSettings: Local, derived settings used by UI (`src/services/userSettings.ts`).
- Endpoint selection: Choosing Normal vs. Rest-Pause recommendation routes based on server flags and exercise context.

---

## Current State Summary (What we’re fixing)

- `src/services/userSettings.ts#getUserSettings()` fetches server info but ignores the response (TODO), so it never uses `IsNormalSet`, `IsPyramid`, `MassUnit`.
- `src/hooks/useWorkoutRecommendations.ts` calls `getUserSettings()` inside every recommendation load, triggering repeated server fetches.
- `src/services/endpointSelection.ts` has its own cache; other modules don’t benefit from it → duplicated logic and potential inconsistency.

---

## High-Level Plan (Phase Outline)

1. Shared user info cache module with TTL and clear.
2. Endpoint selection uses shared cache; remove per-file cache.
3. User settings service derives from shared cache (map `IsNormalSet`, `IsPyramid`, `MassUnit`).
4. Hook uses a single, memoized settings fetch rather than per-exercise fetching.
5. Invalidate on logout; optional prefetch on login; expose minimal fields in `authStore.cachedUserInfo`.
6. Verify/align set rendering to trust server flags; bodyweight normalization guard.
7. Integration tests to validate reduced calls and correct behavior.
8. Documentation updates and rollout notes.

---

## Breakdown → Iterative Chunks

- Chunk A: Introduce shared `userInfoCache` with TTL and tests.
- Chunk B: Switch `endpointSelection` to shared cache; delete its local cache; add tests.
- Chunk C: Update `userSettings` to read from shared cache; map fields; tests.
- Chunk D: Refactor `useWorkoutRecommendations` to memoize settings; tests ensuring only one fetch.
- Chunk E: Add cache invalidation on logout (and optional prefetch on login); tests.
- Chunk F: Audit/verify set rendering logic; add tests for response flags and bodyweight normalization.
- Chunk G: Integration tests across services + hooks; docs updates.

---

## Chunk Refinement → Right-Sized Steps

- Step A1: Create `src/services/userInfoCache.ts` with in-memory `cache { data, timestamp }`, `TTL = 5–10 min`, `getServerUserInfoCached()`, `clearUserInfoCache()`; log via `logger`.
- Step A2: Unit tests for cache: first fetch hits network, second returns cached within TTL; after `clear`, fetch again; handle null response.

- Step B1: Update `src/services/endpointSelection.ts` to import `getServerUserInfoCached()`; remove file-local cache; keep function signatures; maintain existing logs.
- Step B2: Unit tests for endpoint selection: cases for `(IsNormalSet === false)`, `(IsPyramid && bodyweight)`, default normal; verify cached calls are reused.

- Step C1: Update `src/services/userSettings.ts#getUserSettings()` to call `getServerUserInfoCached()` and map:
  - `setStyle = info?.IsNormalSet === false ? 'RestPause' : 'Normal'`
  - `isPyramid = !!info?.IsPyramid`
  - `isQuickMode = info?.IsQuickMode ?? null` (if available; else leave existing behavior)
  - Preserve existing computed fields (`isStrengthPhase`, `lightSessionDays`, `isFreePlan`).
- Step C2: Add `getUserSettingsSync()` that reads from `userInfoCache` if present; no network.
- Step C3: Unit tests for mappings and sync path.

- Step D1: Add `userSettingsOnce` memoization in `src/hooks/useWorkoutRecommendations.ts` using module-level promise or `useRef` to ensure single fetch per hook instance.
- Step D2: Replace per-exercise `getUserSettings()` calls with the memoized accessor; keep `getExerciseRecommendation()` unchanged.
- Step D3: Tests verifying `getUserSettings()` is invoked once for multiple exercise recommendations in a session.

- Step E1: In `useAuthStore.logout()`, call `clearUserInfoCache()`; test to ensure cache is cleared.
- Step E2: Optional: on successful login (`setAuth`), prefetch `getServerUserInfoCached()` and store essential fields (`MassUnit`) into `cachedUserInfo`; tests.

- Step F1: Identify set rendering utilities/components; ensure they rely only on recommendation payload flags (Drop, Pyramid, Reverse, Back-off, Rest-Pause) per guide priority.
- Step F2: Implement MAUI drop set rules: minimum 3 total sets, one-third reps for drop sets (Math.max(1, Math.floor(baseReps / 3))), 10% cumulative weight reduction.
- Step F3: Enforce bodyweight normalization: if exercise is bodyweight, normalize to Rest-Pause; ignore Drop/Pyramid/Reverse/Back-off; keep `NbPauses`.
- Step F4: Unit tests for set rendering across scenarios including MAUI drop set rules.

- Step G1: Integration test for `useWorkoutRecommendations` ensuring: endpoint selection follows rules; server flags drive rendering; minimal user settings fetches.
- Step G2: Update docs (this plan, guide references) and add a short rollout note.

These steps are small enough for safe, testable increments and large enough to provide value per step.

---

## TDD-Friendly Prompts (Sequential, No Orphans)

Each prompt is self-contained, builds on the prior work, and includes acceptance criteria. Use the repo’s conventions, avoid unrelated changes, and keep logs consistent with `logger`.

### Prompt 1 — Add Shared User Info Cache

```text
Goal: Introduce a shared, in-memory cache for server user info (GetUserInfoPyramid) with TTL and clear.

Context:
- Implement at `src/services/userInfoCache.ts`.
- Use `userProfileApi.getUserInfo()` as the fetcher.
- Provide: `getServerUserInfoCached()`, `clearUserInfoCache()`, `CACHE_DURATION` (5 min default).
- Use `logger` for debug/error logs.

Tasks:
1) Create `src/services/userInfoCache.ts` exporting:
   - `getServerUserInfoCached(): Promise<UserInfosModel | null>` — returns cached if fresh, else fetches and caches; returns null on error.
   - `clearUserInfoCache(): void` — resets in-memory cache.
   - `CACHE_DURATION` constant.
2) Handle network errors gracefully; log via `logger.error`.
3) Add unit tests at `src/services/__tests__/userInfoCache.test.ts`:
   - First call fetches from API; second call within TTL returns cached without calling API again.
   - After `clearUserInfoCache()`, next call refetches.
   - If API returns null or throws, function returns null and caches null with timestamp.

Acceptance Criteria:
- Tests pass; cache prevents redundant network calls within TTL.
- No changes to other modules yet.
```

### Prompt 2 — Switch Endpoint Selection to Shared Cache

```text
Goal: Make `endpointSelection` use the shared `userInfoCache` and remove its local cache logic.

Context:
- File: `src/services/endpointSelection.ts`.
- Replace internal `getUserSettings()` with imported `getServerUserInfoCached()`.
- Keep `selectRecommendationEndpoint()` API stable.

Tasks:
1) Import `getServerUserInfoCached` and use it where user info is needed.
2) Remove local cache structure and `clearUserSettingsCache()`; re-export a `clearUserSettingsCache()` that delegates to `clearUserInfoCache()` for test compatibility, or update tests accordingly.
3) Ensure selection rules per guide:
   - If `IsNormalSet === false` => Rest-Pause endpoint.
   - Else if `(exercise.IsBodyweight === true && info.IsPyramid === true)` => Rest-Pause endpoint.
   - Else => Normal endpoint.
4) Unit tests at `src/services/__tests__/endpointSelection.test.ts` covering:
   - Normal preference, non-bodyweight => Normal endpoint.
   - Rest-Pause preference (`IsNormalSet === false`) => Rest-Pause endpoint.
   - Bodyweight + `IsPyramid === true` => Rest-Pause endpoint.
   - Cache reuse: subsequent calls within TTL do not refetch user info.

Acceptance Criteria:
- All endpoint selection tests pass.
- No unrelated behavior changes.
```

### Prompt 3 — Map User Settings from Server Cache

```text
Goal: Update `userSettings` to use the shared cache and map server fields to local `UserSettings`.

Context:
- File: `src/services/userSettings.ts`.
- Replace the TODO path with real mapping based on `getServerUserInfoCached()`.

Tasks:
1) Import and use `getServerUserInfoCached()`.
2) Implement mapping:
   - `setStyle = info?.IsNormalSet === false ? 'RestPause' : 'Normal'`.
   - `isPyramid = !!info?.IsPyramid`.
   - `isQuickMode = info?.IsQuickMode ?? null` if available; otherwise preserve existing default.
   - Keep current computed fields (`isStrengthPhase`, `isFreePlan`, `lightSessionDays`).
3) Add `getUserSettingsSync()` that reads from in-memory cache only (no network), returns partial settings if needed.
4) Unit tests at `src/services/__tests__/userSettings.mapping.test.ts` validating mappings and sync path behavior.

Acceptance Criteria:
- Tests verify correct mapping from `UserInfosModel` to `UserSettings`.
- No network calls in `getUserSettingsSync()`.
```

### Prompt 4 — Memoize User Settings in useWorkoutRecommendations

```text
Goal: Stop fetching user settings per exercise; fetch once per hook instance.

Context:
- File: `src/hooks/useWorkoutRecommendations.ts`.
- Currently calls `getUserSettings()` inside each `loadRecommendation`.

Tasks:
1) Add a memoized accessor (module-level `let userSettingsPromise: Promise<UserSettings> | null = null`, or a `useRef` inside the hook) named `getUserSettingsOnce()`.
2) Replace `await getUserSettings()` calls with `await getUserSettingsOnce()`.
3) Ensure no behavior change for how `getExerciseRecommendation()` is invoked.
4) Tests at `src/hooks/__tests__/useWorkoutRecommendations.user-settings-fetch.test.tsx`:
   - Multiple calls to `loadRecommendation()` only trigger a single `getUserSettings()` fetch.
   - Pending request de-duplication still works.

Acceptance Criteria:
- Tests pass; `getUserSettings()` runs at most once per hook lifecycle.
```

### Prompt 5 — Cache Invalidation on Logout; Optional Prefetch on Login

```text
Goal: Ensure cache clears on logout and optionally prefetch user info on login for better UX.

Context:
- File: `src/stores/authStore.ts`.

Tasks:
1) Import `clearUserInfoCache()` and call it in `logout()`.
2) Optional: In `setAuth()`, after successful auth, kick off `getServerUserInfoCached()` and store `MassUnit` (and potentially a few essentials) into `cachedUserInfo` for display without extra calls.
3) Tests at `src/stores/__tests__/authStore.cache-invalidations.test.ts`:
   - After `logout()`, subsequent call to `getServerUserInfoCached()` refetches from network.
   - If prefetch enabled, after `setAuth()` the cache is populated and `cachedUserInfo` holds `MassUnit`.

Acceptance Criteria:
- Tests validate proper invalidation and optional prefetch behavior.
```

### Prompt 6 — Verify Set Rendering Uses Server Flags; Bodyweight Normalization

```text
Goal: Ensure rendering logic builds sets strictly from server flags and applies bodyweight normalization.

Context:
- Identify set construction utilities/components (search for usage of `IsDropSet`, `IsPyramid`, `IsReversePyramid`, `NbPauses`, etc.).
- If logic already exists, add tests; if missing, add minimal utility wrappers that interpret flags without changing UI structure.

Tasks:
1) Locate or add a small utility (e.g., `src/utils/setStyleInterpreter.ts`) that takes the recommendation payload plus exercise metadata and returns a normalized set plan per the guide's priority: Drop > Pyramid > Reverse > Rest-Pause > Back-off > Normal.
2) Implement MAUI drop set parity rules:
   - Enforce minimum 3 total sets (add drop sets if Series + NbPauses < 3)
   - Apply one-third reps rule: drop sets use Math.max(1, Math.floor(baseReps / 3))
   - Apply 10% cumulative weight reduction for each drop set
3) Enforce bodyweight normalization: for bodyweight exercises, override to Rest-Pause and ignore other styles; keep `NbPauses`.
4) Unit tests at `src/utils/__tests__/setStyleInterpreter.test.ts` covering:
   - Drop sets with MAUI rules (min 3 sets, one-third reps, 10% weight drops)
   - Pyramid and Reverse Pyramid interpretations
   - Rest-Pause micro-sets from `NbPauses`/`NbRepsPauses`
   - Back-off recognition
   - Bodyweight normalization

Acceptance Criteria:
- Tests encode the guide’s logic; no changes to API calling paths here.
```

### Prompt 7 — Integration Tests and Docs

```text
Goal: Validate end-to-end behavior and update docs.

Context:
- Hook: `useWorkoutRecommendations` integration tests already exist under `src/hooks/__tests__`.

Tasks:
1) Add integration tests `src/hooks/__tests__/useWorkoutRecommendations.endpoint-selection.integration.test.ts` to verify:
   - Route choice follows: Rest-Pause if `IsNormalSet === false`, or bodyweight×`IsPyramid`; Normal otherwise.
   - Only one user info fetch per session due to caching & memoization.
   - Rendering uses server flags correctly (can assert normalized summaries if rendering helpers are present).
2) Update docs: Add a short note to `docs/guides/comprehensive-sets-implementation-guide-2.md` (or a separate doc) referencing the shared cache and endpoint selection rules.

Acceptance Criteria:
- Integration tests pass; docs updated.
```

### Prompt 8 — Wire-Up and Cleanup

```text
Goal: Final wiring and cleanup without functional changes.

Tasks:
1) Remove any now-dead code or TODOs that referenced old settings paths.
2) Ensure `endpointSelection` re-exports a cache clear for tests if needed.
3) Ensure all new files adhere to lint rules; run format on changed files.
4) Summarize changes in a small changelog entry or PR description.

Acceptance Criteria:
- Lint and tests pass; no orphaned code; PR is cohesive.
```

## Notes and Best Practices

- Favor pure functions and dependency injection in tests: mock `userProfileApi`, not global state.
- Keep caching simple (in-memory TTL). Avoid adding persistent caches unless necessary.
- Use existing logging utilities; avoid console noise in production.
- Guard against null user info and absence of fields (server variability).
- Keep types precise: prefer `UserInfosModel` from `src/types/api/common.ts`.
- Minimize API requests: cache in `userInfoCache`, memoize inside hooks.

## Rollout Considerations

- Ship behind a small internal flag if needed (not required by plan but simple to add).
- Monitor logs for cache hit/miss ratios and endpoint selection outcomes.
- QA: Compare with MAUI behavior for representative exercises (normal, bodyweight, drop, pyramid, reverse pyramid) using the same account.
