import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import { SwipeableStatCard } from '../SwipeableStatCard'
import type { UserStats } from '@/types'

// Mock framer-motion to avoid animation issues in tests
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  useAnimation: () => ({
    start: vi.fn(),
  }),
}))

// Mock haptic feedback
vi.mock('@/utils/haptic', () => ({
  useHaptic: () => ({
    trigger: vi.fn(),
  }),
}))

// Mock auth store
vi.mock('@/stores/authStore', () => ({
  useAuthStore: () => ({
    user: { massUnit: 'lbs' },
  }),
}))

describe('SwipeableStatCard - Gold Gradient Icons', () => {
  const mockStats: UserStats = {
    weekStreak: 5,
    workoutsCompleted: 42,
    lbsLifted: 12500,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders icons with gold gradient fill', () => {
    const { container } = render(
      <SwipeableStatCard stats={mockStats} isLoading={false} />
    )

    // Check for gradient definition in SVG
    const gradientDefs = container.querySelectorAll('linearGradient')
    expect(gradientDefs.length).toBeGreaterThan(0)

    // Verify gradient has correct ID
    const gradient = gradientDefs[0]
    expect(gradient).toHaveAttribute(
      'id',
      expect.stringContaining('gold-gradient')
    )

    // Check gradient stops match theme colors
    const stops = gradient.querySelectorAll('stop')
    expect(stops).toHaveLength(3)
    expect(stops[0]).toHaveAttribute('stop-color', '#d4af37')
    expect(stops[1]).toHaveAttribute('stop-color', '#f7e98e')
    expect(stops[2]).toHaveAttribute('stop-color', '#d4af37')
  })

  it('applies gradient to all stat icons', () => {
    const { container } = render(
      <SwipeableStatCard stats={mockStats} isLoading={false} />
    )

    // Find all SVG icons
    const svgIcons = container.querySelectorAll('svg.w-8.h-8')
    expect(svgIcons.length).toBeGreaterThan(0)

    // Check each icon uses gradient fill or stroke
    svgIcons.forEach((svg) => {
      const paths = svg.querySelectorAll('path')
      paths.forEach((path) => {
        const strokeAttr = path.getAttribute('stroke')
        const fillAttr = path.getAttribute('fill')

        // Should reference gradient URL
        const hasGradientStroke = strokeAttr?.includes('url(#gold-gradient')
        const hasGradientFill = fillAttr?.includes('url(#gold-gradient')

        expect(hasGradientStroke || hasGradientFill).toBe(true)
      })
    })
  })

  it('maintains gradient during loading state', () => {
    const { container } = render(<SwipeableStatCard stats={null} isLoading />)

    // Even during loading, gradient should be defined
    const gradientDefs = container.querySelectorAll('linearGradient')
    expect(gradientDefs.length).toBeGreaterThan(0)
  })

  it('preserves gradient when switching between stats', () => {
    const { container, rerender } = render(
      <SwipeableStatCard stats={mockStats} isLoading={false} />
    )

    // Initial render has gradient
    let gradientDefs = container.querySelectorAll('linearGradient')
    expect(gradientDefs.length).toBeGreaterThan(0)

    // Simulate stat change
    const updatedStats = { ...mockStats, weekStreak: 10 }
    rerender(<SwipeableStatCard stats={updatedStats} isLoading={false} />)

    // Gradient still present after update
    gradientDefs = container.querySelectorAll('linearGradient')
    expect(gradientDefs.length).toBeGreaterThan(0)
  })

  it('gradient works with different mass units', () => {
    // Mock auth store with kg unit preference
    const mockUseAuthStore = vi.fn(() => ({
      user: { massUnit: 'kg' },
    }))

    vi.doMock('@/stores/authStore', () => ({
      useAuthStore: mockUseAuthStore,
    }))

    const { container } = render(
      <SwipeableStatCard stats={mockStats} isLoading={false} />
    )

    // Gradient should still be present regardless of unit
    const gradientDefs = container.querySelectorAll('linearGradient')
    expect(gradientDefs.length).toBeGreaterThan(0)

    // Since we're starting at the first stat (lbs/kg lifted), check for that
    expect(screen.getByText('lbs lifted')).toBeInTheDocument()
  })

  it('handles zero stats with gradient icons', () => {
    const zeroStats: UserStats = {
      weekStreak: 0,
      workoutsCompleted: 0,
      lbsLifted: 0,
    }

    const { container } = render(
      <SwipeableStatCard stats={zeroStats} isLoading={false} />
    )

    // Gradient should render even with zero values
    const gradientDefs = container.querySelectorAll('linearGradient')
    expect(gradientDefs.length).toBeGreaterThan(0)

    // Icons should still use gradient
    const svgIcons = container.querySelectorAll('svg.w-8.h-8')
    expect(svgIcons.length).toBeGreaterThan(0)
  })

  it('gradient definition includes correct angle', () => {
    const { container } = render(
      <SwipeableStatCard stats={mockStats} isLoading={false} />
    )

    const gradient = container.querySelector('linearGradient')
    expect(gradient).toBeTruthy()

    // Check for 135-degree angle transformation
    expect(gradient).toHaveAttribute('x1', '0%')
    expect(gradient).toHaveAttribute('y1', '0%')
    expect(gradient).toHaveAttribute('x2', '100%')
    expect(gradient).toHaveAttribute('y2', '100%')
  })

  it('does not duplicate gradient definitions', () => {
    const { container } = render(
      <SwipeableStatCard stats={mockStats} isLoading={false} />
    )

    // Should have exactly one gradient definition per unique gradient
    const gradientDefs = container.querySelectorAll(
      'linearGradient#gold-gradient'
    )
    expect(gradientDefs).toHaveLength(1)
  })

  it('gradient styling does not interfere with animations', () => {
    const { container } = render(<SwipeableStatCard stats={null} isLoading />)

    // Check for loading message which has animation
    const loadingMessage = screen.getByText('Loading stats...')
    expect(loadingMessage).toBeInTheDocument()
    expect(loadingMessage).toHaveClass('animate-pulse')

    // Gradient should coexist with animations
    const gradientDefs = container.querySelectorAll('linearGradient')
    expect(gradientDefs.length).toBeGreaterThan(0)
  })

  it('validates gradient matches theme token colors', () => {
    const { container } = render(
      <SwipeableStatCard stats={mockStats} isLoading={false} />
    )

    const gradient = container.querySelector('linearGradient')
    const stops = gradient?.querySelectorAll('stop')

    // Verify exact theme token colors
    expect(stops?.[0]).toHaveAttribute('stop-color', '#d4af37') // --color-brand-primary
    expect(stops?.[1]).toHaveAttribute('stop-color', '#f7e98e') // --color-brand-secondary
    expect(stops?.[2]).toHaveAttribute('stop-color', '#d4af37') // --color-brand-primary
  })
})
