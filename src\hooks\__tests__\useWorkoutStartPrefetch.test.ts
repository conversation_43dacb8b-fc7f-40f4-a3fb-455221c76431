import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useWorkoutStartPrefetch } from '../useWorkoutStartPrefetch'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useExerciseSetsPrefetch } from '../useExerciseSetsPrefetch'

// Mock dependencies
vi.mock('@/stores/workoutStore')
vi.mock('../useExerciseSetsPrefetch')

describe('useWorkoutStartPrefetch', () => {
  const mockStartWorkout = vi.fn()
  const mockPrefetchExerciseSets = vi.fn()
  const mockExercises = [
    { Id: 1, Label: 'Bench Press' },
    { Id: 2, Label: 'Squats' },
    { Id: 3, Label: 'Deadlifts' },
    { Id: 4, Label: 'Rows' },
  ]

  beforeEach(() => {
    vi.clearAllMocks()

    // Mock useWorkoutStore
    vi.mocked(useWorkoutStore).mockReturnValue({
      startWorkout: mockStartWorkout,
      exercises: mockExercises,
      workoutSession: null,
    } as any)

    // Mock useExerciseSetsPrefetch
    vi.mocked(useExerciseSetsPrefetch).mockReturnValue({
      prefetchExerciseSets: mockPrefetchExerciseSets,
      prefetchStatus: {},
      isPrefetching: false,
      prefetchedExerciseIds: [],
      getPrefetchProgress: vi.fn(),
    })
  })

  it('should prefetch first 3 exercises when workout starts', async () => {
    const { result } = renderHook(() => useWorkoutStartPrefetch())

    // Start workout
    await act(async () => {
      await result.current.startWorkoutWithPrefetch()
    })

    // Should call original startWorkout
    expect(mockStartWorkout).toHaveBeenCalledTimes(1)

    // Should prefetch first 3 exercises
    expect(mockPrefetchExerciseSets).toHaveBeenCalledWith([1, 2, 3])
  })

  it('should not prefetch if workout session already exists', async () => {
    // Mock active workout session
    vi.mocked(useWorkoutStore).mockReturnValue({
      startWorkout: mockStartWorkout,
      exercises: mockExercises,
      workoutSession: { id: 'session-1', startTime: new Date() },
    } as any)

    const { result } = renderHook(() => useWorkoutStartPrefetch())

    await act(async () => {
      await result.current.startWorkoutWithPrefetch()
    })

    // Should not call startWorkout or prefetch
    expect(mockStartWorkout).not.toHaveBeenCalled()
    expect(mockPrefetchExerciseSets).not.toHaveBeenCalled()
  })

  it('should handle fewer than 3 exercises', async () => {
    // Mock only 2 exercises
    vi.mocked(useWorkoutStore).mockReturnValue({
      startWorkout: mockStartWorkout,
      exercises: [
        { Id: 1, Label: 'Bench Press' },
        { Id: 2, Label: 'Squats' },
      ],
      workoutSession: null,
    } as any)

    const { result } = renderHook(() => useWorkoutStartPrefetch())

    await act(async () => {
      await result.current.startWorkoutWithPrefetch()
    })

    // Should prefetch only available exercises
    expect(mockPrefetchExerciseSets).toHaveBeenCalledWith([1, 2])
  })

  it('should handle empty exercises array', async () => {
    // Mock empty exercises
    vi.mocked(useWorkoutStore).mockReturnValue({
      startWorkout: mockStartWorkout,
      exercises: [],
      workoutSession: null,
    } as any)

    const { result } = renderHook(() => useWorkoutStartPrefetch())

    await act(async () => {
      await result.current.startWorkoutWithPrefetch()
    })

    // Should still start workout but not prefetch
    expect(mockStartWorkout).toHaveBeenCalledTimes(1)
    expect(mockPrefetchExerciseSets).not.toHaveBeenCalled()
  })

  it('should not block workout start if prefetch fails', async () => {
    // Mock prefetch failure
    mockPrefetchExerciseSets.mockRejectedValueOnce(new Error('Network error'))

    const { result } = renderHook(() => useWorkoutStartPrefetch())

    await act(async () => {
      await result.current.startWorkoutWithPrefetch()
    })

    // Should still start workout
    expect(mockStartWorkout).toHaveBeenCalledTimes(1)
    expect(mockPrefetchExerciseSets).toHaveBeenCalledWith([1, 2, 3])
  })

  it('should return prefetch status from useExerciseSetsPrefetch', () => {
    const mockPrefetchStatus = {
      1: 'loading' as const,
      2: 'success' as const,
    }

    vi.mocked(useExerciseSetsPrefetch).mockReturnValue({
      prefetchExerciseSets: mockPrefetchExerciseSets,
      prefetchStatus: mockPrefetchStatus,
      isPrefetching: true,
      prefetchedExerciseIds: [2],
      getPrefetchProgress: vi.fn(),
    })

    const { result } = renderHook(() => useWorkoutStartPrefetch())

    expect(result.current.prefetchStatus).toEqual(mockPrefetchStatus)
    expect(result.current.isPrefetching).toBe(true)
    expect(result.current.prefetchedExerciseIds).toEqual([2])
  })

  it('should skip preview-skipped exercises when prefetching', async () => {
    // Mock with preview skips
    vi.mocked(useWorkoutStore).mockReturnValue({
      startWorkout: mockStartWorkout,
      exercises: mockExercises,
      workoutSession: null,
      previewExerciseSkips: new Set([1, 3]), // Skip exercises 1 and 3
    } as any)

    const { result } = renderHook(() => useWorkoutStartPrefetch())

    await act(async () => {
      await result.current.startWorkoutWithPrefetch()
    })

    // Should prefetch only non-skipped exercises (2, 4)
    expect(mockPrefetchExerciseSets).toHaveBeenCalledWith([2, 4])
  })
})
