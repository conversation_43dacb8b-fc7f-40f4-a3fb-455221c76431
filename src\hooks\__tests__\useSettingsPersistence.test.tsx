import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { renderHook, act, waitFor } from '@testing-library/react'
import { useSettingsPersistence } from '../useSettingsPersistence'
import { updateUserSettings } from '@/services/updateUserSettings'
import { toast } from '@/components/ui/toast'
import type { LocalSettings } from '../../types/settings'
import type { SettingsData } from '../useSettingsData'

// Mock the updateUserSettings service
vi.mock('@/services/updateUserSettings', () => ({
  updateUserSettings: vi.fn(),
}))

// Mock toast notifications
vi.mock('@/components/ui/toast', () => ({
  toast: vi.fn(),
}))

// Mock auth store
const mockAuthStore = {
  getCachedUserInfo: vi.fn(),
  clearUserInfoCache: vi.fn(),
  updateUserInfo: vi.fn(),
  isAuthenticated: true,
}

vi.mock('@/stores/authStore', () => ({
  useAuthStore: () => mockAuthStore,
}))

// Mock cache manager
const mockCacheManager = {
  delete: vi.fn(),
  clear: vi.fn(),
}

vi.mock('@/cache/CacheManager', () => ({
  getCacheManager: () => mockCacheManager,
}))

// Mock useSettingsData hook
const mockSettingsData: SettingsData = {
  quickMode: false,
  weightUnit: 'lbs',
  setStyle: 'Normal',
  repRange: { min: 6, max: 12 },
  weightIncrement: 5,
  warmupSets: 3,
}

vi.mock('../useSettingsData', () => ({
  useSettingsData: vi.fn(() => ({
    data: mockSettingsData,
    isLoading: false,
    error: null,
  })),
}))

const mockUpdateUserSettings = updateUserSettings as ReturnType<typeof vi.fn>
const mockToast = toast as ReturnType<typeof vi.fn>

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
  writable: true,
})

describe('useSettingsPersistence', () => {
  const mockInitialSettings: LocalSettings = {
    quickMode: false,
    weightUnit: 'lbs',
    setStyle: 'Normal',
    repsMin: 6,
    repsMax: 12,
    weightIncrement: 5,
    warmupSets: 3,
  }

  beforeEach(() => {
    vi.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
    localStorageMock.setItem.mockClear()
    localStorageMock.removeItem.mockClear()
    mockAuthStore.getCachedUserInfo.mockReturnValue({
      Email: '<EMAIL>',
      MassUnit: 'lbs',
      IsQuickMode: false,
      IsNormalSet: true,
      RepsMinimum: 6,
      RepsMaximum: 12,
      WeightIncrement: 5,
      WarmupSets: 3,
    })
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  /**
   * Test rationale: The hook should initialize with settings derived from
   * cached userInfo, providing immediate UI state without loading delays.
   */
  it('should initialize with settings from cached userInfo', () => {
    // Act: Render the hook
    const { result } = renderHook(() => useSettingsPersistence())

    // Assert: Should have initial state derived from cache
    expect(result.current.localSettings).toEqual(mockInitialSettings)
    expect(result.current.hasChanges).toBe(false)
    expect(result.current.isSaving).toBe(false)
    expect(result.current.saveError).toBeNull()
  })

  /**
   * Test rationale: The hook should track changes to determine when to show
   * the preview banner and enable the save button.
   */
  it('should track changes and update hasChanges flag', () => {
    const { result } = renderHook(() => useSettingsPersistence())

    // Initially no changes
    expect(result.current.hasChanges).toBe(false)

    // Act: Update a setting
    act(() => {
      result.current.updateSetting('quickMode', true)
    })

    // Assert: Should detect changes
    expect(result.current.hasChanges).toBe(true)
    expect(result.current.localSettings.quickMode).toBe(true)
  })

  /**
   * Test rationale: Save operation should manage loading states properly
   * to provide user feedback and prevent double-submissions.
   */
  it('should manage loading states during save operation', async () => {
    mockUpdateUserSettings.mockImplementation(
      () =>
        new Promise((resolve) =>
          setTimeout(() => resolve({ success: true }), 100)
        )
    )

    const { result } = renderHook(() => useSettingsPersistence())

    // Make a change to enable saving
    act(() => {
      result.current.updateSetting('quickMode', true)
    })

    // Act: Start save operation
    act(() => {
      result.current.saveSettings()
    })

    // Assert: Should show loading state immediately
    expect(result.current.isSaving).toBe(true)
    expect(result.current.saveError).toBeNull()

    // Wait for save completion
    await waitFor(() => {
      expect(result.current.isSaving).toBe(false)
    })

    // Assert: Should complete successfully
    expect(result.current.hasChanges).toBe(false) // Changes cleared after save
    expect(mockUpdateUserSettings).toHaveBeenCalledWith({
      quickMode: true,
      weightUnit: 'lbs',
      setStyle: 'Normal',
      repsMin: 6,
      repsMax: 12,
      weightIncrement: 5,
      warmupSets: 3,
    })
  })

  /**
   * Test rationale: Save errors should be captured and displayed to users
   * while preserving their changes for retry attempts.
   */
  it('should handle save errors and preserve changes', async () => {
    const mockError = new Error('Network error. Please try again.')
    mockUpdateUserSettings.mockRejectedValue(mockError)

    const { result } = renderHook(() => useSettingsPersistence())

    // Make a change
    act(() => {
      result.current.updateSetting('quickMode', true)
    })

    // Act: Attempt save
    act(() => {
      result.current.saveSettings()
    })

    // Wait for error
    await waitFor(() => {
      expect(result.current.isSaving).toBe(false)
    })

    // Assert: Should preserve error state and changes
    expect(result.current.saveError).toBe('Network error. Please try again.')
    expect(result.current.hasChanges).toBe(true) // Changes preserved for retry
    expect(result.current.localSettings.quickMode).toBe(true)
  })

  /**
   * Test rationale: FAILING TEST - The hook should allow state updates to proceed
   * even during validation errors to keep UI responsive, but prevent saves.
   */
  it('should update state immediately during validation errors for responsive UI', () => {
    const { result } = renderHook(() =>
      useSettingsPersistence(mockInitialSettings)
    )

    // Act: Set invalid rep range (min > max) - this should still update state
    act(() => {
      result.current.updateSetting('repsMin', 15) // Invalid: exceeds max (12)
    })

    // Assert: State should update immediately (responsive UI)
    expect(result.current.localSettings.repsMin).toBe(15)
    expect(result.current.hasChanges).toBe(true)
    expect(result.current.saveError).toContain(
      'Minimum reps cannot be greater than maximum reps'
    )
  })

  /**
   * Test rationale: FAILING TEST - Sequential updateSetting calls should not cause race conditions
   * This reproduces the CardLayout issue where RepRangeInput onChange triggers two updateSetting calls
   */
  it('should handle sequential updateSetting calls without race conditions', () => {
    const { result } = renderHook(() =>
      useSettingsPersistence(mockInitialSettings)
    )

    // Simulate CardLayout's RepRangeInput onChange behavior:
    // onChange={(values) => {
    //   updateSetting('repsMin', values.minReps)  // First call
    //   updateSetting('repsMax', values.maxReps)  // Second call
    // }}

    act(() => {
      // User clicks increment button: { minReps: 7, maxReps: 12 }
      result.current.updateSetting('repsMin', 7) // Should not cause validation error
      result.current.updateSetting('repsMax', 12) // Should not be blocked by first call
    })

    // Assert: Both values should be updated correctly
    expect(result.current.localSettings.repsMin).toBe(7)
    expect(result.current.localSettings.repsMax).toBe(12)
    expect(result.current.saveError).toBeNull() // No validation errors
    expect(result.current.hasChanges).toBe(true)
  })

  /**
   * Test rationale: FAILING TEST - Boundary case sequential updates should not freeze UI
   */
  it('should handle boundary validation in sequential calls without blocking UI', () => {
    const { result } = renderHook(() =>
      useSettingsPersistence(mockInitialSettings)
    )

    // Start with boundary values: min=11, max=12
    act(() => {
      result.current.updateSetting('repsMin', 11)
      result.current.updateSetting('repsMax', 12)
    })

    // Clear any existing errors
    expect(result.current.saveError).toBeNull()

    // Simulate user trying to increment min to equal max (invalid)
    // This happens when RepRangeInput calls onChange with { minReps: 12, maxReps: 12 }
    act(() => {
      result.current.updateSetting('repsMin', 12) // This will trigger validation error
      result.current.updateSetting('repsMax', 12) // This should still work for UI consistency
    })

    // Assert: UI state should update (responsive) but validation error should exist
    expect(result.current.localSettings.repsMin).toBe(12) // UI responsive
    expect(result.current.localSettings.repsMax).toBe(12) // UI responsive
    expect(result.current.saveError).toContain(
      'Minimum reps cannot be greater than maximum reps'
    )
    expect(result.current.hasChanges).toBe(true) // Changes tracked for potential fix
  })

  /**
   * Test rationale: Auto-save should be blocked when validation errors exist
   */
  it('should prevent auto-save when validation errors exist', async () => {
    vi.useFakeTimers()
    mockUpdateUserSettings.mockResolvedValue({ success: true })

    const { result } = renderHook(() =>
      useSettingsPersistence(mockInitialSettings)
    )

    // Act: Set invalid rep range that creates validation error
    act(() => {
      result.current.updateSetting('repsMin', 15) // Invalid: exceeds max (12)
    })

    // Trigger auto-save timer
    act(() => {
      vi.advanceTimersByTime(2000)
    })

    // Assert: Auto-save should not trigger when validation error exists
    expect(mockUpdateUserSettings).not.toHaveBeenCalled()
    expect(result.current.saveError).toContain(
      'Minimum reps cannot be greater than maximum reps'
    )
    expect(result.current.isSaving).toBe(false)

    vi.restoreAllMocks()
  })

  /**
   * Test rationale: The hook should validate settings before attempting
   * to save, preventing invalid API calls.
   */
  it('should validate settings before saving', async () => {
    const { result } = renderHook(() => useSettingsPersistence())

    // Act: Set invalid rep range (min > max)
    act(() => {
      result.current.updateSetting('repsMin', 15)
      result.current.updateSetting('repsMax', 10)
    })

    await act(async () => {
      await result.current.saveSettings()
    })

    // Assert: Should not call API with invalid data
    expect(mockUpdateUserSettings).not.toHaveBeenCalled()
    expect(result.current.saveError).toContain('validation')
    expect(result.current.isSaving).toBe(false)
  })

  /**
   * Test rationale: After successful save, the hook should update the
   * auth store cache and clear local change flags.
   */
  it('should update auth store and clear changes after successful save', async () => {
    mockUpdateUserSettings.mockResolvedValue({ success: true })

    const { result } = renderHook(() => useSettingsPersistence())

    // Make changes
    act(() => {
      result.current.updateSetting('quickMode', true)
      result.current.updateSetting('weightUnit', 'kg')
    })

    // Act: Save settings
    act(() => {
      result.current.saveSettings()
    })

    await waitFor(() => {
      expect(result.current.isSaving).toBe(false)
    })

    // Assert: Should update auth store with new values
    expect(mockAuthStore.updateUserInfo).toHaveBeenCalledWith({
      IsQuickMode: true,
      MassUnit: 'kg',
    })

    // Should clear related caches
    expect(mockCacheManager.delete).toHaveBeenCalledWith('userInfo', 'userInfo')

    // Should reset change tracking
    expect(result.current.hasChanges).toBe(false)
  })

  /**
   * Test rationale: Weight unit changes should trigger recalculation of
   * weight increment to maintain proper scaling (5 lbs ≈ 2.5 kg).
   */
  it('should recalculate weight increment when unit changes', () => {
    const { result } = renderHook(() => useSettingsPersistence())

    // Act: Change from lbs to kg
    act(() => {
      result.current.updateSetting('weightUnit', 'kg')
    })

    // Assert: Should auto-adjust weight increment
    expect(result.current.localSettings.weightIncrement).toBe(2.5) // 5 lbs ≈ 2.5 kg
    expect(result.current.hasChanges).toBe(true)

    // Act: Change back to lbs
    act(() => {
      result.current.updateSetting('weightUnit', 'lbs')
    })

    // Assert: Should convert back
    expect(result.current.localSettings.weightIncrement).toBe(5) // 2.5 kg ≈ 5 lbs
  })

  /**
   * Test rationale: The hook should handle concurrent setting updates
   * properly without race conditions or state corruption.
   */
  it('should handle rapid setting updates without race conditions', () => {
    const { result } = renderHook(() => useSettingsPersistence())

    // Act: Rapid updates to multiple settings
    act(() => {
      result.current.updateSetting('quickMode', true)
      result.current.updateSetting('weightUnit', 'kg')
      result.current.updateSetting('quickMode', false) // Toggle back
      result.current.updateSetting('repsMin', 8)
    })

    // Assert: Should have final state
    expect(result.current.localSettings).toMatchObject({
      quickMode: false, // Final value
      weightUnit: 'kg',
      repsMin: 8,
      weightIncrement: 2.5, // Auto-converted for kg
    })
    expect(result.current.hasChanges).toBe(true)
  })

  /**
   * Test rationale: The hook should prevent saves when there are no changes
   * to avoid unnecessary API calls and provide clear user feedback.
   */
  it('should prevent saving when no changes exist', () => {
    const { result } = renderHook(() => useSettingsPersistence())

    // Act: Attempt to save without changes
    act(() => {
      result.current.saveSettings()
    })

    // Assert: Should not make API call
    expect(mockUpdateUserSettings).not.toHaveBeenCalled()
    expect(result.current.saveError).toBe('No changes to save')
    expect(result.current.isSaving).toBe(false)
  })

  /**
   * Test rationale: When user is not authenticated, the hook should handle
   * this gracefully and provide appropriate error messaging.
   */
  it('should handle unauthenticated state gracefully', async () => {
    mockAuthStore.isAuthenticated = false
    mockAuthStore.getCachedUserInfo.mockReturnValue(null)

    const { result } = renderHook(() => useSettingsPersistence())

    // Make a change
    act(() => {
      result.current.updateSetting('quickMode', true)
    })

    // Act: Attempt to save while unauthenticated
    act(() => {
      result.current.saveSettings()
    })

    await waitFor(() => {
      expect(result.current.isSaving).toBe(false)
    })

    // Assert: Should handle unauthenticated state
    expect(result.current.saveError).toContain('authentication')
    expect(mockUpdateUserSettings).not.toHaveBeenCalled()
  })

  /**
   * Test rationale: The hook should provide a reset function to discard
   * unsaved changes and return to the original state.
   */
  it('should reset changes to original values', () => {
    const { result } = renderHook(() => useSettingsPersistence())

    // Make multiple changes
    act(() => {
      result.current.updateSetting('quickMode', true)
      result.current.updateSetting('weightUnit', 'kg')
      result.current.updateSetting('repsMin', 8)
    })

    expect(result.current.hasChanges).toBe(true)

    // Act: Reset changes
    act(() => {
      result.current.resetChanges()
    })

    // Assert: Should return to original state
    expect(result.current.localSettings).toEqual(mockInitialSettings)
    expect(result.current.hasChanges).toBe(false)
    expect(result.current.saveError).toBeNull()
  })

  /**
   * AUTO-SAVE TESTS
   * Test rationale: The hook should automatically save settings after
   * a debounce period (2 seconds) to reduce friction.
   */
  describe('Auto-save functionality', () => {
    beforeEach(() => {
      vi.useFakeTimers()
      mockToast.mockClear()
    })

    afterEach(() => {
      vi.clearAllTimers()
      vi.restoreAllMocks()
    })

    it('should auto-save after 2 second debounce', async () => {
      mockUpdateUserSettings.mockResolvedValue({ success: true })

      const { result } = renderHook(() => useSettingsPersistence())

      // Make a change
      act(() => {
        result.current.updateSetting('quickMode', true)
      })

      // Should not save immediately
      expect(mockUpdateUserSettings).not.toHaveBeenCalled()
      expect(result.current.hasChanges).toBe(true)

      // Advance time by 1 second - should not save yet
      act(() => {
        vi.advanceTimersByTime(1000)
      })
      expect(mockUpdateUserSettings).not.toHaveBeenCalled()

      // Advance time to complete debounce (2 seconds total)
      act(() => {
        vi.advanceTimersByTime(1000)
      })

      // Should trigger save
      await waitFor(() => {
        expect(mockUpdateUserSettings).toHaveBeenCalledWith({
          quickMode: true,
          weightUnit: 'lbs',
          setStyle: 'Normal',
          repsMin: 6,
          repsMax: 12,
          weightIncrement: 5,
          warmupSets: 3,
        })
      })

      // Should show success toast
      expect(mockToast).toHaveBeenCalledWith({
        description: 'Settings saved',
        variant: 'subtle',
        duration: 3000,
      })

      // Should clear hasChanges
      expect(result.current.hasChanges).toBe(false)
    })

    it('should reset debounce timer on rapid changes', async () => {
      mockUpdateUserSettings.mockResolvedValue({ success: true })

      const { result } = renderHook(() => useSettingsPersistence())

      // Make multiple rapid changes
      act(() => {
        result.current.updateSetting('quickMode', true)
      })

      act(() => {
        vi.advanceTimersByTime(1000) // 1 second passed
      })

      // Make another change before debounce completes
      act(() => {
        result.current.updateSetting('weightUnit', 'kg')
      })

      act(() => {
        vi.advanceTimersByTime(1000) // 1 second from last change
      })

      // Should not have saved yet
      expect(mockUpdateUserSettings).not.toHaveBeenCalled()

      // Make final change
      act(() => {
        result.current.updateSetting('repsMin', 8)
      })

      // Complete debounce from final change
      act(() => {
        vi.advanceTimersByTime(2000)
      })

      // Should save with all changes
      await waitFor(() => {
        expect(mockUpdateUserSettings).toHaveBeenCalledWith({
          quickMode: true,
          weightUnit: 'kg',
          setStyle: 'Normal',
          repsMin: 8,
          repsMax: 12,
          weightIncrement: 2.5, // Auto-converted for kg
          warmupSets: 3,
        })
      })

      // Should have made only one API call
      expect(mockUpdateUserSettings).toHaveBeenCalledTimes(1)
    })

    it('should show error toast on auto-save failure', async () => {
      const mockError = new Error('Network error')
      mockUpdateUserSettings.mockRejectedValue(mockError)

      const { result } = renderHook(() => useSettingsPersistence())

      // Make a change
      act(() => {
        result.current.updateSetting('quickMode', true)
      })

      // Trigger auto-save
      act(() => {
        vi.advanceTimersByTime(2000)
      })

      // Wait for save to fail
      await waitFor(() => {
        expect(mockToast).toHaveBeenCalledWith({
          description: 'Failed to save settings. Retrying...',
          variant: 'error',
          duration: 5000,
        })
      })

      // Changes should be preserved for retry
      expect(result.current.hasChanges).toBe(true)
      expect(result.current.localSettings.quickMode).toBe(true)
    })

    it('should retry with exponential backoff on failure', async () => {
      // First 2 attempts fail, third succeeds
      mockUpdateUserSettings
        .mockRejectedValueOnce(new Error('Network error'))
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({ success: true })

      const { result } = renderHook(() => useSettingsPersistence())

      // Make a change
      act(() => {
        result.current.updateSetting('quickMode', true)
      })

      // Initial auto-save attempt (after 2s debounce)
      act(() => {
        vi.advanceTimersByTime(2000)
      })

      await waitFor(() => {
        expect(mockUpdateUserSettings).toHaveBeenCalledTimes(1)
      })

      // First retry after 1 second
      act(() => {
        vi.advanceTimersByTime(1000)
      })

      await waitFor(() => {
        expect(mockUpdateUserSettings).toHaveBeenCalledTimes(2)
      })

      // Second retry after 2 seconds (exponential backoff)
      act(() => {
        vi.advanceTimersByTime(2000)
      })

      await waitFor(() => {
        expect(mockUpdateUserSettings).toHaveBeenCalledTimes(3)
      })

      // Should show success toast after eventual success
      expect(mockToast).toHaveBeenCalledWith({
        description: 'Settings saved',
        variant: 'subtle',
        duration: 3000,
      })

      // Changes should be cleared
      expect(result.current.hasChanges).toBe(false)
    })

    it('should cancel auto-save timer on component unmount', () => {
      const clearTimeoutSpy = vi.spyOn(global, 'clearTimeout')

      const { result, unmount } = renderHook(() => useSettingsPersistence())

      // Make a change to start auto-save timer
      act(() => {
        result.current.updateSetting('quickMode', true)
      })

      // Unmount before auto-save completes
      unmount()

      // Should clean up timer
      expect(clearTimeoutSpy).toHaveBeenCalled()

      // Advance time - should not trigger save
      act(() => {
        vi.advanceTimersByTime(2000)
      })

      expect(mockUpdateUserSettings).not.toHaveBeenCalled()
    })

    it('should handle auth token expiry during auto-save', async () => {
      // Mock 401 response (auth expired)
      mockUpdateUserSettings.mockRejectedValue({
        response: { status: 401 },
        description: 'Authentication failed',
      })

      const { result } = renderHook(() => useSettingsPersistence())

      // Make a change
      act(() => {
        result.current.updateSetting('quickMode', true)
      })

      // Trigger auto-save
      act(() => {
        vi.advanceTimersByTime(2000)
      })

      // Wait for auth error
      await waitFor(() => {
        expect(mockToast).toHaveBeenCalledWith({
          description: 'Please log in again to save settings',
          variant: 'error',
          duration: 7000,
        })
      })

      // Changes should be preserved
      expect(result.current.hasChanges).toBe(true)
      expect(result.current.localSettings.quickMode).toBe(true)

      // Should not retry on auth errors
      act(() => {
        vi.advanceTimersByTime(10000)
      })

      // Should have only attempted once
      expect(mockUpdateUserSettings).toHaveBeenCalledTimes(1)
    })

    it('should queue saves properly when changes occur during save', async () => {
      // Mock delayed save
      mockUpdateUserSettings.mockImplementation(
        () =>
          new Promise((resolve) =>
            setTimeout(() => resolve({ success: true }), 500)
          )
      )

      const { result } = renderHook(() => useSettingsPersistence())

      // Make first change
      act(() => {
        result.current.updateSetting('quickMode', true)
      })

      // Trigger first auto-save
      act(() => {
        vi.advanceTimersByTime(2000)
      })

      // Make another change while first save is in progress
      act(() => {
        result.current.updateSetting('weightUnit', 'kg')
      })

      // Complete first save
      act(() => {
        vi.advanceTimersByTime(500)
      })

      await waitFor(() => {
        expect(mockUpdateUserSettings).toHaveBeenCalledTimes(1)
      })

      // Second save should trigger after debounce
      act(() => {
        vi.advanceTimersByTime(2000)
      })

      await waitFor(() => {
        expect(mockUpdateUserSettings).toHaveBeenCalledTimes(2)
      })

      // Second save should include both changes
      expect(mockUpdateUserSettings).toHaveBeenLastCalledWith(
        expect.objectContaining({
          quickMode: true,
          weightUnit: 'kg',
          weightIncrement: 2.5, // Auto-converted
        })
      )
    })

    it('should stop retrying after max attempts', async () => {
      // All attempts fail
      mockUpdateUserSettings.mockRejectedValue(new Error('Persistent error'))

      const { result } = renderHook(() => useSettingsPersistence())

      // Make a change
      act(() => {
        result.current.updateSetting('quickMode', true)
      })

      // Initial attempt
      act(() => {
        vi.advanceTimersByTime(2000)
      })

      // Retry attempts with exponential backoff
      // 1s, 2s, 4s, 8s = total 15s for 4 retries
      act(() => {
        vi.advanceTimersByTime(15000)
      })

      await waitFor(() => {
        // Initial + 4 retries = 5 total attempts
        expect(mockUpdateUserSettings).toHaveBeenCalledTimes(5)
      })

      // Should show final error
      expect(mockToast).toHaveBeenLastCalledWith({
        description: 'Unable to save settings. Please try again later.',
        variant: 'error',
        duration: 10000,
      })

      // Advance more time - should not retry anymore
      act(() => {
        vi.advanceTimersByTime(20000)
      })

      // Should still be 5 attempts
      expect(mockUpdateUserSettings).toHaveBeenCalledTimes(5)
    })
  })
})

/**
 * Test Coverage Analysis:
 *
 * This test suite covers:
 * ✅ Hook initialization with cached data
 * ✅ Change tracking and hasChanges flag management
 * ✅ Loading state management during save operations
 * ✅ Error handling with change preservation
 * ✅ Input validation before API calls
 * ✅ Auth store updates after successful save
 * ✅ Weight unit conversion logic
 * ✅ Concurrent/rapid updates handling
 * ✅ Save prevention when no changes exist
 * ✅ Unauthenticated state handling
 * ✅ Change reset functionality
 *
 * Expected coverage: 95%+
 * Test-first approach: Written before useSettingsPersistence exists
 *
 * These tests define the complete API that the hook must implement:
 * - localSettings: LocalSettings
 * - hasChanges: boolean
 * - isSaving: boolean
 * - saveError: string | null
 * - updateSetting: (key: keyof LocalSettings, value: any) => void
 * - saveSettings: () => Promise<void>
 * - resetChanges: () => void
 */
