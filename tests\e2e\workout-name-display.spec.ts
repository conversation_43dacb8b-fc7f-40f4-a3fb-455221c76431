import { test, expect } from '@playwright/test'
import { setupAuthenticatedUser } from './helpers/auth-helper'

test.describe('Workout Name Display', () => {
  test.beforeEach(async ({ page }) => {
    await setupAuthenticatedUser(page)
  })

  test('should display workout name on program page button', async ({
    page,
  }) => {
    // Navigate to program page
    await page.goto('/program')

    // Wait for program data to load
    await page.waitForSelector('[data-testid="program-overview-page"]')

    // Wait for the floating CTA button
    const ctaButton = await page.waitForSelector(
      '[data-testid="start-workout-button"]'
    )

    // Get button text
    const buttonText = await ctaButton.textContent()

    // Should show workout name in button
    expect(buttonText).toContain('Open Workout:')
    // Should not just show default text
    expect(buttonText).not.toBe('Open Workout')
  })

  test('should display workout name in navigation on workout page', async ({
    page,
  }) => {
    // Navigate to workout page
    await page.goto('/workout')

    // Wait for workout to load
    await page.waitForSelector('[data-testid="workout-overview-container"]')

    // Check navigation title
    const navTitle = await page.waitForSelector('[data-testid="nav-title"]')
    const titleText = await navTitle.textContent()

    // Should show actual workout name, not just "Workout"
    expect(titleText).not.toBe('Workout')
    expect(titleText?.length).toBeGreaterThan(0)
  })

  test('should handle long workout names gracefully', async ({ page }) => {
    // Navigate to program page
    await page.goto('/program')

    // Wait for the button
    const ctaButton = await page.waitForSelector(
      '[data-testid="start-workout-button"]'
    )

    // Check that button doesn't overflow
    const buttonBox = await ctaButton.boundingBox()
    const viewportSize = page.viewportSize()

    // Button should fit within viewport with padding
    expect(buttonBox?.width).toBeLessThan(viewportSize!.width - 32) // 16px padding on each side
  })

  test('should update workout name when navigating between pages', async ({
    page,
  }) => {
    // Start on program page
    await page.goto('/program')

    // Get workout name from button
    const ctaButton = await page.waitForSelector(
      '[data-testid="start-workout-button"]'
    )
    const programPageText = await ctaButton.textContent()

    // Navigate to workout
    await ctaButton.click()

    // Wait for workout page
    await page.waitForSelector('[data-testid="workout-overview-container"]')

    // Navigate back
    await page.goBack()

    // Check button still shows workout name
    const ctaButtonAfterNav = await page.waitForSelector(
      '[data-testid="start-workout-button"]'
    )
    const textAfterNav = await ctaButtonAfterNav.textContent()

    expect(textAfterNav).toBe(programPageText)
  })
})
