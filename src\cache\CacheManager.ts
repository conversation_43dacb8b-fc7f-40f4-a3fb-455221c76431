/* eslint-disable no-restricted-syntax, no-await-in-loop, class-methods-use-this */
/**
 * Core CacheManager implementation
 *
 * This is the main cache manager that provides a high-level interface
 * for cache operations. It adds features like namespacing, TTL management,
 * and statistics on top of the underlying cache adapters.
 */

import type {
  CacheManager as ICacheManager,
  CacheAdapter,
  CacheOptions,
  CacheMetadata,
  CacheStats,
  CacheManagerConfig,
  NamespaceStats,
} from './types'
import {
  CacheError,
  CacheKeyError,
  CacheSerializationError,
  DEFAULT_CACHE_CONFIG,
  CACHE_ENTRY_VERSION,
} from './types'

/**
 * Core cache manager implementation
 */
export class CacheManager implements ICacheManager {
  private readonly adapter: CacheAdapter

  private readonly config: CacheManagerConfig

  private cleanupTimer?: NodeJS.Timeout

  private stats: CacheStats

  constructor(adapter: CacheAdapter, config: Partial<CacheManagerConfig> = {}) {
    this.adapter = adapter
    this.config = { ...DEFAULT_CACHE_CONFIG, ...config }
    this.stats = this.initializeStats()

    if (this.config.enableStats) {
      this.startCleanup()
    }
  }

  /**
   * Get a value from the cache
   */
  async get<T>(key: string, namespace?: string): Promise<T | null> {
    try {
      this.validateKey(key)
      const fullKey = this.buildKey(key, namespace)

      const entry = await this.adapter.get<T>(fullKey)

      if (entry) {
        // Update stats
        if (this.config.enableStats) {
          this.updateHitStats(namespace || this.config.defaultNamespace)
        }
        return entry.value
      }

      // Update miss stats
      if (this.config.enableStats) {
        this.updateMissStats(namespace || this.config.defaultNamespace)
      }

      return null
    } catch (error) {
      throw new CacheError(
        `Failed to get cache entry for key "${key}"`,
        'GET_ERROR',
        error instanceof Error ? error : undefined
      )
    }
  }

  /**
   * Set a value in the cache
   */
  async set<T>(
    key: string,
    value: T,
    options: CacheOptions = {}
  ): Promise<void> {
    try {
      this.validateKey(key)
      this.validateValue(value)

      const namespace = options.namespace || this.config.defaultNamespace
      const fullKey = this.buildKey(key, namespace)
      const ttl = options.ttl || this.config.defaultTTL

      const metadata: CacheMetadata = {
        size: 0, // Will be calculated by adapter
        created: Date.now(),
        accessed: Date.now(),
        expires: ttl > 0 ? Date.now() + ttl : 0,
        namespace,
        priority: options.priority || 1,
        compressed: options.compress || false,
        version: CACHE_ENTRY_VERSION,
      }

      await this.adapter.set(fullKey, value, metadata)

      // Update namespace stats
      if (this.config.enableStats) {
        this.updateNamespaceStats(namespace, 'set')
      }
    } catch (error) {
      throw new CacheError(
        `Failed to set cache entry for key "${key}"`,
        'SET_ERROR',
        error instanceof Error ? error : undefined
      )
    }
  }

  /**
   * Delete a value from the cache
   */
  async delete(key: string, namespace?: string): Promise<void> {
    try {
      this.validateKey(key)
      const fullKey = this.buildKey(key, namespace)

      await this.adapter.delete(fullKey)

      // Update namespace stats
      if (this.config.enableStats) {
        this.updateNamespaceStats(
          namespace || this.config.defaultNamespace,
          'delete'
        )
      }
    } catch (error) {
      throw new CacheError(
        `Failed to delete cache entry for key "${key}"`,
        'DELETE_ERROR',
        error instanceof Error ? error : undefined
      )
    }
  }

  /**
   * Clear cache entries
   */
  async clear(namespace?: string): Promise<void> {
    try {
      if (namespace) {
        // Clear specific namespace
        const keys = await this.getAllKeys(namespace)
        await Promise.all(
          keys.map(async (key) => {
            const fullKey = this.buildKey(key, namespace)
            await this.adapter.delete(fullKey)
          })
        )
      } else {
        // Clear all
        await this.adapter.clear()
      }

      // Reset stats for cleared namespace(s)
      if (this.config.enableStats) {
        if (namespace) {
          delete this.stats.namespaces[namespace]
        } else {
          this.stats = this.initializeStats()
        }
      }
    } catch (error) {
      throw new CacheError(
        `Failed to clear cache${namespace ? ` for namespace "${namespace}"` : ''}`,
        'CLEAR_ERROR',
        error instanceof Error ? error : undefined
      )
    }
  }

  /**
   * Get multiple values from the cache
   */
  async getMany<T>(
    keys: string[],
    namespace?: string
  ): Promise<Map<string, T>> {
    const results = new Map<string, T>()

    if (this.adapter.supportsBatch() && this.adapter.getMany) {
      // Use batch operation if supported
      const fullKeys = keys.map((key) => this.buildKey(key, namespace))
      const entries = await this.adapter.getMany<T>(fullKeys)

      for (const [fullKey, entry] of entries) {
        const originalKey = this.extractKey(fullKey, namespace)
        results.set(originalKey, entry.value)
      }
    } else {
      // Fall back to individual operations
      await Promise.all(
        keys.map(async (key) => {
          const value = await this.get<T>(key, namespace)
          if (value !== null) {
            results.set(key, value)
          }
        })
      )
    }

    return results
  }

  /**
   * Set multiple values in the cache
   */
  async setMany<T>(
    entries: Map<string, T>,
    options: CacheOptions = {}
  ): Promise<void> {
    if (this.adapter.supportsBatch() && this.adapter.setMany) {
      // Use batch operation if supported
      const namespace = options.namespace || this.config.defaultNamespace
      const ttl = options.ttl || this.config.defaultTTL

      const batchEntries = new Map<
        string,
        { value: T; metadata: CacheMetadata }
      >()

      for (const [key, value] of entries) {
        const fullKey = this.buildKey(key, namespace)
        const metadata: CacheMetadata = {
          size: 0,
          created: Date.now(),
          accessed: Date.now(),
          expires: ttl > 0 ? Date.now() + ttl : 0,
          namespace,
          priority: options.priority || 1,
          compressed: options.compress || false,
          version: CACHE_ENTRY_VERSION,
        }

        batchEntries.set(fullKey, { value, metadata })
      }

      await this.adapter.setMany(batchEntries)
    } else {
      // Fall back to individual operations
      await Promise.all(
        Array.from(entries.entries()).map(async ([key, value]) => {
          await this.set(key, value, options)
        })
      )
    }
  }

  /**
   * Delete multiple values from the cache
   */
  async deleteMany(keys: string[], namespace?: string): Promise<void> {
    if (this.adapter.supportsBatch() && this.adapter.deleteMany) {
      // Use batch operation if supported
      const fullKeys = keys.map((key) => this.buildKey(key, namespace))
      await this.adapter.deleteMany(fullKeys)
    } else {
      // Fall back to individual operations
      await Promise.all(
        keys.map(async (key) => {
          await this.delete(key, namespace)
        })
      )
    }
  }

  /**
   * Get metadata for a cache entry
   */
  async getMetadata(
    key: string,
    namespace?: string
  ): Promise<CacheMetadata | null> {
    try {
      this.validateKey(key)
      const fullKey = this.buildKey(key, namespace)

      const entry = await this.adapter.get(fullKey)
      return entry?.metadata || null
    } catch (error) {
      throw new CacheError(
        `Failed to get metadata for key "${key}"`,
        'METADATA_ERROR',
        error instanceof Error ? error : undefined
      )
    }
  }

  /**
   * Get all keys in the cache
   */
  async getAllKeys(namespace?: string): Promise<string[]> {
    try {
      const allKeys = await this.adapter.getAllKeys()

      if (namespace) {
        const prefix = `${namespace}:`
        return allKeys
          .filter((key) => key.startsWith(prefix))
          .map((key) => key.slice(prefix.length))
      }

      return allKeys.map((key) => this.extractKey(key))
    } catch (error) {
      throw new CacheError(
        'Failed to get cache keys',
        'KEYS_ERROR',
        error instanceof Error ? error : undefined
      )
    }
  }

  /**
   * Get the total size of the cache
   */
  async getSize(namespace?: string): Promise<number> {
    if (namespace) {
      // Calculate size for specific namespace
      const keys = await this.getAllKeys(namespace)
      let totalSize = 0

      for (const key of keys) {
        const metadata = await this.getMetadata(key, namespace)
        if (metadata) {
          totalSize += metadata.size
        }
      }

      return totalSize
    }

    return this.adapter.getSize()
  }

  /**
   * Invalidate cache entries matching a pattern
   */
  async invalidate(
    pattern: string | RegExp,
    namespace?: string
  ): Promise<void> {
    const keys = await this.getAllKeys(namespace)
    const keysToDelete = keys.filter((key) => {
      if (typeof pattern === 'string') {
        return key.includes(pattern)
      }
      return pattern.test(key)
    })

    await this.deleteMany(keysToDelete, namespace)
  }

  /**
   * Manually trigger cache cleanup
   */
  async cleanup(): Promise<void> {
    if (
      'cleanup' in this.adapter &&
      typeof this.adapter.cleanup === 'function'
    ) {
      await this.adapter.cleanup()
    }
  }

  /**
   * Get cache statistics
   */
  async getStats(): Promise<CacheStats> {
    return { ...this.stats }
  }

  /**
   * Start background cleanup process
   */
  startCleanup(): void {
    if (this.cleanupTimer) {
      return
    }

    this.cleanupTimer = setInterval(() => {
      this.cleanup().catch((error) => {
        console.warn('Cache cleanup failed:', error)
      })
    }, this.config.cleanupInterval)

    // Don't keep the process alive for cleanup
    if (typeof this.cleanupTimer === 'object' && 'unref' in this.cleanupTimer) {
      this.cleanupTimer.unref()
    }
  }

  /**
   * Stop background cleanup process
   */
  stopCleanup(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = undefined
    }
  }

  /**
   * Destroy the cache manager and clean up resources
   */
  destroy(): void {
    this.stopCleanup()
    if (
      'destroy' in this.adapter &&
      typeof this.adapter.destroy === 'function'
    ) {
      this.adapter.destroy()
    }
  }

  /**
   * Build a namespaced key
   */
  private buildKey(key: string, namespace?: string): string {
    const ns = namespace || this.config.defaultNamespace
    return `${ns}:${key}`
  }

  /**
   * Extract the original key from a namespaced key
   */
  private extractKey(fullKey: string, namespace?: string): string {
    const ns = namespace || this.config.defaultNamespace
    const prefix = `${ns}:`

    if (fullKey.startsWith(prefix)) {
      return fullKey.slice(prefix.length)
    }

    // If no namespace prefix, try to extract from any namespace
    const colonIndex = fullKey.indexOf(':')
    return colonIndex > 0 ? fullKey.slice(colonIndex + 1) : fullKey
  }

  /**
   * Validate a cache key
   */
  private validateKey(key: string): void {
    if (!key || typeof key !== 'string') {
      throw new CacheKeyError('Cache key must be a non-empty string')
    }

    if (key.includes(':')) {
      throw new CacheKeyError('Cache key cannot contain colon (:) character')
    }
  }

  /**
   * Validate a cache value
   */
  private validateValue(value: unknown): void {
    if (value === undefined) {
      throw new CacheSerializationError('Cannot cache undefined values')
    }
  }

  /**
   * Initialize cache statistics
   */
  private initializeStats(): CacheStats {
    return {
      entryCount: 0,
      totalSize: 0,
      hits: 0,
      misses: 0,
      hitRatio: 0,
      evictions: 0,
      expirations: 0,
      namespaces: {},
    }
  }

  /**
   * Update hit statistics
   */
  private updateHitStats(namespace: string): void {
    this.stats.hits++
    this.stats.hitRatio =
      this.stats.hits / (this.stats.hits + this.stats.misses)

    if (!this.stats.namespaces[namespace]) {
      this.stats.namespaces[namespace] = CacheManager.initializeNamespaceStats()
    }

    this.stats.namespaces[namespace].hits++
    this.stats.namespaces[namespace].lastAccess = Date.now()
  }

  /**
   * Update miss statistics
   */
  private updateMissStats(namespace: string): void {
    this.stats.misses++
    this.stats.hitRatio =
      this.stats.hits / (this.stats.hits + this.stats.misses)

    if (!this.stats.namespaces[namespace]) {
      this.stats.namespaces[namespace] = CacheManager.initializeNamespaceStats()
    }

    this.stats.namespaces[namespace].misses++
  }

  /**
   * Update namespace statistics
   */
  private updateNamespaceStats(
    namespace: string,
    operation: 'set' | 'delete'
  ): void {
    if (!this.stats.namespaces[namespace]) {
      this.stats.namespaces[namespace] = CacheManager.initializeNamespaceStats()
    }

    const nsStats = this.stats.namespaces[namespace]

    if (operation === 'set') {
      nsStats.entryCount++
      this.stats.entryCount++
    } else if (operation === 'delete') {
      nsStats.entryCount = Math.max(0, nsStats.entryCount - 1)
      this.stats.entryCount = Math.max(0, this.stats.entryCount - 1)
    }
  }

  /**
   * Initialize namespace statistics
   */
  private static initializeNamespaceStats(): NamespaceStats {
    return {
      entryCount: 0,
      totalSize: 0,
      hits: 0,
      misses: 0,
      lastAccess: 0,
    }
  }
}
