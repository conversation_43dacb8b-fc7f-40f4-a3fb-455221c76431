/**
 * User Settings Types
 */

export interface UserSettings {
  isQuickMode: boolean | null // Can be true/false/null like mobile app
  isStrengthPhase: boolean
  isFreePlan: boolean
  isFirstWorkoutOfStrengthPhase: boolean
  lightSessionDays: number | null
  setStyle?: 'Normal' | 'RestPause' | 'Pyramid' | undefined // User's preferred set style
  isPyramid?: boolean // Whether user prefers pyramid sets
}
