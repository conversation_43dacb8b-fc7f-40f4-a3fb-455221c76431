'use client'

import { useSettingsPersistence } from '@/hooks/useSettingsPersistence'
import SettingsPageSkeleton from './SettingsPageSkeleton'
import SettingsHeader from '@/components/settings/SettingsHeader'
import CardLayout from '@/components/settings/layouts/CardLayout'

export default function SettingsPageClient() {
  const { localSettings, updateSetting, isSaving } = useSettingsPersistence()

  if (!localSettings) {
    return <SettingsPageSkeleton />
  }

  return (
    <main className="min-h-screen bg-background">
      <SettingsHeader />

      <div className="px-4 py-6" data-testid="settings-content">
        <CardLayout
          settings={localSettings}
          updateSetting={updateSetting}
          isSaving={isSaving}
        />
      </div>
    </main>
  )
}
