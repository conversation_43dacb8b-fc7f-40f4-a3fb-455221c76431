import { WorkoutCacheService } from './workoutCacheService'
import { offlineQueue } from './offlineQueue'
import type {
  WorkoutTemplateModel,
  RecommendationModel,
  WorkoutLogSerieModel,
} from '@/types'

/**
 * Service for handling workout execution in offline mode
 */
export class OfflineWorkoutService {
  private workoutCacheService: WorkoutCacheService

  constructor() {
    this.workoutCacheService = new WorkoutCacheService()
  }

  /**
   * Get cached workout data for offline execution
   */
  async getCachedWorkout(
    workoutId: string
  ): Promise<WorkoutTemplateModel | null> {
    try {
      return await this.workoutCacheService.getCachedWorkoutDetails(workoutId)
    } catch (error) {
      // Failed to get cached workout - error logged for debugging
      return null
    }
  }

  /**
   * Get cached exercise recommendation for offline execution
   */
  async getCachedRecommendation(
    exerciseId: number,
    workoutId: number,
    username: string
  ): Promise<RecommendationModel | null> {
    try {
      const request = {
        ExerciseId: exerciseId,
        WorkoutId: workoutId,
        Username: username,
        IsQuickMode: false,
        LightSessionDays: 0,
        IsStrengthPhashe: false,
        IsFreePlan: false,
        VersionNo: 1,
      }

      return await this.workoutCacheService.getCachedExerciseRecommendation(
        request
      )
    } catch (error) {
      // Failed to get cached recommendation - error logged for debugging
      return null
    }
  }

  /**
   * Save set data offline (queue for later sync)
   */
  async saveSetOffline(
    setData: Partial<WorkoutLogSerieModel>
  ): Promise<boolean> {
    try {
      // Create a complete set record
      const completeSetData: WorkoutLogSerieModel = {
        Id: setData.Id || 0,
        UserId: setData.UserId || '',
        ExerciseId: setData.ExerciseId || 0,
        Reps: setData.Reps || 0,
        Weight: setData.Weight || { Lb: 0, Kg: 0 },
        RIR: setData.RIR || 0,
        IsWarmups: setData.IsWarmups || false,
        IsNext: setData.IsNext || false,
        IsFinished: setData.IsFinished || false,
        IsEditing: setData.IsEditing || false,
        SetNo: setData.SetNo || '',
        RepsValue: setData.RepsValue || '',
        WeightValue: setData.WeightValue || '',
        ...setData,
      }

      // Save to offline queue for later sync
      offlineQueue.addToQueue({
        url: '/api/sets',
        method: 'POST',
        data: completeSetData,
      })

      // Also save to local storage for immediate access
      this.saveSetToLocalStorage(completeSetData)

      // Set saved offline
      return true
    } catch (error) {
      // Failed to save set offline - error logged for debugging
      return false
    }
  }

  /**
   * Save workout completion offline
   */
  async completeWorkoutOffline(workoutData: {
    workoutId: string
    completedSets: WorkoutLogSerieModel[]
    startTime: Date
    endTime: Date
    notes?: string
  }): Promise<boolean> {
    try {
      // Queue workout completion for sync
      offlineQueue.addToQueue({
        url: '/api/workouts/complete',
        method: 'POST',
        data: workoutData,
      })

      // Save completion to local storage
      this.saveWorkoutCompletionToLocalStorage(workoutData)

      // Workout completed offline
      return true
    } catch (error) {
      // Failed to complete workout offline - error logged for debugging
      return false
    }
  }

  /**
   * Get offline workout progress
   */
  // eslint-disable-next-line class-methods-use-this
  getOfflineWorkoutProgress(workoutId: string): {
    completedSets: WorkoutLogSerieModel[]
    startTime?: Date
    currentExerciseId?: number
  } {
    try {
      const stored = localStorage.getItem(
        `offline-workout-progress-${workoutId}`
      )
      if (stored) {
        const data = JSON.parse(stored)
        return {
          completedSets: data.completedSets || [],
          startTime: data.startTime ? new Date(data.startTime) : undefined,
          currentExerciseId: data.currentExerciseId,
        }
      }
    } catch (error) {
      // Failed to get offline workout progress - error logged for debugging
    }

    return { completedSets: [] }
  }

  /**
   * Update offline workout progress
   */
  updateOfflineWorkoutProgress(
    workoutId: string,
    progress: {
      completedSets?: WorkoutLogSerieModel[]
      startTime?: Date
      currentExerciseId?: number
    }
  ): void {
    try {
      const existing = this.getOfflineWorkoutProgress(workoutId)
      const updated = {
        ...existing,
        ...progress,
        lastUpdated: new Date().toISOString(),
      }

      localStorage.setItem(
        `offline-workout-progress-${workoutId}`,
        JSON.stringify(updated)
      )
    } catch (error) {
      // Failed to update offline workout progress - error logged for debugging
    }
  }

  /**
   * Clear offline workout progress
   */
  // eslint-disable-next-line class-methods-use-this
  clearOfflineWorkoutProgress(workoutId: string): void {
    try {
      localStorage.removeItem(`offline-workout-progress-${workoutId}`)
    } catch (error) {
      // Failed to clear offline workout progress - error logged for debugging
    }
  }

  /**
   * Check if workout can be executed offline
   */
  async canExecuteOffline(workoutId: string): Promise<{
    canExecute: boolean
    missingData: string[]
  }> {
    const missingData: string[] = []

    try {
      // Check if workout details are cached
      const workout = await this.getCachedWorkout(workoutId)
      if (!workout) {
        missingData.push('Workout details')
      }

      // Check if exercise recommendations are cached
      if (workout?.Exercises) {
        // Process exercises sequentially to avoid performance issues and satisfy ESLint
        const exerciseChecks = workout.Exercises.map(async (exercise) => {
          if (exercise.Id) {
            const recommendation = await this.getCachedRecommendation(
              exercise.Id,
              parseInt(workoutId),
              '<EMAIL>' // TODO: Get from auth
            )
            if (!recommendation) {
              return `Recommendation for ${exercise.Label}`
            }
          }
          return null
        })

        const results = await Promise.allSettled(exerciseChecks)
        results.forEach((result) => {
          if (result.status === 'fulfilled' && result.value) {
            missingData.push(result.value)
          }
        })
      }

      return {
        canExecute: missingData.length === 0,
        missingData,
      }
    } catch (error) {
      // Failed to check offline capability - error logged for debugging
      return {
        canExecute: false,
        missingData: ['Error checking cached data'],
      }
    }
  }

  /**
   * Get sync queue status
   */
  // eslint-disable-next-line class-methods-use-this
  async getSyncQueueStatus(): Promise<{
    pendingCount: number
    lastSyncAttempt?: Date
    nextSyncAttempt?: Date
  }> {
    try {
      const pendingCount = offlineQueue.getQueueLength()
      return {
        pendingCount,
        lastSyncAttempt: undefined, // TODO: Implement sync tracking
        nextSyncAttempt: undefined,
      }
    } catch (error) {
      // Failed to get sync queue status - error logged for debugging
      return { pendingCount: 0 }
    }
  }

  /**
   * Private helper methods
   */
  // eslint-disable-next-line class-methods-use-this
  private saveSetToLocalStorage(setData: WorkoutLogSerieModel): void {
    try {
      const key = `offline-sets-${setData.ExerciseId}`
      const existing = localStorage.getItem(key)
      const sets = existing ? JSON.parse(existing) : []
      sets.push({
        ...setData,
        localId: `${Date.now()}-${Math.random()}`,
        savedAt: new Date().toISOString(),
      })
      localStorage.setItem(key, JSON.stringify(sets))
    } catch (error) {
      // Failed to save set to localStorage - error logged for debugging
    }
  }

  // eslint-disable-next-line class-methods-use-this
  private saveWorkoutCompletionToLocalStorage(workoutData: {
    workoutId: string
    completedSets: WorkoutLogSerieModel[]
    startTime: Date
    endTime: Date
    notes?: string
  }): void {
    try {
      const key = `offline-workout-completion-${workoutData.workoutId}`
      localStorage.setItem(
        key,
        JSON.stringify({
          ...workoutData,
          completedAt: new Date().toISOString(),
        })
      )
    } catch (error) {
      // Failed to save workout completion to localStorage - error logged for debugging
    }
  }
}

/**
 * Singleton instance
 */
export const offlineWorkoutService = new OfflineWorkoutService()
