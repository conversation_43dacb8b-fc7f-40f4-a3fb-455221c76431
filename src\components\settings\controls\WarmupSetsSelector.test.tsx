import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import WarmupSetsSelector from './WarmupSetsSelector'

describe('WarmupSetsSelector', () => {
  const mockOnChange = vi.fn()

  beforeEach(() => {
    mockOnChange.mockClear()
  })

  describe('Text Consistency - Working Sets vs Work Sets', () => {
    it('should display "work sets" not "working sets" for single warmup set', () => {
      render(<WarmupSetsSelector value={1} onChange={mockOnChange} />)

      // Test will FAIL initially because current text says "working sets"
      expect(
        screen.getByText('1 warmup set before work sets')
      ).toBeInTheDocument()
    })

    it('should display "work sets" not "working sets" for multiple warmup sets', () => {
      render(<WarmupSetsSelector value={3} onChange={mockOnChange} />)

      // Test will FAIL initially because current text says "working sets"
      expect(
        screen.getByText('3 warmup sets before work sets')
      ).toBeInTheDocument()
    })

    it('should display "No warmup sets" for zero value', () => {
      render(<WarmupSetsSelector value={0} onChange={mockOnChange} />)

      expect(screen.getByText('No warmup sets')).toBeInTheDocument()
    })
  })

  describe('Button Grid Functionality', () => {
    it('should render 6 warmup set options (0-5)', () => {
      render(<WarmupSetsSelector value={2} onChange={mockOnChange} />)

      for (let i = 0; i <= 5; i++) {
        expect(screen.getByLabelText(`${i} warmup sets`)).toBeInTheDocument()
      }
    })

    it('should call onChange when warmup set button is clicked', () => {
      render(<WarmupSetsSelector value={2} onChange={mockOnChange} />)

      const button3 = screen.getByLabelText('3 warmup sets')
      fireEvent.click(button3)

      expect(mockOnChange).toHaveBeenCalledWith(3)
    })

    it('should highlight selected warmup set value', () => {
      render(<WarmupSetsSelector value={2} onChange={mockOnChange} />)

      const selectedButton = screen.getByLabelText('2 warmup sets')
      expect(selectedButton).toHaveAttribute('aria-pressed', 'true')
    })
  })

  describe('Touch Target Compliance', () => {
    it('should have 52px minimum height for all buttons', () => {
      render(<WarmupSetsSelector value={1} onChange={mockOnChange} />)

      const buttons = screen.getAllByRole('button')
      buttons.forEach((button) => {
        expect(button).toHaveClass('min-h-[52px]')
      })
    })
  })

  describe('Disabled State', () => {
    it('should disable all buttons when disabled prop is true', () => {
      render(<WarmupSetsSelector value={2} onChange={mockOnChange} disabled />)

      const buttons = screen.getAllByRole('button')
      buttons.forEach((button) => {
        expect(button).toBeDisabled()
        expect(button).toHaveClass('opacity-50', 'cursor-not-allowed')
      })
    })

    it('should not call onChange when disabled and clicked', () => {
      render(<WarmupSetsSelector value={2} onChange={mockOnChange} disabled />)

      const button3 = screen.getByLabelText('3 warmup sets')
      fireEvent.click(button3)

      expect(mockOnChange).not.toHaveBeenCalled()
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels for each button', () => {
      render(<WarmupSetsSelector value={2} onChange={mockOnChange} />)

      for (let i = 0; i <= 5; i++) {
        const button = screen.getByLabelText(`${i} warmup sets`)
        expect(button).toHaveAttribute('aria-label', `${i} warmup sets`)
        expect(button).toHaveAttribute(
          'aria-pressed',
          i === 2 ? 'true' : 'false'
        )
      }
    })
  })
})
