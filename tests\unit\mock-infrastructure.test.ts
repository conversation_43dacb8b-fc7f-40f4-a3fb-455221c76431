import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { chromium, <PERSON><PERSON><PERSON>, BrowserContext, Page } from '@playwright/test'
import { mockApi } from '../e2e/helpers/mock-api'
import { existsSync, readFileSync } from 'fs'
import { join } from 'path'

describe('Mock Infrastructure Integration', () => {
  let browser: Browser
  let context: BrowserContext
  let page: Page

  beforeAll(async () => {
    browser = await chromium.launch({ headless: true })
    context = await browser.newContext()
    page = await context.newPage()
  })

  afterAll(async () => {
    await context.close()
    await browser.close()
  })

  describe('Fixture Files', () => {
    it('should have all required fixture files', () => {
      const fixturesDir = join(process.cwd(), 'tests', 'fixtures')
      const requiredFixtures = [
        'POST/login.json',
        'GET/workout.json',
        'GET/workout/exercise/123.json',
        'GET/recommendations/123.json',
      ]

      requiredFixtures.forEach((fixture) => {
        const fixturePath = join(fixturesDir, fixture)
        expect(existsSync(fixturePath)).toBe(true)
      })
    })

    it('should have valid JSON structure in fixtures', () => {
      const loginFixture = JSON.parse(
        readFileSync(
          join(process.cwd(), 'tests/fixtures/POST/login.json'),
          'utf-8'
        )
      )
      expect(loginFixture.Token).toBeDefined()
      expect(loginFixture.User).toBeDefined()
      expect(loginFixture.User.Email).toBe('<EMAIL>')

      const workoutFixture = JSON.parse(
        readFileSync(
          join(process.cwd(), 'tests/fixtures/GET/workout.json'),
          'utf-8'
        )
      )
      expect(Array.isArray(workoutFixture)).toBe(true)
      expect(workoutFixture[0].Id).toBeDefined()

      const exerciseFixture = JSON.parse(
        readFileSync(
          join(process.cwd(), 'tests/fixtures/GET/workout/exercise/123.json'),
          'utf-8'
        )
      )
      expect(exerciseFixture.Id).toBe(123)
      expect(exerciseFixture.Name).toBeDefined()

      const recommendationFixture = JSON.parse(
        readFileSync(
          join(process.cwd(), 'tests/fixtures/GET/recommendations/123.json'),
          'utf-8'
        )
      )
      expect(recommendationFixture.Weight).toBeDefined()
      expect(recommendationFixture.Reps).toBeDefined()
    })
  })

  describe('Route Interception', () => {
    it('should intercept all API routes', async () => {
      const interceptedRoutes: string[] = []

      await page.route('**/api/**', async (route, request) => {
        interceptedRoutes.push(`${request.method()} ${request.url()}`)
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ intercepted: true }),
        })
      })

      // Navigate to a data URL to avoid needing a server
      await page.goto(
        'data:text/html,<html><head><base href="http://localhost:3000/"></head><body>Test</body></html>'
      )

      // Make test API calls using page.evaluate to trigger from within the page context
      await page.evaluate(async () => {
        await fetch('/api/test/endpoint')
        await fetch('/api/another/endpoint', { method: 'POST' })
      })

      expect(interceptedRoutes.length).toBeGreaterThan(0)
      expect(
        interceptedRoutes.some((route) => route.includes('/api/test/endpoint'))
      ).toBe(true)
      expect(
        interceptedRoutes.some((route) =>
          route.includes('/api/another/endpoint')
        )
      ).toBe(true)
    })

    it('should serve correct fixture data for known endpoints', async () => {
      await mockApi(page)
      await page.goto(
        'data:text/html,<html><head><base href="http://localhost:3000/"></head><body>Test</body></html>'
      )

      // Test login endpoint using page.evaluate
      const loginData = await page.evaluate(async () => {
        const response = await fetch('/api/Account/Login', { method: 'POST' })
        return {
          status: response.status,
          data: await response.json(),
        }
      })
      expect(loginData.status).toBe(200)
      expect(loginData.data.Token).toBe('TEST_TOKEN_12345')

      // Test workout endpoint using page.evaluate
      const workoutData = await page.evaluate(async () => {
        const response = await fetch('/api/Workout/GetUserWorkoutTemplateGroup')
        return {
          status: response.status,
          data: await response.json(),
        }
      })
      expect(workoutData.status).toBe(200)
      expect(workoutData.data[0].Label).toBe('Test Workout A')
    })

    it('should handle missing fixtures gracefully', async () => {
      await mockApi(page)
      await page.goto(
        'data:text/html,<html><head><base href="http://localhost:3000/"></head><body>Test</body></html>'
      )

      const result = await page.evaluate(async () => {
        const response = await fetch('/api/missing/endpoint')
        return {
          status: response.status,
          data: await response.json(),
        }
      })
      expect(result.status).toBe(404)
      expect(result.data.error).toBe('Mock not found')
      expect(result.data.fixture).toBe('GET/missing/endpoint.json')
    })

    it('should handle different HTTP methods correctly', async () => {
      await mockApi(page)
      await page.goto(
        'data:text/html,<html><head><base href="http://localhost:3000/"></head><body>Test</body></html>'
      )

      // Test different methods using page.evaluate
      const results = await page.evaluate(async () => {
        const getResponse = await fetch('/api/nonexistent')
        const getData = await getResponse.json()

        const postResponse = await fetch('/api/nonexistent', { method: 'POST' })
        const postData = await postResponse.json()

        const putResponse = await fetch('/api/nonexistent', { method: 'PUT' })
        const putData = await putResponse.json()

        return {
          get: { status: getResponse.status, data: getData },
          post: { status: postResponse.status, data: postData },
          put: { status: putResponse.status, data: putData },
        }
      })

      expect(results.get.status).toBe(404)
      expect(results.get.data.fixture).toBe('GET/nonexistent.json')
      expect(results.post.status).toBe(404)
      expect(results.post.data.fixture).toBe('POST/nonexistent.json')
      expect(results.put.status).toBe(404)
      expect(results.put.data.fixture).toBe('PUT/nonexistent.json')
    })
  })

  describe('Error Handling', () => {
    it('should handle malformed fixture files gracefully', async () => {
      // This test would require creating a malformed fixture file
      // For now, we'll test the error handling path by mocking the file system
      await mockApi(page)
      await page.goto(
        'data:text/html,<html><head><base href="http://localhost:3000/"></head><body>Test</body></html>'
      )

      // Test with a path that should exist but might have issues
      const result = await page.evaluate(async () => {
        const response = await fetch('/api/test/error')
        return response.status
      })
      expect([404, 500]).toContain(result)
    })
  })
})
