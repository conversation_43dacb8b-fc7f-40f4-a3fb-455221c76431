// eslint-disable-next-line import/no-extraneous-dependencies
import { defineConfig, devices } from '@playwright/test'

/**
 * Stable Playwright configuration for reliable E2E testing
 * Optimized to prevent timeouts and resource contention issues
 */
export default defineConfig({
  testDir: './tests/e2e',
  /* Run tests sequentially to prevent resource contention */
  fullyParallel: false,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: process.env.CI ? 2 : 0,
  /* Use single worker to prevent resource contention */
  workers: 1,
  /* Reporter to use */
  reporter: [['html', { outputFolder: 'playwright-report' }], ['list']],
  /* Global timeout for each test - increased for stability */
  timeout: 90000,
  /* Expect timeout for assertions */
  expect: {
    timeout: 10000,
  },
  /* Shared settings for all projects */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: 'http://localhost:3000',
    /* Collect trace when retrying the failed test */
    trace: 'on-first-retry',
    /* Screenshot on failure */
    screenshot: 'only-on-failure',
    /* Video on failure */
    video: 'retain-on-failure',
    /* Timeout for each action - increased for stability */
    actionTimeout: 30000,
    /* Navigation timeout - increased for stability */
    navigationTimeout: 60000,
    /* Ignore HTTPS errors */
    ignoreHTTPSErrors: true,
    /* Bypass CSP */
    bypassCSP: true,
  },

  /* Configure projects for stable testing - single project to avoid resource contention */
  projects: [
    /* Desktop Chrome - Most stable, single project for reliability */
    {
      name: 'chromium',
      use: {
        ...devices['Desktop Chrome'],
        /* Increase timeouts for stability */
        actionTimeout: 45000,
        navigationTimeout: 90000,
        /* Additional stability settings */
        launchOptions: {
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
          ],
        },
      },
    },
  ],

  /* Run local dev server before tests with extended timeout */
  webServer: {
    command: 'npm run dev',
    port: 3000,
    reuseExistingServer: true,
    timeout: 180000, // 3 minutes for server startup
    /* Add environment variables for stable testing */
    env: {
      NODE_ENV: 'test',
      NEXT_PUBLIC_API_URL: 'https://drmuscle.azurewebsites.net',
      NEXT_PUBLIC_APP_ENV: 'development',
      NEXT_PUBLIC_APPLE_SERVICES_ID: 'test.apple.services.id',
      NEXT_PUBLIC_GOOGLE_CLIENT_ID: 'test-google-client-id',
      NEXT_PUBLIC_FIREBASE_API_KEY: 'test-firebase-api-key',
      NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: 'test-firebase-auth-domain',
      NEXT_PUBLIC_FIREBASE_PROJECT_ID: 'test-firebase-project-id',
    },
  },

  /* Global setup and teardown */
  globalSetup: require.resolve('./tests/e2e/global-setup.ts'),
  globalTeardown: require.resolve('./tests/e2e/global-teardown.ts'),
})
