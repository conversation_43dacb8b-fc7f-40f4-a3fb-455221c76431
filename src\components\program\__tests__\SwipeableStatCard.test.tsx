import { render, screen } from '@testing-library/react'
import { userEvent } from '@testing-library/user-event'
import { vi } from 'vitest'
import { SwipeableStatCard } from '../SwipeableStatCard'
import type { UserStats } from '@/types'

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, drag, onDragEnd, animate, ...props }: any) => (
      <div {...props}>{children}</div>
    ),
  },
  useAnimation: () => ({
    start: vi.fn(),
    set: vi.fn(),
  }),
}))

// Mock haptic utils
vi.mock('@/utils/haptic', () => ({
  useHaptic: () => ({
    trigger: vi.fn(),
    withHandler: (fn: () => void) => fn,
  }),
}))

// Mock useAuthStore to provide user data
vi.mock('@/stores/authStore', () => ({
  useAuthStore: vi.fn(),
}))

describe('SwipeableStatCard', () => {
  const mockStats: UserStats = {
    weekStreak: 5,
    workoutsCompleted: 42,
    lbsLifted: 12500,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('shows first stat (lbs lifted) in focused view initially', () => {
    render(<SwipeableStatCard stats={mockStats} isLoading={false} />)

    // Should show lbs lifted value first
    expect(screen.getByText('12,500')).toBeInTheDocument()
    expect(screen.getByText('lbs lifted')).toBeInTheDocument()

    // Should not show other stats initially
    expect(screen.queryByText('42')).not.toBeInTheDocument()
    expect(screen.queryByText('5')).not.toBeInTheDocument()
  })

  it('shows loading state when isLoading is true', () => {
    render(<SwipeableStatCard stats={null} isLoading />)

    // Should show animated counter starting at 0
    expect(screen.getByText('0')).toBeInTheDocument()
    expect(screen.getByText('lbs lifted')).toBeInTheDocument()

    // Should show loading message
    expect(screen.getByText('Loading stats...')).toBeInTheDocument()
  })

  it('shows stat indicators for navigation', () => {
    render(<SwipeableStatCard stats={mockStats} isLoading={false} />)

    // Should show 3 dots for 3 stats
    const indicators = screen.getAllByTestId('stat-indicator')
    expect(indicators).toHaveLength(3)

    // First indicator should be active
    expect(indicators[0]).toHaveClass('bg-brand-primary')
    expect(indicators[1]).toHaveClass('bg-bg-tertiary')
    expect(indicators[2]).toHaveClass('bg-bg-tertiary')
  })

  it('shows next stat when swiping left', async () => {
    const user = userEvent.setup()
    render(<SwipeableStatCard stats={mockStats} isLoading={false} />)

    // Simulate swipe left by clicking next indicator
    const indicators = screen.getAllByTestId('stat-indicator')
    await user.click(indicators[1])

    // Should now show workouts (second stat)
    expect(screen.getByText('42')).toBeInTheDocument()
    expect(screen.getByText('Workouts')).toBeInTheDocument()

    // Should not show other stats
    expect(screen.queryByText('12,500')).not.toBeInTheDocument()
    expect(screen.queryByText('5')).not.toBeInTheDocument()
  })

  it('cycles through all stats and back to first', async () => {
    const user = userEvent.setup()
    render(<SwipeableStatCard stats={mockStats} isLoading={false} />)

    const indicators = screen.getAllByTestId('stat-indicator')

    // Go to second stat (Workouts)
    await user.click(indicators[1])
    expect(screen.getByText('42')).toBeInTheDocument()

    // Go to third stat (Weeks streak)
    await user.click(indicators[2])
    expect(screen.getByText('5')).toBeInTheDocument()
    expect(screen.getByText('Weeks streak')).toBeInTheDocument()

    // Go back to first stat (Lbs Lifted)
    await user.click(indicators[0])
    expect(screen.getByText('12,500')).toBeInTheDocument() // Formatted number
    expect(screen.getByText('lbs lifted')).toBeInTheDocument()
  })

  it('handles null stats gracefully', () => {
    render(<SwipeableStatCard stats={null} isLoading={false} />)

    // Should show zero values for first stat (Lbs Lifted)
    expect(screen.getByText('0')).toBeInTheDocument()
    expect(screen.getByText('lbs lifted')).toBeInTheDocument()
  })

  it('handles undefined values in stats', async () => {
    vi.useFakeTimers()

    const partialStats: Partial<UserStats> = {
      weekStreak: 5,
      // workoutsCompleted and lbsLifted are undefined
    }
    render(
      <SwipeableStatCard stats={partialStats as UserStats} isLoading={false} />
    )

    // Should show animation initially
    expect(screen.getByTestId('stat-value')).toHaveTextContent('0')

    // Wait for animation to complete
    const { act } = await import('@testing-library/react')
    await act(async () => {
      vi.advanceTimersByTime(1500)
    })

    // Should show first stat (Workouts) with 0 since it's undefined
    expect(screen.getByText('0')).toBeInTheDocument()
    expect(screen.getByText('Workouts')).toBeInTheDocument()

    vi.useRealTimers()
  })

  it('shows swipe hint text', () => {
    render(<SwipeableStatCard stats={mockStats} isLoading={false} />)

    expect(screen.getByText('Swipe to view more')).toBeInTheDocument()
  })

  it('applies transparent background with proper styling', () => {
    render(<SwipeableStatCard stats={mockStats} isLoading={false} />)

    const card = screen.getByTestId('swipeable-stat-card')

    // Should have transparent background instead of gray
    expect(card).toHaveClass('bg-transparent')
    expect(card).not.toHaveClass('bg-surface-secondary')

    // Should maintain other styling
    expect(card).toHaveClass('rounded-2xl')
    expect(card).toHaveClass('shadow-lg')

    // Should NOT have hover effects
    expect(card).not.toHaveClass('hover:scale-[1.02]')
    expect(card).not.toHaveClass('hover:shadow-theme-lg')
  })

  describe('Counter Animation', () => {
    it('shows counter display when loading', () => {
      render(<SwipeableStatCard stats={null} isLoading />)

      // Should show initial value of 0
      expect(screen.getByText('0')).toBeInTheDocument()
      expect(screen.getByText('Workouts')).toBeInTheDocument()

      // Should show loading message
      expect(
        screen.getByText('Loading stats and workout...')
      ).toBeInTheDocument()
    })

    it('uses animated values when loading', () => {
      // This test verifies the component structure is correct for animation
      // Actual animation behavior is tested via E2E tests due to RAF limitations in unit tests
      render(<SwipeableStatCard stats={null} isLoading />)

      // Should show initial value
      expect(screen.getByTestId('stat-value')).toHaveTextContent('0')

      // Should have loading message
      expect(
        screen.getByText('Loading stats and workout...')
      ).toBeInTheDocument()

      // Should have correct stat label
      expect(screen.getByText('Workouts')).toBeInTheDocument()
    })

    it('transitions to real value when data arrives', async () => {
      vi.useFakeTimers()

      const { rerender } = render(<SwipeableStatCard stats={null} isLoading />)

      // Should start with 0
      expect(screen.getByText('0')).toBeInTheDocument()

      // Provide real data
      rerender(<SwipeableStatCard stats={mockStats} isLoading={false} />)

      // Should still show animation initially
      expect(screen.getByTestId('stat-value')).toHaveTextContent('0')

      // Wait for animation to complete
      vi.advanceTimersByTime(1500)

      const { waitFor } = await import('@testing-library/react')
      await waitFor(() => {
        // Now should show real value (Workouts is first now)
        expect(screen.getByText('42')).toBeInTheDocument()
      })

      // Loading message should disappear
      expect(
        screen.queryByText('Loading stats and workout...')
      ).not.toBeInTheDocument()

      vi.useRealTimers()
    })

    it('respects reduced motion preference', () => {
      // Mock reduced motion preference
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: vi.fn().mockImplementation((query) => ({
          matches: query === '(prefers-reduced-motion: reduce)',
          media: query,
          onchange: null,
          addListener: vi.fn(),
          removeListener: vi.fn(),
          addEventListener: vi.fn(),
          removeEventListener: vi.fn(),
          dispatchEvent: vi.fn(),
        })),
      })

      render(<SwipeableStatCard stats={null} isLoading />)

      // Should show 0 immediately without animation
      expect(screen.getByText('0')).toBeInTheDocument()
    })
  })

  describe('Loading Message', () => {
    it('shows loading message below card when loading', () => {
      render(<SwipeableStatCard stats={null} isLoading />)

      expect(
        screen.getByText('Loading stats and workout...')
      ).toBeInTheDocument()
    })

    it('hides loading message when data is loaded', () => {
      render(<SwipeableStatCard stats={mockStats} isLoading={false} />)

      expect(
        screen.queryByText('Loading stats and workout...')
      ).not.toBeInTheDocument()
    })
  })

  describe('Weight Unit Display', () => {
    beforeEach(async () => {
      // Reset mocks
      vi.clearAllMocks()
      const { useAuthStore } = await import('@/stores/authStore')
      const mockUseAuthStore = useAuthStore as unknown as ReturnType<
        typeof vi.fn
      >
      mockUseAuthStore.mockReturnValue({
        user: null,
      })
    })

    it('should display weight in kg when user preference is kg', async () => {
      vi.useFakeTimers()

      const { useAuthStore } = await import('@/stores/authStore')
      const mockUseAuthStore = useAuthStore as unknown as ReturnType<
        typeof vi.fn
      >

      // Mock user with kg preference
      mockUseAuthStore.mockReturnValue({
        user: {
          email: '<EMAIL>',
          firstName: 'Test',
          massUnit: 'kg',
        },
      })

      const user = userEvent.setup({ delay: null })
      render(<SwipeableStatCard stats={mockStats} isLoading={false} />)

      // Wait for initial animation to complete
      const { act } = await import('@testing-library/react')
      await act(async () => {
        vi.advanceTimersByTime(1500)
      })

      // Navigate to weight stat (third stat)
      const indicators = screen.getAllByTestId('stat-indicator')
      await user.click(indicators[2])

      // Should show kg value (12500 lbs = 5670 kg)
      expect(screen.getByTestId('stat-value')).toHaveTextContent('5,670')
      expect(screen.getByText('Kg Lifted')).toBeInTheDocument()

      vi.useRealTimers()
    })

    it('should display weight in lbs when user preference is lbs', async () => {
      vi.useFakeTimers()

      const { useAuthStore } = await import('@/stores/authStore')
      const mockUseAuthStore = useAuthStore as unknown as ReturnType<
        typeof vi.fn
      >

      // Mock user with lbs preference
      mockUseAuthStore.mockReturnValue({
        user: {
          email: '<EMAIL>',
          firstName: 'Test',
          massUnit: 'lbs',
        },
      })

      const user = userEvent.setup({ delay: null })
      render(<SwipeableStatCard stats={mockStats} isLoading={false} />)

      // Wait for initial animation to complete
      const { act } = await import('@testing-library/react')
      await act(async () => {
        vi.advanceTimersByTime(1500)
      })

      // Navigate to weight stat (third stat)
      const indicators = screen.getAllByTestId('stat-indicator')
      await user.click(indicators[2])

      // Should show lbs value
      expect(screen.getByTestId('stat-value')).toHaveTextContent('12,500')
      expect(screen.getByText('lbs lifted')).toBeInTheDocument()

      vi.useRealTimers()
    })

    it('should default to lbs when no user preference available', async () => {
      vi.useFakeTimers()

      const { useAuthStore } = await import('@/stores/authStore')
      const mockUseAuthStore = useAuthStore as unknown as ReturnType<
        typeof vi.fn
      >

      // Mock user without mass unit preference
      mockUseAuthStore.mockReturnValue({
        user: {
          email: '<EMAIL>',
          firstName: 'Test',
        },
      })

      const user = userEvent.setup({ delay: null })
      render(<SwipeableStatCard stats={mockStats} isLoading={false} />)

      // Wait for initial animation to complete
      const { act } = await import('@testing-library/react')
      await act(async () => {
        vi.advanceTimersByTime(1500)
      })

      // Navigate to weight stat (third stat)
      const indicators = screen.getAllByTestId('stat-indicator')
      await user.click(indicators[2])

      // Should default to lbs
      expect(screen.getByTestId('stat-value')).toHaveTextContent('12,500')
      expect(screen.getByText('lbs lifted')).toBeInTheDocument()

      vi.useRealTimers()
    })

    it('should handle null user gracefully', async () => {
      vi.useFakeTimers()

      const { useAuthStore } = await import('@/stores/authStore')
      const mockUseAuthStore = useAuthStore as unknown as ReturnType<
        typeof vi.fn
      >

      mockUseAuthStore.mockReturnValue({
        user: null,
      })

      const user = userEvent.setup({ delay: null })
      render(<SwipeableStatCard stats={mockStats} isLoading={false} />)

      // Wait for initial animation to complete
      const { act } = await import('@testing-library/react')
      await act(async () => {
        vi.advanceTimersByTime(1500)
      })

      // Navigate to weight stat (third stat)
      const indicators = screen.getAllByTestId('stat-indicator')
      await user.click(indicators[2])

      // Should default to lbs
      expect(screen.getByTestId('stat-value')).toHaveTextContent('12,500')
      expect(screen.getByText('lbs lifted')).toBeInTheDocument()

      vi.useRealTimers()
    })
  })

  describe('Initial Mount Animation', () => {
    beforeEach(() => {
      // Reset matchMedia to default
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: vi.fn().mockImplementation((query) => ({
          matches: false,
          media: query,
          onchange: null,
          addListener: vi.fn(),
          removeListener: vi.fn(),
          addEventListener: vi.fn(),
          removeEventListener: vi.fn(),
          dispatchEvent: vi.fn(),
        })),
      })
    })

    it('shows animation on initial mount even with cached data', () => {
      // Test that animation shows for first 1-2 seconds even when stats exist
      render(<SwipeableStatCard stats={mockStats} isLoading={false} />)

      // Should show animated value (0) initially, not real value (42)
      expect(screen.getByTestId('stat-value')).toHaveTextContent('0')
      expect(screen.queryByText('42')).not.toBeInTheDocument()
    })

    it('transitions from animated to real values after delay', async () => {
      vi.useFakeTimers()

      render(<SwipeableStatCard stats={mockStats} isLoading={false} />)

      // Initially shows animated value
      expect(screen.getByTestId('stat-value')).toHaveTextContent('0')

      // After animation duration, should show real value
      const { act } = await import('@testing-library/react')
      await act(async () => {
        vi.advanceTimersByTime(1500)
      })

      // Should now show real value (Workouts first)
      expect(screen.getByTestId('stat-value')).toHaveTextContent('42')

      vi.useRealTimers()
    })

    it('shows loading message during initial animation', () => {
      render(<SwipeableStatCard stats={mockStats} isLoading={false} />)

      // Should show loading message even with cached data during initial animation
      expect(
        screen.getByText('Loading stats and workout...')
      ).toBeInTheDocument()
    })

    it('hides loading message after initial animation completes', async () => {
      vi.useFakeTimers()

      render(<SwipeableStatCard stats={mockStats} isLoading={false} />)

      // Initially shows loading message
      expect(
        screen.getByText('Loading stats and workout...')
      ).toBeInTheDocument()

      // After animation duration
      const { act } = await import('@testing-library/react')
      await act(async () => {
        vi.advanceTimersByTime(1500)
      })

      // Loading message should be gone
      expect(
        screen.queryByText('Loading stats and workout...')
      ).not.toBeInTheDocument()

      vi.useRealTimers()
    })

    it('does not show initial animation if reduced motion is preferred', () => {
      // Mock reduced motion preference
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: vi.fn().mockImplementation((query) => ({
          matches: query === '(prefers-reduced-motion: reduce)',
          media: query,
          onchange: null,
          addListener: vi.fn(),
          removeListener: vi.fn(),
          addEventListener: vi.fn(),
          removeEventListener: vi.fn(),
          dispatchEvent: vi.fn(),
        })),
      })

      render(<SwipeableStatCard stats={mockStats} isLoading={false} />)

      // Should show real value immediately, no animation
      expect(screen.getByTestId('stat-value')).toHaveTextContent('42')
      expect(
        screen.queryByText('Loading stats and workout...')
      ).not.toBeInTheDocument()
    })

    it('handles component unmount during animation gracefully', () => {
      vi.useFakeTimers()

      const { unmount } = render(
        <SwipeableStatCard stats={mockStats} isLoading={false} />
      )

      // Unmount during animation
      vi.advanceTimersByTime(1000)
      unmount()

      // Should not throw any errors
      vi.advanceTimersByTime(1000)

      vi.useRealTimers()
    })
  })
})
