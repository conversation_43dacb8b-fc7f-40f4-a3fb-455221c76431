import { useState, useEffect, useMemo } from 'react'
import { useWorkoutStore } from '@/stores/workoutStore'
import { debugLog } from '@/utils/debugLog'
import type { ExerciseWorkSetsModel, WorkoutTemplateModel } from '@/types'

export function useWorkoutPreview(
  workout: WorkoutTemplateModel | undefined,
  workoutSession: unknown,
  exerciseWorkSetsModels: ExerciseWorkSetsModel[]
) {
  const previewExerciseSkips = useWorkoutStore(
    (state) => state.previewExerciseSkips
  )

  // State for managing preview exercises
  const [previewExercisesState, setPreviewExercisesState] = useState<
    ExerciseWorkSetsModel[]
  >([])

  // Extract exercises from workout data for display
  const previewExercises = useMemo(() => {
    if (!workout?.Exercises) {
      return []
    }
    // Create preview models from workout exercises
    return workout.Exercises.map(
      (exercise): ExerciseWorkSetsModel => ({
        Id: exercise.Id,
        Label: exercise.Label,
        BodyPartId: exercise.BodyPartId || 0,
        IsFinished: false,
        IsNextExercise: false,
        isLoadingSets: false,
        setsError: null,
        lastSetsUpdate: 0,
        sets: [], // Empty sets for preview
        IsBodyweight: exercise.IsBodyweight,
      })
    )
  }, [workout])

  // Update preview exercises state when workout changes
  useEffect(() => {
    setPreviewExercisesState(previewExercises)
  }, [previewExercises])

  // Use exerciseWorkSetsModels when workout is active, otherwise use preview
  // If exerciseWorkSetsModels is empty but we have a session, still show preview
  const displayExercises = useMemo(() => {
    const baseExercises =
      workoutSession && exerciseWorkSetsModels.length > 0
        ? exerciseWorkSetsModels
        : previewExercisesState

    // Apply preview skips to exercises if not in active workout
    if (!workoutSession && previewExerciseSkips.size > 0) {
      return baseExercises.map((exercise) => ({
        ...exercise,
        IsFinished:
          previewExerciseSkips.has(exercise.Id) || exercise.IsFinished,
      }))
    }

    return baseExercises
  }, [
    workoutSession,
    exerciseWorkSetsModels,
    previewExercisesState,
    previewExerciseSkips,
  ])

  // Handle skip exercise
  const handleSkipExercise = (exerciseId: number) => {
    debugLog('[WorkoutOverview] Skipping exercise:', exerciseId)

    // If workout is active, use the store action
    if (workoutSession) {
      const { skipExercise } = useWorkoutStore.getState()
      skipExercise(exerciseId)
    } else {
      // Use preview skip functionality for persistence
      const { skipPreviewExercise } = useWorkoutStore.getState()
      skipPreviewExercise(exerciseId)
    }
  }

  return {
    displayExercises,
    handleSkipExercise,
  }
}
