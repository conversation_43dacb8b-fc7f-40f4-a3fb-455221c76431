import { test, expect } from '@playwright/test'

test.describe('V2 Exercise Page - Navbar Updates', () => {
  test('should display Workout title in navbar and exercise info below', async ({
    page,
  }) => {
    // Navigate directly to V2 exercise page with mock auth
    await page.goto('/workout/exercise-v2/1')

    // Wait for the page to load (less strict selector)
    await page.waitForLoadState('networkidle')

    // Check that navbar shows "Workout" title
    const navTitle = await page.locator('header h1').textContent()
    expect(navTitle).toBe('Workout')

    // Check that exercise info header is visible below navbar
    const exerciseInfo = page.locator('[data-testid="exercise-info-header"]')
    await expect(exerciseInfo).toBeVisible()

    // Check that progress bar is NOT in the navbar
    const navProgressBar = page.locator(
      'header [data-testid="nav-progress-bar"]'
    )
    await expect(navProgressBar).not.toBeVisible()

    // Check that progress bar IS in the exercise info header
    const infoProgressBar = page.locator(
      '[data-testid="exercise-info-header"] [data-testid="exercise-progress-bar"]'
    )
    await expect(infoProgressBar).toBeVisible()
  })
})
