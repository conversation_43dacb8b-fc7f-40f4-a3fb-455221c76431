import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, act } from '@testing-library/react'
import { RestTimer } from '../RestTimer'
import type { RestTimerState } from '@/stores/workoutStore/types'

// Mock the workout store
const mockSetRestTimerState = vi.fn()
let mockRestTimerState: RestTimerState = {
  isActive: false,
  duration: 0,
}

vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: () => ({
    restTimerState: mockRestTimerState,
    setRestTimerState: mockSetRestTimerState,
  }),
}))

// Mock haptics
vi.mock('@/utils/haptics', () => ({
  vibrate: vi.fn(),
}))

// Mock framer-motion to render immediately without animations
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => children,
}))

describe('RestTimer - Next Set Info Display', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockRestTimerState = { isActive: false, duration: 0 }
  })

  it('should display next set info when provided', () => {
    // Given: RestTimer is active with next set info
    mockRestTimerState = {
      isActive: true,
      duration: 90,
      nextSetInfo: {
        reps: 8,
        weight: 135,
        unit: 'lbs',
      },
    }

    // When: RestTimer renders
    render(<RestTimer />)

    // Then: Next set info has been removed from the UI
    expect(
      screen.queryByText('Get ready for: 8 x 135 lbs')
    ).not.toBeInTheDocument()
  })

  it('should display next set info with kg unit', () => {
    // Given: RestTimer with kg unit
    mockRestTimerState = {
      isActive: true,
      duration: 60,
      nextSetInfo: {
        reps: 10,
        weight: 60,
        unit: 'kg',
      },
    }

    // When: RestTimer renders
    render(<RestTimer />)

    // Then: Next set info has been removed from the UI
    expect(
      screen.queryByText('Get ready for: 10 x 60 kg')
    ).not.toBeInTheDocument()
  })

  it('should display generic message when no next set info', () => {
    // Given: RestTimer without next set info
    mockRestTimerState = {
      isActive: true,
      duration: 45,
      // No nextSetInfo provided
    }

    // When: RestTimer renders
    render(<RestTimer />)

    // Then: Next set info has been removed from the UI
    expect(
      screen.queryByText('Get ready for your next set!')
    ).not.toBeInTheDocument()
  })

  it('should handle bodyweight exercises (0 weight)', () => {
    // Given: RestTimer for bodyweight exercise
    mockRestTimerState = {
      isActive: true,
      duration: 60,
      nextSetInfo: {
        reps: 15,
        weight: 0,
        unit: 'lbs',
      },
    }

    // When: RestTimer renders
    render(<RestTimer />)

    // Then: Next set info has been removed from the UI
    expect(screen.queryByText('Get ready for: 15 reps')).not.toBeInTheDocument()
  })

  it('should update timer display when state changes', () => {
    // Given: RestTimer starts inactive
    const { rerender } = render(<RestTimer />)

    // When: State changes to active with next set info
    act(() => {
      mockRestTimerState = {
        isActive: true,
        duration: 30,
        nextSetInfo: {
          reps: 5,
          weight: 225,
          unit: 'lbs',
        },
      }
    })
    rerender(<RestTimer />)

    // Then: Next set info has been removed from the UI
    expect(
      screen.queryByText('Get ready for: 5 x 225 lbs')
    ).not.toBeInTheDocument()
  })

  it('should handle fractional weights', () => {
    // Given: RestTimer with fractional weight
    mockRestTimerState = {
      isActive: true,
      duration: 90,
      nextSetInfo: {
        reps: 12,
        weight: 22.5,
        unit: 'kg',
      },
    }

    // When: RestTimer renders
    render(<RestTimer />)

    // Then: Next set info has been removed from the UI
    expect(
      screen.queryByText('Get ready for: 12 x 22.5 kg')
    ).not.toBeInTheDocument()
  })
})
