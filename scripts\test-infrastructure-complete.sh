#!/bin/bash

# Complete Infrastructure Test Script
# This script verifies that all testing infrastructure is working properly

echo "🚀 Starting Complete Infrastructure Test Suite"
echo "=============================================="

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
        exit 1
    fi
}

print_info() {
    echo -e "${YELLOW}ℹ️  $1${NC}"
}

# Test 1: Unit Tests (Mock Infrastructure)
print_info "Running Unit Tests - Mock Infrastructure..."
npm run test tests/unit/mock-infrastructure.test.ts -- --run
print_status $? "Unit Tests - Mock Infrastructure"

# Test 2: E2E Infrastructure Test
print_info "Running E2E Infrastructure Tests..."
npx playwright test tests/e2e/infrastructure-test.spec.ts --config=playwright.stable.config.ts
print_status $? "E2E Infrastructure Tests"

# Test 3: Final Infrastructure Test
print_info "Running Final Infrastructure Tests..."
npx playwright test tests/e2e/final-infrastructure-test.spec.ts --config=playwright.stable.config.ts
print_status $? "Final Infrastructure Tests"

# Test 4: Combined E2E Tests
print_info "Running Combined E2E Tests..."
npx playwright test tests/e2e/infrastructure-test.spec.ts tests/e2e/final-infrastructure-test.spec.ts --config=playwright.stable.config.ts
print_status $? "Combined E2E Tests"

# Test 5: Sample Unit Test (Warmup Calculator)
print_info "Running Sample Unit Test..."
npm run test src/utils/__tests__/warmupCalculator.test.ts -- --run
print_status $? "Sample Unit Test"

echo ""
echo "🎉 ALL INFRASTRUCTURE TESTS PASSED!"
echo "=================================="
echo ""
echo "✅ Mock Infrastructure: Working"
echo "✅ E2E Test Infrastructure: Working"
echo "✅ API Mocking: Working"
echo "✅ Server Startup: Working"
echo "✅ Browser Automation: Working"
echo "✅ Unit Testing: Working"
echo "✅ Multiple Test Execution: Working"
echo ""
echo "🚀 The testing infrastructure is fully functional and ready for use!"
echo ""
echo "📋 Available Test Commands:"
echo "  npm run test:unit                    # Run unit tests"
echo "  npm run test:e2e                     # Run E2E tests (default config)"
echo "  npx playwright test --config=playwright.stable.config.ts  # Run E2E tests (stable config)"
echo "  npm run test tests/unit/mock-infrastructure.test.ts        # Test mock infrastructure"
echo ""
echo "📁 Key Files Created/Fixed:"
echo "  - playwright.stable.config.ts        # Optimized Playwright configuration"
echo "  - tests/e2e/infrastructure-test.spec.ts     # Basic infrastructure tests"
echo "  - tests/e2e/final-infrastructure-test.spec.ts  # Comprehensive tests"
echo "  - tests/unit/mock-infrastructure.test.ts     # Mock infrastructure tests"
echo "  - tests/fixtures/POST/Account/Login.json     # API mock fixtures"
echo ""
