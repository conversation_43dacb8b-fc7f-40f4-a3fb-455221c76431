import { test, expect } from '@playwright/test'

test.describe('Rest Timer Layout - Mobile', () => {
  test('should display compact rest timer layout @mock', async ({ page }) => {
    // Go directly to rest timer page
    await page.goto('/workout/rest-timer')

    // Check if rest timer is visible
    const container = await page.locator('[data-testid="rest-timer-container"]')

    // If not visible, use console to trigger it
    await page.evaluate(() => {
      if (window.useWorkoutStore) {
        window.useWorkoutStore.getState().setRestTimerState({
          isActive: true,
          duration: 90,
        })
      }
    })

    // Wait for timer to appear
    await expect(container).toBeVisible({ timeout: 5000 })

    // Verify timer row has both time and "Rest" text inline
    const timerRow = await page.locator('[data-testid="timer-row"]')
    await expect(timerRow).toBeVisible()

    // Both elements should be in the same row
    const timeDisplay = await timerRow.locator('[data-testid="time-display"]')
    const restText = await timerRow.locator('text=Rest')

    await expect(timeDisplay).toBeVisible()
    await expect(restText).toBeVisible()

    // Take screenshot for visual verification
    await page.screenshot({ path: 'rest-timer-layout.png', fullPage: false })

    // Verify buttons are visible and wider
    const durationButton = await page.locator(
      '[data-testid="duration-setting-button"]'
    )
    const hideButton = await page.locator('button:has-text("Hide")')

    await expect(durationButton).toBeVisible()
    await expect(hideButton).toBeVisible()

    // Check that buttons have proper width classes
    await expect(durationButton).toHaveClass(/px-8/)
    await expect(hideButton).toHaveClass(/px-8/)
  })
})
