import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useLoginPrefetch } from '../useLoginPrefetch'
import * as workoutApi from '@/api/workouts'
import * as apiWorkout from '@/services/api/workout'
import { useWorkoutStore } from '@/stores/workoutStore'

// Mock dependencies
vi.mock('@/api/workouts')
vi.mock('@/services/api/workout')
vi.mock('@/stores/workoutStore')
vi.mock('@/hooks/useUserStats', () => ({
  usePrefetchUserStats: () => ({ prefetch: vi.fn() }),
}))
vi.mock('@/utils/logger', () => ({
  logger: {
    log: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
  },
}))

// Create coordinator mock outside to share between tests
const mockCoordinator = {
  startLoading: vi.fn(),
  completeLoading: vi.fn(),
  isLoading: vi.fn(() => false),
  isAnyLoading: vi.fn(() => false),
  reset: vi.fn(),
  canStartLoading: vi.fn(() => true),
}

// Mock RecommendationLoadingCoordinator
vi.mock('@/utils/RecommendationLoadingCoordinator', () => ({
  RecommendationLoadingCoordinator: {
    getInstance: () => mockCoordinator,
  },
}))

const mockLoadAllExerciseRecommendations = vi.fn().mockResolvedValue(undefined)
const mockLoadExerciseRecommendation = vi.fn().mockResolvedValue(undefined)
const mockSetWorkout = vi.fn()

vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: vi.fn(() => ({
    loadAllExerciseRecommendations: mockLoadAllExerciseRecommendations,
    loadExerciseRecommendation: mockLoadExerciseRecommendation,
    setWorkout: mockSetWorkout,
    currentWorkout: null,
    workoutSession: null,
  })),
}))

describe('useLoginPrefetch with RecommendationLoadingCoordinator', () => {
  let queryClient: QueryClient

  beforeEach(() => {
    vi.useFakeTimers()

    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
      },
    })

    // Reset mock functions
    vi.clearAllMocks()
    mockLoadAllExerciseRecommendations.mockClear()
    mockLoadAllExerciseRecommendations.mockResolvedValue(undefined)
    mockLoadExerciseRecommendation.mockClear()
    mockLoadExerciseRecommendation.mockResolvedValue(undefined)
    mockSetWorkout.mockClear()

    // Reset coordinator mock behavior
    mockCoordinator.isLoading.mockReturnValue(false)
    mockCoordinator.isAnyLoading.mockReturnValue(false)
    mockCoordinator.canStartLoading.mockReturnValue(true)
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )

  it('should skip duplicate loads when recommendations already loading', async () => {
    // GIVEN recommendations already loading for exercises
    const mockExercises = [
      { Id: 1, Label: 'Bench Press' },
      { Id: 2, Label: 'Squat' },
    ]

    // Mock coordinator to indicate exercises are already loading
    mockCoordinator.isLoading.mockImplementation((id) => id === 1 || id === 2)
    mockCoordinator.isAnyLoading.mockReturnValue(true)

    // Mock API responses
    vi.mocked(apiWorkout.getUserWorkoutProgramInfo).mockResolvedValue({
      GetUserProgramInfoResponseModel: {
        NextWorkoutTemplate: { Id: 1, Label: 'Test Workout' },
      },
    } as any)

    vi.mocked(workoutApi.workoutApi.getUserWorkout).mockResolvedValue([
      {
        Id: 1,
        Label: 'Test Workout',
        Exercises: mockExercises,
      },
    ] as any)

    // WHEN prefetch starts
    const { result } = renderHook(() => useLoginPrefetch(), { wrapper })

    await act(async () => {
      result.current.startPrefetch()
      // Wait for async operations
      await vi.runAllTimersAsync()
    })

    // THEN skips duplicate loads
    expect(mockLoadAllExerciseRecommendations).not.toHaveBeenCalled()
  })

  it('should initiate loading when no active loads', async () => {
    // GIVEN no active loads
    expect(mockCoordinator.isAnyLoading([1, 2])).toBe(false)

    // Mock API responses
    vi.mocked(apiWorkout.getUserWorkoutProgramInfo).mockResolvedValue({
      GetUserProgramInfoResponseModel: {
        NextWorkoutTemplate: { Id: 1, Label: 'Test Workout' },
      },
    } as any)

    vi.mocked(workoutApi.workoutApi.getUserWorkout).mockResolvedValue([
      {
        Id: 1,
        Label: 'Test Workout',
        Exercises: [
          { Id: 1, Label: 'Bench Press' },
          { Id: 2, Label: 'Squat' },
        ],
      },
    ] as any)

    // WHEN prefetch starts
    const { result } = renderHook(() => useLoginPrefetch(), { wrapper })

    await act(async () => {
      result.current.startPrefetch()
      await vi.runAllTimersAsync()
    })

    // THEN initiates loading of first exercise
    expect(mockLoadExerciseRecommendation).toHaveBeenCalledWith(1)

    // Wait for setTimeout
    await act(async () => {
      await vi.advanceTimersByTimeAsync(500)
    })

    // Then loads all recommendations
    expect(mockLoadAllExerciseRecommendations).toHaveBeenCalled()
  })

  it('should report accurate progress considering existing loads', async () => {
    // GIVEN partial loads already complete
    mockCoordinator.startLoading(1)
    mockCoordinator.completeLoading(1)

    vi.mocked(apiWorkout.getUserWorkoutProgramInfo).mockResolvedValue({
      GetUserProgramInfoResponseModel: {
        NextWorkoutTemplate: { Id: 1, Label: 'Test Workout' },
      },
    } as any)

    vi.mocked(workoutApi.workoutApi.getUserWorkout).mockResolvedValue([
      {
        Id: 1,
        Label: 'Test Workout',
        Exercises: [
          { Id: 1, Label: 'Bench Press' },
          { Id: 2, Label: 'Squat' },
          { Id: 3, Label: 'Deadlift' },
        ],
      },
    ] as any)

    // WHEN checked
    const { result } = renderHook(() => useLoginPrefetch(), { wrapper })

    await act(async () => {
      result.current.startPrefetch()
      await vi.runAllTimersAsync()
    })

    // THEN reports accurate progress
    expect(result.current.progress).toBeGreaterThan(0)
    expect(result.current.isComplete).toBe(true)
  })

  it('should handle coordinator integration errors gracefully', async () => {
    // GIVEN coordinator throws error
    const mockError = new Error('Coordinator error')
    mockCoordinator.canStartLoading.mockImplementation(() => {
      throw mockError
    })

    vi.mocked(apiWorkout.getUserWorkoutProgramInfo).mockResolvedValue({
      GetUserProgramInfoResponseModel: {
        NextWorkoutTemplate: { Id: 1, Label: 'Test Workout' },
      },
    } as any)

    // WHEN prefetch runs
    const { result } = renderHook(() => useLoginPrefetch(), { wrapper })

    await act(async () => {
      result.current.startPrefetch()
      await vi.runAllTimersAsync()
    })

    // THEN handles error gracefully and continues
    expect(result.current.error).toBe(null) // Prefetch doesn't surface errors
    expect(result.current.isComplete).toBe(true)
  })

  it('should coordinate with workoutStore loading states', async () => {
    // Mock workout store with loading states
    const mockLoadingStates = new Map([
      [1, true],
      [2, false],
    ])

    vi.mocked(useWorkoutStore as any).mockReturnValue({
      loadAllExerciseRecommendations: mockLoadAllExerciseRecommendations,
      loadExerciseRecommendation: mockLoadExerciseRecommendation,
      setWorkout: mockSetWorkout,
      currentWorkout: null,
      workoutSession: null,
      loadingStates: mockLoadingStates,
    })

    vi.mocked(apiWorkout.getUserWorkoutProgramInfo).mockResolvedValue({
      GetUserProgramInfoResponseModel: {
        NextWorkoutTemplate: { Id: 1, Label: 'Test Workout' },
      },
    } as any)

    vi.mocked(workoutApi.workoutApi.getUserWorkout).mockResolvedValue([
      {
        Id: 1,
        Label: 'Test Workout',
        Exercises: [
          { Id: 1, Label: 'Bench Press' },
          { Id: 2, Label: 'Squat' },
        ],
      },
    ] as any)

    // WHEN prefetch runs
    const { result } = renderHook(() => useLoginPrefetch(), { wrapper })

    await act(async () => {
      result.current.startPrefetch()
      await vi.runAllTimersAsync()
    })

    // THEN respects existing loading states
    // Should load first exercise
    expect(mockLoadExerciseRecommendation).toHaveBeenCalledWith(1)

    // Wait for setTimeout
    await act(async () => {
      await vi.advanceTimersByTimeAsync(500)
    })

    // Then should call loadAll but coordinator will filter
    expect(mockLoadAllExerciseRecommendations).toHaveBeenCalled()
  })
})
