import { test, expect } from '@playwright/test'
import { login } from './helpers'

test.describe('Program Page Welcome Header', () => {
  test('displays welcome header immediately on program page', async ({
    page,
  }) => {
    // Login to the app
    await login(page)

    // Should navigate to program page after login
    await expect(page).toHaveURL('/program')

    // Welcome header should be visible immediately (no waiting for API)
    const welcomeHeader = page.locator('h1').filter({ hasText: /Welcome back/ })
    await expect(welcomeHeader).toBeVisible({ timeout: 1000 })

    // Check that motivational message is displayed
    const motivationalText = page.locator(
      'text=/Great morning|Perfect time|End your day|Late night gains/'
    )
    await expect(motivationalText).toBeVisible()
  })

  test('welcome header appears without delay on page reload', async ({
    page,
  }) => {
    // Login first
    await login(page)

    // Should be on program page
    await expect(page).toHaveURL('/program')

    // Reload the page
    await page.reload()

    // Welcome header should be visible immediately after reload
    const welcomeHeader = page.locator('h1').filter({ hasText: /Welcome back/ })
    await expect(welcomeHeader).toBeVisible({ timeout: 1000 }) // Short timeout to ensure it's immediate
  })

  test('welcome header shows correct time-based message', async ({ page }) => {
    // Login to the app
    await login(page)

    // Get current hour to determine expected message
    const hour = new Date().getHours()
    let expectedMessage: string

    if (hour >= 5 && hour < 12) {
      expectedMessage = 'Great morning to build strength!'
    } else if (hour >= 12 && hour < 17) {
      expectedMessage = 'Perfect time for your workout!'
    } else if (hour >= 17 && hour < 21) {
      expectedMessage = 'End your day strong!'
    } else {
      expectedMessage = 'Late night gains incoming!'
    }

    // Check that the correct motivational message is displayed
    const motivationalText = page.getByText(expectedMessage)
    await expect(motivationalText).toBeVisible()
  })

  test('welcome header uses proper mobile viewport styling', async ({
    page,
  }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })

    // Login to the app
    await login(page)

    // Welcome header should be visible and properly styled
    const welcomeHeader = page.locator('h1').filter({ hasText: /Welcome back/ })
    await expect(welcomeHeader).toBeVisible()

    // Check that it has proper responsive text size classes for mobile
    const headerElement = await welcomeHeader.elementHandle()
    const className = await headerElement?.getAttribute('class')
    expect(className).toContain('text-2xl') // Mobile base size
    expect(className).toContain('sm:text-3xl') // Tablet size
    expect(className).toContain('lg:text-4xl') // Desktop size
    expect(className).toContain('font-bold')

    // Ensure no horizontal overflow on mobile
    const bodyWidth = await page.evaluate(() => document.body.scrollWidth)
    const viewportWidth = await page.evaluate(() => window.innerWidth)
    expect(bodyWidth).toBeLessThanOrEqual(viewportWidth)
  })

  test('welcome header displays with animation on mount', async ({ page }) => {
    // Login to the app
    await login(page)

    // Check for animation classes
    const welcomeContainer = page.locator('div.animate-in')
    await expect(welcomeContainer).toBeVisible()

    // Verify it has the expected animation classes
    const containerElement = await welcomeContainer.first().elementHandle()
    const className = await containerElement?.getAttribute('class')
    expect(className).toContain('fade-in')
    expect(className).toContain('zoom-in-95')
  })
})
