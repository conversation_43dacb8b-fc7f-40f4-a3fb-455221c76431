import { test, expect } from '@playwright/test'
import { performLogin } from './helpers/auth-helpers'

test.describe('Exercise Loading States', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login and authenticate
    await page.goto('/login')
    await performLogin(page, '<EMAIL>', 'Dr123456')
    await page.waitForURL('/workout')
  })

  test('should show loading animation when navigating to exercise page', async ({
    page,
  }) => {
    // Navigate to exercise page
    await page.goto('/workout/exercise/1')

    // Check for loading state
    const loadingSpinner = page.getByTestId('exercise-loading-spinner')
    await expect(loadingSpinner).toBeVisible({ timeout: 1000 })

    // Verify theme-aware styling
    const container = page.locator('.bg-bg-primary')
    await expect(container).toBeVisible()

    // Verify loading text
    await expect(page.getByText('Loading exercise...')).toBeVisible()
  })

  test('should show skeleton loading when navigating to exercise-v2 page', async ({
    page,
  }) => {
    // Navigate to exercise-v2 page
    await page.goto('/workout/exercise-v2/1')

    // Check for skeleton elements
    const skeletons = page.locator('.animate-pulse')
    await expect(skeletons.first()).toBeVisible({ timeout: 1000 })

    // Verify multiple skeleton elements are shown
    const skeletonCount = await skeletons.count()
    expect(skeletonCount).toBeGreaterThan(5) // Should have multiple skeleton elements

    // Verify theme-aware background
    const container = page.locator('.bg-bg-primary')
    await expect(container).toBeVisible()
  })

  test('should not show blank page during loading on mobile viewport', async ({
    page,
  }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 812 })

    // Navigate to exercise page
    await page.goto('/workout/exercise/1')

    // Verify something is always visible
    const anyContent = page.locator(
      '[data-testid="exercise-loading-spinner"], .animate-pulse, h1, h2'
    )
    await expect(anyContent.first()).toBeVisible({ timeout: 1000 })

    // Navigate to exercise-v2 page
    await page.goto('/workout/exercise-v2/1')

    // Verify skeleton or content is visible
    await expect(anyContent.first()).toBeVisible({ timeout: 1000 })
  })

  test('should transition from loading to content smoothly', async ({
    page,
  }) => {
    // Mock API response for faster testing
    await page.route('**/api/workout/recommendation/*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Exercise: { Id: 1, Label: 'Bench Press' },
          Reps: 10,
          Weight: { Kg: 50, Lb: 110 },
          WeightIncrement: 5,
        }),
      })
    })

    // Navigate to exercise-v2 page
    await page.goto('/workout/exercise-v2/1')

    // First verify skeleton is shown
    const skeletons = page.locator('.animate-pulse')
    await expect(skeletons.first()).toBeVisible({ timeout: 1000 })

    // Then verify content appears
    await expect(page.getByText('Bench Press')).toBeVisible({ timeout: 5000 })

    // Verify skeletons are gone
    await expect(skeletons.first()).not.toBeVisible()
  })
})
