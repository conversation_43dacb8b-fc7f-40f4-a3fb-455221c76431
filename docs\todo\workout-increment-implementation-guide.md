# Workout Increment Implementation Guide

## Overview

This document provides a comprehensive guide for the web app team to implement the same workout increment logic currently used in the DrMuscle MAUI mobile application. The system calculates weight recommendations based on a three-tier preference hierarchy: exercise-specific, equipment-based, and global preferences.

## Architecture

### Preference Hierarchy (Priority Order)

```mermaid
graph TD
    A[Exercise Opened] --> B{Exercise-Level Settings?}
    B -->|Yes| C[Use Exercise Settings]
    B -->|No| D{Equipment-Based Settings?}
    D -->|Yes| E[Use Equipment Settings]
    D -->|No| F[Use Global Settings]
    
    C --> G[Apply Increment Logic]
    E --> G
    F --> G
    
    G --> H[Calculate Weight Recommendation]
    H --> I[Round to Nearest Valid Weight]
    I --> J[Return Final Recommendation]
```

### Core Components

| Component | Purpose | Priority |
|-----------|---------|----------|
| **Exercise Settings** | User-specific preferences per exercise | 1 (Highest) |
| **Equipment Settings** | Location/equipment-based preferences | 2 (Medium) |
| **Global Settings** | User's default preferences | 3 (Lowest) |

## Data Models

### RecommendationModel

```typescript
interface RecommendationModel {
  series: number;
  reps: number;
  weight: MultiUnityWeight;
  oneRMProgress: number;
  recommendationInKg: number;
  warmUpsList: WarmUp[];
  warmupsCount: number;
  isBodyweight: boolean;
  increments: MultiUnityWeight | null;
  max: MultiUnityWeight | null;
  min: MultiUnityWeight | null;
  isNormalSets: boolean;
  isDeload: boolean;
  minReps: number;
  maxReps: number;
  isPlateAvailable: boolean;
  isDumbbellAvailable: boolean;
  isPulleyAvailable: boolean;
  isBandsAvailable: boolean;
}
```

### ExerciseSettingsModel

```typescript
interface ExerciseSettingsModel {
  exerciseSettingsId: number;
  exerciseId: number;
  isCustomIncrements: boolean;
  increments: MultiUnityWeight | null;
  min: MultiUnityWeight | null;
  max: MultiUnityWeight | null;
  isCustomReps: boolean;
  repsMaxValue: number | null;
  repsMinValue: number | null;
  isCustomSets: boolean;
  isNormalSets: boolean | null;
  warmupsValue: number | null;
  setCount: number | null;
  isFavorite: boolean;
}
```

### MultiUnityWeight

```typescript
interface MultiUnityWeight {
  kg: number;
  lb: number;
  entered: number;
  unit: 'kg' | 'lb';
}

class MultiUnityWeight {
  constructor(value: number, unit: 'kg' | 'lb') {
    this.entered = value;
    this.unit = unit;
    
    if (unit === 'kg') {
      this.kg = value;
      this.lb = value * 2.20462;
    } else {
      this.lb = value;
      this.kg = value / 2.20462;
    }
  }
}
```

## API Call Flow

### Complete Recommendation Flow

```mermaid
sequenceDiagram
    participant Web as Web App
    participant API as DrMuscle API
    participant DB as Database
    
    Web->>API: POST /api/Exercise/GetRecommendationNormalRIRForExercise
    API->>DB: Get exercise history & 1RM data
    API->>Web: Base recommendation (weight, reps, series)
    
    Web->>API: POST /api/Exercise/GetExerciseSettingsPyramid
    API->>DB: Get user's exercise-specific settings
    API->>Web: Exercise settings (increments, min, max)
    
    Web->>API: POST /api/Account/GetUserInfoPyramid
    API->>DB: Get user profile & equipment settings
    API->>Web: User info (equipment, global increments)
    
    Note over Web: Apply 3-tier preference hierarchy
    Note over Web: Calculate final weight with increments
    Note over Web: Apply equipment-specific adjustments
    
    Web->>User: Display final recommendation
```

### API Timing Considerations

- **Parallel Calls:** Exercise settings and user info can be fetched in parallel
- **Caching:** Cache user info for 5 minutes, exercise settings for 10 minutes
- **Fallbacks:** If exercise settings fail, proceed with equipment/global settings

## API Endpoints

### 1. Get Exercise Recommendation

**Endpoint:** `POST /api/Exercise/GetRecommendationNormalRIRForExercise`

**Request Model:**
```typescript
interface GetRecommendationForExerciseModel {
  username: string;
  exerciseId: number;
  workoutId?: number;
  isQuickMode?: boolean;
  lightSessionDays?: number;
  swapedExId?: number;
  isStrengthPhase: boolean;
  isFreePlan: boolean;
  isFirstWorkoutOfStrengthPhase: boolean;
  versionNo: number;
}
```

**Response:** `RecommendationModel`

**Alternative Endpoints:**
- `GetRecommendationRestPauseRIRForExercise` - For rest-pause sets
- `GetRecommendationForExercise` - Legacy endpoint
- `GetRecommendationNormalRIRForExerciseWithoutDeload` - Skip deload logic

### 2. Get Exercise Settings

**Endpoint:** `POST /api/Exercise/GetExerciseSettingsPyramid`

**Request Model:**
```typescript
interface ExerciseSettingsRequest {
  exerciseId: number;
  username: string;
}
```

**Response:** `ExerciseSettingsModel`

### 3. Get User Equipment Settings

**Endpoint:** `POST /api/Account/GetUserInfoPyramid`

**Response:** Contains equipment preferences in `UserInfosModel`

## Equipment String Format Reference

### Plate Configuration Strings

**Format:** `weight_count_isSystem|weight_count_isSystem|...`

**Examples:**
```typescript
// Kg plates: "25_20_True|20_20_True|15_20_True|10_20_True|5_20_True|2.5_20_True|1.25_20_True|0.5_20_True"
// Lb plates: "45_20_True|35_20_True|25_20_True|10_20_True|5_20_True|2.5_20_True|1.25_20_True"

interface PlateConfig {
  weight: number;     // Weight of individual plate
  count: number;      // Number of plates available
  isSystem: boolean;  // True = system default, False = user added
}

// Parsing function
function parsePlateString(plateString: string): PlateConfig[] {
  return plateString.split('|').map(item => {
    const [weight, count, isSystem] = item.split('_');
    return {
      weight: parseFloat(weight),
      count: parseInt(count),
      isSystem: isSystem === 'True'
    };
  });
}
```

### Dumbbell Configuration Strings

**Format:** `weight_count_isSystem|weight_count_isSystem|...`

**Examples:**
```typescript
// Kg dumbbells: "50_2_True|47.5_2_True|45_2_True|42.5_2_True|40_2_True|37.5_2_True|35_2_True|..."
// Lb dumbbells: "90_2_True|85_2_True|80_2_True|75_2_True|70_2_True|65_2_True|60_2_True|..."

// Note: Dumbbells typically have count of 1 or 2 (for pairs)
function parseDumbbellString(dumbbellString: string): PlateConfig[] {
  return dumbbellString.split('|').map(item => {
    const [weight, count, isSystem] = item.split('_');
    return {
      weight: parseFloat(weight),
      count: Math.min(parseInt(count), 1), // Mobile app limits to 1 per dumbbell weight
      isSystem: isSystem === 'True'
    };
  });
}
```

### Pulley Configuration Strings

**Format:** `weight_count_isSystem|weight_count_isSystem|...`

**Examples:**
```typescript
// Kg pulley: "5_20_True|1.5_2_True"
// Lb pulley: "10_20_True|5_2_True|2.5_2_True"

// Pulley weights are individual increments (not pairs)
function parsePulleyString(pulleyString: string): PlateConfig[] {
  return pulleyString.split('|').map(item => {
    const [weight, count, isSystem] = item.split('_');
    return {
      weight: parseFloat(weight),
      count: parseInt(count),
      isSystem: isSystem === 'True'
    };
  });
}
```

### Bands Configuration Strings

**Format:** `color_resistance_count_isSystem|color_resistance_count_isSystem|...`

**Examples:**
```typescript
// Kg bands: "Black_40_2_True|Blue_30_2_True|Green_20_2_True|Red_10_2_True|Yellow_4_2_True"
// Lb bands: "Black_90_2_True|Blue_65_2_True|Green_45_2_True|Red_25_2_True|Yellow_10_2_True"

interface BandConfig {
  color: string;      // Band color identifier
  resistance: number; // Resistance in kg or lb
  count: number;      // Number of bands available
  isSystem: boolean;  // True = system default, False = user added
}

function parseBandString(bandString: string): BandConfig[] {
  return bandString.split('|').map(item => {
    const [color, resistance, count, isSystem] = item.split('_');
    return {
      color,
      resistance: parseFloat(resistance),
      count: Math.min(parseInt(count), 1), // Mobile app limits to 1 per band resistance
      isSystem: isSystem === 'True'
    };
  });
}
```

### Equipment String Location Mapping

```typescript
interface EquipmentStringMapping {
  gym: {
    plates: 'PlatesKg' | 'PlatesLb';
    dumbbells: 'DumbbellKg' | 'DumbbellLb';
    pulley: 'PulleyKg' | 'PulleyLb';
    bands: 'BandsKg' | 'BandsLb';
  };
  home: {
    plates: 'HomePlatesKg' | 'HomePlatesLb';
    dumbbells: 'HomeDumbbellKg' | 'HomeDumbbellLb';
    pulley: 'HomePulleyKg' | 'HomePulleyLb';
    bands: 'HomeBandsKg' | 'HomeBandsLb';
  };
  other: {
    plates: 'OtherPlatesKg' | 'OtherPlatesLb';
    dumbbells: 'OtherDumbbellKg' | 'OtherDumbbellLb';
    pulley: 'OtherPulleyKg' | 'OtherPulleyLb';
    bands: 'OtherBandsKg' | 'OtherBandsLb';
  };
}
```

## Mobile App Code Examples

### C# RoundToNearestIncrement (Mobile App)

```csharp
// From RecoComputation.cs in mobile app
public static decimal RoundToNearestIncrement(decimal numToRound, decimal Increments, decimal? min, decimal? max)
{
    if (Increments == 0 || Increments == (decimal)0.01)
    {
        return numToRound;
    }

    if (min != null)
    {
        var numAdjustedForMin = (decimal)min;
        while (numAdjustedForMin < numToRound)
        {
            numAdjustedForMin += Increments;
        }
        var numRounded = numAdjustedForMin;

        if (max != null)
        {
            if (numRounded > max)
                numRounded = (decimal)max;
        }

        return numRounded;
    }
    else
    {
        //Calc the floor value of numToRound
        decimal floor = ((long)(numToRound / Increments)) * Increments;

        //Round up if more than 50% way to Increments
        decimal numRounded = floor;
        decimal remainder = numToRound - floor;
        if (remainder > (Increments * (decimal)0.50))
            numRounded += Increments;

        if (max != null)
        {
            if (numRounded > max)
                numRounded = (decimal)max;
        }

        return numRounded;
    }
}
```

### TypeScript Equivalent

```typescript
function roundToNearestIncrement(
  numToRound: number,
  increments: number,
  min?: number,
  max?: number
): number {
  if (increments === 0 || increments === 0.01) {
    return numToRound;
  }

  if (min !== undefined) {
    let numAdjustedForMin = min;
    while (numAdjustedForMin < numToRound) {
      numAdjustedForMin += increments;
    }
    let numRounded = numAdjustedForMin;

    if (max !== undefined && numRounded > max) {
      numRounded = max;
    }

    return numRounded;
  } else {
    // Calculate the floor value of numToRound
    const floor = Math.floor(numToRound / increments) * increments;

    // Round up if more than 50% way to increments
    let numRounded = floor;
    const remainder = numToRound - floor;
    if (remainder > (increments * 0.5)) {
      numRounded += increments;
    }

    if (max !== undefined && numRounded > max) {
      numRounded = max;
    }

    return numRounded;
  }
}
```

### C# Plate Weight Calculation (Mobile App)

```csharp
// From RecoComputation.cs - GetPlatesWeight method
public static MultiUnityWeight GetPlatesWeight(string availablePlates, decimal weight, double bar, bool isKg, bool isGreater = false, bool isManual = false)
{
    if (weight < (decimal)bar)
    {
        decimal plateWeight = FindPlateWeightWithoutBar(availablePlates, weight);
        return new MultiUnityWeight(plateWeight, isKg ? "kg" : "lb");
    }

    var finals = weight - (decimal)bar;
    var platesItems = new List<AvailablePlateModel>();
    
    string[] items = availablePlates.Split('|');
    foreach (var item in items)
    {
        string[] pair = item.Split('_');
        if (pair.Length == 3)
        {
            var model = new AvailablePlateModel
            {
                Weight = Convert.ToDouble(pair[0], System.Globalization.CultureInfo.InvariantCulture),
                Value = Int32.Parse(pair[1]),
                IsSystemPlates = pair[2] == "True"
            };
            if (model.Value != 0)
                platesItems.Add(model);
        }
    }
    
    // Sort plates by weight (heaviest first)
    platesItems.Sort((c1, c2) => c2.Weight.CompareTo(c1.Weight));
    
    // Calculate plates needed (uses pairs)
    for (var i = 0; i < platesItems.Count; i++)
    {
        platesItems[i].CalculatedPlatesCount = 0;
        for (var a = 1; a <= platesItems[i].Value / 2; a++)
        {
            if (finals >= (decimal)(platesItems[i].Weight * 2))
            {
                platesItems[i].CalculatedPlatesCount++;
                finals -= (decimal)(platesItems[i].Weight * 2);
            }
        }
    }
    
    // Calculate total weight
    var platesWeight = platesItems.Sum(p => p.Weight * p.CalculatedPlatesCount);
    return new MultiUnityWeight((decimal)(bar + (platesWeight * 2)), isKg ? "kg" : "lb");
}
```

## Edge Case Scenarios

### 1. Bodyweight Exercises

**Special Behavior:**
- Increments are ignored for bodyweight exercises
- Weight recommendations are in reps, not weight
- Equipment settings don't apply

```typescript
function handleBodyweightExercise(recommendation: RecommendationModel): RecommendationModel {
  if (recommendation.isBodyweight) {
    // Don't apply weight increments
    recommendation.increments = null;
    recommendation.min = null;
    recommendation.max = null;
    
    // Equipment flags should be false
    recommendation.isPlateAvailable = false;
    recommendation.isDumbbellAvailable = false;
    recommendation.isPulleyAvailable = false;
    recommendation.isBandsAvailable = false;
    
    return recommendation;
  }
  return recommendation;
}
```

### 2. Assisted Exercises (Pull-ups, Dips)

**Special Behavior:**
- Weight represents assistance (negative weight)
- User's body weight is added to calculations
- Min/max values work differently

```typescript
function handleAssistedExercise(
  recommendation: RecommendationModel,
  userBodyWeight: number,
  isAssisted: boolean
): RecommendationModel {
  if (isAssisted) {
    // Assistance weight calculation
    const assistanceWeight = recommendation.weight.kg;
    const effectiveWeight = userBodyWeight - assistanceWeight;
    
    // Ensure assistance doesn't exceed body weight
    if (assistanceWeight >= userBodyWeight) {
      recommendation.weight = new MultiUnityWeight(userBodyWeight - 1, 'kg');
    }
    
    return recommendation;
  }
  return recommendation;
}
```

### 3. Deload Scenarios

**Special Behavior:**
- Weight is reduced by 10-20%
- Reps may be reduced
- Series count may be halved

```typescript
function handleDeloadRecommendation(
  recommendation: RecommendationModel,
  incrementSettings: IncrementSettings
): RecommendationModel {
  if (recommendation.isDeload) {
    // Reduce weight by 10-20%
    const deloadWeight = recommendation.recommendationInKg * 0.85;
    
    // Apply increment rounding to deload weight
    const roundedDeloadWeight = roundToNearestIncrement(
      deloadWeight,
      incrementSettings.increments.kg,
      incrementSettings.min?.kg,
      incrementSettings.max?.kg
    );
    
    recommendation.weight = new MultiUnityWeight(roundedDeloadWeight, 'kg');
    
    // Reduce series count
    recommendation.series = Math.max(Math.floor(recommendation.series / 2), 2);
    
    return recommendation;
  }
  return recommendation;
}
```

### 4. Weighted Exercises (Weighted Pull-ups, Dips)

**Special Behavior:**
- User's body weight is baseline
- Additional weight is added
- Min value is typically 0 (no added weight)

```typescript
function handleWeightedExercise(
  exerciseId: number,
  recommendation: RecommendationModel,
  userBodyWeight: number
): RecommendationModel {
  const weightedExerciseIds = [18627, 18628, 21234, 862, 863, 6992, 6993, 13446, 13449, 14297];
  
  if (weightedExerciseIds.includes(exerciseId)) {
    // Weight represents additional weight beyond body weight
    const additionalWeight = recommendation.weight.kg;
    const totalWeight = userBodyWeight + additionalWeight;
    
    // Set minimum to 0 (no additional weight)
    if (!recommendation.min) {
      recommendation.min = new MultiUnityWeight(0, 'kg');
    }
    
    return recommendation;
  }
  return recommendation;
}
```

### 5. Equipment Unavailability

**Special Behavior:**
- Fallback to bodyweight or different equipment
- Warning messages to user
- Alternative exercise suggestions

```typescript
function handleEquipmentUnavailability(
  recommendation: RecommendationModel,
  availableEquipment: EquipmentType[]
): RecommendationModel {
  const requiredEquipment = this.getRequiredEquipment(recommendation);
  const hasRequiredEquipment = requiredEquipment.every(eq => availableEquipment.includes(eq));
  
  if (!hasRequiredEquipment) {
    // Log warning
    console.warn('Required equipment not available', {
      required: requiredEquipment,
      available: availableEquipment
    });
    
    // Fallback to bodyweight if possible
    if (this.canConvertToBodyweight(recommendation)) {
      recommendation.isBodyweight = true;
      recommendation.weight = new MultiUnityWeight(0, 'kg');
    }
    
    return recommendation;
  }
  return recommendation;
}
```

## Validation Rules Reference

### Increment Validation

```typescript
interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

class IncrementValidator {
  static validateIncrementSettings(settings: IncrementSettings): ValidationResult {
    const errors: string[] = [];
    
    // Rule 1: Increment must be positive
    if (!settings.increments || settings.increments.kg <= 0) {
      errors.push('Increment must be greater than 0');
    }
    
    // Rule 2: Increment should be reasonable (0.25kg to 50kg)
    if (settings.increments && (settings.increments.kg < 0.25 || settings.increments.kg > 50)) {
      errors.push('Increment should be between 0.25kg and 50kg');
    }
    
    // Rule 3: Min cannot exceed max
    if (settings.min && settings.max && settings.min.kg > settings.max.kg) {
      errors.push('Minimum weight cannot exceed maximum weight');
    }
    
    // Rule 4: Min should be positive or zero
    if (settings.min && settings.min.kg < 0) {
      errors.push('Minimum weight cannot be negative');
    }
    
    // Rule 5: Max should be reasonable (up to 500kg)
    if (settings.max && settings.max.kg > 500) {
      errors.push('Maximum weight should not exceed 500kg');
    }
    
    // Rule 6: Increment should divide evenly into min-max range
    if (settings.min && settings.max && settings.increments) {
      const range = settings.max.kg - settings.min.kg;
      const steps = range / settings.increments.kg;
      if (steps > 100) {
        errors.push('Increment too small for the min-max range (would create too many steps)');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
  
  static validateRecommendationModel(recommendation: RecommendationModel): ValidationResult {
    const errors: string[] = [];
    
    // Rule 1: Weight must be positive for non-bodyweight exercises
    if (!recommendation.isBodyweight && recommendation.weight.kg <= 0) {
      errors.push('Weight must be positive for weighted exercises');
    }
    
    // Rule 2: Reps must be between 1 and 100
    if (recommendation.reps < 1 || recommendation.reps > 100) {
      errors.push('Reps must be between 1 and 100');
    }
    
    // Rule 3: Series must be between 1 and 10
    if (recommendation.series < 1 || recommendation.series > 10) {
      errors.push('Series must be between 1 and 10');
    }
    
    // Rule 4: Reps should be within min-max range
    if (recommendation.reps < recommendation.minReps) {
      errors.push('Reps below minimum range');
    }
    if (recommendation.reps > recommendation.maxReps) {
      errors.push('Reps above maximum range');
    }
    
    // Rule 5: At least one equipment type should be available for weighted exercises
    if (!recommendation.isBodyweight) {
      const hasEquipment = recommendation.isPlateAvailable || 
                          recommendation.isDumbbellAvailable || 
                          recommendation.isPulleyAvailable || 
                          recommendation.isBandsAvailable;
      if (!hasEquipment) {
        errors.push('No equipment available for weighted exercise');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
  
  static validateEquipmentConfiguration(equipment: string): ValidationResult {
    const errors: string[] = [];
    
    if (!equipment || equipment.trim() === '') {
      errors.push('Equipment configuration cannot be empty');
    }
    
    try {
      const items = equipment.split('|');
      for (const item of items) {
        const parts = item.split('_');
        
        // Validate format
        if (parts.length < 3) {
          errors.push(`Invalid equipment format: ${item}`);
          continue;
        }
        
        // Validate weight
        const weight = parseFloat(parts[0]);
        if (isNaN(weight) || weight <= 0) {
          errors.push(`Invalid weight in equipment: ${parts[0]}`);
        }
        
        // Validate count
        const count = parseInt(parts[1]);
        if (isNaN(count) || count < 0) {
          errors.push(`Invalid count in equipment: ${parts[1]}`);
        }
        
        // Validate boolean
        if (parts[2] !== 'True' && parts[2] !== 'False') {
          errors.push(`Invalid boolean in equipment: ${parts[2]}`);
        }
      }
    } catch (error) {
      errors.push('Failed to parse equipment configuration');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
```

### Business Rule Validation

```typescript
class BusinessRuleValidator {
  static validateRecommendationBusinessRules(
    recommendation: RecommendationModel,
    userAge: number,
    programType: string
  ): ValidationResult {
    const errors: string[] = [];
    
    // Rule 1: Strength phase rules
    if (this.isInStrengthPhase(programType, userAge)) {
      if (recommendation.reps > 8) {
        errors.push('Strength phase should focus on lower reps (1-8)');
      }
    }
    
    // Rule 2: Beginner protection (first month)
    if (this.isBeginnerUser(userAge)) {
      if (recommendation.weight.kg > this.getBeginnerMaxWeight(userAge)) {
        errors.push('Weight too high for beginner user');
      }
    }
    
    // Rule 3: Deload frequency
    if (recommendation.isDeload && this.hasRecentDeload()) {
      errors.push('Deload too frequent (minimum 2 weeks between deloads)');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
```

## Implementation Steps

### Step 1: Equipment Detection Logic

```typescript
class EquipmentDetector {
  static detectAvailableEquipment(userInfo: UserInfosModel): EquipmentType[] {
    const equipment: EquipmentType[] = [];
    
    // Check active location (gym, home, other)
    const activeLocation = this.getActiveLocation(userInfo);
    
    // Check equipment availability based on location
    switch (activeLocation) {
      case 'gym':
        if (userInfo.equipmentModel.isPlateEnabled) equipment.push('plates');
        if (userInfo.equipmentModel.isDumbbellEnabled) equipment.push('dumbbells');
        if (userInfo.equipmentModel.isPullyEnabled) equipment.push('pulley');
        if (userInfo.equipmentModel.isBands) equipment.push('bands');
        break;
        
      case 'home':
        if (userInfo.equipmentModel.isHomePlate) equipment.push('plates');
        if (userInfo.equipmentModel.isHomeDumbbell) equipment.push('dumbbells');
        if (userInfo.equipmentModel.isHomePully) equipment.push('pulley');
        if (userInfo.equipmentModel.isHomeBands) equipment.push('bands');
        break;
        
      case 'other':
        if (userInfo.equipmentModel.isOtherPlate) equipment.push('plates');
        if (userInfo.equipmentModel.isOtherDumbbell) equipment.push('dumbbells');
        if (userInfo.equipmentModel.isOtherPully) equipment.push('pulley');
        if (userInfo.equipmentModel.isOtherBands) equipment.push('bands');
        break;
    }
    
    return equipment;
  }
  
  private static getActiveLocation(userInfo: UserInfosModel): string {
    if (userInfo.equipmentModel.active === 'gym') return 'gym';
    if (userInfo.equipmentModel.active === 'home') return 'home';
    if (userInfo.equipmentModel.active === 'other') return 'other';
    return 'gym'; // default
  }
}
```

### Step 2: Increment Resolution Service

```typescript
class IncrementResolver {
  static resolveIncrements(
    exerciseId: number,
    exerciseSettings: ExerciseSettingsModel | null,
    userInfo: UserInfosModel,
    equipment: EquipmentType[]
  ): IncrementSettings {
    
    // Priority 1: Exercise-level settings
    if (exerciseSettings?.isCustomIncrements && exerciseSettings.increments) {
      return {
        increments: exerciseSettings.increments,
        min: exerciseSettings.min,
        max: exerciseSettings.max,
        source: 'exercise'
      };
    }
    
    // Priority 2: Equipment-based settings
    const equipmentIncrements = this.getEquipmentIncrements(equipment, userInfo);
    if (equipmentIncrements) {
      return {
        ...equipmentIncrements,
        source: 'equipment'
      };
    }
    
    // Priority 3: Global settings
    const globalIncrements = this.getGlobalIncrements(userInfo);
    return {
      ...globalIncrements,
      source: 'global'
    };
  }
  
  private static getEquipmentIncrements(
    equipment: EquipmentType[],
    userInfo: UserInfosModel
  ): IncrementSettings | null {
    
    // Primary equipment determines increments
    const primaryEquipment = equipment[0];
    const isKg = userInfo.massUnit === 'kg';
    
    switch (primaryEquipment) {
      case 'plates':
        return {
          increments: new MultiUnityWeight(isKg ? 2.5 : 5, userInfo.massUnit),
          min: null,
          max: null
        };
        
      case 'dumbbells':
        return {
          increments: new MultiUnityWeight(isKg ? 2.5 : 5, userInfo.massUnit),
          min: null,
          max: null
        };
        
      case 'pulley':
        return {
          increments: new MultiUnityWeight(isKg ? 1.5 : 2.5, userInfo.massUnit),
          min: null,
          max: null
        };
        
      case 'bands':
        return {
          increments: new MultiUnityWeight(isKg ? 4 : 10, userInfo.massUnit),
          min: null,
          max: null
        };
        
      default:
        return null;
    }
  }
  
  private static getGlobalIncrements(userInfo: UserInfosModel): IncrementSettings {
    const isKg = userInfo.massUnit === 'kg';
    const defaultIncrement = isKg ? 1 : 2.5;
    
    // Check if user has custom global increments
    if (userInfo.workoutIncrements) {
      return {
        increments: new MultiUnityWeight(userInfo.workoutIncrements, userInfo.massUnit),
        min: null,
        max: null
      };
    }
    
    return {
      increments: new MultiUnityWeight(defaultIncrement, userInfo.massUnit),
      min: null,
      max: null
    };
  }
}
```

### Step 3: Weight Calculation Engine

```typescript
class WeightCalculator {
  static calculateRecommendation(
    exerciseId: number,
    userId: string,
    workoutId?: number
  ): Promise<RecommendationModel> {
    
    // 1. Get base recommendation from API
    const baseRecommendation = await this.getBaseRecommendation(exerciseId, userId, workoutId);
    
    // 2. Get exercise settings
    const exerciseSettings = await this.getExerciseSettings(exerciseId, userId);
    
    // 3. Get user info and equipment
    const userInfo = await this.getUserInfo(userId);
    const equipment = EquipmentDetector.detectAvailableEquipment(userInfo);
    
    // 4. Resolve increment settings
    const incrementSettings = IncrementResolver.resolveIncrements(
      exerciseId,
      exerciseSettings,
      userInfo,
      equipment
    );
    
    // 5. Apply increment logic
    const finalWeight = this.applyIncrementLogic(
      baseRecommendation.recommendationInKg,
      incrementSettings,
      equipment,
      userInfo
    );
    
    // 6. Update recommendation
    baseRecommendation.weight = finalWeight;
    baseRecommendation.increments = incrementSettings.increments;
    baseRecommendation.min = incrementSettings.min;
    baseRecommendation.max = incrementSettings.max;
    
    return baseRecommendation;
  }
  
  private static applyIncrementLogic(
    targetWeight: number,
    settings: IncrementSettings,
    equipment: EquipmentType[],
    userInfo: UserInfosModel
  ): MultiUnityWeight {
    
    const isKg = userInfo.massUnit === 'kg';
    
    // Apply increment rounding
    let roundedWeight = this.roundToNearestIncrement(
      targetWeight,
      settings.increments.kg,
      settings.min?.kg,
      settings.max?.kg
    );
    
    // Apply equipment-specific adjustments
    const primaryEquipment = equipment[0];
    switch (primaryEquipment) {
      case 'plates':
        roundedWeight = this.adjustForPlates(roundedWeight, userInfo);
        break;
        
      case 'dumbbells':
        roundedWeight = this.adjustForDumbbells(roundedWeight, userInfo);
        break;
        
      case 'pulley':
        roundedWeight = this.adjustForPulley(roundedWeight, userInfo);
        break;
        
      case 'bands':
        roundedWeight = this.adjustForBands(roundedWeight, userInfo);
        break;
    }
    
    return new MultiUnityWeight(roundedWeight, 'kg');
  }
  
  private static roundToNearestIncrement(
    weight: number,
    increment: number,
    min?: number,
    max?: number
  ): number {
    
    if (increment === 0 || increment === 0.01) {
      return weight;
    }
    
    if (min !== undefined) {
      let adjusted = min;
      while (adjusted < weight) {
        adjusted += increment;
      }
      
      if (max !== undefined && adjusted > max) {
        adjusted = max;
      }
      
      return adjusted;
    } else {
      // Calculate floor value
      const floor = Math.floor(weight / increment) * increment;
      
      // Round up if more than 50% way to increment
      let rounded = floor;
      const remainder = weight - floor;
      if (remainder > (increment * 0.5)) {
        rounded += increment;
      }
      
      if (max !== undefined && rounded > max) {
        rounded = max;
      }
      
      return rounded;
    }
  }
}
```

### Step 4: Equipment-Specific Adjustments

```typescript
class EquipmentAdjustments {
  static adjustForPlates(weight: number, userInfo: UserInfosModel): number {
    const availablePlates = this.getAvailablePlates(userInfo);
    const barWeight = this.getBarWeight(userInfo);
    
    return this.calculatePlateWeight(availablePlates, weight, barWeight);
  }
  
  static adjustForDumbbells(weight: number, userInfo: UserInfosModel): number {
    const availableDumbbells = this.getAvailableDumbbells(userInfo);
    
    return this.calculateDumbbellWeight(availableDumbbells, weight);
  }
  
  static adjustForPulley(weight: number, userInfo: UserInfosModel): number {
    const availablePulleyWeights = this.getAvailablePulleyWeights(userInfo);
    
    return this.calculatePulleyWeight(availablePulleyWeights, weight);
  }
  
  static adjustForBands(weight: number, userInfo: UserInfosModel): number {
    const availableBands = this.getAvailableBands(userInfo);
    
    return this.calculateBandsWeight(availableBands, weight);
  }
  
  private static calculatePlateWeight(
    availablePlates: string,
    targetWeight: number,
    barWeight: number
  ): number {
    
    if (targetWeight < barWeight) {
      return this.findPlateWeightWithoutBar(availablePlates, targetWeight);
    }
    
    const remainingWeight = targetWeight - barWeight;
    const plates = this.parseAvailablePlates(availablePlates);
    
    // Sort plates by weight (heaviest first)
    plates.sort((a, b) => b.weight - a.weight);
    
    let totalPlateWeight = 0;
    
    for (const plate of plates) {
      while (totalPlateWeight + (plate.weight * 2) <= remainingWeight && plate.count >= 2) {
        totalPlateWeight += plate.weight * 2;
        plate.count -= 2; // Use plates in pairs
      }
    }
    
    return barWeight + totalPlateWeight;
  }
  
  private static calculateDumbbellWeight(
    availableDumbbells: string,
    targetWeight: number
  ): number {
    
    const dumbbells = this.parseAvailableDumbbells(availableDumbbells);
    
    // Sort by weight (heaviest first for closest match)
    dumbbells.sort((a, b) => b.weight - a.weight);
    
    // Find closest available dumbbell
    for (const dumbbell of dumbbells) {
      if (targetWeight >= dumbbell.weight && dumbbell.count > 0) {
        return dumbbell.weight;
      }
    }
    
    // If no suitable weight found, return lightest available
    return dumbbells[dumbbells.length - 1]?.weight || targetWeight;
  }
  
  private static parseAvailablePlates(plateString: string): PlateModel[] {
    const plates: PlateModel[] = [];
    const items = plateString.split('|');
    
    for (const item of items) {
      const parts = item.split('_');
      if (parts.length === 3) {
        plates.push({
          weight: parseFloat(parts[0]),
          count: parseInt(parts[1]),
          isSystem: parts[2] === 'True'
        });
      }
    }
    
    return plates;
  }
}
```

## Testing Strategy

### Unit Tests

```typescript
describe('IncrementResolver', () => {
  it('should prioritize exercise settings over equipment settings', () => {
    const exerciseSettings = {
      isCustomIncrements: true,
      increments: new MultiUnityWeight(1.25, 'kg')
    };
    
    const result = IncrementResolver.resolveIncrements(
      123,
      exerciseSettings,
      mockUserInfo,
      ['plates']
    );
    
    expect(result.source).toBe('exercise');
    expect(result.increments.kg).toBe(1.25);
  });
  
  it('should use equipment settings when no exercise settings exist', () => {
    const result = IncrementResolver.resolveIncrements(
      123,
      null,
      mockUserInfo,
      ['plates']
    );
    
    expect(result.source).toBe('equipment');
    expect(result.increments.kg).toBe(2.5);
  });
  
  it('should fall back to global settings', () => {
    const result = IncrementResolver.resolveIncrements(
      123,
      null,
      mockUserInfoWithGlobalIncrements,
      []
    );
    
    expect(result.source).toBe('global');
  });
});
```

### Integration Tests

```typescript
describe('WeightCalculator Integration', () => {
  it('should calculate correct weight with plate equipment', async () => {
    // Mock API responses
    mockApiResponse('/api/Exercise/GetRecommendationNormalRIRForExercise', {
      recommendationInKg: 67.5,
      // ... other fields
    });
    
    const result = await WeightCalculator.calculateRecommendation(123, 'user1');
    
    expect(result.weight.kg).toBe(70); // Rounded to nearest plate increment
  });
});
```

## Error Handling

```typescript
class IncrementErrorHandler {
  static handleCalculationError(error: Error, fallbackWeight: number): MultiUnityWeight {
    console.error('Weight calculation failed:', error);
    
    // Log error for monitoring
    this.logError('WEIGHT_CALCULATION_ERROR', error);
    
    // Return safe fallback
    return new MultiUnityWeight(fallbackWeight, 'kg');
  }
  
  static validateIncrementSettings(settings: IncrementSettings): boolean {
    if (!settings.increments || settings.increments.kg <= 0) {
      return false;
    }
    
    if (settings.min && settings.max && settings.min.kg > settings.max.kg) {
      return false;
    }
    
    return true;
  }
}
```

## Performance Considerations

### Caching Strategy

```typescript
class RecommendationCache {
  private static cache = new Map<string, CachedRecommendation>();
  private static readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes
  
  static getCachedRecommendation(key: string): RecommendationModel | null {
    const cached = this.cache.get(key);
    if (!cached || Date.now() - cached.timestamp > this.CACHE_TTL) {
      this.cache.delete(key);
      return null;
    }
    return cached.recommendation;
  }
  
  static setCachedRecommendation(key: string, recommendation: RecommendationModel): void {
    this.cache.set(key, {
      recommendation,
      timestamp: Date.now()
    });
  }
  
  static generateCacheKey(exerciseId: number, userId: string, workoutId?: number): string {
    return `rec_${userId}_${exerciseId}_${workoutId || 'default'}`;
  }
}
```

## Monitoring and Debugging

### Logging Requirements

```typescript
interface IncrementCalculationLog {
  userId: string;
  exerciseId: number;
  originalWeight: number;
  finalWeight: number;
  incrementSource: 'exercise' | 'equipment' | 'global';
  incrementValue: number;
  equipmentUsed: string[];
  calculationTime: number;
  timestamp: Date;
}

class IncrementLogger {
  static logCalculation(log: IncrementCalculationLog): void {
    // Send to monitoring service
    console.log('Increment Calculation:', log);
  }
  
  static logError(type: string, error: Error, context?: any): void {
    // Send to error tracking service
    console.error(`${type}:`, error, context);
  }
}
```