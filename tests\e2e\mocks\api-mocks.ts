import { Page } from '@playwright/test'

/**
 * Mock exercise data for E2E tests
 */
export async function mockExerciseData(page: Page) {
  await page.route('**/api/exercise/**', (route) => {
    const url = route.request().url()

    if (url.includes('/GetRecommendationForExercise')) {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Result: true,
          Data: {
            ExerciseId: 1,
            ExerciseName: 'Test Exercise',
            Weight: 100,
            Reps: 10,
            Sets: 3,
            RestTime: 120,
          },
        }),
      })
    } else if (url.includes('/GetExerciseHistory')) {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Result: true,
          Data: [],
        }),
      })
    } else {
      route.continue()
    }
  })
}

/**
 * Mock workout data for E2E tests
 */
export async function mockWorkoutData(page: Page) {
  await page.route('**/api/workout/**', (route) => {
    const url = route.request().url()

    if (url.includes('/GetCurrentWorkout')) {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Result: true,
          Data: {
            WorkoutId: 1,
            WorkoutName: 'Test Workout',
            Exercises: [
              {
                ExerciseId: 1,
                ExerciseName: 'Test Exercise',
                Sets: 3,
                Reps: 10,
                Weight: 100,
              },
            ],
          },
        }),
      })
    } else if (url.includes('/StartWorkout')) {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Result: true,
          Data: {
            WorkoutId: 1,
            ExerciseLogId: 1,
          },
        }),
      })
    } else {
      route.continue()
    }
  })
}

/**
 * Mock user authentication data
 */
export async function mockAuthData(page: Page) {
  await page.route('**/api/auth/**', (route) => {
    const url = route.request().url()

    if (url.includes('/login')) {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Result: true,
          Data: {
            Token: 'mock-token',
            User: {
              Id: 'test-user',
              Email: '<EMAIL>',
              Name: 'Test User',
            },
          },
        }),
      })
    } else {
      route.continue()
    }
  })
}

/**
 * Mock all common API responses
 */
export async function mockAllApiData(page: Page) {
  await mockExerciseData(page)
  await mockWorkoutData(page)
  await mockAuthData(page)
}
