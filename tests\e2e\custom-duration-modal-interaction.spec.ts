import { test, expect } from '@playwright/test'
import { WorkoutPage } from './pages/WorkoutPage'
import { ExercisePage } from './pages/ExercisePage'
import { setupAuthenticatedUser } from './helpers/auth-helper'

test.describe('Custom Duration Modal Interaction', () => {
  test.beforeEach(async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    await setupAuthenticatedUser(page)

    // Navigate to workout and start
    await page.goto('/workout')
    const workoutPage = new WorkoutPage(page)
    await workoutPage.waitForPageLoad()
    await workoutPage.startWorkout()

    // Navigate to exercise page
    await page.goto('/workout/exercise-v2/1')
    const exercisePage = new ExercisePage(page)
    await exercisePage.waitForPageLoad()

    // Save a set to trigger rest timer
    await page.fill('input[placeholder="Reps"]', '10')
    await page.fill('input[placeholder*="Weight"]', '100')
    await page.click('button:has-text("Save set")')

    // Wait for rest timer to appear
    await expect(page.getByTestId('rest-timer-container')).toBeVisible()
  })

  test('should allow modal interaction without closing when clicking on modal content', async ({
    page,
  }) => {
    // Open duration picker
    await page.click('[data-testid="duration-setting-button"]')
    await expect(page.getByTestId('duration-picker')).toBeVisible()

    // Click custom duration
    await page.click('[data-testid="duration-option-custom"]')

    // Check modal is visible
    const modal = page.getByTestId('custom-duration-modal')
    await expect(modal).toBeVisible()

    // Click on the modal content (not backdrop) - should NOT close
    await modal.click()
    await expect(modal).toBeVisible()

    // Click on the input field - should NOT close
    const input = page.locator('#duration-input')
    await input.click()
    await expect(modal).toBeVisible()

    // Type in the input - should work
    await input.fill('120')
    await expect(input).toHaveValue('120')

    // Click confirm button - should work and close modal
    await page.click('button:has-text("Confirm")')
    await expect(modal).not.toBeVisible()
  })

  test('should close modal when clicking backdrop', async ({ page }) => {
    // Open duration picker
    await page.click('[data-testid="duration-setting-button"]')
    await expect(page.getByTestId('duration-picker')).toBeVisible()

    // Click custom duration
    await page.click('[data-testid="duration-option-custom"]')

    // Check modal is visible
    const modal = page.getByTestId('custom-duration-modal')
    await expect(modal).toBeVisible()

    // Click on backdrop (outside modal) - should close
    await page.click('body', { position: { x: 10, y: 10 } })
    await expect(modal).not.toBeVisible()
  })

  test('should handle keyboard interaction properly', async ({ page }) => {
    // Open duration picker
    await page.click('[data-testid="duration-setting-button"]')
    await expect(page.getByTestId('duration-picker')).toBeVisible()

    // Click custom duration
    await page.click('[data-testid="duration-option-custom"]')

    // Check modal is visible
    const modal = page.getByTestId('custom-duration-modal')
    await expect(modal).toBeVisible()

    // Input should be focused
    const input = page.locator('#duration-input')
    await expect(input).toBeFocused()

    // Type new value
    await input.fill('150')
    await expect(input).toHaveValue('150')

    // Press Enter to submit
    await page.keyboard.press('Enter')
    await expect(modal).not.toBeVisible()

    // Check that duration was updated
    await expect(page.getByTestId('duration-setting')).toContainText('2:30')
  })

  test('should not appear grayed out or in background', async ({ page }) => {
    // Open duration picker
    await page.click('[data-testid="duration-setting-button"]')
    await expect(page.getByTestId('duration-picker')).toBeVisible()

    // Click custom duration
    await page.click('[data-testid="duration-option-custom"]')

    // Check modal is visible
    const modal = page.getByTestId('custom-duration-modal')
    await expect(modal).toBeVisible()

    // Check modal has proper z-index and opacity
    const modalStyles = await modal.evaluate((el) => {
      const computed = window.getComputedStyle(el)
      return {
        zIndex: computed.zIndex,
        opacity: computed.opacity,
        position: computed.position,
      }
    })

    expect(modalStyles.opacity).toBe('1')
    expect(modalStyles.position).toBe('fixed')
    expect(parseInt(modalStyles.zIndex)).toBeGreaterThan(50)

    // Check that modal content is interactive
    const confirmButton = page.locator('button:has-text("Confirm")')
    await expect(confirmButton).toBeVisible()
    await expect(confirmButton).toBeEnabled()

    const cancelButton = page.locator('button:has-text("Cancel")')
    await expect(cancelButton).toBeVisible()
    await expect(cancelButton).toBeEnabled()
  })
})
