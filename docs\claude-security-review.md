# Security Review System

This project uses a comprehensive security review system combining multiple free tools for automated security analysis of code changes.

## How It Works

### Automatic Security Review

- **Triggers**: Automatically runs on all Pull Requests targeting the `main` branch
- **Multi-layered Analysis**: Combines dependency auditing, security linting, and pattern detection
- **Fast & Free**: Uses only free tools with no external API dependencies
- **Required Check**: Must pass for PR to be merged into main

### What It Analyzes

#### 1. Dependency Security (npm audit)

- **Known Vulnerabilities**: Checks for CVEs in dependencies
- **Severity Filtering**: Fails on moderate+ severity issues
- **Automatic Fixes**: Suggests `npm audit fix` for resolvable issues

#### 2. Security Linting (ESLint Security Plugin)

- **Code Patterns**: Detects insecure coding patterns
- **Object Injection**: Prevents dynamic property access vulnerabilities
- **Best Practices**: Enforces security-focused coding standards

#### 3. Pattern Detection (Custom Script)

- **Hardcoded Secrets**: API keys, tokens, passwords in code
- **Code Injection**: eval() usage and similar dangerous patterns
- **XSS Prevention**: innerHTML assignments with variables
- **Information Leakage**: console.log in production code

### Local Development

#### Using Claude Code

If you have Claude Code installed, you can run security reviews locally:

```bash
# Run security review on current changes
/security-review
```

#### Comprehensive Security Review

Run all security checks locally:

```bash
# Run complete security review (audit + linting + patterns)
npm run security:review

# Fix auto-fixable security issues
npm run security:fix
```

#### Individual Security Checks

Run specific security checks:

```bash
# Check for dependency vulnerabilities
npm run security:audit

# Run security linting
npm run lint:security

# Run pattern-based security checks
npm run security:patterns
```

## Configuration

The security review is configured in `.github/workflows/ci-optimized.yml`:

- **Timeout**: 15 minutes for analysis
- **Excluded Directories**: `node_modules`, `dist`, `build`, `.next`, `coverage`
- **PR Comments**: Enabled - findings posted directly on code lines
- **Artifacts**: Security reports uploaded for review

## Required Setup

### GitHub Secrets

The workflow requires a `CLAUDE_API_KEY` secret to be configured in the repository:

1. Go to repository Settings → Secrets and variables → Actions
2. Add a new secret named `CLAUDE_API_KEY`
3. Set the value to your Anthropic Claude API key

### API Key Setup

To get a Claude API key:

1. Visit [Anthropic Console](https://console.anthropic.com/)
2. Create an account or sign in
3. Generate an API key
4. Add it to GitHub repository secrets

## Benefits

### Over Traditional SAST Tools

- **Contextual Understanding**: Understands code semantics, not just patterns
- **Lower False Positives**: AI reduces noise by understanding actual vulnerabilities
- **Detailed Explanations**: Clear explanations of issues and how to fix them
- **Adaptive**: Can understand complex business logic and custom security patterns

### Integration Benefits

- **Automated**: No manual intervention required
- **Fast Feedback**: Results posted directly on PR within minutes
- **Blocking**: Prevents insecure code from reaching main branch
- **Educational**: Helps developers learn about security best practices

## Troubleshooting

### Common Issues

**Security Review Fails**

- Check PR comments for specific security findings
- Review the uploaded security artifacts
- Use `/security-review` in Claude Code for local analysis

**API Key Issues**

- Verify `CLAUDE_API_KEY` is set in repository secrets
- Ensure the API key has sufficient credits/permissions
- Check the Actions logs for authentication errors

**Timeout Issues**

- Large PRs may take longer to analyze
- Consider breaking large changes into smaller PRs
- Check if excluded directories are properly configured

### Getting Help

1. **PR Comments**: Check the automated comments on your PR
2. **Action Logs**: Review the GitHub Actions logs for detailed output
3. **Local Analysis**: Use Claude Code's `/security-review` command
4. **Documentation**: See the [official documentation](https://github.com/anthropics/claude-code-security-review)

## Best Practices

1. **Small PRs**: Keep changes focused for faster, more accurate analysis
2. **Regular Reviews**: Don't wait until the end to run security checks
3. **Learn from Findings**: Use Claude's explanations to improve security knowledge
4. **Address Issues Promptly**: Fix security findings before requesting review
5. **Use Local Analysis**: Run `/security-review` during development
