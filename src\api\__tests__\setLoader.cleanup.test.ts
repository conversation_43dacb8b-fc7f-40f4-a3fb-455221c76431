import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { SetLoader } from '../setLoader'
import { workoutApi } from '@/api/workouts'
import type { WorkoutLogSerieModel } from '@/types'

// Mock the workoutApi
vi.mock('@/api/workouts', () => ({
  workoutApi: {
    getExerciseSets: vi.fn(),
  },
}))

describe('SetLoader Cleanup Mechanism', () => {
  let setLoader: SetLoader
  let mockAbortController: AbortController
  const mockSets: WorkoutLogSerieModel[] = [
    {
      Id: 1,
      ExerciseId: 123,
      Reps: 10,
      Weight: { Kg: 50, Lb: 110 },
      IsWarmups: false,
      SetTitle: 'Set 1',
    },
  ]

  beforeEach(() => {
    setLoader = new SetLoader()
    mockAbortController = {
      abort: vi.fn(),
      signal: {
        aborted: false,
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
        onabort: null,
      },
    } as any

    // Mock AbortController
    global.AbortController = vi.fn(() => mockAbortController) as any

    // Reset mocks
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('cancelPendingRequests', () => {
    it('should abort pending request when cancelPendingRequests is called', async () => {
      // Mock a slow API call that checks for abort
      vi.mocked(workoutApi.getExerciseSets).mockImplementation(
        () =>
          new Promise((resolve, reject) => {
            // Check abort signal periodically
            const checkAbort = setInterval(() => {
              if (mockAbortController.signal.aborted) {
                clearInterval(checkAbort)
                reject(new Error('Request aborted'))
              }
            }, 10)

            setTimeout(() => {
              clearInterval(checkAbort)
              resolve(mockSets)
            }, 1000)
          })
      )

      // Start a request
      const loadPromise = setLoader.loadExerciseSets(123)

      // Wait a bit to ensure request is in progress
      await new Promise((resolve) => setTimeout(resolve, 50))

      // Cancel pending requests - simulate abort
      mockAbortController.signal.aborted = true
      setLoader.cancelPendingRequests()

      // Verify abort was called
      expect(mockAbortController.abort).toHaveBeenCalled()

      // The promise should reject with abort error
      await expect(loadPromise).rejects.toThrow('Request aborted')
    })

    it('should clear all pending requests from the map', () => {
      // Mock multiple slow API calls
      vi.mocked(workoutApi.getExerciseSets).mockImplementation(
        () =>
          new Promise((resolve) => {
            setTimeout(() => resolve(mockSets), 1000)
          })
      )

      // Start multiple requests
      setLoader.loadExerciseSets(123)
      setLoader.loadExerciseSets(456)
      setLoader.loadExerciseSets(789)

      // Cancel all pending requests
      setLoader.cancelPendingRequests()

      // All requests should be cleared
      expect(setLoader.getPendingRequestCount()).toBe(0)
    })
  })

  describe('clearStuckStates', () => {
    it('should clear stuck loading states from cache', async () => {
      // First, load some data to populate cache
      vi.mocked(workoutApi.getExerciseSets).mockResolvedValue(mockSets)
      await setLoader.loadExerciseSets(123)

      // Simulate a stuck state by marking cache entry as loading
      setLoader.markAsLoading(123)

      // Clear stuck states
      setLoader.clearStuckStates()

      // Cache should be cleared for stuck entries
      await setLoader.loadExerciseSets(123)

      // Should make a new API call since cache was cleared
      expect(workoutApi.getExerciseSets).toHaveBeenCalledTimes(2)
    })
  })

  describe('retryFailed', () => {
    it('should retry failed request with exponential backoff', async () => {
      // First call fails, second succeeds
      vi.mocked(workoutApi.getExerciseSets)
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce(mockSets)

      // Mark as failed
      setLoader.markAsFailed(123)

      // Retry failed requests
      const result = await setLoader.retryFailed()

      expect(result[0].success).toBe(true)
      expect(result[0].exerciseId).toBe(123)
      expect(result[0].sets).toEqual(mockSets)
    })

    it('should handle multiple failed requests and retry in order', async () => {
      // Mock different responses for different exercises
      vi.mocked(workoutApi.getExerciseSets).mockImplementation((exerciseId) => {
        if (exerciseId === 123) return Promise.resolve(mockSets)
        if (exerciseId === 456) return Promise.resolve([])
        return Promise.reject(new Error('Still failing'))
      })

      // Mark multiple as failed
      setLoader.markAsFailed(123)
      setLoader.markAsFailed(456)
      setLoader.markAsFailed(789)

      // Retry all failed
      const results = await setLoader.retryFailed()

      expect(results).toHaveLength(3)
      expect(results[0].success).toBe(true)
      expect(results[1].success).toBe(true)
      expect(results[2].success).toBe(false)
    })
  })

  describe('Edge Cases', () => {
    it('should handle request completing during cancel', async () => {
      let resolveRequest: (value: WorkoutLogSerieModel[]) => void
      const requestPromise = new Promise<WorkoutLogSerieModel[]>((resolve) => {
        resolveRequest = resolve
      })

      vi.mocked(workoutApi.getExerciseSets).mockReturnValue(requestPromise)

      // Start request
      const loadPromise = setLoader.loadExerciseSets(123)

      // Complete request before cancel
      resolveRequest!(mockSets)

      // Then cancel (should handle gracefully)
      setLoader.cancelPendingRequests()

      // Should still get the result
      const result = await loadPromise
      expect(result).toEqual(mockSets)
    })

    it('should handle retry when network is offline', async () => {
      // Simulate offline
      vi.mocked(workoutApi.getExerciseSets).mockRejectedValue(
        new Error('Network request failed')
      )

      setLoader.markAsFailed(123)
      const results = await setLoader.retryFailed()

      expect(results[0].success).toBe(false)
      expect(results[0].error).toContain('Network request failed')
    })

    it('should handle memory pressure during operations', () => {
      // Create many pending requests
      vi.mocked(workoutApi.getExerciseSets).mockImplementation(
        () => new Promise(() => {}) // Never resolves
      )

      // Start many requests
      for (let i = 0; i < 100; i++) {
        setLoader.loadExerciseSets(i)
      }

      // Should be able to cancel all without memory issues
      expect(() => setLoader.cancelPendingRequests()).not.toThrow()
      expect(setLoader.getPendingRequestCount()).toBe(0)
    })
  })
})
