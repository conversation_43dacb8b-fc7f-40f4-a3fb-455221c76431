import { useEffect, useRef } from 'react'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useExerciseSetsPrefetch } from './useExerciseSetsPrefetch'
import { useIntelligentPrefetchScheduling } from './useIntelligentPrefetchScheduling'
import { logger } from '@/utils/logger'

/**
 * Hook that implements progressive prefetching of exercise sets
 * Automatically prefetches upcoming exercises as the user progresses through their workout
 * Now includes intelligent scheduling based on user pace
 */
export function useProgressivePrefetch() {
  const {
    exercises,
    currentExerciseIndex,
    workoutSession,
    previewExerciseSkips,
  } = useWorkoutStore()
  const { prefetchExerciseSets, prefetchedExerciseIds } =
    useExerciseSetsPrefetch()

  // Track last prefetch position to avoid duplicate prefetches
  const lastPrefetchIndexRef = useRef<number>(-1)
  const hasInitializedRef = useRef(false)

  // Use intelligent scheduling to optimize prefetch timing and quantity
  const { schedulePrefetch, userPace, schedulingActive } =
    useIntelligentPrefetchScheduling({
      exercises,
      currentExerciseIndex,
      workoutSession,
      prefetchedExerciseIds,
      previewExerciseSkips,
      prefetchExerciseSets,
      autoSchedule: true,
    })

  useEffect(() => {
    // Only prefetch if workout is active
    if (!workoutSession) {
      return
    }

    // Skip initial render to avoid prefetching before user starts
    if (!hasInitializedRef.current) {
      hasInitializedRef.current = true
      lastPrefetchIndexRef.current = currentExerciseIndex
      return
    }

    // Check if we've already prefetched for this position
    if (currentExerciseIndex === lastPrefetchIndexRef.current) {
      return
    }

    // Use intelligent scheduling if available, otherwise fall back to basic logic
    if (schedulingActive) {
      logger.log(
        `[useProgressivePrefetch] Using intelligent scheduling (pace: ${userPace.toFixed(2)} ex/min) ` +
          `at exercise ${currentExerciseIndex + 1}/${exercises.length}`
      )

      // Update last prefetch index and let intelligent scheduling handle the rest
      lastPrefetchIndexRef.current = currentExerciseIndex

      // Trigger intelligent prefetch (auto-schedule will handle timing)
      schedulePrefetch().catch((error) => {
        logger.error(
          '[useProgressivePrefetch] Intelligent prefetch failed:',
          error
        )
      })
    } else {
      // Fallback to basic prefetch logic for compatibility
      logger.log('[useProgressivePrefetch] Using basic prefetch logic')

      // Calculate prefetch window based on current position
      // Start prefetching 2 exercises ahead of current
      const prefetchStartIndex = currentExerciseIndex + 2
      const targetPrefetchCount = 2

      // Get exercises to prefetch - look ahead until we find enough exercises
      const exercisesToPrefetch: number[] = []

      for (
        let i = prefetchStartIndex;
        i < exercises.length &&
        exercisesToPrefetch.length < targetPrefetchCount;
        i++
      ) {
        const exercise = exercises[i]
        if (
          exercise &&
          !prefetchedExerciseIds.includes(exercise.Id) &&
          !previewExerciseSkips?.has(exercise.Id)
        ) {
          exercisesToPrefetch.push(exercise.Id)
        }
      }

      // If we have exercises to prefetch, do it
      if (exercisesToPrefetch.length > 0) {
        logger.log(
          `[useProgressivePrefetch] Basic prefetch: exercises ${exercisesToPrefetch.join(', ')} ` +
            `(user at exercise ${currentExerciseIndex + 1}/${exercises.length})`
        )

        // Update last prefetch index
        lastPrefetchIndexRef.current = currentExerciseIndex

        // Prefetch in the background
        prefetchExerciseSets(exercisesToPrefetch).catch((error) => {
          logger.error('[useProgressivePrefetch] Basic prefetch failed:', error)
        })
      }
    }
  }, [
    currentExerciseIndex,
    exercises,
    workoutSession,
    previewExerciseSkips,
    prefetchExerciseSets,
    prefetchedExerciseIds,
    schedulingActive,
    schedulePrefetch,
    userPace,
  ])
}
