import { renderHook, act } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { useVisibilityChange } from '../useVisibilityChange'

describe('useVisibilityChange', () => {
  let mockVisibilityState: string
  let visibilityChangeListeners: Array<() => void> = []
  let focusListeners: Array<() => void> = []
  let blurListeners: Array<() => void> = []

  beforeEach(() => {
    // Mock document.visibilityState
    mockVisibilityState = 'visible'
    Object.defineProperty(document, 'visibilityState', {
      get: () => mockVisibilityState,
      configurable: true,
    })

    // Mock addEventListener and removeEventListener
    vi.spyOn(document, 'addEventListener').mockImplementation(
      (event: string, listener: any) => {
        if (event === 'visibilitychange') {
          visibilityChangeListeners.push(listener)
        }
      }
    )
    vi.spyOn(document, 'removeEventListener').mockImplementation(
      (event: string, listener: any) => {
        if (event === 'visibilitychange') {
          visibilityChangeListeners = visibilityChangeListeners.filter(
            (l) => l !== listener
          )
        }
      }
    )

    vi.spyOn(window, 'addEventListener').mockImplementation(
      (event: string, listener: any) => {
        if (event === 'focus') {
          focusListeners.push(listener)
        } else if (event === 'blur') {
          blurListeners.push(listener)
        }
      }
    )
    vi.spyOn(window, 'removeEventListener').mockImplementation(
      (event: string, listener: any) => {
        if (event === 'focus') {
          focusListeners = focusListeners.filter((l) => l !== listener)
        } else if (event === 'blur') {
          blurListeners = blurListeners.filter((l) => l !== listener)
        }
      }
    )
  })

  afterEach(() => {
    visibilityChangeListeners = []
    focusListeners = []
    blurListeners = []
    vi.restoreAllMocks()
  })

  it('should call callback with "hidden" when visibility changes to hidden', () => {
    const callback = vi.fn()
    renderHook(() => useVisibilityChange(callback))

    // Simulate visibility change to hidden
    act(() => {
      mockVisibilityState = 'hidden'
      visibilityChangeListeners.forEach((listener) => listener())
    })

    expect(callback).toHaveBeenCalledWith('hidden')
  })

  it('should call callback with "visible" when visibility changes to visible', () => {
    const callback = vi.fn()
    mockVisibilityState = 'hidden' // Start hidden
    renderHook(() => useVisibilityChange(callback))

    // Simulate visibility change to visible
    act(() => {
      mockVisibilityState = 'visible'
      visibilityChangeListeners.forEach((listener) => listener())
    })

    expect(callback).toHaveBeenCalledWith('visible')
  })

  it('should remove listener when component unmounts', () => {
    const callback = vi.fn()
    const { unmount } = renderHook(() => useVisibilityChange(callback))

    expect(visibilityChangeListeners.length).toBe(1)
    expect(focusListeners.length).toBe(1)
    expect(blurListeners.length).toBe(1)

    unmount()

    expect(visibilityChangeListeners.length).toBe(0)
    expect(focusListeners.length).toBe(0)
    expect(blurListeners.length).toBe(0)
  })

  it('should fire all callbacks when multiple components use the hook', () => {
    const callback1 = vi.fn()
    const callback2 = vi.fn()
    const callback3 = vi.fn()

    renderHook(() => useVisibilityChange(callback1))
    renderHook(() => useVisibilityChange(callback2))
    renderHook(() => useVisibilityChange(callback3))

    // Simulate visibility change
    act(() => {
      mockVisibilityState = 'hidden'
      visibilityChangeListeners.forEach((listener) => listener())
    })

    expect(callback1).toHaveBeenCalledWith('hidden')
    expect(callback2).toHaveBeenCalledWith('hidden')
    expect(callback3).toHaveBeenCalledWith('hidden')
  })

  it('should handle window focus event as visible', () => {
    const callback = vi.fn()
    mockVisibilityState = 'hidden'
    renderHook(() => useVisibilityChange(callback))

    // Simulate window focus
    act(() => {
      focusListeners.forEach((listener) => listener())
    })

    expect(callback).toHaveBeenCalledWith('visible')
  })

  it('should handle window blur event as hidden', () => {
    const callback = vi.fn()
    renderHook(() => useVisibilityChange(callback))

    // Simulate window blur
    act(() => {
      blurListeners.forEach((listener) => listener())
    })

    expect(callback).toHaveBeenCalledWith('hidden')
  })

  it('should handle rapid visibility changes', () => {
    const callback = vi.fn()
    renderHook(() => useVisibilityChange(callback))

    // Simulate rapid changes
    act(() => {
      mockVisibilityState = 'hidden'
      visibilityChangeListeners.forEach((listener) => listener())
      mockVisibilityState = 'visible'
      visibilityChangeListeners.forEach((listener) => listener())
      mockVisibilityState = 'hidden'
      visibilityChangeListeners.forEach((listener) => listener())
    })

    expect(callback).toHaveBeenCalledTimes(3)
    expect(callback).toHaveBeenNthCalledWith(1, 'hidden')
    expect(callback).toHaveBeenNthCalledWith(2, 'visible')
    expect(callback).toHaveBeenNthCalledWith(3, 'hidden')
  })

  it('should not fire callback when state does not change', () => {
    const callback = vi.fn()
    renderHook(() => useVisibilityChange(callback))

    // Simulate visibility event without state change
    act(() => {
      visibilityChangeListeners.forEach((listener) => listener())
    })

    expect(callback).not.toHaveBeenCalled()
  })

  it('should handle browser without visibility API gracefully', () => {
    // Remove visibility API
    Object.defineProperty(document, 'visibilityState', {
      get: () => undefined,
      configurable: true,
    })

    const callback = vi.fn()
    renderHook(() => useVisibilityChange(callback))

    // First blur to set state to hidden
    act(() => {
      blurListeners.forEach((listener) => listener())
    })

    expect(callback).toHaveBeenCalledWith('hidden')
    callback.mockClear()

    // Then focus should work and set to visible
    act(() => {
      focusListeners.forEach((listener) => listener())
    })

    expect(callback).toHaveBeenCalledWith('visible')
  })

  it('should handle unmount during visibility change', () => {
    const callback = vi.fn()
    const { unmount } = renderHook(() => useVisibilityChange(callback))

    // Start visibility change but unmount before it completes
    act(() => {
      mockVisibilityState = 'hidden'
      // Unmount immediately
      unmount()
      // Then trigger listeners (simulating async event)
      visibilityChangeListeners.forEach((listener) => listener())
    })

    // Callback should not be called after unmount
    expect(callback).not.toHaveBeenCalled()
  })
})
