'use client'

interface QuickModeToggleProps {
  value: boolean
  onChange: (value: boolean) => void
  disabled?: boolean
}

export default function QuickModeToggle({
  value,
  onChange,
  disabled = false,
}: QuickModeToggleProps) {
  return (
    <div className="flex items-center justify-between min-h-[52px]">
      <div className="flex-1 pr-4">
        <div className="text-sm font-medium text-text-primary">Quick Mode</div>
        <div className="text-xs text-text-secondary mt-1">
          Faster workout flow with fewer prompts and automatic progression
        </div>
      </div>
      <button
        onClick={() => onChange(!value)}
        disabled={disabled}
        className={`
          relative inline-flex min-h-[52px] min-w-[88px] items-center rounded-full
          px-1 transition-colors duration-200 ease-in-out
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
          ${value ? 'bg-brand-primary' : 'bg-surface-tertiary'}
        `}
        role="switch"
        aria-checked={value}
        aria-label="Quick Mode"
      >
        <span
          className={`
            inline-block h-11 w-11 transform rounded-full
            bg-white transition-transform duration-200 ease-in-out shadow-sm
            ${value ? 'translate-x-9' : 'translate-x-0'}
          `}
        />
      </button>
    </div>
  )
}
