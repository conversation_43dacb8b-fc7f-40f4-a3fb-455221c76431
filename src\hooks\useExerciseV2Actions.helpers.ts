import type { ExerciseModel, WorkoutLogSerieModel } from '@/types'

// Extended type to match generateAllSets output
export type ExtendedWorkoutLogSerieModel = WorkoutLogSerieModel & {
  WarmUpReps?: number
  WarmUpWeightSet?: { Lb: number; Kg: number }
}

export function getNextSetInfo(
  allSets: ExtendedWorkoutLogSerieModel[],
  currentSetIdx: number,
  unit: 'kg' | 'lbs'
) {
  const nextSetData = allSets[currentSetIdx + 1]
  if (!nextSetData) return undefined

  return {
    reps:
      nextSetData.IsWarmups && nextSetData.WarmUpReps
        ? nextSetData.WarmUpReps
        : nextSetData.Reps || 0,
    weight:
      nextSetData.IsWarmups && nextSetData.WarmUpWeightSet
        ? nextSetData.WarmUpWeightSet[unit === 'kg' ? 'Kg' : 'Lb']
        : nextSetData.Weight?.[unit === 'kg' ? 'Kg' : 'Lb'] || 0,
    unit,
  }
}

export function getRestDuration(isWarmup: boolean): number {
  return isWarmup ? 30 : 90 // 30s for warmups, 90s for work sets
}

export function findNextExercise(
  exercises: ExerciseModel[],
  currentExerciseId: number
): ExerciseModel | null {
  const currentIndex = exercises.findIndex((ex) => ex.Id === currentExerciseId)
  if (currentIndex === -1 || currentIndex >= exercises.length - 1) {
    return null
  }
  return exercises[currentIndex + 1] || null
}

export function logSaveAttempt(
  currentExercise: ExerciseModel | null,
  workoutSession: unknown,
  setData: { reps: number; weight: number }
) {
  // eslint-disable-next-line no-console
  console.log('[ExerciseV2] Save button clicked - checking required data:', {
    hasCurrentExercise: !!currentExercise,
    hasWorkoutSession: !!workoutSession,
    currentExerciseId: currentExercise?.Id,
    currentExerciseLabel: currentExercise?.Label,
    workoutSessionId:
      workoutSession &&
      typeof workoutSession === 'object' &&
      'id' in workoutSession
        ? String(workoutSession.id)
        : 'null',
    setDataReps: setData.reps,
    setDataWeight: setData.weight,
  })
}

export function logMissingData(currentExercise: ExerciseModel | null) {
  console.warn('[ExerciseV2] Cannot save set - missing required data:', {
    hasCurrentExercise: !!currentExercise,
    hasWorkoutSession: false,
    currentExerciseId: currentExercise?.Id,
    currentExerciseLabel: currentExercise?.Label,
  })
}
