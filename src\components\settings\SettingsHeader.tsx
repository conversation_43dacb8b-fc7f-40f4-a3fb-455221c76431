'use client'

import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/stores/authStore'
import { IOSNavigationBar } from '@/components/navigation/IOSNavigationBar'
import { GradientChevronLeftIcon } from '@/components/icons/GradientChevronLeftIcon'

export default function SettingsHeader() {
  const router = useRouter()
  const { getCachedUserInfo } = useAuthStore()
  const userInfo = getCachedUserInfo() as Record<string, unknown>

  const handleBack = () => {
    router.back()
  }

  return (
    <IOSNavigationBar
      title="Settings"
      leftElement={
        <button
          onClick={handleBack}
          className="flex items-center justify-center w-11 h-11 -ml-3 active:opacity-60 transition-opacity"
          aria-label="Go back"
        >
          <GradientChevronLeftIcon className="w-6 h-6" />
        </button>
      }
      rightElement={
        userInfo?.Email && typeof userInfo.Email === 'string' ? (
          <div className="text-xs text-text-secondary truncate max-w-[120px]">
            {userInfo.Email}
          </div>
        ) : null
      }
    />
  )
}
